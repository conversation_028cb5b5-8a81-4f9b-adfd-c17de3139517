#!/usr/bin/env php
<?php

if (!file_exists(dirname(__DIR__).'/vendor/bin/phpunit')) {
    echo "Unable to find the `phpunit` script in `vendor/bin/`.\n";
    exit(1);
}

if (false === getenv('SYMFONY_PHPUNIT_VERSION')) {
    putenv('SYMFONY_PHPUNIT_VERSION=10.1');
}
if (false === getenv('SYMFONY_PHPUNIT_DIR')) {
    putenv('SYMFONY_PHPUNIT_DIR='.__DIR__.'/vendor/phpunit/phpunit');
}

require dirname(__DIR__).'/vendor/bin/phpunit';
