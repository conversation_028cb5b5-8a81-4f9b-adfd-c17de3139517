<?php

namespace Sparefoot\MyFootService\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class TwigExtension extends AbstractExtension
{
    public function getFunctions(): array
    {
        return [
            new TwigFunction('call_method', [$this, 'callMethod']),
            new TwigFunction('call_static', [$this, 'callStaticMethod']),
            new TwigFunction('get_class', 'get_class'),
            new TwigFunction('getenv', 'getenv'),
            new TwigFunction('intval', 'intval'),
        ];
    }

    public function callMethod($object, string $method)
    {
        return $object->$method();
    }

    public function callStaticMethod(string $class, string $method, ...$args)
    {
        return call_user_func([$class, $method], ...$args);
    }
}
