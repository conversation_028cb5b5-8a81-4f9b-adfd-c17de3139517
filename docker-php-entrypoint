#!/bin/sh
set -e

# first arg is `-f` or `--some-option`
if [ "${1#-}" != "$1" ]; then
    set -- apache2-foreground "$@"
fi

if [ "$SF_ENV" = "local" ]; then
    echo "opcache.enable=0" >> /usr/local/etc/php/conf.d/sf_php.ini
    echo "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)" >> /usr/local/etc/php/conf.d/sf_php.ini
    echo "xdebug.remote_enable=1" >> /usr/local/etc/php/conf.d/sf_php.ini
    echo "xdebug.remote_port=9090" >> /usr/local/etc/php/conf.d/sf_php.ini
    
    # Remove explicit loading of opentelemetry as it's likely already loaded by the base image
    echo "extension=opentelemetry.so" >> /usr/local/etc/php/conf.d/sf_php.ini

    # IF USING XDEBUG V3, USE CONFIG BELOW INSTEAD
    echo "xdebug.start_with_request=yes" >> /usr/local/etc/php/conf.d/sf_php.ini
    echo "xdebug.mode=debug" >> /usr/local/etc/php/conf.d/sf_php.ini
fi

exec "$@"
