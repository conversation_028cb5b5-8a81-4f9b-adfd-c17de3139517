{"repositories": [{"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/emails_service_client.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/phlow_client.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/reservation_rules.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/authorization.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/error_logger.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/pillar_lib.git"}, {"type": "vcs", "url": "https://github.com/SpareFoot/rollbar-php"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/genesis.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/sf_service_bundle.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/salesforce_client.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/marketplace_api_client_bundle"}], "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=7.4", "sparefoot/emails_service_client": "dev-guzzle6-lock", "sparefoot/error-logging": "dev-master", "sparefoot/phlow_client": "dev-master", "sparefoot/pillar": "dev-master", "sparefoot/reservation_rules": "dev-master", "zendframework/zendframework1": "1.*", "zordius/lightncandy": "^1.2", "lmc/steward": "2.3.4", "sparefoot/authorization": "dev-master", "phpunit/phpunit": "^5.7.11", "sparefoot/salesforce_client": "dev-master", "ext-zip": "^1.15", "ext-soap": "^7.4", "ext-gd": "^7.4", "ext-pdo": "^7.2", "ext-json": "*", "sparefoot/genesis": "dev-master"}, "require-dev": {"andreas-weber/php-junit-merge": "^1.0", "browserstack/browserstack-local": "^1.1"}, "autoload": {"psr-4": {"MyfootTests\\": "tests/", "": "application/controller"}, "files": ["application/util/CsrfUtil.php", "application/util/StatementData.php"]}}