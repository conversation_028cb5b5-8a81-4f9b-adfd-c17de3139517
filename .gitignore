.idea/
.tags
.vimprj
*.sw[op]
docker-compose-local.yml
docker_compose/local/docker-compose.yml
docker_compose/local/.env
junit.xml
junit.merged.xml
library/
local.log
logs/
errorShots/
/mocha.json
nbproject/
node_modules/
results.json
screenshots/
supervisord.log
supervisord.pid
*.iml
.DS_Store

# Built files
public/dist/ember/
public/dist/init.js
public/dist/app.css
public/dist/app.js
public/css/app.css
public/new-ui/dist/myfoot.css
public/new-ui/dist/myfoot.js

# Zip Creation - ant create-zip
dist/myfoot.zip
npm-debug.log

/docker_compose_v2/docker-compose-local.yml
/docker_compose_v2/docker-compose.yml
auth.json
.vscode/launch.json
.vscode
###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###
###> symfony/phpunit-bridge ###
.phpunit
/phpunit.xml
.phpunit.result.cache
###< symfony/phpunit-bridge ###
/.php-cs-fixer.cache
.DS_Store

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
.phpunit.cache/*
###< phpunit/phpunit ###

###> friendsofphp/php-cs-fixer ###
/.php-cs-fixer.php
/.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###

###> squizlabs/php_codesniffer ###
/.phpcs-cache
/phpcs.xml
###< squizlabs/php_codesniffer ###