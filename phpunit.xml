<?xml version="1.0" encoding="UTF-8"?>
<phpunit
        colors="true"
        stopOnFailure="false"
        syntaxCheck="false"
        verbose="true"
        convertErrorsToExceptions="true"
        convertNoticesToExceptions="false"
        convertWarningsToExceptions="false"
        processIsolation="false"
        bootstrap="tests/bootstrap.php">
    <testsuites>
        <testsuite name="MyFoot_Unit_Test_Suite">
            <directory>tests/UnitTests/</directory>
        </testsuite>
        <testsuite name="MyFoot_IntegrationTests_Suite">
            <directory>tests/IntegrationTests/Api</directory>
        </testsuite>
    </testsuites>
    <logging>
        <log type="junit" target="./logs/junit.xml" logIncompleteSkipped="false"/>
    </logging>
</phpunit>
