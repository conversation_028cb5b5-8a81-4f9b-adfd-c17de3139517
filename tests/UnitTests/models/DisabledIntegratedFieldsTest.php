<?php

namespace MyfootTests\UnitTests\Models;

use AccountMgmt_Service_IntegratedFields;
use Genesis_Entity_Source;

class DisabledIntegratedFieldsTest extends \PHPUnit_Framework_TestCase
{
    public function testEssIntegratedFields()
    {
        $essSourceId = Genesis_Entity_Source::ID_ESS;
        $originalEssMappedAmenities = [
            'price_regular',
            'unitName',
            'unit_width',
            'unit_length',
            'unit_height',
        ];

        $testEssAmenities = AccountMgmt_Service_IntegratedFields::getBaseIntegratedFields($essSourceId);
        // ensure order of arrays for comparison
        sort($originalEssMappedAmenities);
        sort($testEssAmenities);

        $this->assertEquals($originalEssMappedAmenities, $testEssAmenities);
    }

    public function testSitelinkIntegratedFields()
    {
        $sitelinkSourceId = Genesis_Entity_Source::ID_SITELINK;
        $originalSitelinkMappedAmenities = [
            'accountId',
            'ada',
            'alarm',
            'approved',
            'unit_type',
            'climate',
            'floor',
            'unit_length',
            'listingAvailId',
            'listingAvailSpaceId',
            'outdoor_access',
            'power',
            'publish',
            'qty',
            'price_regular',
            'promotion',
            'unit_width'
        ];

        $testSitelinkAmenities = AccountMgmt_Service_IntegratedFields::getBaseIntegratedFields($sitelinkSourceId);
        // ensure order of arrays for comparison
        sort($originalSitelinkMappedAmenities);
        sort($testSitelinkAmenities);

        $this->assertEquals($originalSitelinkMappedAmenities, $testSitelinkAmenities);
    }

    public function testStoredgeIntegratedFields()
    {
        $storedgeSourceId = Genesis_Entity_Source::ID_STOREDGE;
        $originalStoredgeMappedAmenities = [
            'promotion',
            'price_sf',
            'price_regular',
        ];

        $testStoredgeAmenities = AccountMgmt_Service_IntegratedFields::getBaseIntegratedFields($storedgeSourceId);
        // ensure order of arrays for comparison
        sort($originalStoredgeMappedAmenities);
        sort($testStoredgeAmenities);

        $this->assertEquals($originalStoredgeMappedAmenities, $testStoredgeAmenities);
    }
}
