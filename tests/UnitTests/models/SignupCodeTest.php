<?php

namespace MyfootTests\UnitTests\Models;

use AccountMgmt_Models_SignupCode;
use Genesis_Entity_Account as Account;
use InvalidArgumentException;

class SignupCodeTest extends \PHPUnit_Framework_TestCase
{
    public function testCreateAndValidateSignupCodeWithInvalidCodeLetter()
    {
        // Build list of characters A-Z excluding C and R, which *are* valid sign-up code letters
        $invalidCodeLetters = array_filter(range('A', 'Z'), function($value) {
            return !in_array($value, ['C', 'R']);
        });

        foreach($invalidCodeLetters as $invalidCodeLetter) {
            try {
                $code = $invalidCodeLetter . '0X0';
                $signupCode = new AccountMgmt_Models_SignupCode($code);
                $this->fail('No exception thrown for code "' . $code . '".');
            } catch(\Exception $e) {
                $this->assertTrue($e instanceof \RuntimeException);
            }
        }
    }

    public function testCreateAndValidateCpaSignupCodeWithInvalidSubcodeLetter()
    {
        // Build list of characters A-Z excluding P and X, which *are* valid sign-up code letters
        $invalidCodeLetters = array_filter(range('A', 'Z'), function($value) {
            return !in_array($value, ['P', 'X']);
        });

        foreach($invalidCodeLetters as $invalidCodeLetter) {
            try {
                $code = 'C0' . $invalidCodeLetter . '0';
                $signupCode = new AccountMgmt_Models_SignupCode($code);
                $this->fail('No exception thrown for code "' . $code . '".');
            } catch(\Exception $e) {
                $this->assertTrue($e instanceof \RuntimeException);
            }
        }
    }

    public function testCreateAndValidateResidualSignupCodeWithInvalidSubcodeLetter()
    {
        // Build list of characters A-Z excluding P and X, which *are* valid sign-up code letters
        $invalidCodeLetters = array_filter(range('A', 'Z'), function($value) {
            return !in_array($value, ['P', 'X']);
        });

        foreach($invalidCodeLetters as $invalidCodeLetter) {
            try {
                $code = 'R0' . $invalidCodeLetter . '0';
                $signupCode = new AccountMgmt_Models_SignupCode($code);
                $this->fail('No exception thrown for code "' . $code . '".');
            } catch(\Exception $e) {
                $this->assertTrue($e instanceof \RuntimeException);
            }
        }
    }

    public function testCreateAndValidatePercentCpaCodeWithInvalidBidValue()
    {
        $generator = self::generateInvalidBidValue(Account::BID_TYPE_PERCENT);
        foreach ($generator as $bidValue) {
            try {
                $digits = self::splitBidValue($bidValue);
                $code = 'C' . $digits[0] . 'P' . $digits[1];
                $signupCode = new AccountMgmt_Models_SignupCode($code);
                $this->fail('No exception thrown for code "' . $code . '".');
            } catch(\Exception $e) {
                $this->assertTrue($e instanceof \RuntimeException);
            }
        }
    }

    public function testCreateAndValidateResidualCodeWithInvalidBidValue()
    {
        $generator = self::generateInvalidBidValue(Account::BID_TYPE_RESIDUAL);
        foreach ($generator as $bidValue) {
            try {
                $digits = self::splitBidValue($bidValue);
                $code = 'R' . $digits[0] . 'X' . $digits[1];
                $signupCode = new AccountMgmt_Models_SignupCode($code);
                $this->fail('No exception thrown for code "' . $code . '".');
            } catch(\Exception $e) {
                $this->assertTrue($e instanceof \RuntimeException);
            }
        }
    }

    public function testCreateAndValidatePercentCpaCode()
    {
        $generator = self::generateValidSignupCode(Account::BID_TYPE_PERCENT);
        foreach ($generator as $code => $bidValue) {
            $signupCode = new AccountMgmt_Models_SignupCode($code);
            $this->assertEquals($code, $signupCode->getCode(), 'Expected sign-up code to match');
            $this->assertEquals(Account::BID_TYPE_PERCENT, $signupCode->getBidType(), 'Expected bid type to match');
            $this->assertEquals($bidValue, $signupCode->getValue(), 'Expected value to match');
        }
    }

    public function testCreateAndValidateResidualCode()
    {
        $generator = self::generateValidSignupCode(Account::BID_TYPE_RESIDUAL);
        foreach ($generator as $code => $bidValue) {
            $signupCode = new AccountMgmt_Models_SignupCode($code);
            $this->assertEquals($code, $signupCode->getCode(), 'Expected sign-up code to match');
            $this->assertEquals(Account::BID_TYPE_RESIDUAL, $signupCode->getBidType(), 'Expected bid type to match');
            $this->assertEquals($bidValue, $signupCode->getValue(), 'Expected value to match');
        }
    }

    /**
     * @param string $bidType
     * @param int|float $bidValue
     * @return string
     */
    private static function formatSignupCode($bidType = Account::BID_TYPE_PERCENT, $bidValue)
    {
        // Determine bid code
        switch ($bidType) {
            case Account::BID_TYPE_PERCENT:
                $bidTypeCode = 'C';
                $bidTypeSubCode = 'P';
                break;
            case Account::BID_TYPE_RESIDUAL:
                $bidTypeCode = 'R';
                $bidTypeSubCode = 'X';
                break;
            default:
                throw new InvalidArgumentException('Unsupported bid type: ' . $bidType);
        }

        $digits = self::splitBidValue($bidValue);

        return $bidTypeCode . $digits[0] . $bidTypeSubCode . $digits[1];
    }

    /**
     * @param int|float $bidValue
     * @return array
     */
    private static function splitBidValue($bidValue)
    {
        // Convert to string and reverse order
        $rev = strrev($bidValue);

        // If there's a decimal point, then just split around it
        if (false !== strpos($rev, '.')) {
            return explode('.', $rev);
        }

        // Otherwise...
        return[
            substr($rev, 0, -1), // All digits except the last
            substr($rev, -1) // The last digit
        ];
    }

    /**
     * Generate invalid bid values, i.e. outside of the acceptable range for the given bid type.
     *
     * @param string $bidType
     * @return \Generator
     */
    private static function generateInvalidBidValue($bidType = Account::BID_TYPE_PERCENT)
    {
        for($i = 0; $i < 10000; ++$i) {
            if ($bidType === Account::BID_TYPE_PERCENT) {
                // Generate random number NOT in the range [1.00, 9.95]
                yield number_format(self::getRandOutsideRange(-1000, 1000, 100, 995) / 100, 2);
            } else {
                // Generate random number NOT in the range [10, 300]
                yield self::getRandOutsideRange(-1000, 1000, 10, 300);
            }
        }
    }

    /**
     * Generate valid sign-up codes
     *
     * @param string $bidType
     * @return \Generator
     */
    private static function generateValidSignupCode($bidType = Account::BID_TYPE_PERCENT)
    {
        for($i = 0; $i < 10000; ++$i) {
            if ($bidType === Account::BID_TYPE_PERCENT) {
                // Generate random number in range [1.00, 9.95], rounded to nearest 0.05
                $value = number_format(((rand(100, 995) / 100) / 0.05) * 0.05, 2);
            } else {
                // Generate random number in range [10, 300], rounded to nearest 5
                $value = round(rand(10, 300) / 5) * 5;
            }

            $code = self::formatSignupCode($bidType, $value);
            #echo $value . ' => ' . $code . PHP_EOL;

            yield $code => $value;
        }
    }

    /**
     * Return a random integer in the range [$min, $max] but *not* in the range [$excludeMin, $excludeMax].
     *
     * @param int $min
     * @param int $max
     * @param int $excludeMin
     * @param int $excludeMax
     * @return int
     */
    private static function getRandOutsideRange($min, $max, $excludeMin, $excludeMax)
    {
        do {
            $n = rand($min, $max);
        } while($n >= $excludeMin && $n <= $excludeMax);

        return $n;
    }
}
