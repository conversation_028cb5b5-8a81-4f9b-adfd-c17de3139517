const DataDog = require('dogapi');

module.exports = class DataDogWrapper {
    constructor(result) {
        const api_key = process.env.DATADOG_API_KEY;
        const app_key = process.env.DATADOG_APPLICATION_KEY;

        this.result = result;

        if (api_key && app_key) {
            console.log('Sending DataDog metric');
            this.dataDog = DataDog.initialize({
                api_key,
                app_key
            });
        } else {
            console.log('Not sending to DataDog');
        }
    }

    sendMetrics() {
        const tags = [`env:${process.env.NODE_ENV}`];
        this.dataDog.metric.send('mysparefoot.e2e_test_result', this.result, { tags }, function(err) {
            if(err) {
                console.log('Error sending to DataDog:', err);
            }
        });
    }
};