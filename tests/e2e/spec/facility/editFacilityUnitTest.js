
describe("Edit facility unit", function() {
	var webdriverio = require('webdriverio');
	var baseUrl = browser.options.baseUrl;

	beforeEach(function() {
		// Load page, log in and go to Features
		let username = process.env.MYFOOT_USERNAME;
		let password = process.env.MYFOOT_USER_PWD;
		browser.url(baseUrl+'/login/logout');
    	browser.waitForExist('#forgot-password');
    	browser.element('[name=email]').setValue(username);
    	browser.element('[name=password]').setValue(password);

    	browser.element('#login-button').click();
    	browser.waitForExist('#user');

		browser.url(baseUrl+'/features/units?fid=105463');
    	browser.waitForExist('#facility-header');
	});

    function openFirstEdit() {
        var editBtn;
        var btns = browser.elements('#units-table tr:not(.disabled-row) a');
        for(var i=0; i < btns.value.length; i++) {
            if(btns.value[i].getText() === 'Edit') {
                editBtn = btns.value[i];
                break;
            }
        }

        expect(typeof editBtn).not.toBe('undefined');

        editBtn.click();
    }

    it('validates the changes in unit are persisted', function(){

        // This tests makes the changes of a checkbox twice
        // to confirm that the value can be set to on and then back off
        // Issue was that checkboxes could not be unchecked.

        var integratedFields;
    	browser.waitUntil(function() {
                integratedFields =  browser.execute(function() {
                    return window.integratedFields;
                });
                return typeof integratedFields !== 'undefined' && !!integratedFields.value;
            });


        openFirstEdit();

        var shelvesBox = browser.element('#shelvesinunit');
        var saveBtn = browser.element('#unit-modal-save');

        var isChecked = shelvesBox.getAttribute('checked');

        shelvesBox.click();
        saveBtn.click();

        browser.waitUntil(function() {
            return !browser.element('#unit-modal').isVisible();
        });

        browser.pause(4000); // Brief reload after making a change in a unit

        openFirstEdit();


        shelvesBox = browser.element('#shelvesinunit');
        saveBtn = browser.element('#unit-modal-save');

        shelvesBox.click();
        saveBtn.click();

        browser.waitUntil(function() {
            return !browser.element('#unit-modal').isVisible();
        });

        openFirstEdit();

        shelvesBox = browser.element('#shelvesinunit');

        expect(shelvesBox.getAttribute('checked')).toEqual(isChecked);

    });
});
