
describe("Disabled fields", function() {
	var webdriverio = require('webdriverio');
	var baseUrl = browser.options.baseUrl;

	beforeEach(function() {
		// Load page, log in and go to Features
		let username = process.env.MYFOOT_USERNAME;
		let password = process.env.MYFOOT_USER_PWD;
		browser.url(baseUrl+'/login/logout');
    	browser.waitForExist('#forgot-password');
    	browser.element('[name=email]').setValue(username);
    	browser.element('[name=password]').setValue(password);

    	browser.element('#login-button').click();
    	browser.waitForExist('#user');

		browser.url(baseUrl+'/features/units?fid=105463');
    	browser.waitForExist('#facility-header');
	});

    it('validates all integrated fields are disabled', function(){

        var integratedFields;
    	browser.waitUntil(function() {
                integratedFields =  browser.execute(function() {
                    return window.integratedFields;
                });
                return typeof integratedFields !== 'undefined' && !!integratedFields.value;
            });

        expect(integratedFields.value.length).toBeGreaterThan(0);

        for(let i=0; i<integratedFields.value.length; i++) {
            if($('[name="'+integratedFields.value[i]+'"]').state == 'success') {
                // For some reason, hidden fields attribute in wdio will result to null
                // hence we are excluding hidden inputs from this validation.
                if(browser.element('[name="'+integratedFields.value[i]+'"]').length && browser.element('[name="'+integratedFields.value[i]+'"]').getAttribute('type') !== 'hidden') {
                    expect(!!browser.element('[name='+integratedFields.value[i]+'"]').getAttribute('disabled')).toBeTruthy();
                }
            }
        }

    });
});
