version: "3"
networks:
  default:
    external:
      name: localnet
services:
    selenium:
        image: selenium/standalone-chrome
        ports:
        - "4444:4444"
    tests:
        image: 850077434821.dkr.ecr.us-east-1.amazonaws.com/sparefoot/myfoot:stage
        environment:
            SELENIUM_BASE_URL: https://my.sparefoot.com/
            SELENIUM_HOST: 'selenium'
            BROWSERSTACK_USER: sparefoot_DYFcTyZ8MZr
            BROWSERSTACK_KEY: ********************
            NODE_ENV: local
            MYFOOT_USERNAME:
            MYFOOT_USER_PWD:
        links:
            - selenium
        depends_on:
            - selenium
        volumes:
          - /opt/sparefoot/apps/myfoot/package.json:/var/www/html/package.json
          - /opt/sparefoot/apps/myfoot/:/var/www/html
        command: npm run e2e
