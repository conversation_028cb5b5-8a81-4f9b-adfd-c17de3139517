const _= require('lodash');
const common = require('./common.config').config;
const DataDog = require('dogapi');

exports.config = _.merge(common, {
    user: process.env.BROWSERSTACK_USER,
    key: process.env.BROWSERSTACK_KEY,
    services: ['browserstack'],
    maxInstances: 2,
    after: function(result, capabilities, specs){
        return;
        //These env vars should be configured on gitlab
        const api_key = process.env.DATADOG_API_KEY;
        const app_key = process.env.DATADOG_APPLICATION_KEY;

        if (api_key && app_key) {
            console.log('Sending DataDog metric');
            DataDog.initialize({
                api_key,
                app_key
            });

            const tags = [`env:${process.env.NODE_ENV}`];
            DataDog.metric.send('mysparefoot.smoke_test_result', result, { tags }, function(err) {
                if(err) {
                    console.log('Error sending to DataDog:', err);
                }
            });
        } else {
            console.log('Not sending to DataDog');
        }

    }
});