exports.config = {
    specs: [
        './tests/e2e/spec/**/*.js'
    ],
    suites: {
        smoke: [
            './tests/e2e/tests/smoke/*.js'
        ]
    },
    capabilities: [
    {
        browserName: 'chrome',
        resolution: '1280x1024',
        name: 'MyFoot Smoke Tests in Chrome',
        project: 'MyFoot',
        build: process.env.CI_PIPELINE_ID
    }],
    sync: true,
    logLevel : 'error',
    coloredLogs: true,
    bail: 1,
    screenshotPath: './logs/smoke/screenshots',
    baseUrl: process.env.SELENIUM_BASE_URL,
    waitforTimeout: 30000,
    connectionRetryTimeout: 90000,
    connectionRetryCunt: 3,
    framework: 'jasmine',
    reporters: ['json'],
    reporterOptions: {
        outputDir: './logs/smoke',
    },
    jasmineNodeOpts: {
        defaultTimeoutInterval: 120000
    }
}