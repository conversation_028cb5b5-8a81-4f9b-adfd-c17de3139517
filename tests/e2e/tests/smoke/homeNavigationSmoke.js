const { expect } = require('chai');

describe("The home navgiation links are accessed", function() {
    var webdriverio = require('webdriverio');
    var baseUrl = browser.options.baseUrl;

    beforeAll(function() {
        // Load page and log in with specific user to see Bidding link
        let username = process.env.MYFOOT_TEST_EMAIL;
        let password = process.env.MYFOOT_TEST_PASSWORD;
        browser.url(baseUrl + '/login');
        browser.element('[name=email]').setValue(username);
        browser.element('[name=password]').setValue(password);
        browser.element('#login-button').click();
        $('#menu-dashboard').waitForExist({ timeout: 10000 });
    });

    describe('after logging in', () => {
        it("loads the Dashboard link", function () {
            browser.url(baseUrl+'/dashboard?fid=155561');
            const selector = $('.submit-rate-wrapper .widget-header h2');
            selector.waitForExist({ timeout: 10000 });
            const text = selector.getText().trim();
            expect(text).to.equal("Submit Rate");
        });

        it("loads the Bidding link", function () {
            browser.url(baseUrl+'/features/bid?fid=155561');
            const selector = $('.col-md-12 h2');
            selector.waitForExist({ timeout: 10000 });
            const text = selector.getText().trim();
            expect(text.startsWith("Adjust Your Bid Modifier to Get More Leads")).to.equal(true);
        });

        it("loads the Customers link", function () {
            browser.url(baseUrl+'/customers/reservations?fid=155561');
            const selector = $('#facility-reservations');
            selector.waitForExist({ timeout: 10000 });
            const text = selector.getText().trim();
            expect(text).to.equal("Reservations");
        });

        it("loads the Features link", function () {
            browser.url(baseUrl+'/listings?fid=155561');
            const selector = $('.ember-view.facility-summary h3');
            selector.waitForExist({ timeout: 10000 });
            const text = selector.getText().trim();
            expect(text).to.equal("Information");
        });

        it("loads the Reviews link", function () {
            browser.url(baseUrl+'/reviews?fid=155561');
            const selector = $('#featuresApp h2');
            selector.waitForExist({ timeout: 10000 });
            const text = selector.getText().trim();
            expect(text).to.equal("Reviews");
        });

        it("loads the Statements page", function () {
            browser.url(baseUrl+'/statement?account_id=2318');
            const selector = $('#billing-history');
            selector.waitForExist({ timeout: 10000 });
            // NOTE: Using regex and getHTML because of an issue on IE and H2
            const regex = /Billing History/;
            text = selector.getHTML();
            match = regex.test(text);

            expect(match).to.equal(true);
        });

        it("loads the Move-Ins link", function () {
            browser.url(baseUrl+'/move-ins/contactless');
            const selector = $('.nav-tabs a');
            selector.waitForExist({ timeout: 10000 });
            const text = selector.getText().trim();
            expect(text).to.equal("Contactless Badge");
        });
    });
});
