<?php
define('MEMCACHE_COMPRESSED', 2); //php 7.2 throws on this constant missing
/**
 * Created by PhpStorm.
 * User: mland
 * Date: 7/1/14
 * Time: 1:49 PM
 */
date_default_timezone_set('America/Chicago');

defined('APPLICATION_PATH')
|| define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../application'));

/**
 * @param $className
 * spl_autoloader wants a function, not a class
 * so we're using function to map that
 */
//require_once __DIR__ . '/../library/genesis/lib/autoload.php';
require_once __DIR__ . '/../vendor/autoload.php';
//require_once __DIR__ . '/../library/genesis/src/util/ClassLoader.class.php';
require_once APPLICATION_PATH . '/ClassLoader.php';

$autoLoader = Zend_Loader_Autoloader::getInstance();
$autoLoader->pushAutoloader(array('AccountMgmt_ClassLoader', 'load'), 'AccountMgmt');
$autoLoader->pushAutoloader(array('AccountMgmt_ClassLoader', 'load'), 'BidOptimizer');
