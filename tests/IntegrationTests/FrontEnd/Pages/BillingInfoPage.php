<?php
namespace MyfootTests\IntegrationTests\FrontEnd\Pages;

use Facebook\WebDriver\WebDriverSelect;
use Genesis_Entity_Feature;
use Genesis_Service_Feature;
use Lmc\Steward\Component\AbstractComponent;

class BillingInfoPage extends AbstractComponent
{
    // Test credit card account numbers
    const CC_AMEX = '***************';
    const CC_DISCOVER = '****************';
    const CC_JSB = '****************';
    const CC_MASTERCARD = '****************';
    const CC_VISA = '****************';

    /**
     * Fill in and submit the Billing Info form
     *
     * @param string $email
     * @return \Facebook\WebDriver\Remote\RemoteWebElement
     */
    public function fillAndSubmit($email) {
        // Ensure page has loaded
        $this->waitForId('billing-form', true);

        // Skip billing info while netsuite is down
        if (Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_TEST_NETSUITE_SANDBOX)) {
            $this->waitForId('do_this_later', true);
            return $this->findById('do_this_later')->click();
        }

        // Input billing information
        $this->findById('payment-type-nickname')->sendKeys(uniqid('Test Card'));
        $this->findById('credit-card-number')->clear()->sendKeys(self::CC_VISA);
        $this->findById('credit-card-name')->clear()->sendKeys('Test Tester');
        $nextMonth = strtotime('+1 month');
        $monthSelect = new WebDriverSelect($this->findById('credit-card-expiration-month'));
        $monthSelect->selectByVisibleText(date('m', $nextMonth));
        $yearSelect = new WebDriverSelect($this->findById('credit-card-expiration-year'));
        $yearSelect->selectByVisibleText(date('Y', $nextMonth));
        $this->findById('billing-address-same')->click();
        $this->findById('address')->clear()->sendKeys('720 Brazos Street');
        $this->findById('city')->clear()->sendKeys('Austin');
        $stateSelect = new WebDriverSelect($this->findById('state'));
        $stateSelect->selectByVisibleText('TX');
        $this->findById('zip')->clear()->sendKeys('78701');
        $this->findById('emails')->clear()->sendKeys($email);

        // @todo Re-enable submission of this form once the Zendesk issue is resolved
        //$this->findById('submit')->click();
        $this->findById('do_this_later')->click();
    }
}
