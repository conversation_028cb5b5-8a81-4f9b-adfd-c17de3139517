<?php

namespace MyfootTests\IntegrationTests\FrontEnd\Pages;

use Lmc\Steward\Component\AbstractComponent;

/**
 * Signup page representation using Page Object pattern
 * @see http://martinfowler.com/bliki/PageObject.html
 */
class SignupCodePage extends AbstractComponent
{
    const SIGNUP_BUTTON_SELECTOR = 'signup-button';
    const SIGNUP_CODE_INPUT_SELECTOR = 'signup-code';
    const SIGNUP_CODE_SUBMIT_BUTTON_SELECTOR = 'submit';

    /**
     * Load the log-in page and click the "Sign Up" button
     *
     * @param string $baseUrl Base URL of site
     */
    public function load($baseUrl) {
        // Load login page, click sign-up button
        $this->wd->get($baseUrl . '/login/logout');
        $signupButton = $this->waitForId(self::SIGNUP_BUTTON_SELECTOR, true);
        $signupButton->click();

        $this->waitForId(self::SIGNUP_CODE_INPUT_SELECTOR);
    }

    /**
     * Find the sign-up code field, input the given sign-up code, submit the form, and wait for the next page to load.
     *
     * @param string $code Sign-up code
     */
    public function fillAndSubmitCode($code) {
        // Find sign-up code input field, input sign-up code, and submit form
        $codeInput = $this->waitForId(self::SIGNUP_CODE_INPUT_SELECTOR);
        $codeInput->sendKeys($code);
        $signupButton = $this->waitForId(self::SIGNUP_CODE_SUBMIT_BUTTON_SELECTOR, true);
        $signupButton->click();

        // Wait for Company Information page to load
        $this->waitForId(CompanyInfoPage::PAGE_LOADED_SELECTOR);
    }
}
