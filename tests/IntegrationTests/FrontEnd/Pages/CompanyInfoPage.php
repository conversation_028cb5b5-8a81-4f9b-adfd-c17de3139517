<?php
namespace MyfootTests\IntegrationTests\FrontEnd\Pages;

use Facebook\WebDriver\WebDriverSelect;
use Lmc\Steward\Component\AbstractComponent;

/**
 * Signup page representation using Page Object pattern
 * @see http://martinfowler.com/bliki/PageObject.html
 */
class CompanyInfoPage extends AbstractComponent
{
    const PAGE_LOADED_SELECTOR = 'companyinformation';
    const FORM_SUBMIT_BUTTON_SELECTOR = 'submit';

    // Default password used for creating new test accounts
    const DEFAULT_PASSWORD = 'testtesttest';

    // Company info selectors
    const COMPANY_NAME_SELECTOR = 'company-name';
    const ADDRESS_STREET_SELECTOR = 'address';
    const ADDRESS_CITY_SELECTOR = 'city';
    const ADDRESS_STATE_SELECTOR = 'state';
    const ADDRESS_ZIP_SELECTOR = 'zip';

    // User info selectors
    const FIRST_NAME_SELECTOR = 'first-name';
    const LAST_NAME_SELECTOR = 'last-name';
    const EMAIL_SELECTOR = 'email';
    const PHONE_SELECTOR = 'phone';
    const PASSWORD_SELECTOR = 'password';
    const PASSWORD_CONFIRM_SELECTOR = 'password-confirm';

    /**
     * Fill in and submit Company Information form
     *
     * @param string $prefix Prefix to be used for emails, names, etc.
     * @return string Return email address that was generated for form
     */
    public function fillAndSubmit($prefix = 'Test') {
        // Ensure page has loaded
        $this->waitForId(self::PAGE_LOADED_SELECTOR);

        $email = uniqid($prefix) . '@sparefoot.com';

        // Comnpany info
        $this->findById(self::COMPANY_NAME_SELECTOR)->sendKeys(
            uniqid('Company ' . ucwords(str_replace('-', ' ', $prefix)))
        );
        $this->findById(self::ADDRESS_STREET_SELECTOR)->sendKeys('720 Brazos Street');
        $this->findById(self::ADDRESS_CITY_SELECTOR)->sendKeys('Austin');
        $stateSelect = new WebDriverSelect($this->findById(self::ADDRESS_STATE_SELECTOR));
        $stateSelect->selectByVisibleText('TX');
        $this->findById(self::ADDRESS_ZIP_SELECTOR)->sendKeys('78701');

        // User info
        $this->findById(self::FIRST_NAME_SELECTOR)->sendKeys('Test');
        $this->findById(self::LAST_NAME_SELECTOR)->sendKeys(ucwords(str_replace('-', ' ', $prefix)));
        $this->findById(self::PHONE_SELECTOR)->sendKeys('5127056208');
        $this->findById(self::EMAIL_SELECTOR)->sendKeys($email);

        $this->fillPasswords();

        $this->findById(self::FORM_SUBMIT_BUTTON_SELECTOR)->click();

        return $email;
    }

    /**
     * Fill password fields of Company Info form
     *
     * @param string $password
     */
    protected function fillPasswords($password = self::DEFAULT_PASSWORD)
    {
        $this->findById(self::PASSWORD_SELECTOR)->clear()->sendKeys($password);
        $this->findById(self::PASSWORD_CONFIRM_SELECTOR)->clear()->sendKeys($password);
    }
}
