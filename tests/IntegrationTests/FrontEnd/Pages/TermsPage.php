<?php
namespace MyfootTests\IntegrationTests\FrontEnd\Pages;

use Genesis_Entity_Account;
use Lmc\Steward\Component\AbstractComponent;

/**
 * Signup page representation using Page Object pattern
 * @see http://martinfowler.com/bliki/PageObject.html
 */
class TermsPage extends AbstractComponent
{
    /**
     * Fill in and submit the Terms of Service form
     *
     * @param string $bidType
     */
    public function fillAndSubmit($bidType = Genesis_Entity_Account::BID_TYPE_PERCENT) {
        // Ensure page has loaded
        $this->waitForId('terms-form', true);

        // First checkbox is only required for accounts NOT using CPA Percent
        if ($bidType !== Genesis_Entity_Account::BID_TYPE_PERCENT) {
            $this->findById('agree1')->click();
        }

        // Last three checkboxes are required for ALL account bid types
        $this->findById('agree2')->click();
        $this->findById('agree3')->click();
        $this->findById('agree4')->click();

        $this->findById('submit')->click();
    }
}
