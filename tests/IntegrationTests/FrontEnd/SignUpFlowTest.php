<?php

namespace MyfootTests\IntegrationTests\FrontEnd;

use Genesis_Entity_Account;
use Genesis_Service_User;
use MyfootTests\IntegrationTests\FrontEnd\Pages\BillingInfoPage;
use MyfootTests\IntegrationTests\FrontEnd\Pages\CompanyInfoPage;
use MyfootTests\IntegrationTests\FrontEnd\Pages\SignupCodePage;
use MyfootTests\IntegrationTests\FrontEnd\Pages\TermsPage;

class SignUpFlowTest extends SeleniumTestCase
{
    const PERCENT_CPA_SIGNUP_CODE = 'C52P1';

    /**
     * @var SignupCodePage The sign-up code page Page Object
     */
    protected $signupCodePage;

    /**
     * @var CompanyInfoPage The Company Information page Page Object
     */
    protected $companyInfoPage;

    /**
     * @var TermsPage The Terms Agreement page Page Object
     */
    protected $termsPage;

    /**
     * @var BillingInfoPage The Billing Information page Page Object
     */
    protected $billingInfoPage;

    /**
     * @before
     */
    public function init()
    {
        $this->signupCodePage = new SignupCodePage($this);
        $this->companyInfoPage = new CompanyInfoPage($this);
        $this->termsPage = new TermsPage($this);
        $this->billingInfoPage = new BillingInfoPage($this);
        $this->wd->get(self::$baseUrl . '/login/logout');
    }

    /**
     * Test signing up as a %CPA client
     */
    public function testCpaSignup()
    {
        // Begin sign-up process
        $this->signupCodePage->load(self::$baseUrl);

        // Input CPA Percent sign-up code and submit form
        $this->signupCodePage->fillAndSubmitCode(self::PERCENT_CPA_SIGNUP_CODE);

        // Input company/user info and submit form
        $userEmail = $this->companyInfoPage->fillAndSubmit('test-cpa-percent');

        // Agree to terms of service
        $this->termsPage->fillAndSubmit(\Genesis_Entity_Account::BID_TYPE_PERCENT);

        // Enter billing info
        $this->billingInfoPage->fillAndSubmit($userEmail);

        // Have we reached the Welcome page?

        $jumbo = $this->waitForCss('.jumbo');
        $this->assertElementContainsText($jumbo, 'Let\'s add your first facility.');

        // Assert account creation
        $user = Genesis_Service_User::loadByEmail($userEmail);
        $this->assertTrue((bool) $user, "User object must be fetched with email address: " . $userEmail);
        $userAccess = $user->getUserAccess();
        $account = $userAccess->getAccount();
        $this->assertEquals(
            Genesis_Entity_Account::BID_TYPE_PERCENT,
            $account->getBidType(),
            'Expected account bid type to be ' . Genesis_Entity_Account::BID_TYPE_PERCENT
        );
        $this->assertEquals('1.25', $account->getMinBid(), 'Expected account minBid to be 1.25');
    }
}


