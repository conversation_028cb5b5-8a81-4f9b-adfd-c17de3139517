<?php

namespace MyfootTests\IntegrationTests\FrontEnd;

use BrowserStack\Local;
use BrowserStack\LocalException;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Lmc\Steward\ConfigProvider;
use Lmc\Steward\Test\AbstractTestCase;
use RuntimeException;

/**
 * Abstract class for custom tests, could eg. define some properties or instantiate some common components in setUp().
 */
abstract class SeleniumTestCase extends AbstractTestCase
{
    /** @var int Default width of browser window (Steward's default is 1280) */
    public static $browserWidth = 1920;

    /** @var int Default height of browser window (<PERSON>eward's default is 1024) */
    public static $browserHeight = 1080;

    /** @var string */
    public static $baseUrl;

    /**
     * BrowserStack Local object
     *
     * @var \BrowserStack\Local
     */
    protected $bsLocal;

        /**
     * @return void
     * @throws RuntimeException
     * @throws LocalException
     */
    public function __construct() {
        if (getenv('BROWSERSTACK_KEY') === false) {
            throw new \RuntimeException('The BROWSERSTACK_KEY environment variable must be set');
        }

        // Run BrowserStack Local? (needed if BrowserStack can't hit the box)
        $bsLocalArgs = [
            'key' => getenv('BROWSERSTACK_KEY'),
            #'localIdentifier' => 'MyFoot',
        ];

        /**
         * @var BrowserStack\Local
         */
        $this->bsLocal = new Local();
        $this->bsLocal->start($bsLocalArgs);

    }

    /**
     * @inheritdoc
     */
    public function tearDown()
    {
        // Stop BrowserStack Local if it's running
        if($this->bsLocal) {
            $this->bsLocal->stop();
        }

        parent::tearDown();
    }

    /**
     * @inheritdoc
     */
    public function setUp()
    {
        // If in container, then just use localhost
        self::$baseUrl = 'http://localhost';

        $this->debug('Base URL set to "%s"', self::$baseUrl);

        if (ConfigProvider::getInstance()->env == 'production') {
            $this->warn('The tests are run against production, so be careful!');
        }

        parent::setUp();
    }

    /**
     * Asserts that given element contains given text
     *
     * @param RemoteWebElement $element
     * @param string $expectedText
     */
    protected function assertElementContainsText(RemoteWebElement $element, $expectedText) {
        $this->assertTrue(stripos($element->getText(), $expectedText) !== false, 'Expected page to contain text "' . $expectedText . '"');
    }
}
