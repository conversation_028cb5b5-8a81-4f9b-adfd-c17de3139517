# Front-End Testing

Tests are run using the [<PERSON>eward](https://github.com/lmc-eu/steward) test-runner and sent through [BrowserStack](https://www.browserstack.com/automate) via [BrowserStack Local](https://github.com/browserstack/browserstack-local-php), which allows the tests to be run from within a Docker container.

## Tests and Their Locations
* run_integration_tests
    * MyfootTests\IntegrationTests\Api
* run_steward_tests
    * Searching for testcases in directory "tests/IntegrationTests/FrontEnd" that have the pattern "*Test.php"
* run_tests
    * MyfootTests\UnitTests\Models\SignupCodeTest
* smoke
    * tests/e2e/tests/smoke

## Helpful Links

* https://www.browserstack.com/automate/phpunit#running-local-test
* https://github.com/facebook/php-webdriver
* https://github.com/facebook/php-webdriver/wiki/Example-command-reference
