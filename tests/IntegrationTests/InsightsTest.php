<?php

namespace MyfootTests\UnitTests\IntegrationTests;

use Genesis_Service_Insights;
use Genesis_Entity_Insights;
use Genesis_Service_Facility;
use AccountMgmt_Clients_SearchClient;

class InsightsTest extends \PHPUnit_Framework_TestCase
{
    public function testInsights()
    {
        $facId = 202166;
        $facility = Genesis_Service_Facility::loadById($facId);
        $result = Genesis_Service_Insights::generate($facility, Genesis_Entity_Insights::DEFAULT_REPORT_TYPE, new AccountMgmt_Clients_SearchClient('phpunit-insights', false));
        $this->assertEquals($facId, $result->getListingAvailId(), 'Insights should not be empty'.print_r($result, true));
    }
}