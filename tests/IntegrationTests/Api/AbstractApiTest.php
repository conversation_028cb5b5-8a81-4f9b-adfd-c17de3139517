<?php
namespace MyfootTests\IntegrationTests\Api;

use Genesis_Entity_Account;
use Genesis_Entity_Source;
use Genesis_Entity_UserAccess;
use Genesis_Service_Account;
use Genesis_Service_Corporation;
use Genesis_Service_Facility;
use Genesis_Service_Location;
use Genesis_Service_StorageSpace;
use Genesis_Service_User;
use Genesis_Service_UserAccess;
use GenesisTests\Entity\AccountTest;
use GenesisTests\Entity\CorporationTest;
use GenesisTests\Entity\FacilityTest;
use GenesisTests\Entity\StorageSpaceTest;
use GenesisTests\Entity\UserAccessTest;
use GenesisTests\Entity\UserTest;
use GuzzleHttp\Client;

abstract class AbstractApiTest extends \PHPUnit_Framework_TestCase
{
    /**
     * Echo debugging log statements?
     */
    const DEBUG = false;

    /**
     * Test API responses vs. documentation?
     */
    const TEST_DOCUMENTATION = false;
    const DEMO_ACCOUNT_ID = 2318;

    /**
     * Mock/shell account used to login to API for testing
     *
     * @var Genesis_Entity_Account
     */
    protected static $accountShell;

    /**
     * HTTP client used to make API requests for testing.
     * (Because working with curl directly is painful)
     *
     * @var \GuzzleHttp\Client;
     */
    protected static $client;

    /**
     * @inheritdoc
     */
    public static function setUpBeforeClass() {
        // Create shell account (if one does not already exist)
        if (! self::$accountShell) {
            self::debug('Creating shell account...');
            self::$accountShell = self::createAccountShell();

            // Add four units
            self::$accountShell->units = [];
            for ($i = 0; $i < 4; ++$i) {
                $unit = StorageSpaceTest::mock(self::$accountShell->facility->getId());
                self::$accountShell->units[] = Genesis_Service_StorageSpace::save($unit);
            }
        }

        // Create HTTP client
        self::debug('Creating client...');
        $baseUri = (getenv('SF_ENV') == 'local') ? 'my.sparefoot.local' : 'localhost';
        self::$client = new Client([
            // Base URI is used with relative requests (testing from wihin container, so use localhost)
            'base_uri' => 'http://' . $baseUri,
            // Use a shared cookie jar for all requests
            'cookies' => true,
        ]);

        // Login to API
        self::debug('Logging in to API with account ID ' . self::$accountShell->account->getId() . '...');
        $response = self::$client->get('/api/login', ['query' => [
            'email' =>self::$accountShell->user->getEmail(),
            'password' => self::$accountShell->password
        ]]);

        self::debug('Decoding and verifying response...');
        $body = json_decode($response->getBody());

        // Assert response values
        self::assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        self::assertTrue($body->success);
    }

    /**
     * @inheritdoc
     */
    public static function tearDownAfterClass()
    {
        self::$accountShell = null;
    }

    /**
     * Create a mock/shell account for use in testing
     *
     * @param string $bidType
     * @param int $sourceId
     * @param string $role
     * @return \stdClass
     * @throws \Exception
     */
    public static function createAccountShell(
        $bidType = Genesis_Entity_Account::BID_TYPE_PERCENT,
        $sourceId = Genesis_Entity_Source::ID_MANUAL,
        $role = Genesis_Entity_UserAccess::ROLE_GOD
    ) {
        // Create a user
        self::debug('Creating mock user...');
        $user = UserTest::mock();
        $password = md5(uniqid('statementFactory').rand(0,100000));
        $user->setRawPassword($password);
        $user = Genesis_Service_User::save($user);

        // Load demo account
        self::debug('Loading demo account...');
        $account = Genesis_Service_Account::loadById(self::DEMO_ACCOUNT_ID);

        // Put the user on the account as '$role'
        self::debug('Creating mock user-access and assigning role...');
        $userAccess = UserAccessTest::mock($user->getId(), $role, $account->getId());
        $userAccess = Genesis_Service_UserAccess::save($userAccess);

        self::debug('Creating mock corporation...');
        $corporation = CorporationTest::mock($account->getId(), $sourceId);
        $corporation = Genesis_Service_Corporation::save($corporation);

        self::debug('Creating mock facility...');
        // Save time by harcoding geocoded location object here...
        // $location = Genesis_Service_Location::geoCode('720 Brazos Street, Austin Texas');
        $location = Genesis_Service_Location::loadById('********');
        $facility = FacilityTest::mock($corporation->getId(), $location);
        $facility->setInsights(0); // Generating the Insights reports takes too long
        $facility = Genesis_Service_Facility::save($facility);

        // Construct shell object
        self::debug('Constructing shell account object...');
        $obj = new \stdClass();
        $obj->user = $user;
        $obj->password = $password;
        $obj->account = $account;
        $obj->userAccess = $userAccess;
        $obj->corporation = $corporation;
        $obj->facility = $facility;

        return $obj;
    }

    /**
     * Echo message
     *
     * @param string $format sprintf format string
     * @param mixed ...$args Variable number of arguments for sprintf
     */
    public static function log($format, ...$args)
    {
        echo self::formatOutput($format, $args);
    }

    /**
     * Echo message with [WARN] prefix
     *
     * @param string $format sprintf format string
     * @param mixed ...$args Variable number of arguments for sprintf
     */
    public static function warn($format, ...$args)
    {
        echo self::formatOutput($format, $args, 'WARN');
    }

    /**
     * Echo message with [DEBUG] prefix (and only if debugging output is enabled)
     *
     * @param string $format sprintf format string
     * @param mixed ...$args Variable number of arguments for sprintf
     */
    public static function debug($format, ...$args)
    {
        #if (ConfigProvider::getInstance()->debug) {
        if (self::DEBUG) {
            echo self::formatOutput($format, $args, 'DEBUG');
        }
    }

    /**
     * Format output
     *
     * @param string $format
     * @param array $args Array of arguments passed to original sprintf()-like function
     * @param string $type Specific log severity type (WARN, DEBUG) prefixed to output
     * @return string Formatted output
     */
    protected static function formatOutput($format, array $args, $type = '')
    {
        // If first item of arguments contains another array use it as arguments
        if (!empty($args) && is_array($args[0])) {
            $args = $args[0];
        }

        return '[' . date('Y-m-d H:i:s') . ']'
            . ($type ? " [$type]" : '')
            . ': '
            . vsprintf($format, $args)
            . "\n";
    }

    /**
     * Compare two objects for different keys
     *
     * @param object $obj1
     * @param object $obj2
     * @return array
     */
    protected static function objectKeyDiff($obj1, $obj2)
    {
        $diff = [];

        foreach($obj1 as $key => $value) {
            if(! property_exists($obj2, $key)) {
                $diff[] = $key;
                continue;
            }

            // If there is an array of objects, make sure that all the keys are matching in the object
            if (is_array($value)) {
                if (! empty($value) && is_object($value[0])) {
                    $obj2Value = $obj2->$key;
                    if (! is_array($obj2Value)) {
                        // $obj2Value should be an array like $value
                        $diff[] = $key;
                    } elseif (empty($obj2Value)) {
                        // Do nothing, should be okay to have an empty array
                    } elseif (! is_object($obj2Value[0]) && $obj2Value !== null) {
                        // This should be an object just like $value[0], so return it as broken
                        $diff[] = $key;
                    } else {
                        // Looks good so far, Recursively traverse these two objects to see if there are any mishaps
                        $diff = array_merge($diff, self::objectKeyDiff($value[0], $obj2Value[0]));
                    }
                }
            }

            // If it is an object, just compare
            if (is_object($value)) {
                if (is_object($obj2->$key)) {
                    $diff = array_merge($diff, self::objectKeyDiff($value, $obj2->$key));
                } elseif ($obj2->$key !== null) {
                    $diff[] = $key;
                }
            }
        }

        return $diff;
    }

    /**
     * @param int $sparedocApiId
     * @param array $response
     */
    protected static function assertDocumentation($sparedocApiId, $response)
    {
        if(! self::TEST_DOCUMENTATION) {
            return;
        }

        // Retrieve example response from API documentation
        $endpoint = 'http://sparedoc.sparefoot.com/api/view_field/json_example_success/' . $sparedocApiId;
        $masterPoint = 'http://sparedoc.sparefoot.com/api/edit/' . $sparedocApiId;
        $documentationResponse = self::$client->get($endpoint);
        // Documentation response JSON is missing leading curly brace?!
        $documentationResponse = '{' . $documentationResponse->getBody();
        $documentationResponse = json_decode($documentationResponse);
        $errorString =  $endpoint . PHP_EOL . 'Edit at ' . $masterPoint . PHP_EOL;

        // Tests two levels of nested objects to make sure the keys exist
        $documentationDiff = self::objectKeyDiff($documentationResponse, $response);
        if (! empty($documentationDiff)) {
            $errorString .= 'The following fields exist in the documentation, but not in the response: ';
            $errorString .= implode(', ', $documentationDiff);
        }

        $responseDiff = self::objectKeyDiff($response, $documentationResponse);
        if (! empty($responseDiff)) {
            $errorString .= 'The following fields exist in the response, but not in the documentation: ';
            $errorString .= implode(', ', $responseDiff);
        }

        // If any differences found, fail the current test
        if (! empty($responseDiff) || ! empty($documentationDiff)) {
            self::fail($errorString);
        }
    }
}
