<?php
namespace MyfootTests\IntegrationTests\Api;

use AccountMgmt_Service_Account;
use AccountMgmt_Service_Booking;
use AccountMgmt_Service_Facility;
use AccountMgmt_Service_Inquiry;
use AccountMgmt_Service_Unit;
use Exception;
use Genesis_Dao_Review;
use Genesis_Entity_Facility;
use Genesis_Service_ConsumerContact;
use Genesis_Service_Facility;
use Genesis_Service_FacilityImage;
use Genesis_Service_StorageSpace;
use GenesisTests\Entity\ConsumerContactTest;
use GenesisTests\Entity\ReservationRequestTest;
use GenesisTests\Entity\ReviewTest;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;

class FacilitiesTest extends AbstractApiTest
{
    const GET_SINGLE_FACILITY_DOC_ID = 71;
    const GET_FACILITY_UNITS_DOC_ID = 87;
    const PUT_SINGLE_FACILITY_DOC_ID = 90;
    const GET_FACILITY_PHOTOS_DOC_ID = 76;
    const POST_FACILITY_PHOTO_DOC_ID = 78;
    const GET_SINGLE_FACILITY_PHOTO_DOC_ID = 77;
    const PUT_SINGLE_FACILITY_PHOTO_DOC_ID = 79;
    const DELETE_FACILITY_PHOTO_DOC_ID = 80;
    const GET_BOOKINGS_DOC_ID = 107;
    const GET_BOOKING_DOC_ID = 108;

    /**
     * Test retrieval of a facility's bookings
     * @throws Exception
     */
    public function testGetBookings()
    {
        // Get account's first unit
        $unit = Genesis_Service_StorageSpace::loadById(self::$accountShell->units[0]->getId());

        // Reserve unit
        $reservation = ReservationRequestTest::mock($unit->getId());
        $reservation->setFirstName('Test');
        $reservation->setMoveInDate(date('Y-m-d'));
        AccountMgmt_Service_Booking::bookUnit($reservation);

        // Reserve unit again
        $reservation = ReservationRequestTest::mock($unit->getId());
        $reservation->setFirstName('Test2');
        $reservation->setMoveInDate(date('Y-m-d'));
        AccountMgmt_Service_Booking::bookUnit($reservation);

        // Make API request
        $facility = $this->getUserFacility();
        $response = self::$client->get('/api/facilities/' . $facility->getId() . '/bookings');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expectedResponse = AccountMgmt_Service_Facility::serializeFacilityBookings($facility);
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));
        self::assertDocumentation(self::GET_BOOKINGS_DOC_ID, $body);
    }

    /**
     * Test retrieval of a specific booking at a facility
     * @throws Exception
     */
    public function testGetBooking()
    {
        // Get account's first unit
        $unit = Genesis_Service_StorageSpace::loadById(self::$accountShell->units[0]->getId());

        // Reserve unit
        $reservation = ReservationRequestTest::mock($unit->getId());
        $reservation->setFirstName('Test');
        $reservation->setMoveInDate(date('Y-m-d'));
        $reservationResponse = AccountMgmt_Service_Booking::bookUnit($reservation);
        $transaction = $reservationResponse->getTransaction();
        $confirmationCode = $transaction->getConfirmationCode();

        // Make request
        $facility = $this->getUserFacility();
        $response = self::$client->get('/api/facilities/' . $facility->getId() . '/bookings/' . $confirmationCode);
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expectedResponse = AccountMgmt_Service_Booking::toArray($transaction);
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));
        self::assertDocumentation(self::GET_BOOKING_DOC_ID, $body);
    }

    /**
     * Test retrieval of all facilities for an account
     */
    // public function testGetAllFacilitiesForAccount()
    // {
    //     // Make API request
    //     $response = self::$client->get('/api/facilities/account_id/' . self::$accountShell->account->getId());
    //     $body = json_decode($response->getBody());

    //     // Prepare expected response
    //     $facilities = AccountMgmt_Service_Account::getAllFacilities(self::$accountShell->account->getId());
    //     $expectedResponse = [];
    //     foreach ($facilities as $facility) {
    //         $expectedResponse[] = AccountMgmt_Service_Facility::toArray($facility);
    //     }

    //     // Assert response values
    //     $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
    //     $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
    //     $this->assertEquals(count($expectedResponse), count($body->data));
    //     for ($i = 0; $i < count($body->data); $i++) {
    //         $this->assertEquals(json_encode($expectedResponse[$i]), json_encode($body->data[$i]));
    //     }
    // }

    /**
     * Test retrieval of a single facility
     * @throws Exception
     */
    // public function testGetFacility()
    // {
    //     $facility = $this->getUserFacility();

    //     // Make request
    //     $response = self::$client->get('/api/facilities/' . $facility->getId());
    //     $body = json_decode($response->getBody());

    //     // Assert response values
    //     $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
    //     $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
    //     $expectedResponse = AccountMgmt_Service_Facility::toArray($facility);
    //     $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));
    //     self::assertDocumentation(self::GET_SINGLE_FACILITY_DOC_ID, $body);
    // }

    /**
     * Test retrieval of a single facility's units
     * @throws Exception
     */
    public function testGetFacilityUnits()
    {
        // Make request
        $facility = $this->getUserFacility();
        $response = self::$client->get('/api/facilities/' . $facility->getId() . '/units');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expectedResponse = AccountMgmt_Service_Facility::getAllUnitsArray($facility);
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));
        self::assertDocumentation(self::GET_FACILITY_UNITS_DOC_ID, $body);
    }

    /**
     * Test updating a facility
     * @throws Exception
     */
    // public function testUpdateFacility()
    // {
    //     $salesForceUrl = getenv('SALESFORCE_AUTH_BASE_URL');
    //     $this->assertTrue(
    //         strpos(''.$salesForceUrl, 'salesforce.com') !== false,
    //         'Missing environment variable SALESFORCE_AUTH_BASE_URL, ok for local testing');
    //     $previousFacility = $this->getUserFacility();
    //     $updateData = self::getFacilityObj();
    //     $updateData->corporation_id = $previousFacility->getCorporationId();

    //     // Make API request
    //     $response = self::$client->put(
    //         '/api/facilities/' . $previousFacility->getId(),
    //         ['json' => $updateData]
    //     );
    //     $body = json_decode($response->getBody());

    //     // Assert response values
    //     $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
    //     $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
    //     foreach ($updateData as $property => $expectedValue) {
    //         // Amenities will be tested separately below
    //         if ($property === 'amenities') {
    //             continue;
    //         }

    //         if (! property_exists($body->data, $property)) {
    //             $this->fail('Expected property ' . $property . ' to be set in response data.');
    //         }
    //         $this->assertEquals($expectedValue, $body->data->$property, $property . ' not updated correctly.');
    //     }

    //     // Test amenities
    //     foreach ($updateData->amenities as $property => $expectedValue) {
    //         if (! property_exists($body->data->amenities, $property)) {
    //             $this->fail('Expected amenity property ' . $property . ' to be set in response data.');
    //         }
    //         $this->assertEquals($expectedValue, $body->data->amenities->$property, $property . ' amenity was not updated correctly.');
    //     }

    //     // Assert response body
    //     $expectedResponse = AccountMgmt_Service_Facility::toArray($this->getUserFacility());
    //     $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));

    //     // Assert documentation matches
    //     self::assertDocumentation(self::PUT_SINGLE_FACILITY_DOC_ID, $body);

    //     // Reset facility to prior state (before the update)
    //     Genesis_Service_Facility::save($previousFacility);
    // }

    /**
     * Test partially updating a facility
     * @throws Exception
     */
    // public function testUpdateFacilityPartialChange()
    // {
    //     $previousFacility = $this->getUserFacility();

    //     $updateData = [
    //         'title' => 'Fake Updated Storage Facility',
    //         'phone' => '**********'
    //     ];

    //     // Make request
    //     $response = self::$client->put(
    //         '/api/facilities/' . $previousFacility->getId(),
    //         ['json' => $updateData]
    //     );
    //     $body = json_decode($response->getBody());

    //     // Assert response values
    //     $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
    //     $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
    //     foreach ($updateData as $property => $expectedValue) {
    //         if (! property_exists($body->data, $property)) {
    //             $this->fail('Expected property ' . $property . ' to be set in response.');
    //         }
    //         $this->assertEquals($expectedValue, $body->data->$property, $property . ' was not updated correctly.');
    //     }

    //     // Assert that unchanged values remain unchanged
    //     foreach ($previousFacility as $property => $previousValue) {
    //         // Amenities will be tested separately below
    //         if ($property === 'amenities') {
    //             continue;
    //         }

    //         if (! property_exists($updateData->{$property})) {
    //             $this->assertEquals($previousValue, $body->data->$property, $property . ' was not updated yet the value changed!');
    //         }
    //     }

    //     // Assert that amenities updated correctly (i.e., they remained unchanged)
    //     if (! empty($previousFacility->amenities)) {
    //         foreach ($previousFacility->amenities as $property => $previousValue) {
    //             if (! property_exists($updateData->data->amenities, $property)) {
    //                 $this->assertEquals($previousValue, $response->data->amenities->$property, $property . ' was not updated yet the value changed!');
    //             }
    //         }
    //     }

    //     // Reset facility to state prior to update
    //     Genesis_Service_Facility::save($previousFacility);
    // }

    /**
     * Test adding a unit to a facility
     * @throws Exception
     */
    public function testPostUnit()
    {
        $createData = [
            'size_length' => '7.00',
            'size_width' => '7.00',
            'unit_type' => 'unit',
            'regular_price' => 50
        ];

        // Make API request
        $response = self::$client->post(
            '/api/facilities/' . self::$accountShell->facility->getId() . '/units/',
            ['json' => $createData]
        );
        $body = json_decode($response->getBody());

        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        foreach ($createData as $key => $value) {
            $this->assertEquals($value, $body->data->$key, $key . ' was not updated correctly.');
        }

        // Check to see if the unit with id is in the database
        $unit = Genesis_Service_StorageSpace::loadById($body->data->id);
        $this->assertNotEquals(null, $unit);

        // Check to see if the toJSON matches the response
        $expectedJsonResponse = AccountMgmt_Service_Unit::toJson($unit);
        $this->assertEquals($expectedJsonResponse, json_encode($body->data));

        // Remove unit
        Genesis_Service_StorageSpace::delete($unit->getId());
    }

    /**
     * Test adding a photo to a facility
     * @throws Exception
     */
    public function testPostPhoto()
    {
        $facility = $this->getUserFacility();

        // confirm we're getting a file
        $this_path = __DIR__ . "/data/storage-test-pic.jpg";
        $fopen_orig = fopen($this_path, 'r');

        // Make request
        $response = self::$client->post('/api/facilities/' . $facility->getId() . '/photos', [
            'multipart' => [
                [
                    'name' => 'photos[]',
                    'contents' => $fopen_orig
                ]
            ]
        ]);
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertCount(1, $body->data, 'Expected response[body] to contain data');
        $photo = Genesis_Service_FacilityImage::loadById($body->data[0]->id);
        $this->assertEquals(AccountMgmt_Service_Facility::serializeFacilityPhoto($photo)['filename'], $body->data[0]->filename);
        self::assertDocumentation(self::POST_FACILITY_PHOTO_DOC_ID, $body);

        // Save photo to account shell so we can re-use it in tests below
        self::$accountShell->photo = $photo;
    }

    /**
     * Test retrieving a facility's photos
     *
     * @depends testPostPhoto
     */
    public function testGetFacilityPhotos()
    {
        $facility = self::$accountShell->facility;

        // Make API request
        $response = self::$client->get('/api/facilities/' . $facility->getId() . '/photos');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expectedResponse = (object) AccountMgmt_Service_Facility::serializeFacilityPhotos($facility)[0];
        $this->assertEquals($expectedResponse->filename, $body->data[0]->filename);
        self::assertDocumentation(self::GET_FACILITY_PHOTOS_DOC_ID, $body);
    }

    /**
     * Test retrieving a specific photo of a facility
     *
     * @depends testPostPhoto
     * @throws Exception
     */
    public function testGetPhoto()
    {
        $facility = $this->getUserFacility();

        $photo = self::$accountShell->photo;
        if (! $photo) {
            $this->fail('No photo found. Setup failed.');
        }

        // Make API request
        $response = self::$client->get('/api/facilities/' . $facility->getId() . '/photos/' . $photo->getPhotoId());
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expected = (object) AccountMgmt_Service_Facility::serializeFacilityPhoto($photo);
        $this->assertEquals($expected->filename, $body->data->filename);
        self::assertDocumentation(self::GET_SINGLE_FACILITY_PHOTO_DOC_ID, $body);
    }

    /**
     * Test updating a specific photo of a facility
     *
     * @depends testPostPhoto
     * @throws Exception
     */
    public function testUpdatePhoto()
    {
        $facility = $this->getUserFacility();

        $photo = self::$accountShell->photo;
        if (! $photo) {
            $this->fail('No photo found. Setup failed.');
        }

        $updateData = json_encode(['default' => true]);

        // Make API request
        $response = self::$client->put(
            '/api/facilities/' . $facility->getId() . '/photos/' . $photo->getPhotoId(),
            ['json' => $updateData]
        );
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertEquals($photo->getPhotoId(), $body->data->id);
        $this->assertEquals(1, $body->data->picture_number);
        $photo = Genesis_Service_FacilityImage::loadByFacilityIdAndPictureNum($facility->getId(), 1);
        $this->assertEquals($photo->getPhotoId(), $body->data->id);
        self::assertDocumentation(self::PUT_SINGLE_FACILITY_PHOTO_DOC_ID, $body);
    }

    /**
     * Test deleting a specific photo from a facility
     *
     * @depends testPostPhoto
     * @throws Exception
     */
    public function testDeletePhoto()
    {
        $facility = $this->getUserFacility();
        $photoToDelete = Genesis_Service_FacilityImage::loadByFacilityIdAndPictureNum($facility->getId(), 1);
        if (! $photoToDelete) {
            $this->fail('Setup failed. No photo to delete.');
        }

        // Make API request
        $response = self::$client->delete('/api/facilities/' . $facility->getId() . '/photos/' . $photoToDelete->getPhotoId());
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $photos = $this->getUserFacility()->getImages();
        foreach($photos as $photo) {
            if($photoToDelete->getPhotoId() === $photo->getPhotoId()) {
                $this->fail('Photo was not deleted.');
            }
        }

        foreach($body->data as $data) {
            if($data->id === $photoToDelete->getPhotoId()) {
                $this->fail('Photo was not deleted.');
            }
        }

        self::assertDocumentation(self::DELETE_FACILITY_PHOTO_DOC_ID, $body);
    }

    /**
     * Test retrieval of a facility's inquiries
     * @throws Exception
     */
    public function testGetInquiries()
    {
        $facility = $this->getUserFacility();

        // Generate some random consumer contacts
        $count = rand(1, 10);
        for($i = 0; $i < $count; $i++) {
            $params = [
                'first_name' => substr(md5(microtime()), rand(0, 26), 5),
                'last_name' => substr(md5(microtime()), rand(0, 26), 5),
                'email' => '<EMAIL>',
                'listing_avail_id' => $facility->getId(),
                'timestamp' => date('Y-m-d'),
            ];
            Genesis_Service_ConsumerContact::save(
                ConsumerContactTest::mock($params)
            );
        }

        // Make API request
        $response = self::$client->get('/api/facilities/' . $facility->getId() . '/inquiries/');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expectedResponse = AccountMgmt_Service_Facility::serializeFacilityInquiries($facility);
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));
        $this->assertEquals($count, count($body->data));
        $this->assertEquals($count, $body->meta->count);
    }

    /**
     * Test retrieval of a facility's inquiries by start date
     * @throws Exception
     */
    // public function testGetInquiriesStartDate()
    // {
    //     $facility = $this->getUserFacility();

    //     // Generate a random consumer contact
    //     $dateTomorrow = date('Y-m-d', time() + 86400);
    //     $params = [
    //         'first_name' => 'Test1',
    //         'last_name' => 'Test2',
    //         'email' => '<EMAIL>',
    //         'listing_avail_id' => $facility->getId(),
    //         'timestamp' => $dateTomorrow,
    //     ];
    //     $consumerContact = Genesis_Service_ConsumerContact::save(ConsumerContactTest::mock($params));

    //     // Make API request
    //     $response = self::$client->get('/api/facilities/' . $facility->getId() . '/inquiries', [
    //         'query' => [
    //             'start_date' => date('c')
    //         ]
    //     ]);
    //     $body = json_decode($response->getBody());

    //     // Assert response values
    //     $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
    //     $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
    //     $expectedResponse = AccountMgmt_Service_Inquiry::toArray($consumerContact);
    //     $this->assertGreaterThanOrEqual(1, $body->meta->count);
    //     $this->assertContains(json_encode($expectedResponse), json_encode($body->data[0]));
    // }

    /**
     * Test retrieval of a facility's inquiries by end date
     * @throws Exception
     */
    public function testGetInquiriesEndDate()
    {
        $facility = $this->getUserFacility();

        // Generate a random consumer contact
        $dateYesterday = date('Y-m-d', time() - 86400);
        $params = [
            'first_name' => 'Test1',
            'last_name' => 'Test2',
            'email' => '<EMAIL>',
            'listing_avail_id' => $facility->getId(),
            'timestamp' => $dateYesterday,
        ];
        $consumerContact = Genesis_Service_ConsumerContact::save(ConsumerContactTest::mock($params));

        // Make API request
        $response = self::$client->get('/api/facilities/' . $facility->getId() . '/inquiries', [
            'query' => [
                'end_date' => date('c')
            ]
        ]);
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertGreaterThanOrEqual(1, $body->meta->count);
        $this->assertContains("\"id\"", json_encode($body->data[0]));
        $this->assertContains("\"facility_id\"", json_encode($body->data[0]));
    }

    /**
     * Test retrieval of specific facility inquiry
     * @throws Exception
     */
    // public function testGetInquiry()
    // {
    //     $facility = $this->getUserFacility();

    //     $params = [
    //         'first_name' => 'Test1',
    //         'last_name' => 'Test2',
    //         'email' => '<EMAIL>',
    //         'listing_avail_id' => $facility->getId(),
    //         'timestamp' => date('Y-m-d'),
    //     ];
    //     $consumerContact = Genesis_Service_ConsumerContact::save(ConsumerContactTest::mock($params));

    //     // Make API request
    //     $response = self::$client->get('/api/facilities/' . $facility->getId() . '/inquiries/' . $consumerContact->getId());
    //     $body = json_decode($response->getBody());

    //     // Assert response values
    //     $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
    //     $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
    //     $this->assertContains("\"id\"", json_encode($body->data[0]));
    //     $this->assertContains("\"facility_id\"", json_encode($body->data[0]));
    // }

    /**
     * Test retrieval of a facility's reviews
     * @throws Exception
     */
    public function testGetReviews()
    {
        $facility = $this->getUserFacility();
        $review = ReviewTest::mock($facility->getId());
        $review->setRating(rand(1, 5));
        $review->setMessage('This is a message');
        $review->setSiteId('');
        $review->setTimestamp(date('Y-m-d H:i:s'));
        /**
         * Genesis_Service_Review::save sends emails, which chokes up on local testing.
         * So we're bypassing the service layer by using the DAO in order to avoid that.
         */
        Genesis_Dao_Review::insert($review);

        // Make API request
        $response = self::$client->get('/api/facilities/' . $facility->getId() . '/reviews');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expectedResponse = AccountMgmt_Service_Facility::serializeFacilityReviews($facility);
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));
    }

    /**
     * Convenience method to retrieve shared facility for use in tests
     *
     * @return Genesis_Entity_Facility
     * @throws Exception
     */
    protected function getUserFacility()
    {
        return Genesis_Service_Facility::loadById(self::$accountShell->facility->getId());
    }

    /**
     * Test data used for updating a facility
     *
     * @return mixed
     */
    private static function getFacilityObj() {
        static $facilityObj;

        if (! $facilityObj) {
            $facilityObj = json_decode(file_get_contents(__DIR__ . '/data/facilityTest.json'));
        }

        return $facilityObj;
    }
}
