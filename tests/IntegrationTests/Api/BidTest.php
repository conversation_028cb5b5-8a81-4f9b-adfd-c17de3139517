<?php


namespace MyfootTests\IntegrationTests\Api;

use AccountMgmt_Clients_ClientApiClient;

class BidTest extends AbstractApiTest
{
    const ACCOUNT_ID = 2318;
    const FACILITY_ID = 205902;

    const AUTH_BEARER_TOKEN = 'auth_bearer_token';

    private $clientApi;
    private $authToken;

    public function setUp()
    {
        $this->clientApi = new AccountMgmt_Clients_ClientApiClient();

        $cookieJar = self::$client->getConfig('cookies')->toArray();

        $rawToken = NULL;
        foreach ($cookieJar as &$cookie) {

            if (self::AUTH_BEARER_TOKEN === $cookie['Name']) {
                $rawToken = $cookie['Value'];
                break;
            }
        }

        $this->authToken = urldecode($rawToken);
    }

    public function testClientApiPingAuth()
    {
        // testing to get bid opps is too flaky in stage
        // this test just needs to make sure myfoot can talk to and authenticate with the client api
        $this->assertNotEmpty($this->authToken, 'auth token is missing');

        $params = ['token' => 'iamsparefoot'];
        $response = $this->clientApi->pingAuth($this->authToken, $params);

        $this->assertNotEmpty($response, 'empty response : ' . json_encode($response));
    }
}