<?php
namespace MyfootTests\IntegrationTests\Api;


class SpecialsTest extends AbstractApiTest
{
    /**
     * Test retrieval of specials
     */
    public function testGetSpecials()
    {
        // Make API request
        $response = self::$client->get('/api/specials');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertEquals($body->count, count($body->data));
        $this->assertGreaterThan(5, $body->data); // @todo Why 5?
        $this->assertEquals('Waived admin fee when you reserve online', $body->data[1]->string); // @todo Why this message?
    }
}
