<?php
namespace MyfootTests\IntegrationTests\Api;

use Exception;
use Genesis_Entity_UserAccess;
use Genesis_Service_User;
use Genesis_Service_UserAccess;
use GenesisTests\Entity\UserTest;
use GenesisTests\Entity\UserAccessTest;

class UsersTest extends AbstractApiTest
{
    /**
     * Test removal of a user
     */
    public function testDeleteUser()
    {
        // Create mock user
        $user = Genesis_Service_User::save(UserTest::mock());
        $userAccess = Genesis_Service_UserAccess::save(UserAccessTest::mock(
            $user->getId(),
            Genesis_Entity_UserAccess::ROLE_LIMITED,
            self::$accountShell->account->getId()
        ));
        $this->assertEquals(self::$accountShell->account->getId(), $userAccess->getAccountId());

        // Make API request
        $response = self::$client->delete('/api/users/' . $user->getId());
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(204, $response->getStatusCode(), 'Expected a 204 HTTP status code');
        #$this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $updatedUserAccess = Genesis_Service_UserAccess::loadById($user->getId());
        $this->assertFalse($updatedUserAccess);
    }

    /**
     * Test unauthorized removal of a user
     */
    public function testUnauthorizedDeleteUser()
    {
        // Reduce user's role
        $loggedUser = self::$accountShell->user;
        $loggedUser = Genesis_Service_UserAccess::loadById($loggedUser->getId());
        $loggedUser->setMyfootRole(Genesis_Entity_UserAccess::ROLE_LIMITED);
        Genesis_Service_UserAccess::save($loggedUser);

        // Create mock user to be deleted
        $user = Genesis_Service_User::save(UserTest::mock());
        $userAccess = Genesis_Service_UserAccess::save(UserAccessTest::mock(
            $user->getId(),
            Genesis_Entity_UserAccess::ROLE_LIMITED,
            self::$accountShell->account->getId()
        ));
        $this->assertEquals(self::$accountShell->account->getId(), $userAccess->getAccountId());

        // Make API request (we expect a Guzzle ClientException to be thrown, but we want to capture it and assert some values)
        $exception = false;
        try {
            self::$client->delete('/api/users/' . $user->getId());
        } catch(Exception $e) {
            $exception = $e;
        }

        // Assert exception type
        $this->assertTrue($exception instanceof \GuzzleHttp\Exception\ClientException, 'Expected GuzzleHttp ClientException');

        // Extract response from exception
        $response = $exception->getResponse();
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(401, $response->getStatusCode(), 'Expected a 401 HTTP status code');
        $this->assertObjectHasAttribute('errors', $body, 'Expected response body to contain \'errors\' property');
        $this->assertEquals(401, $body->errors[0]->status);
        $this->assertEquals('Unauthorized', $body->errors[0]->title, 'Expected "Unauthorized" error title');

        // Revert user to admin role
        $loggedUser->setMyfootRole(Genesis_Entity_UserAccess::ROLE_ADMIN);
        Genesis_Service_UserAccess::save($loggedUser);
    }
}
