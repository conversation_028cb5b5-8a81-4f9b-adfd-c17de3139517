<?php
namespace MyfootTests\IntegrationTests\Api;

use AccountMgmt_Service_Booking;
use Genesis_Service_Booking;
use Genesis_Service_Facility;
use Genesis_Service_StorageSpace;
use Genesis_Service_Transaction;
use GenesisTests\Entity\FacilityTest;
use GenesisTests\Entity\ReservationRequestTest;
use GenesisTests\Entity\StorageSpaceTest;

class BookingsTest extends AbstractApiTest
{
    const GET_BOOKING_DOC_ID = 108;

    /**
     * Shared booking object for use in tests
     *
     * @var Genesis_Service_Transaction
     */
    protected static $booking;

    /**
     * @inheritdoc
     */
    public static function setUpBeforeClass()
    {
        parent::setUpBeforeClass();

        // Get one of the account's units
        $unit = self::$accountShell->units[0];
        $unit = Genesis_Service_StorageSpace::loadById($unit->getId());

        // Reserve the unit
        $reservation = ReservationRequestTest::mock($unit->getId());
        $reservation->setFirstName('Test');
        $reservation->setMoveInDate(date('Y-m-d'));
        $reservationResponse = AccountMgmt_Service_Booking::bookUnit($reservation);
        self::$booking = $reservationResponse->getTransaction();
    }

    /**
     * Test retrieval of a single booking
     */
    public function testGetBooking()
    {
        $confirmationCode = self::$booking->getConfirmationCode();

        // Make request
        $response = self::$client->get('/api/bookings/' . $confirmationCode);
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expectedResponse = json_encode(AccountMgmt_Service_Booking::toArray(self::$booking));
        $this->assertEquals($expectedResponse, json_encode($body->data));
        self::assertDocumentation(self::GET_BOOKING_DOC_ID, $response);
    }

    /**
     * Test updating a single booking
     */
    public function testUpdateBooking() {
        // Generte a mock facility
        $facility = FacilityTest::mock(self::$accountShell->corporation->getId());
        Genesis_Service_Facility::save($facility);

        // Generate a mock unit at that facility
        $unit = StorageSpaceTest::mock($facility->getId());
        Genesis_Service_StorageSpace::save($unit);

        // Prepare data to update booking
        $updateData = [
            'facility_id' => $facility->getId(),
            'unit_id' => $unit->getId(),
            'unit_number' => '4a',
            'first_name' => 'Test',
            'last_name' => 'Booking',
            'email' => '<EMAIL>',
            'phone' => '**********',
            'move_in_date' => '2015-07-30',
            'dispute_reason' => 'test',
            'size_length' => 20,
            'size_width' => 50,
            'auto_state' => 'confirmed'
        ];

        // Make request
        $response = self::$client->put(
            '/api/bookings/' . self::$booking->getConfirmationCode(),
            ['json' => $updateData]
        );
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertEquals($facility->getId(), $body->data->facility_id, 'Expected same facility ID');

        // Make sure the actual booking object has the new faciltiy id
        $booking = Genesis_Service_Transaction::loadById(self::$booking->getConfirmationCode());
        $this->assertEquals($facility->getId(), $booking->getFacilityId());

        // Make sure the booking object is updated for each of the fields
        $expectedResponse = AccountMgmt_Service_Booking::toArray($booking);
        foreach($updateData as $key => $value) {
            $this->assertEquals($expectedResponse[$key], $value);
        }

        // Make sure the response matches the expected
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));

        $this->_testUpdateMoveOut($unit);
        $this->_testChangeCustomerState(\AccountMgmt_Service_Booking::VERIFIED_STATE_MOVED_IN);
        $this->_testChangeCustomerState(\AccountMgmt_Service_Booking::VERIFIED_STATE_DID_NOT_MOVE_IN);
        $this->_testChangeCustomerState(\AccountMgmt_Service_Booking::VERIFIED_STATE_MOVED_OUT);
    }

    /**
     * Helper method to test updating a booking's move out date
     */
    private function _testUpdateMoveOut() {
        $moveOutDate = '2014-03-02';
        $updateData = [
            'move_out_date' => $moveOutDate
        ];

        // Make request
        $response = self::$client->put(
            '/api/bookings/' . self::$booking->getConfirmationCode(),
            ['json' => $updateData]
        );
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');

        // Check to see if the move_out_date and customer_status was updated
        $this->assertEquals($moveOutDate, $body->data->move_out_date);

        // Check to see if the customer state was updated
        $this->assertEquals(AccountMgmt_Service_Booking::VERIFIED_STATE_MOVED_OUT, $body->data->customer_state);
    }

    /**
     * Helper method to test updating a booking's customer state
     *
     * @param string $state One of theAccountMgmt_Service_Booking::VERIFIED_* constants
     */
    private function _testChangeCustomerState($state) {
        $updateData = [
            'customer_state' => $state
        ];

        // Make request
        $response = self::$client->put(
            '/api/bookings/' . self::$booking->getConfirmationCode(),
            ['json' => $updateData]
        );
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertEquals($state, $body->data->customer_state, 'Expected customer state of '. $state);
    }
}
