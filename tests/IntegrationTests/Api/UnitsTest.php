<?php
namespace MyfootTests\IntegrationTests\Api;

use AccountMgmt_Service_Unit;
use Genesis_Service_StorageSpace;
use Genesis_Service_UnitSpecial;
use GenesisTests\Entity\StorageSpaceTest;
use GenesisTests\Entity\UnitSpecialTest;

class UnitsTest extends AbstractApiTest
{
    const GET_ALL_UNITS_FOR_FACILITY_DOC_ID = 81;
    const GET_SINGLE_UNIT_DOC_ID = 83;
    const PUT_SINGLE_UNIT_DOC_ID = 85;
    const POST_SINGLE_UNIT_DOC_ID = 82;
    const GET_UNIT_SPECIALS_DOC_ID = 89;
    const POST_UNIT_SPECIAL_DOC_ID = 91;
    const DELETE_UNIT_SPECIAL_DOC_ID = 92;

    /**
     * Test retrieval of units by facility ID
     */
    public function testGetUnits()
    {
        $response = self::$client->get(
            '/api/units',
            ['query' => ['facility_id' => self::$accountShell->facility->getId()]]
        );
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertSameSize(self::$accountShell->units, $body->data);
        self::assertDocumentation(self::GET_ALL_UNITS_FOR_FACILITY_DOC_ID, $body);
    }

    /**
     * Test retrieval of unit by unit ID
     */
    public function testGetUnit()
    {
        $unit = self::$accountShell->units[0];

        // Make API request
        $response = self::$client->get('/api/units/' . $unit->getId());
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $expectedResponse = (object) AccountMgmt_Service_Unit::toArray($unit);
        $this->assertEquals($expectedResponse, $body->data);
        self::assertDocumentation(self::GET_SINGLE_UNIT_DOC_ID, $body);
    }

    /**
     * Test retrieval of several units by ID
     */
    public function testGetSeveralUnits()
    {
        $unit1 = self::$accountShell->units[1];
        $unit2 = self::$accountShell->units[2];

        if (! $unit1) {
            self::markTestSkipped('Units were not created due to a prior test');
        }

        // Make API request
        $response = self::$client->get(
            '/api/units/' . $unit1->getId() . ',' . $unit2->getId(),
            ['query' => ['facility_id' => self::$accountShell->facility->getId()]]
        );
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertEquals(2, count($body->data), 'Expected two units in response.');
        $unit1 = (object)\AccountMgmt_Service_Unit::toArray($unit1);
        $unit2 = (object) AccountMgmt_Service_Unit::toArray($unit2);
        foreach ($body->data as $unit) {
            // Force reservation days to be the same value since other tests may override this
            $resWindowDays = 60;
            $unit->reservation_days = $resWindowDays;
            $unit1->reservation_days = $resWindowDays;
            if (json_encode($unit) === json_encode($unit1)) {
                continue;
            }

            $unit2->reservation_days = $resWindowDays;
            if (json_encode($unit) === json_encode($unit2)) {
                continue;
            }

            $this->fail(sprtinf('Exact match unit not found: %s among (%s, %s)\nUnit:%s\nUnit1:%s\nUnit2:%s',
                $unit->id,
                $unit1->id,
                $unit2->id,
                var_export($unit, true),
                var_export($unit1, true),
                var_export($unit2, true)
            ));
        }
    }

    /**
     * Test updating a unit
     */
    public function testPutUnit()
    {
        //@todo update PUT api/units/:id
        $updateData = [
            'size_length' => '7.00',
            'size_width' => '7.00',
            'size_height' => null,
            'unit_type' => 'unit',
            'floor' => '1',
            'reservation_days' => '60',
            'url' => 'test.com',
            'regular_price' => '30.00',
            'sparefoot_price' => '15.00',
            'deposit' => 50,
            'quantity' => '1',
            'climate_controlled' => true,
            'humidity_controlled' => true,
            'air_cooled' => true,
            'heated' => true,
            'covered' => true,
            'vehicle_only' => false,
            'vehicle_allowed' => true,
            'drive_up' => true,
            'stacked' => true,
            'underground' => true,
            'parking_warehouse' => true,
            'pull_through' => true,
            'is_grouped' => false,
            'grouped_num_available' => '1',
            'grouped_quantity' => '1',
            'power' => true,
            'alarm' => true,
            'shelves' => true,
            'lighted' => true,
            'active' => true,
            'publish' => true,
            'approved' => true,
            'lot_type' => 'gravel',
            'door_type' => 'ROLL_UP',
            'door_width' => 5,
            'door_height' => 5,
            'outdoor_access' => true
        ];

        $unit = self::$accountShell->units[0];
        if (! $unit) {
            self::markTestSkipped('Units were not created due to a prior test.');
        }

        // Make API request
        $response = self::$client->put(
            '/api/units/' . $unit->getId(),
            ['json' => $updateData]
        );
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        foreach ($updateData as $key => $value) {
            $this->assertEquals($value, $body->data->$key, $key. ' was not updated correctly.');
        }
        $expectedResponse = AccountMgmt_Service_Unit::toArray(Genesis_Service_StorageSpace::loadById($unit->getId()));
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));
        self::assertDocumentation(self::PUT_SINGLE_UNIT_DOC_ID, $body);
    }

    /**
     * Test adding a unit
     */
    public function testPostUnit()
    {
        $createData = [
            'size_length' => '7.00',
            'size_width' => '7.00',
            'unit_type' => 'unit',
            'regular_price' => 50,
            'facility_id' => self::$accountShell->facility->getId()
        ];

        // Make API request
        $response = self::$client->post(
            '/api/units/',
            ['json' => $createData]
        );
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        foreach ($createData as $key => $value) {
            $this->assertEquals($value, $body->data->$key, $key. ' was not updated correctly.');
        }

        // Check to see if the unit with id is in the database
        $unit = Genesis_Service_StorageSpace::loadById($body->data->id);
        $this->assertNotEquals(null, $unit);

        // Check to see if the toJSON matches the response
        $expectedJsonResponse = AccountMgmt_Service_Unit::toJson($unit);
        $this->assertEquals($expectedJsonResponse, json_encode($body->data));

        // Assert response matches documentation
        self::assertDocumentation(self::POST_SINGLE_UNIT_DOC_ID, $body);
    }

    /**
     * Test retrieval of a unit's special
     */
    public function testGetUnitSpecial()
    {
        $unit = self::$accountShell->units[1];

        // Create mock unit special
        Genesis_Service_UnitSpecial::save(UnitSpecialTest::mock($unit));

        // Make API request
        $response = self::$client->get('/api/units/' . $unit->getId() . '/specials');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertEquals(json_encode(AccountMgmt_Service_Unit::toSpecialsArray($unit)[0]), json_encode($body->data[0]));
        self::assertDocumentation(self::GET_UNIT_SPECIALS_DOC_ID, $body);
    }

    /**
     * Test retrieval of a unit's specials by type
     */
    public function testGetUnitSpecialsType()
    {
        $unit = self::$accountShell->units[3];

        // Create mock unit special
        Genesis_Service_UnitSpecial::save(UnitSpecialTest::mock($unit, 4));
        // Make API request
        $response = self::$client->get('/api/units/' . $unit->getId() . '/specials/4');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertEquals(json_encode(AccountMgmt_Service_Unit::toSpecialsArray($unit)[0]), json_encode($body->data[0]));
    }

    /**
     * Test adding unit specials
     */
    public function testPostUnitSpecial()
    {
        $facilityId = self::$accountShell->facility->getId();

        $unit = Genesis_Service_StorageSpace::save(StorageSpaceTest::mock($facilityId));
        $updateData = [
            'special_type' => 'promo_percent',
            'is_promo' => true,
            'percent_off' => '0.5',
            'dollar_off' => null,
            'dollar_override' => null,
            'free_item_id' => null,
            'requires_prepaid_months' => false,
            'requires_minimum_lease_months' => false,
            'months' => '1,2',
            'free_item_name' => null
        ];
        $this->_testUnitSpecialCreation($unit, $updateData);

        $unit = Genesis_Service_StorageSpace::save(StorageSpaceTest::mock($facilityId));
        $updateData = [
            'special_type' => 'promo_dollar',
            'is_promo' => true,
            'percent_off' => null,
            'dollar_off' => 25,
            'dollar_override' => null,
            'free_item_id' => null,
            'requires_prepaid_months' => false,
            'requires_minimum_lease_months' => false,
            'months' => '1,2',
            'free_item_name' => null
        ];
        $this->_testUnitSpecialCreation($unit, $updateData);

        $unit = Genesis_Service_StorageSpace::save(StorageSpaceTest::mock($facilityId));
        $updateData = [
            'special_type' => 'discount_percent',
            'is_promo' => false,
            'percent_off' => '0.5',
            'dollar_off' => null,
            'dollar_override' => null,
            'free_item_id' => null,
            'requires_prepaid_months' => false,
            'requires_minimum_lease_months' => false,
            'months' => null,
            'free_item_name' => null
        ];
        $this->_testUnitSpecialCreation($unit, $updateData);

        $unit = Genesis_Service_StorageSpace::save(StorageSpaceTest::mock($facilityId));
        $updateData = [
            'special_type' => 'discount_dollar',
            'is_promo' => false,
            'percent_off' => null,
            'dollar_off' => 10,
            'dollar_override' => null,
            'free_item_id' => null,
            'requires_prepaid_months' => false,
            'requires_minimum_lease_months' => false,
            'months' => null,
            'free_item_name' => null
        ];
        $this->_testUnitSpecialCreation($unit, $updateData);

        $unit = Genesis_Service_StorageSpace::save(StorageSpaceTest::mock($facilityId));
        $updateData = ['id' => 1];
        $response = $this->_testUnitSpecialCreation($unit, $updateData);

        self::assertDocumentation(self::POST_UNIT_SPECIAL_DOC_ID, $response);
    }

    /**
     * Helper method to add a unit special
     *
     * @param \Genesis_Entity_StorageSpace $unit
     * @param array $updateData
     * @return mixed
     */
    private function _testUnitSpecialCreation($unit, $updateData)
    {
        // Make API request
        $response = self::$client->post(
            '/api/units/' . $unit->getId() . '/specials',
            ['json' => $updateData]
        );
        $body = json_decode($response->getBody());

        // Make sure all the fields were updated
        foreach($updateData as $key => $value) {
            $this->assertEquals($value, $body->data[0]->$key, $key . ' was not updated properly.');
        }

        // Make sure the json matches the service toArray function
        $expectedResponse = AccountMgmt_Service_Unit::toSpecialsArray($unit);
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));

        return $body;
    }
}
