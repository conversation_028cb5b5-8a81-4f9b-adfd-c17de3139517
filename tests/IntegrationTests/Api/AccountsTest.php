<?php
namespace MyfootTests\IntegrationTests\Api;

use Exception;
use Genesis_Entity_Account;
use Genesis_Entity_Source;
use Genesis_Entity_Special;
use Genesis_Entity_UserAccess;
use Genesis_Service_Special;
use Genesis_Service_StorageSpace;
use Genesis_Service_UnitSpecial;
use GenesisTests\Entity\SpecialTest;
use GenesisTests\Entity\StorageSpaceTest;
use GenesisTests\Entity\UnitSpecialTest;
use GuzzleHttp\Client;

class AccountsTest extends AbstractApiTest
{
    /**
     * @inheritdoc
     */
    public static function setUpBeforeClass()
    {
        // Use GOD account
        self::debug('Creating shell account with GOD role...');
        self::$accountShell = self::createAccountShell(
            Genesis_Entity_Account::BID_TYPE_PERCENT,
            Genesis_Entity_Source::ID_MANUAL,
            Genesis_Entity_UserAccess::ROLE_GOD
        );

        parent::setUpBeforeClass();
    }

    /**
     * @inheritdoc
     */
    public static function tearDownAfterClass()
    {
        self::debug('Removing GOD account...');
        self::$accountShell = null;
        parent::tearDownAfterClass();
    }

    /**
     * Test retrieval of accounts
     */
    public function testGetAccounts()
    {
        self::debug('Requesting accounts...');
        $response = self::$client->get('/api/accounts');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertGreaterThan(500, count($body->data), 'Expected at least 500 accounts');
        $this->assertTrue($body->meta->count === count($body->data), 'Expected response\'s count property to match number of account objects in response');
    }

    /**
     * Test retrieval of a single account
     */
    public function testGetAccount() {
        $account = self::$accountShell->account;
        self::debug('Requesting account with ID ' . $account->getId() . '...');
        $response = self::$client->get('/api/accounts/' . $account->getId());
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $this->assertEquals($account->getName(), $body->data->name, 'Expected response body to contain account name');
    }

    /**
     * Test retrieval of account specials
     */
    public function testGetSpecials()
    {
        // Generate mock unit
        self::$accountShell->units[] = Genesis_Service_StorageSpace::save(
            StorageSpaceTest::mock(self::$accountShell->facility->getId())
        );

        // Generate random special
        self::debug('Generating random special...');
        $special = Genesis_Service_Special::save(
            SpecialTest::mockDollarOff(rand(1, 59999) / 100)
        );

        // Add special to unit
        self::debug('Assigning special to unit...');
        Genesis_Service_UnitSpecial::save(
            UnitSpecialTest::mock(self::$accountShell->units[0], $special->getId())
        );

        self::debug('Requesting account specials...');
        $response = self::$client->get('/api/accounts/' . self::$accountShell->account->getId() . '/specials');
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to container \'data\' property');
        $this->assertSpecialResponse($special, $body);
    }

    private function assertSpecialResponse(Genesis_Entity_Special $special, $body) {
        $responseSpecialId = null;

        foreach ($body->data as $responseSpecial) {
            if ($responseSpecial->id == $special->getId()) {
                $responseSpecialId = $special->getId();
            }
        }
        $this->assertEquals($special->getId(), $responseSpecialId, 'Expected response body to have special ID');
    }
}
