<?php

namespace MyfootTests\IntegrationTests\Api;

use BidOptimizer_Clients_BidOptimizerClient as BidOptimizerClient;
class BidOptimizerIntegrationTest extends \PHPUnit_Framework_TestCase{

    public function testHealthEndpoint(){
        $bidOptClient = new BidOptimizerClient();

        $response = $bidOptClient->getHealthStatus();
        
        $this->assertArrayHasKey('status',(array)$response);
    }
}