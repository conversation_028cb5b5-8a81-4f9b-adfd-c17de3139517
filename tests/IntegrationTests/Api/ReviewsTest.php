<?php
namespace MyfootTests\IntegrationTests\Api;

use AccountMgmt_Service_Review;
use Genesis_Dao_Review;
use Genesis_Db_Connection;
use Genesis_Service_Review;
use GenesisTests\Entity\ReviewTest;

class ReviewsTest extends AbstractApiTest
{
    /**
     * Test retrieval of a single review
     */
    public function testGetReview() {
        // Create mock review
        $review = ReviewTest::mock(self::$accountShell->facility->getId());
        $review->setRating(rand(1, 5));
        $review->setMessage('This is a message');
        $review->setSiteId('');
        $review->setTimestamp(date('Y-m-d H:i:s'));
        /**
         * Genesis_Service_Review::save sends emails, which chokes up on local testing.
         * So we're bypassing the service layer and using the DAO in order to avoid that.
         */
        Genesis_Dao_Review::insert($review);
        $reviewId = Genesis_Db_Connection::getInstance()->getLastInsertId();

        // Make API request
        $response = self::$client->get('/api/reviews/' . $reviewId);
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('data', $body, 'Expected response body to contain \'data\' property');
        $review = Genesis_Service_Review::loadById($reviewId);
        $expectedResponse = AccountMgmt_Service_Review::toArray($review);
        $this->assertEquals(json_encode($expectedResponse), json_encode($body->data));
    }
}
