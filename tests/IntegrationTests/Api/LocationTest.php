<?php
namespace MyfootTests\IntegrationTests\Api;

class LocationTest extends AbstractApiTest
{
    /**
     * Test retrieval of polygon location data for given zip code(s)
     */
    public function testGetPolygonsForZips()
    {
        // Make API request
        $query = ['zipCodes' => [78701]];
        $response = self::$client->post('/api/location/polygon', ['json' => $query]);
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertObjectHasAttribute('polygon', $body, 'Expected response body to contain \'polygon\' property');
        $this->assertRegExp('/^POLYGON\s\(\(\-97.+/', $body->polygon);
    }
}
