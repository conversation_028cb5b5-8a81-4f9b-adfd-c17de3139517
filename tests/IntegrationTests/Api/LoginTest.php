<?php
namespace MyfootTests\IntegrationTests\Api;

use Exception;
use GuzzleHttp\Client;

class LoginTest extends \PHPUnit_Framework_TestCase
{
    /**
     * @inheritdoc
     */
    public function setup()
    {
        $this->client = new Client([
            // Base URI is used with relative requests (testing within container, so use localhost0
            'base_uri' => 'http://localhost/api/login',
            #'base_uri' => 'http://my.sparefoot.local/api/login', // For testing locally
            // You can set any number of default request options.
            'timeout'  => 10.0,
        ]);
    }

    /**
     * Test logging in without an email
     */
    public function testLoginWithoutEmail()
    {
        // We expect a Guzzle ClientException to be thrown, but we want to capture it and assert some values
        $exception = false;
        try {
            $response = $this->client->get('?password=password');
        } catch(Exception $e) {
            $exception = $e;
        }

        if($exception) {
            // Assert exception type
            $this->assertTrue($exception instanceof \GuzzleHttp\Exception\ClientException, 'Expected GuzzleHttp ClientException');

            // Extract response from exception
            $response = $exception->getResponse();
            $body = json_decode($response->getBody());

            // Assert response values
            $this->assertEquals(400, $response->getStatusCode(), 'Expected a 400 HTTP status code');
            $this->assertEquals(400, $body->errors[0]->status, 'Expected 400 error status code');
            $this->assertEquals('Bad Request', $body->errors[0]->title, 'Expected "Bad Request" error title');
            $this->assertEquals('Email is required', $body->errors[0]->detail, 'Expected "Email is required" error details message');
        } else {
            $this->assertContains('Empty reply from server', $response->getBody(), 'Expected empty replo   ');
        }
    }

    /**
     * Test logging in without a password
     */
    public function testLoginWithoutPassword()
    {
        // We expect a Guzzle ClientException to be thrown, but we want to capture it and assert some values
        $exception = false;
        try {
            $this->client->get('?email=myfakeemail');
        } catch(Exception $e) {
            $exception = $e;
        }

        // Assert exception type
        $this->assertTrue($exception instanceof \GuzzleHttp\Exception\ClientException, 'Expected GuzzleHttp ClientException');

        // Extract response from exception
        $response = $exception->getResponse();
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(400, $response->getStatusCode(), 'Expected a 400 HTTP status code');
        $this->assertEquals(400, $body->errors[0]->status, 'Expected 400 error status code');
        $this->assertEquals('Bad Request', $body->errors[0]->title, 'Expected "Bad Request" error title');
        $this->assertEquals('Password is required', $body->errors[0]->detail, 'Expected "Password is required" error details message');
    }

    /**
     * Test logging in with an invalid email/password combo
     */
    public function testLoginWithInvalidCredentials()
    {
        // We expect a Guzzle ClientException to be thrown, but we want to capture it and assert some values
        $exception = false;
        try {
            $this->client->get('?email=myfakeemail&password=password');
        } catch(Exception $e) {
            $exception = $e;
        }

        // Assert exception type
        $this->assertTrue($exception instanceof \GuzzleHttp\Exception\ClientException, 'Expected GuzzleHttp ClientException');

        // Extract response from exception
        $response = $exception->getResponse();
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(401, $response->getStatusCode(), 'Expected a 401 HTTP status code');
        $this->assertEquals(401, $body->errors[0]->status, 'Expected 401 error status code');
        $this->assertEquals('Unauthorized', $body->errors[0]->title, 'Expected "Unauthorized" error title');
        $this->assertStringStartsWith('Authentication failed', $body->errors[0]->detail, 'Expected "Authentication failed" error details message');
    }

    /**
     * Test logging in with a valid email/password combo
     */
    public function testLoginWithValidCredentials()
    {
        // Create mock test account
        $shell = AbstractApiTest::createAccountShell();

        $response = $this->client->get(sprintf('?email=%s&password=%s',
            $shell->user->getEmail(),
            $shell->password
        ));
        $body = json_decode($response->getBody());

        // Assert response values
        $this->assertEquals(200, $response->getStatusCode(), 'Expected a 200 HTTP status code');
        $this->assertTrue($body->success);
    }
}
