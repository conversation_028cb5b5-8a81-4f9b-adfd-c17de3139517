<?php namespace MyfootTests\Facility;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 4/15/15
 * Time: 9:46 AM
 */

class BiddingTest extends AbstractFacility
{
    const SLEEP = 5;
    public function testBidding()
    {
        $object = $this->_createAccountShell();

        $user = $object->user;
        $password = $object->password;
        /**
         * @var $facility \Genesis_Entity_Facility
         */
        $facility = $object->facility;

        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityBidding($facility->getId());
        $this->waitForText('Large Units');

        self::byId('bid-amount-display')->attribute('value');
        self::byId('bid-up')->click();
        sleep(self::SLEEP); //ajax
        self::byId('bid-up')->click();
        sleep(self::SLEEP); //ajax

        $value = self::byId('bid-amount-display')->text();
        $this->assertEquals('85.00', $value, 'increment button failed');

        self::byId('bid-down')->click();
        sleep(self::SLEEP); //ajax
        $value = self::byId('bid-amount-display')->text();
        $this->assertEquals('80.00', $value, 'decrement button failed');
        self::byId('submit_button')->click();

        self::waitUntil(function() {
            return self::byId('submit_button')->displayed() ? true : null;
        }, self::timeout());

        $this->assertEquals(number_format($value, 2), self::byId('current_bid')->text());

        //bounce the page and make sure it saved
        self::url('/features/bid-custom?fid=' . $facility->getId());

        //wait
        $this->waitForText('Change Your AdNetwork Bid');

        $this->assertEquals(number_format($value, 2), self::byId('current_bid')->text());
    }
}
