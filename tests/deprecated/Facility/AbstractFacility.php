<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/11/14
 * Time: 12:40 PM
 */
namespace MyfootTests\Facility;
use MyfootTests\MyAbstract;
class AbstractFacility extends MyAbstract
{
    protected function _navigateToFacilityUnits($facilityId)
    {
        self::url('/features/inventory?fid='.$facilityId);
        $this->waitForText('Unit'); //we made it
    }

    protected function _navigateToFacilityDetails($facilityId)
    {
        self::url('/features/details?fid='.$facilityId);
        $this->waitForText('Facility Name');
    }

    protected function _navigateToFacilityHours($facilityId)
    {
        self::url('/features/hours?fid='.$facilityId);
        $this->waitForText('Observed Holidays');
    }

    protected function _navigateToFacilityAmenities($facilityId)
    {
        self::url('/features/amenities?fid='.$facilityId);
        $this->waitForText('Moving Options');
    }

    protected function _navigateToFacilityBidding($facilityId)
    {
        self::url('/features/bid?fid='.$facilityId);
        $this->waitForText('Change Your AdNetwork Bid');
    }
}