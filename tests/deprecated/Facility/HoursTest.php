<?php     namespace MyfootTests\Facility;
/**
     * Created by IntelliJ IDEA.
     * User: anguyen
     * Date: 03/11/15
     * Time: 12:40 PM
     */
use Genesis_Service_Feature;
    use Genesis_Entity_Feature;
    use GenesisTests\Service\Statement\Factory;
class HoursTest extends AbstractFacility
{
    public function testAppointmentOnlyHours()
    {
        $object = $this->_createAccountShell();

        $user = $object->user;
        $password = $object->password;

        Genesis_Service_Feature::setValue(Genesis_Entity_Feature::MYFOOT_APPOINTMENT_ONLY, Genesis_Service_Feature::ACTIVE_KEY, 1);
        $facility = $object->facility;
        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityHours($facility->getId());

        self::byCssSelector('.appointment-only-mon-checkbox')->click();

        self::byName('commit')->click();
        $nextMonday = strtotime('next monday');

        while ($facility->isObservedHoliday($nextMonday)) {
            $nextMonday = strtotime('+1 week', $nextMonday);
        }

        $nextMonday = date('Y-m-d', $nextMonday);


        Factory::mockUnitAndBooking($facility, $nextMonday);
    }
}
