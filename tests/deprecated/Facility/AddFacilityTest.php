<?php namespace MyfootTests\Facility;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 3/4/15
 * Time: 11:58 AM
 */
class AddFacilityTest extends AbstractFacility
{
    protected function clickAddFacility() {
        $this->waitForSource('site-fac-dropdown-default-text');
        self::waitUntil(function() {
            return strlen(self::byId('site-fac-dropdown-default-text')->text()) > 0 ? true : null;
        }, self::timeout());
        self::byXPath("//*[@id='site-fac-dropdown']//*[@class='search']")->click();
        self::byId('add-facility')->click();
        $this->waitForText("Add a facility from an existing integration");
    }

    public function _testAddFacility() {
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_UNCLE_BOBS);

        $user = $object->user;
        $password = $object->password;

        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToDashboard();

        $this->clickAddFacility();

        self::byId('manual')->click();
        self::byId('next')->click();

        $id = uniqid(__FUNCTION__);
        $this->waitForText('Add a Facility');
        self::sendKeys(self::byId('name'), $id);
        self::sendKeys(self::byId('phone'), '**********');
        self::sendKeys(self::byId('address'), '801 Brazos St');
        self::sendKeys(self::byId('city'), 'Austin');
        self::select(self::byId('state'))->selectOptionByLabel('TX');
        self::sendKeys(self::byId('zip'), '78701');
        self::sendKeys(self::byId('reservation-emails'),  uniqid('test') . '@sparefoot.com');
        self::byId('add-facility-button')->click();

        //gocoder taking too long?
        self::waitUntil(function() {
            return stripos($this->byTag('body')->text(), 'New facility added') ? true : null;
        }, self::timeout() * 2);
        $this->waitForText('New facility added');
        $this->waitForSource($id);
    }

    /*
     * DISABLING TEST so that we can get Statements fixed
     * -Kaleb Fulgham <<EMAIL>> 2016-01-03
     */
    public function _testClickeroo() {
        $object = $this->_createAccountShell();

        $user = $object->user;
        $password = $object->password;

        $this->_doQuickLogin($user->getEmail(), $password);
        $this->_navigateToDashboard();

        $this->clickAddFacility();

        self::byId('centershift4')->click();
        self::byId('next')->click();
        $this->waitForText('Centershift 4.0 Integration Setup');
        self::byId('back_btn')->click();
        $this->waitForText('Create a new integration');

        self::byId('quickstor')->click();
        self::byId('next')->click();
        $this->waitForText('Integration Unavailable Online');
        self::byId('next')->click();
        $this->waitForText('Add a Facility');
        self::byId('cancel')->click();
        $this->waitForText('Create a new integration');

        self::byId('selfstoragemanager')->click();
        self::byId('next')->click();
        $this->waitForText('SelfStorageManager Integration Setup');
        self::byId('back_btn')->click();
        $this->waitForText('Create a new integration');

        self::byId('sitelink')->click();
        self::byId('next')->click();
        $this->waitForText('SiteLink Integration Setup');
        self::byId('back_btn')->click();
        $this->waitForText('Create a new integration');

        self::byId('other')->click();
        self::byId('next')->click();
        $this->waitForText('Add a Facility');
        self::byId('cancel')->click();
        $this->waitForText('Create a new integration');

        self::byId('manual')->click();
        self::byId('next')->click();
        $this->waitForText('Add a Facility');
        self::byId('cancel')->click();
        $this->waitForText('Create a new integration');
    }
}
