<?php namespace MyfootTests\Facility;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 2/9/15
 * Time: 5:48 PM
 */

class AmenitiesTest extends AbstractFacility
{

    public function testUpdateAmenities()
    {
        $object = $this->_createAccountShell();

        $user = $object->user;
        $password = $object->password;
        /**
         * @var $facility \Genesis_Entity_Facility
         */
        $facility = $object->facility;
        \Genesis_Dao_Facility::deleteSuppFacilityData($facility->getId());
        $facility->setElevator(null);
        $facility->setFencedLighted(null);
        $facility->setEGateAccess(null);
        $facility->setSurveillance(null);
        $facility->setFreeTruckRental(null);
        $facility->setTruckRental(null);
        \Genesis_Service_Facility::save($facility);
        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityAmenities($facility->getId());

        // Need to scroll down on the page a bit
        self::byId('truck-rental-yes')->click();
        self::byId('free-truck-yes')->click();
        self::byId('free-truck-distance-limit-yes')->click();
        self::byId('free-truck-max-mileage')->value(100);
        self::byId('truck-insurance-required-yes')->click();
        self::byId('free-truck-insurance-amount')->value(100000);
        self::byId('free-truck-fuel-refill-yes')->click();
        self::byId('truck-access-yes')->click();
        self::byId('truck-access-size')->value('10');
        self::byId('allow-18wheeler-dropoff-yes')->click();
        self::byId('has-18wheeler-alleys-true')->click();
        self::byId('handcarts-yes')->click();
        self::byId('elevator-yes')->click();
        self::byId('sell-moving-supplies-yes')->click();

        //security
        self::byId('surveillance-yes')->click();
        self::byId('egate-access-yes')->click();
        self::byId('fenced-lighted-yes')->click();
        self::byId('resident-manager-yes')->click();

        //access
        self::byId('kiosk-yes')->click();
        self::byId('bilingual-manager-available-yes')->click();
        self::byId('bilingual-language')->value('spanglish, klingon');
        self::byId('accept-tenant-mail-yes')->click();

        //payments
        self::byId('payment-accept-cash-yes')->click();
        self::byId('payment-accept-check-yes')->click();
        self::byId('payment-accept-credit-yes')->click();
        self::byId('payment-visa')->click();
        self::byId('payment-mastercard')->click();
        self::byId('payment-amex')->click();
        self::byId('payment-discover')->click();

        //billing
        self::byId('email-invoicing-yes')->click();
        self::byId('auto-payments-yes')->click();
        self::select(self::byId('charge-date'))->selectOptionByValue('rent due on 1st of each month');
        self::byId('security-deposit-required-yes')->click();
        self::byId('security-deposit-refundable-yes')->click();
        self::byId('security-deposit-percent-button')->click();
        self::byId('security-deposit-percent')->value('40');

        //discounts
        self::byId('military-discount-available-yes')->click();
        self::sendKeys(self::byName('military_discount_amount'), '10');
        self::byId('military-discount-reserves-yes')->click();
        self::byId('military-discount-veterans-yes')->click();
        self::byId('senior-discount-available-yes')->click();
        self::sendKeys(self::byName('senior_discount_amount'), '10');
        self::byId('student-discount-available-yes')->click();
        self::sendKeys(self::byName('student_discount_amount'), '10');

        //insurance
        self::byId('insurance-options-yes')->click();
        self::byId('homeowners-insurance-accepted-yes')->click();
        self::byId('insurance-available-yes')->click();

        //vehicle storage
        self::byId('vehicle-requires-title-yes')->click();
        self::byId('vehicle-renter-titled-yes')->click();
        self::byId('vehicle-require-registration-yes')->click();
        self::byId('vehicle-require-insurance-yes')->click();
        self::byId('vehicle-require-running-yes')->click();

        //other amenities
        self::byId('band-practice-allowed-yes')->click();
        self::byId('remote-paperwork-yes')->click();
        self::byId('wash-station-yes')->click();
        self::byId('dump-station-yes')->click();

        self::byName('commit')->click();

        $this->waitForText('Moving Options');


        //reload page to see if changes infact saved
        $this->_navigateToFacilityAmenities($facility->getId());


        $elem = self::byId('military-discount-available-yes');
        $this->assertEquals($elem->attribute('checked'), 'true');

        $elem = self::byId('senior-discount-available-yes');
        $this->assertEquals($elem->attribute('checked'), 'true');

        $elem = self::byId('student-discount-available-yes');
        $this->assertEquals($elem->attribute('checked'), 'true');

        $elem = self::byId('band-practice-allowed-yes');
        $this->assertEquals($elem->attribute('checked'), 'true');

        $elem = self::byId('wash-station-yes');
        $this->assertEquals($elem->attribute('checked'), 'true');

        $elem = self::byId('dump-station-yes');
        $this->assertEquals($elem->attribute('checked'), 'true');
    }
}