<?php namespace MyfootTests\Facility;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 2/17/15
 * Time: 9:33 AM
 */
use GenesisTests\Entity\StorageSpaceTest;
use GenesisTests\Entity\SitelinkFullUnitTest;
class ReservationWindowTest extends AbstractFacility
{
    public function testNoResWindowEditForApiSet()
    {
        \Genesis_Service_Feature::setValue(\Genesis_Entity_Feature::MYFOOT_RESERVATION_WINDOW, \Genesis_Service_Feature::ACTIVE_KEY, 1);
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_UNCLE_BOBS);

        $user = $object->user;
        $password = $object->password;
        $facility = $object->facility;

        //make some units
        $units = [];
        for ($i = 0; $i < 3; $i++) {
            $unit = StorageSpaceTest::mock($facility->getId());
            $units[] = $unit = \Genesis_Service_StorageSpace::save($unit);
            //  $slUnit  = SitelinkUnitTest::mock($facility->getId(), $unit->getId());
            //  \Genesis_Service_SitelinkFullUnit::save($slUnit);
        }

        $this->_doQuickLogin($user->getEmail(), $password);
        $this->_navigateToFacilityDetails($facility->getId());
        $form = self::byId('facility-details-form');
        $this->assertNotContains('Reservation Window', $form->text());
    }

    public function testUnitOverrides()
    {
        \Genesis_Service_Feature::setValue(\Genesis_Entity_Feature::MYFOOT_RESERVATION_WINDOW, \Genesis_Service_Feature::ACTIVE_KEY, 1);
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_SITELINK);

        $user = $object->user;
        $password = $object->password;
        /**
         * @var $facility \Genesis_Entity_Facility
         */
        $facility = $object->facility;

        //make some units
        $sitelinkFullUnits = SitelinkFullUnitTest::mockGroup($facility->getId(), 5);
        $sitelinkFullUnits = SitelinkFullUnitTest::mockGroup($facility->getId(), 4);
        $sitelinkFullUnits = SitelinkFullUnitTest::mockGroup($facility->getId(), 3);
        $units = $facility->getGroupedUnits(true);

        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityDetails($facility->getId());

        //$this->element($this->using('css selector')->value('.create-exceptions-btn'))->click();
        //self::byId('customize-reservation-window-button')->click();
        //$this->waitForText('Reservation Window Exceptions');
        $overrideValues = [21,30,7];
        $checks = [];
        foreach ($units as $key => $unit) {
            $thisWindow = array_pop($overrideValues);
            /**
             * @var $unit \Genesis_Entity_StorageSpace
             */
            self::select(self::byId('unit-reservation-override-' . $unit->getId()))->selectOptionByValue($thisWindow);
            $checks[$unit->getId()] = $thisWindow;
        }
//sleep(10);
        self::byId('facility-details-save')->click();
        $this->waitForText('Facility Name');

        foreach ($checks as $unitId => $thisWindow) {

            $selected = self::byId('unit-reservation-override-' . $unitId)->attribute('value');

            $this->assertEquals($thisWindow, $selected, 'res window override did not match for unit after save');
        }
    }
}
