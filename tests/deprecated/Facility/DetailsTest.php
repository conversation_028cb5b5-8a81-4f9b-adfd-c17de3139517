<?php namespace MyfootTests\Facility;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/11/14
 * Time: 12:40 PM
 */

class DetailsTest extends AbstractFacility
{
    public function testEditFacilityDetails()
    {
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_SITELINK);

        $user = $object->user;
        $password = $object->password;
        $facility = $object->facility;

        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityDetails($facility->getId());

        self::byId('facility_name');
        self::byId('facility_code');
        self::byId('facility_url');
        self::byId('facility_admin_fee');
        self::byId('facility_phone');
        self::byId('facility_address1');
        self::byId('facility_city');
        self::byId('facility_state');
        self::byId('facility_zip');
        //self::byId('facility_promotions');
        self::byId('facility_description');

        $id = uniqid();

        self::byId('facility_name')->clear();
        self::sendKeys(self::byId('facility_name'), 'Selenium Self Storage ' . $id);

        self::byId('facility_admin_fee')->clear();
        self::sendKeys(self::byId('facility_admin_fee'), '10.99');

        self::byId('facility_phone')->clear();
        self::sendKeys(self::byId('facility_phone'), '5128675309');

        self::byId('facility_address1')->clear();
        self::sendKeys(self::byId('facility_address1'), '801 Brazos Street');

        self::byId('facility_city')->clear();
        self::sendKeys(self::byId('facility_city'), 'Austin');

        self::select(self::byId('facility_state'))->selectOptionByLabel('TX');

        self::byId('facility_zip')->clear();
        self::sendKeys(self::byId('facility_zip'), '78701');

        self::byId('has-alt-address-yes')->click();

        self::byId('onsite-office-at-facility-yes')->click();

        //this had rand, but failed the PHONE NUMBER test sometimes
        self::sendKeys(self::byId('facility_description'), 'Test Some Value ');

        self::byName('commit')->click();

        $this->waitForText('Changes saved.');
    }
}
