<?php namespace MyfootTests\Facility;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 1/26/15
 * Time: 1:42 PM
 */

use Genesis_Entity_Facility;

class AutomaticReactivateTest extends AbstractFacility
{
    const SLEEP = 5;

    public function testDateIsShown()
    {
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_SITELINK);

        $user = $object->user;
        $password = $object->password;
        /**
         * @var $facility Genesis_Entity_Facility
         */
        $facility = $object->facility;
        $date = date('Y-m-d', strtotime("+5 days"));
        $facility->setAutomaticReactivationDate($date);
        $facility->setActive(0);

        $facility = \Genesis_Service_Facility::save($facility);

        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityDetails($facility->getId());

        //make sure facility is not active
        $this->assertEquals(1,self::byId('facility-active-no')->selected());
        //and make sure the date is right
        $this->waitForText('This facility is scheduled to reactivate on');

        $this->assertEquals(date('M j, Y', strtotime($date)), self::byId('automatic-reactivation-date-label')->text());
        $this->assertEquals(date('M j, Y', strtotime($date)), self::byId('automatic-reactivation-date-datepicker')->attribute('value'));

        /**
         *set the date in the past and make sure its not displayed
         */
        $oldDate = date('Y-m-d', strtotime("-2 days"));
        $facility->setAutomaticReactivationDate($oldDate);
        $savedFacility = \Genesis_Service_Facility::save($facility);
        $this->assertEquals($facility->getAutomaticReactivationDate(), $savedFacility->getAutomaticReactivationDate());
        //refresh page
        $this->_navigateToFacilityDetails($facility->getId());

        //sleep(10);
        //should see no message
        $this->assertEquals('', self::byId('reactivation-message')->text());

        /**
         * set the date back in the future, but make it active
         */
        $facility->setAutomaticReactivationDate($date);
        $facility->setActive(1);
        $facility = \Genesis_Service_Facility::save($facility);
        //refresh page
        $this->_navigateToFacilityDetails($facility->getId());


        //should see no message
        $this->assertEquals('', self::byId('reactivation-message')->text());

        /**
         * try interacting with the hide facility modals
         */
        $date = date('Y-m-d', strtotime("+1 month"));
        self::byId('facility-active-no')->click();

        //step
        $this->waitForText('Why do you want to turn this facility off?');
        self::byId('hide-reason-full')->click();
        self::byId('hide-facility-next-step')->click();

        //step
        $this->waitForText('Reactivate your facility automatically!');
        $this->_clickDatepickerDate('reactivation_date', $date);
        self::byId('submit-hide-facility')->click();

        //modal dismiss happens
        sleep(self::SLEEP); //ajax happens here

        //make sure it updated the date on the page
        $this->assertEquals(date('M j, Y', strtotime($date)), self::byId('automatic-reactivation-date-label')->text());

        /**
         * now interact with the change dialog
         */
        $newDate = date('Y-m-d', strtotime($date . " +3 days"));
        self::byId('change-automatic-reactivation-date')->click();
        sleep(self::SLEEP); //other datepicker fires
        $this->_clickDatepickerDate('automatic-reactivation-date-datepicker', $newDate);
        //saves the page
        self::byId('facility-details-save')->click();
        $this->waitForText('Facility Name');
        //finally check it again on refresh that it saved
        $this->assertEquals(date('M j, Y', strtotime($newDate)), self::byId('automatic-reactivation-date-label')->text());

        /**
         * test decide later (defect)
         */
        $facility->setAutomaticReactivationDate(null);
        $facility->setActive(1);
        $facility = \Genesis_Service_Facility::save($facility);

        $this->_navigateToFacilityDetails($facility->getId());

        //turn it off
        self::byId('facility-active-no')->click();

        //step
        $this->waitForText('Why do you want to turn this facility off?');
        self::byId('hide-reason-full')->click();
        self::byId('hide-facility-next-step')->click();

        //step
        $this->waitForText('Reactivate your facility automatically!');
        self::byId('reactivation_decide_later')->click();
        self::byId('submit-hide-facility')->click();

        //modal dismiss happens
        sleep(self::SLEEP);
        //make sure it does not have reacto date
        $this->assertEquals('', self::byId('automatic-reactivation-date-input')->text());
        //see the reactivation link
        $this->waitForText('Set an automatic');

        /**
         * test activate tomorrow (defect)
         */
        $facility->setActive(1);
        $facility->setAutomaticReactivationDate(null);
        $facility = \Genesis_Service_Facility::save($facility);

        $this->_navigateToFacilityDetails($facility->getId());

        self::byId('facility-active-no')->click();
        $date = date('Y-m-d', strtotime("tomorrow"));

        //step
        $this->waitForText('Why do you want to turn this facility off?');
        self::byId('hide-reason-full')->click();
        self::byId('hide-facility-next-step')->click();

        //step
        $this->waitForText('Reactivate your facility automatically!');
        $this->_clickDatepickerDate('reactivation_date', $date);
        self::byId('submit-hide-facility')->click();

        //modal dissmiss happens
        sleep(self::SLEEP);

        $this->waitForText('This facility is scheduled to reactivate on');
        $this->assertEquals(date('M j, Y', strtotime($date)), self::byId('automatic-reactivation-date-label')->text());
        //save it
        self::byId('facility-details-save')->click();
        $this->waitForText('Facility Name');

        $this->waitForText('This facility is scheduled to reactivate on');
        $this->assertEquals(date('M j, Y', strtotime($date)), self::byId('automatic-reactivation-date-label')->text());

    }

    public function testNoReactivateSoldFacility()
    {
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_SITELINK);

        $user = $object->user;
        $password = $object->password;
        /**
         * @var $facility \Genesis_Entity_Facility
         */
        $facility = $object->facility;

        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityDetails($facility->getId());

        self::byId('facility-active-no')->click();

        $this->waitForText('Why do you want to turn this facility off?');
        self::byId('hide-reason-sold-facility')->click();
        self::byId('hide-facility-next-step')->click();

        $this->waitForText('Who is the new contact for this facility?');
        self::byId('new-facility-contact-textarea')->value('Anything you want here. Does not matter.');
        self::byId('submit-hide-facility')->click();
        sleep(self::SLEEP);
        $this->waitForText($facility->getTitle());
        $this->assertNotContains('This facility is scheduled to reactivate on', self::byId('reactivation-message')->text());
        self::byId('facility-details-save')->click();
        $this->waitForText($facility->getTitle());

        $this->assertNotContains('This facility is scheduled to reactivate on', self::byId('reactivation-message')->text());

    }

    public function testNoReactivateIAmUnhappySparefoot()
    {
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_SITELINK);

        $user = $object->user;
        $password = $object->password;
        /**
         * @var $facility \Genesis_Entity_Facility
         */
        $facility = $object->facility;

        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityDetails($facility->getId());

        self::byId('facility-active-no')->click();

        $this->waitForText('Why do you want to turn this facility off?');
        self::byId('hide-reason-unhappy')->click();
        self::byId('hide-facility-next-step')->click();

        $this->waitForText('Why are you unhappy with SpareFoot?');
        self::byId('why-unhappy-textarea')->value('I think you ought to know I\'m feeling very depressed. ');
        self::byId('submit-hide-facility')->click();

        $this->waitForText($facility->getTitle());
        $this->assertNotContains('scheduled to reactivate on', self::byId('reactivation-message')->text());
        sleep(self::SLEEP);
        self::byId('facility-details-save')->click();
        $this->waitForText($facility->getTitle());

        $this->assertNotContains('scheduled to reactivate on', self::byId('reactivation-message')->text());
    }

    public function testModalBailOutNoReactivation()
    {
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_SITELINK);

        $user = $object->user;
        $password = $object->password;
        /**
         * @var $facility \Genesis_Entity_Facility
         */
        $facility = $object->facility;

        $this->_doQuickLogin($user->getEmail(), $password);

        $this->_navigateToFacilityDetails($facility->getId());

        self::byId('facility-active-no')->click();

        $this->waitForText('Why do you want to turn this facility off?');
        self::byId('hide-facility-modal-close')->click();

        $this->assertNotContains('scheduled to reactivate on', self::byId('reactivation-message')->text());
        sleep(self::SLEEP);
        self::byId('facility-details-save')->click();
        $this->waitForText($facility->getTitle());

        $this->assertNotContains('scheduled to reactivate on', self::byId('reactivation-message')->text());
    
    }
}