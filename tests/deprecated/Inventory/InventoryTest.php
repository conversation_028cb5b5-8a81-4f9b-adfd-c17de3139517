<?php namespace MyfootTests\Inventory;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;
use MyfootTests\MyAbstract;
class InventoryTest extends MyAbstract
{
    // makes sure we can create units
    public function _testUnitCreate()
    {
        $object = $this->_createAccountShell();

        $user = $object->user;
        $password = $object->password;

        $this->_doQuickLogin($user->getEmail(), $password);

        $width = rand(3000, 9999);
        $length = rand(3000, 9999);

        self::url('/features/units/?fid='.$object->facility->getId());
        $this->waitForText('Add Unit');

        self::byId('add-first-unit')->click();

        $this->waitForText('Set the appropriate attributes and remember to save your changes.');
        self::sendKeys(self::byName('width'), $width);
        self::sendKeys(self::byName('length'), $length);

        self::byId('standard-rate')->value('55');

        self::byId('unit-save')->click();

        $this->waitForText("$width' x $length' Unit");
    }

    // tests if unit are updated
    public function _testUnitsParkingUpdate() {

        $object = $this->_createAccountShell();
        for ($i = 0; $i < 2; $i++) {
            \Genesis_Service_StorageSpace::save(
                \GenesisTests\Entity\StorageSpaceTest::mock(
                    $object->facility->getId(), \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE)
            );
        }
        $user = $object->user;
        $password = $object->password;

        $this->_doQuickLogin($user->getEmail(), $password);

        self::url('/features/units?fid='.$object->facility->getId());
        $this->waitForText('Units');

        $this->byCssSelector('#units-table tr[id="0"]')->click();

        $this->waitForText('Set the appropriate attributes and remember to save your changes.');
        $this->waitForSource('unit-type');
        $typeNum = self::byId('unit-type');
        self::select($typeNum)->selectOptionByValue('4');

        self::byName('width')->clear();
        self::byName('width')->value('30');

        self::byName('length')->clear();
        self::byName('length')->value('60');

        self::byName('height')->clear();
        self::byName('height')->value('10');

        self::byId('standard-rate')->clear();
        self::byId('standard-rate')->value('99');

        $checkboxes = [ 'stacked', 'basement', 'parking_warehouse', 'pull_thru', 'premium', 'ada'];
        foreach ($checkboxes as $name) {
            $input = self::byName($name);
            if ($input->selected() == false) {
                $input->click();
            }
        }

        $radios = ['lot_type' => 'gravel', 'covered'=>'1'];
        foreach ($radios as $key => $value) {
            $input = $this->byCssSelector("[name='$key'][value='$value']");
            if ($input->selected() == false) {
                $input->click();
            }
        }
        self::byName('floor')->clear();
        self::byName('floor')->value(5);

        self::byId('unit-save')->click();

        $this->waitForText('Add Unit'); //finished saving

        $this->byCssSelector('#units-table tr[id="0"]')->click();

        $this->waitForText('Set the appropriate attributes and remember to save your changes');
        $this->assertEquals(30, self::byName('width')->value());
        $this->assertEquals(60, self::byName('length')->value());
        $this->assertEquals(10, self::byName('height')->value());

        $this->assertEquals(99, self::byName('standard_rate')->value());

        foreach ($checkboxes as $name) {
            $input = self::byName($name);
            $this->assertTrue($input->selected(), "failed on $name with " . $input->attribute('selected'));
        }
        foreach ($radios as $key=>$value) {
            $input = $this->byCssSelector("[name='$key'][value='$value']");
            $this->assertTrue($input->selected());
        }
        $this->assertEquals(5, self::byName('floor')->value());
    }

    // update storage unit
    public function _testUnitStorageUpdate()
    {
        $object = $this->_createAccountShell();

        for ($i = 0; $i < 2; $i++) {
            \Genesis_Service_StorageSpace::save(
                \GenesisTests\Entity\StorageSpaceTest::mock(
                    $object->facility->getId(), \Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT)
            );
        }

        $user = $object->user;
        $password = $object->password;

        $this->_doQuickLogin($user->getEmail(), $password);

        self::url('/features/units?fid='.$object->facility->getId());
        $this->waitForText('Units');

        $this->byCssSelector('#units-table tr[id="0"]')->click();

        $this->waitForText('Set the appropriate attributes and remember to save your changes');
        $typeNum = self::byId('unit-type');
        self::select($typeNum)->selectOptionByValue('14');
        self::sendKeys(self::byId('standard-rate'), '99');
        $checkboxes = ['power', 'alarm', 'unit_lights', 'shelves_in_unit', 'climate_controlled', 'humidity_controlled',
            'air_cooled', 'heated',
            'stacked', 'basement', 'parking_warehouse', 'pull_thru', 'premium', 'ada'];

        foreach ($checkboxes as $name) {
            $input = self::byName($name);
            if($input->selected() == false) $input->click();
        }

        $radios = ['door_type' => 'ROLL_UP', 'covered' => '1'];
        foreach ($radios as $key => $radio) {
            $input = $this->byCssSelector("[name='$key'][value='$radio']");
            if ($input->selected() == false) $input->click();
        }

        self::byId('unit-save')->click();

        $this->waitForText('Add Unit'); //finished saving
        $this->byCssSelector('#units-table tr[id="0"]')->click();

        $this->waitForText('Set the appropriate attributes and remember to save your changes');
        foreach ($checkboxes as $name) {
            $input = self::byName($name);
            $this->assertTrue($input->selected());
        }

        foreach ($radios as $key => $radio) {
            $input = $this->byCssSelector("[name='$key'][value='$radio']");
            $this->assertTrue($input->selected());
        }
    }
}
