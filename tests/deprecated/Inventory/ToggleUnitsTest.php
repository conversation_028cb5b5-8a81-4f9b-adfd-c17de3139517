<?php namespace MyfootTests\Inventory;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/22/14
 * Time: 12:36 PM
 */
use GenesisTests\Entity\StorageSpaceTest;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;
use Genesis_Service_StorageSpace;
use MyfootTests\Facility\AbstractFacility;
class ToggleUnitsTest extends AbstractFacility
{
    protected function rowOn($unitNumber) {
        self::byXPath("//*[@id='{$unitNumber}' and contains(@class, 'enabled-row')]");
    }

    protected function rowOff($unitNumber) {
        self::byXPath("//*[@id='{$unitNumber}' and contains(@class, 'disabled-row')]");
    }

    protected function available($unitNumber) {
        if ($unitNumber < 100) {
            $this->assertEquals('true', self::byId("toggle-{$unitNumber}")->attribute('checked'));
        } else {
            $this->assertEquals('true', self::byId("tg_{$unitNumber}")->attribute('checked'));
        }
    }

    protected function notAvailable($unitNumber) {
        if ($unitNumber < 100) {
            $this->assertEquals(null, self::byId("toggle-{$unitNumber}")->attribute('checked'));
        } else {
            $this->assertEquals(null, self::byId("tg_{$unitNumber}")->attribute('checked'));
        }
    }

    protected function toggle($unitNumber) {
        //self::byXPath("//*[@id={$unitNumber}]//div[contains(@class, 'checkbox') and contains(@class, 'ui')]")->click();
        self::byXPath("//tr[@id={$unitNumber}]/td[contains(@class, 'availability-checkbox')]/div[contains(@class, 'checkbox') and contains(@class, 'ui')]")->click();
        sleep(2);
    }

    protected function clickModalButton($confirm) {
        $buttonSelector = ($confirm) ? '.approve' : '.deny';
        $button = $this->_getDisplayedElementByCSS('#single-unit-confirm-modal '.$buttonSelector);
        if ($button) {
            $button->click();
            sleep(1);
        }
    }

    /**
     * Only tests Uncle Bobs toggles. Does not test Site Link
     */
    public function testToggleUnit()
    {
        $object = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_FLAT, \Genesis_Entity_Source::ID_UNCLE_BOBS);
        $units = array();
        for ($i = 0; $i < 2; $i++) { //needs to to surpress the "hide your last unit" dialog
            $unit = StorageSpaceTest::mock($object->facility->getId());
            $units[] = Genesis_Service_StorageSpace::save($unit);
        }

        $facility = $object->facility;

        $this->_doQuickLogin($object->user->getEmail(), $object->password);

        $this->_navigateToFacilityUnits($facility->getId());

        $id = $unit->getId();
        //make sure our unit is on
        $this->rowOn($id);

        //make sure the switch is on
        $this->available($id);

        //flip the toggle
        $this->toggle($id);

        //
        $this->clickModalButton($confirm = true);

        //make sure it turned the row off
        $this->rowOff($id);

        //make sure the switch is off
        $this->notAvailable($id);

        //make sure still correct after page refresh
        $this->refresh();
        $this->waitForText('Unit'); //we made it

        //make sure row is still off
        $this->rowOff($id);
        //make sure the switch is still off
        $this->notAvailable($id);

        //toggle it back on
        $this->toggle($id);

        //make sure it turned the row on
        $this->rowOn($id);
        //make sure the switch is on
        $this->available($id);

        //make sure still correct after page refresh
        $this->refresh();
        $this->waitForText('Unit'); //we made it

        //make sure it turned the row on
        $this->rowOn($id);
        //make sure the switch is on
        $this->available($id);
    }

    private function _getDisplayedElementByCSS($selector) {
        $elements = $this->elements($this->using('css selector')->value($selector));
        foreach ($elements as $element) {
            if ($element->displayed()) {
                return $element;
            }
        }
        return null;
    }

    private function _clickOnDisplayedElementByCSS($selector)
    {
        $elements = $this->elements($this->using('css selector')->value($selector));
        foreach ($elements as $element) {
            if ($element->displayed()) {
                $element->click();
                return;
            }
        }
        $this->fail('There is no visible elements with selector ' . $selector);
    }
}
