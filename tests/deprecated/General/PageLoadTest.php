<?php namespace MyfootTests\General;
use MyfootTests\MyAbstract;
class PageLoadTest extends MyAbstract
{
    public function testPageLoad()
    {

        $shell = $this->_createAccountShell();
        $this->_doQuickLogin($shell->user->getEmail(), $shell->password);
        /**
         * widgets
         */
        self::attempt(function() {
            $this->_testBookingWidgetPagesLoad();
        });

        self::attempt(function() {
            $this->_testBookingWidgetSetupPageLoad();
        });

        self::attempt(function() {
            $this->_testBookingWidgetReservationPageLoad();
        });
        self::attempt(function() {
            $this->_testBookingWidgetAnalyticsPageLoad();
        });

        /**
         * Insights
         *
         * Removed these page checks because they fail around the 15th of the month (12/17/2015)
         */
        // self::attempt(function() {
        //     $this->_testInsightsSourceBuilds();
        // });
        // self::attempt(function() {
        //     $this->_testInsightsOtherReportsPageLoads();
        // });
        // self::attempt(function() {
        //     $this->_testZipCodeReport();
        // });



        /**
         * dashboard
         */
        //$this->_testDashboardLoad(); dead, Mland
        /**
         * payments page
         */
        self::attempt(function() {
            $this->_testForcePaymentPageAddLoads();
        });

        /**
         * users page
         */
        self::attempt(function() {
            $this->_testUserPageLoads();
        });

        /**
         * reservation page
         */
        self::attempt(function() use ($shell) {
            $this->_testReservationsPageLoads($shell->facility->getId());
        });

        /**
         * MUST CALL THIS LAST!
         * logout test
         */
        self::attempt(function() {
            $this->_testLogoutWorksWorks();
        });


    }

    private static function attempt($method)
    {
        try {
            $method();
        } catch (\PHPUnit_Extensions_Selenium2TestCase_NoSeleniumException $e) {
            self::markTestSkipped('restart the grid');
        } catch (\Exception $e) {
            $method();
        }
    }

    public function _testBookingWidgetPagesLoad()
    {
        self::url('/widget');
        $this->waitForText('Get new tenants from your website.');
        self::byId('bookingWidgetOverviewPageTest');
    }

    public function _testBookingWidgetSetupPageLoad()
    {
        self::url('/widget/setup');
        $this->waitForText('Booking Widget Setup');
        self::byId('bookingWidgetSetupPageTest');
    }

    public function _testBookingWidgetReservationPageLoad()
    {

        self::url('/widget/reservations');
        $this->waitForText('Booking Widget Reservations');
        self::byId('bookingWidgetReservationsPageTest');
    }

    public function _testBookingWidgetAnalyticsPageLoad()
    {
        self::url('/widget/analytics');
        $this->waitForText('Booking Widget Analytics');
        self::byId('bookingWidgetAnalyticsPageTest');
    }

    /**
     * if this page doesn't load first, no insights will follow below.
     */
    public function _testInsightsSourceBuilds() {
        self::url('/insights/generatenew');
        $this->waitForSource('"success":0');
    }

    public function _testZipCodeReport() {
        self::url('/insights/custom/report/MyFoot_Quickrep_Report_PriceReportByZipCode');
        $this->waitForText('Price Report by Zip Code');

        self::sendKeys(self::byName('ZIP_CODE'), '33026');
        self::byId('viewInsightsReportBtn')->click();

        // Table doesn't load right away, gotta wait for it
        self::byId('data-table-report');
    }

//    public function _testDashboardLoad() {
//        self::url('/dashboard');
//        $this->waitForSource('page-header');
//
//        self::byId('impression_chart');
//        self::byId('reservations_chart');
//    }

    public function _testForcePaymentPageAddLoads() {
        self::url('/payment');
        $this->waitForText('Complete Account Setup');
        self::byId('complete-setup-now')->click();
        $this->waitForText('Billing Information');
    }

    public function _testUserPageLoads() {
        self::url( '/user');
        $this->waitForText('Users');
        self::byId('usersOverviewPageTest');
    }

    public function _testReservationsPageLoads($facilityId) {
        self::url('/customers/reservations?fid=' . $facilityId);
        $this->waitForText('No results'); //if someone renames this fac, effff
    }

    public function _testLogoutWorksWorks() {
        //$url = MyAbstract::MYFOOT_BASE_URL . self::BASE_URL;

        self::url('/login/logout');
        $this->waitForText('New to SpareFoot');
        $button = self::byId('signup-button');
        $button->click();

        $this->waitForText('Signup Code');
    }

    /**
     * This might appear, it might not. that's why an exception is not an exception
     */
    protected function acceptInsightsTos()
    {
        try {
            self::byId('tos')->click();
            self::byId('accept-terms')->click();
        } catch (\Exception $e) {
            ;
        }
    }
}
