<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 5/28/15
 * Time: 10:37 AM
 */

namespace MyfootTests\General;

use Genesis_Entity_Account;
use Genesis_Entity_Source;
use MyfootTests\MyAbstract;
use Genesis_Entity_UserAccess;

class AccountPickerTest extends MyAbstract
{
    /*
     * This is a test only for god level / interal SpareFoot users.
     * DISABLING TEST until account picker is fixed reliably
     * -<PERSON><PERSON><PERSON> <<EMAIL>> 2015-07-10
     */
    public function _testAccountPicker()
    {
        $sisterShell = $this->_createAccountShell();
        /**
         * @var $account Genesis_Entity_Account;
         */
        $account = $sisterShell->account;
        $account->setName(uniqid('PickMe Account '));
        $account = \Genesis_Service_Account::save($account);

        /**
         * fac name
         * @var $facility \Genesis_Entity_Facility
         */
        $facility = $sisterShell->facility;
        $facility->setTitle(uniqid('PickMe Facility '));
        $sisterShell->facility = \Genesis_Service_Facility::save($facility);

        $shell = $this->_createAccountShell(Genesis_Entity_Account::BID_TYPE_FLAT, Genesis_Entity_Source::ID_MANUAL, Genesis_Entity_UserAccess::ROLE_GOD);
        $this->_doLogin($shell->user->getEmail(), $shell->password);

        //use the search picker
        self::waitUntil(function () {
            return stripos(self::byCssSelector('#accounts-search div.selected')->text(), 'Mock Account') !== false ? true : null;
        }, self::timeout());
        self::byId('accounts-search')->byClassName('selected')->click();

        self::waitUntil(function () {
            return self::byId('accounts-search')->byTag('input')->displayed() ? true : null;
        }, self::timeout());
        self::byId('accounts-search')->byTag('input');//->value($account->getName());

        self::keys($account->getName());

        //wait for it to display
        self::waitUntil(function() {
            return self::byId('accounts-search')->byClassName('results')->byTag('a')->displayed() ? true : null;
        }, self::timeout());

        self::byId('accounts-search')->byClassName('results')->byTag('a')->click();

        //verify new account loaded default facility
        $title = $sisterShell->facility->getTitle().' '.$sisterShell->facility->getCompanyCode();
        self::waitUntil(function() use ($title) {
            return $title ===  $this->waitForTextLoad('site-fac-dropdown-default-text');
        }, self::timeout());

        //click a menu link and verify it took the new facilityID
        self::byId('menu-dashboard')->click();
        $title = $sisterShell->facility->getTitle().' '.$sisterShell->facility->getCompanyCode();
        self::waitUntil(function() use ($title) {
            return $title === $this->waitForTextLoad('site-fac-dropdown-default-text') ? true : null;
        }, self::timeout());
    }

    private function waitForTextLoad($id)
    {
        $this->waitForSource($id);
        self::waitUntil(function() use ($id) {
            return strlen(self::byId($id)->text()) ? true : null;
        }, self::timeout());

        return self::byId($id)->text();
    }
}