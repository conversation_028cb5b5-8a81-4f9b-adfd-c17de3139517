<?php namespace MyfootTests\General;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 4/21/15
 * Time: 2:15 PM
 */
use MyfootTests\MyAbstract;
use Genesis_Entity_Account;
use Genesis_Entity_UserAccess;
use Genesis_Entity_Source;
use GenesisTests\Entity\FacilityTest;
use Genesis_Service_Facility;
use PHPUnit_Extensions_Selenium2TestCase_Keys as Keys;
class FacilityPickerTest extends MyAbstract
{
    public function testFacilityPickerTest()
    {
        $shell = $this->_createAccountShell(Genesis_Entity_Account::BID_TYPE_FLAT, Genesis_Entity_Source::ID_MANUAL, Genesis_Entity_UserAccess::ROLE_ADMIN);
        $names = ['Zeta', 'Beta', 'Alpha', 'Delta', 'Epsilon'];
        $facilities = [$shell->facility];
        foreach ($names as $name) {
            $facility = FacilityTest::mock($shell->corporation->getId());
            $facility->setTitle(uniqid("$name Facility "));
            $facilities[] = Genesis_Service_Facility::save($facility);
        }
        //sort actual list
        usort($facilities, function($a, $b) {
           return strcmp($a->getTitle(),$b->getTitle());
        });

        $this->_doQuickLogin($shell->user->getEmail(), $shell->password);
        self::_navigateToDashboard();

        //test the right default facility loaded
        $title = $facilities[0]->getTitle().' '.$facilities[0]->getCompanyCode();
        self::waitUntil(function() use ($title) {
           return $title === $this->waitForTextLoad('site-fac-dropdown-default-text') ? true : null;
        }, self::timeout());

        //trigger building the facility list in the dom
        self::byXPath("//*[@id='site-fac-dropdown']//*[@class='search']")->click();
        sleep(5);
        //test sorting of the names themselves
        $listOfFacilityNames = explode("\n", self::byId('site-fac-dropdown')->byClassName('items')->text());
        foreach ($facilities as $key => $facility) {
            $this->assertEquals(
                $facility->getTitle().' '.$facility->getCompanyCode(),
                $listOfFacilityNames[$key],
                "failed on $key ". $facility->getTitle()
            );
        }

        //click on the last in the list
        self::byXPath("//*[@id='site-fac-dropdown']//*[contains(@class, 'items')]/a[contains(., 'Zeta')]")->click();

        //make sure it comes up now
        $title = $facilities[count($facilities)-1]->getTitle().' '.$facilities[count($facilities)-1]->getCompanyCode();
        self::waitUntil(function() use ($title) {
            return $title === $this->waitForTextLoad('site-fac-dropdown-default-text') ? true : null;
        }, self::timeout());

        //click on a menu entry to make sure it respected the facility choice
        self::byId('menu-dashboard')->click();
        $title = $facilities[count($facilities)-1]->getTitle().' '.$facilities[count($facilities)-1]->getCompanyCode();
        self::waitUntil(function() use ($title) {
            return $title === $this->waitForTextLoad('site-fac-dropdown-default-text') ? true : null;
        }, self::timeout());

        //make sure its stuck in session
        self::refresh();
        self::waitForText('Dashboard');
        $title = $facilities[count($facilities)-1]->getTitle().' '.$facilities[count($facilities)-1]->getCompanyCode();
        self::waitUntil(function() use ($title) {
            return $title === $this->waitForTextLoad('site-fac-dropdown-default-text') ? true : null;
        }, self::timeout());
    }



    private function waitForTextLoad($id)
    {
        $this->waitForSource($id);
        self::waitUntil(function() use ($id) {
            return strlen(self::byId($id)->text()) ? true : null;
        }, self::timeout());

        return self::byId($id)->text();
    }
}