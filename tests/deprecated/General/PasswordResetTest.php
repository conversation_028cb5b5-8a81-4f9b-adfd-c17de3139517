<?php namespace MyfootTests\General;
use GenesisTests\Entity\AccountTest as AccountEntity;
use GenesisTests\Entity\CorporationTest;
use GenesisTests\Entity\FacilityTest as FacilityEntity;
use GenesisTests\Entity\UserAccessTest;
use GenesisTests\Entity\UserTest;
use Genesis_Entity_Account;
use Genesis_Entitiy_User;
use Genesis_Service_Account;
use Genesis_Service_Facility;
use Genesis_Service_User;
use Genesis_Entity_UserAccess;
use MyfootTests\MyAbstract;
class PasswordResetTest extends MyAbstract
{
    CONST MYFOOT_NEW_PASSWORD = 't00feraps!!';
    CONST MYFOOT_USER_PASSWORD = 'testing';

    public function testPasswordReset() {
        if (! \Genesis_Service_Feature::isActive('myfoot.new_reset_password')) {
            return;
        }

        $shell = $this->_createAccountShell();

        $user = $shell->user;

        self::url('/login');
        $this->waitForSource('password');
        $forgot_password = self::byId('forgot-password');
        $forgot_password->click();
        $this->waitForText('Enter your email address and');
        $email = self::byId('reset-email');
        $email->clear();
        $email->value($user->getEmail());
        $submit = self::byId('password-reset-submit');
        $submit->click();
        $this->waitForText('Directions to reset your password have been emailed to you.');
        self::byId('password-reset-modal-close')->click();

        $user = Genesis_Service_User::loadById($user->getId());

        self::url('/login/forgot-password?email='.$user->getEmail().'&k='.$user->getPasswordResetAuthKey());
        //sleep(15);

        $this->waitForText('Reset Your Password');

        $password = self::byId('new-password');
        $password->clear();
        $password->value(self::MYFOOT_NEW_PASSWORD);

        $confirm_password = self::byId('confirm-new-password');
        $confirm_password->clear();
        $confirm_password->value(self::MYFOOT_NEW_PASSWORD);

        self::byId('reset-password-submit')->click();

        $this->waitForText('Dashboard');
    }
}