<?php
namespace MyfootTests;

use Exception;
use Genesis_Client_HipChat;
use PHPUnit_Extensions_Selenium2TestCase;
use PHPUnit_Extensions_Selenium2TestCase_Element;
use PHPUnit_Extensions_Selenium2TestCase_WebDriverException;
use Genesis_Config_Url;
use Genesis_Config_Server;
use GenesisTests\Entity\UserTest;
use Genesis_Service_User;
use GenesisTests\Entity\AccountTest;
use Genesis_Service_Account;
use GenesisTests\Entity\UserAccessTest;
Use Genesis_Service_UserAccess;
Use GenesisTests\Entity\CorporationTest;
use Genesis_Service_Corporation;
use GenesisTests\Entity\FacilityTest;
use Genesis_Service_Facility;
use Authorization\SDK\Token;
use BrowserStack\Local;

/**
 * Class MyFoot_Tests_Abstract
 */
abstract class MyAbstract extends PHPUnit_Extensions_Selenium2TestCase
{
    const MAX_TIME_GRID     = 60; // Seconds max per wait event
    const MAX_TIME_LOCAL    = 20; // Seconds max per wait event

    /**
     * @var array
     */
    private static $settings;

    /**
     * @var BrowserStack\Local
     */
    private static $browserStackLocal;

    public static function setUpBeforeClass() {
        parent::setUpBeforeClass();

        self::$settings = TestSettings::get();

        // Start BrowserStack Local server, if necessary
        if (array_key_exists('browserstack.local', self::$settings['desiredCapabilities']) && self::$settings['desiredCapabilities']['browserstack.local']) {
            $browserStackLocalArgs = ['key' => self::$settings['desiredCapabilities']['browserstack.key']];
            self::$browserStackLocal = new BrowserStack\Local();
            self::$browserStackLocal->start($browserStackLocalArgs);
        }
    }

    // this sucker gets run before each test
    protected function setUp()
    {
        self::$settings['desiredCapabilities']['name'] = get_class($this).'\\'.$this->getName();

        $this->setHost(self::$settings['host']);
        $this->setPort(self::$settings['port']);
        $this->setBrowser(self::$settings['browser']);
        $this->setBrowserUrl(self::$settings['base_url']);
        $this->setSeleniumServerRequestsTimeout(self::$settings['timeout']);
        $this->setDesiredCapabilities(self::$settings['desiredCapabilities']);

        // Maximize browser window
        $this->prepareSession()->currentWindow()->maximize();
    }

    public function tearDown()
    {
        $this->closeWindow();
    }

    public static function tearDownAfterClass()
    {
        #self::$driver->quit();

        // Stop BrowserStack Local server, if it exists
        if (self::$browserStackLocal) {
            self::$browserStackLocal->stop();
        }

        parent::tearDownAfterClass();
    }

    public static function getMyfootBaseUrl()
    {
        if (! self::$settings) {
            self::$settings = TestSettings::get();
        }
        //set base url
        return self::$settings['base_url'];
    }

    /**
     * @todo I find this method naem to be extremely misleading. Perhaps rename to "setValue"?
     * @param PHPUnit_Extensions_Selenium2TestCase_Element $element
     * @param string $value
     */
    protected static function sendKeys(PHPUnit_Extensions_Selenium2TestCase_Element $element, $value = '')
    {
        $element->clear();
        $element->value($value);
    }

    /**
     * find text in dom body
     * @param string $searchString
     * @return int milliseconds time taken
     * @throws Exception
     */
    protected function waitForText($searchString)
    {
        self::waitForSource('<body');
        try {
            self::waitUntil(function () use ($searchString) {
                return (stripos($this->byTag('body')->text(), $searchString) === false) ? null : true;
            }, self::timeout());
        } catch (\PHPUnit_Extensions_Selenium2TestCase_WebDriverException $e) {
            throw new Exception('Timeout waiting for text ' . $searchString . ' after ' .self::timeout() .' milliseconds. Text is ' . number_format(strlen($this->byTag('body')->text())) . ' chars.');
        }
    }

    /**
     * find text in source
     * @param string $searchString
     * @returns int milliseconds taken
     * @throws Exception
     */
    protected function waitForSource($searchString)
    {
        try {
            self::waitUntil(function () use ($searchString) {
                return (stripos($this->source(), $searchString) === false) ? null : true;
            }, self::timeout());
        } catch (\PHPUnit_Extensions_Selenium2TestCase_WebDriverException $e) {
            throw new Exception('Timeout waiting for source ' . $searchString . ' after ' .self::timeout() .' milliseconds. Source is ' . number_format(strlen($this->source())) .' chars.');
        }
    }

    /**
     * return the correct milliseconds for each environment
     * @return int
     */
    protected static function timeout()
    {
        return 1000 * (Genesis_Config_Server::isLocal() ? self::MAX_TIME_LOCAL : self::MAX_TIME_GRID);
    }

    public function takeScreenShot($fileNameSeed = '')
    {
        $ds = '/';
        $baseDir = './screenshots'; // Into project root

        // Make one folder that all screenshots land in this test run
        if (! defined('FOLDER')) {
            if (getenv('SF_BUILD_NUMBER') > 0) {
                $build = getenv('SF_BUILD_NUMBER');
            } else {
                $build = date('Ymd-His');
            }
            define('FOLDER', $build);
        }

        // Make the screenshot folder if deleted/does not exist
        if (! file_exists($baseDir)) {
            mkdir($baseDir);
        }

        // Make the new build folder if necessary
        $buildFolder = $baseDir . $ds . FOLDER;
        if (! file_exists($buildFolder)) {
            mkdir($buildFolder);
        }

        $class = str_replace('Sparefoot\\Myfoot\\', '', get_called_class());
        $fileName = $buildFolder . $ds . $class . '.' . $fileNameSeed . '.png';
        error_log($fileName);

        try {
            $fileData = $this->currentScreenshot();
            file_put_contents($fileName, $fileData);
        } catch (\Exception $e) {
            $fileName = 'SCREENSHOT_FAILED.PNG';
        }

        return $fileName;
    }

    /**
     * Handler for failed tests
     *
     * Creates screenshot of page and sends notification to HipChat
     *
     * @param Exception $e
     * @throws Exception Rethrows the passed exception
     */
    public function onNotSuccessfulTest(Exception $e)
    {
        if (! $this->getSessionId()) {
            parent::onNotSuccessfulTest($e);
            return;
        }

        $fileName = $this->takeScreenShot($this->getName(false));
        $msg = get_class($this) . '::' . $this->getName(false) . PHP_EOL
            . get_class($e) . PHP_EOL
            . $e->getMessage() . PHP_EOL;
        if (Genesis_Config_Server::isStaging() || Genesis_Config_Server::isDev()) {
            $buildKey = getenv('SF_BUILD_KEY') ?: 'NOBUILDKEY';
            $msg = nl2br($msg) . 'Build ' . $buildKey ;
            if ($fileName) {
                $uri = 'https://my.sparefoot.' . Genesis_Config_Server::getEnvDomain() . '/screenshots/' . FOLDER . '/' . $fileName;
                $msg.=' <br/>'
                    . '<a href="' . $uri . '"><img src="' . $uri . '" alt="screenshot-of-brokenness"></a>';
            }
            $hc = new Genesis_Client_HipChat();
            $room = 'MyFoot Errors';
            $hc->sendToRoom(
                $msg,
                $room,
                'Selenium ' . Genesis_Config_Server::getEnvironmentAsString() . ':' . getenv('SF_BUILD_NUMBER'),
                true,
                Genesis_Client_HipChat::COLOR_RED,
                Genesis_Client_HipChat::MSG_FORMAT_HTML
            );
        }
        if (Genesis_Config_Server::isLocal()) {
            parent::onNotSuccessfulTest(new Exception(
                get_class($e) . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString() . PHP_EOL . 'Screenshot: ' . $fileName
            ));
        } else {
            parent::onNotSuccessfulTest($e);
        }
    }

    /**
     * Log in to Myfoot with the provided credentials
     *
     * @param string $email
     * @param string $password
     * @param bool $wait
     */
    protected function _doLogin($email, $password, $wait = true)
    {
        self::url('/login/logout');
        self::waitForText('New to SpareFoot?');
        self::byName('email')->value($email);
        self::byName('password')->value($password);
        self::byId('login-button')->click();
        if ($wait) {
            self::waitForText('Dashboard'); //we're in like flynn
            //sleep(5);
        }
    }

    /**
     * @param string $email
     * @param string $password
     */
    protected function _doQuickLogin($email, $password) {
        self::url('/api/login?email=' . $email  . '&password=' . $password);
        self::waitForText('{"success":true}');
        $cookie = urldecode($this->cookie()->get('auth_bearer_token'));
        $token = Token::createFromRaw($cookie);
        $this->assertTrue(strlen($token->getIdentifier()) > 10, 'cookie failure');
    }

    /**
     * @param string $bidType
     * @param int $sourceId
     * @param string $role
     * @return \stdClass
     * @throws \Exception
     */
    public static function _createAccountShell($bidType = \Genesis_Entity_Account::BID_TYPE_FLAT, $sourceId = \Genesis_Entity_Source::ID_MANUAL, $role = \Genesis_Entity_UserAccess::ROLE_ADMIN)
    {
        //create a user
        $user = UserTest::mock();
        $password = md5(uniqid('statementFactory').rand(0,100000));
        $user->setRawPassword($password);
        $user = Genesis_Service_User::save($user);

        //make an account
        $account = AccountTest::mock($bidType);
        $account = Genesis_Service_Account::save($account);

        //put the user on the account as '$role'
        $userAccess = UserAccessTest::mock($user->getId(), $role, $account->getId());
        $userAccess = Genesis_Service_UserAccess::save($userAccess);

        $corporation = CorporationTest::mock($account->getId(), $sourceId);
        $corporation = Genesis_Service_Corporation::save($corporation);

        $facility = FacilityTest::mock($corporation->getId());
        $facility = Genesis_Service_Facility::save($facility);

        $obj = new \stdClass();
        $obj->user = $user;
        $obj->password = $password;
        $obj->account = $account;
        $obj->userAccess = $userAccess;
        $obj->corporation = $corporation;
        $obj->facility = $facility;

        return $obj;
    }

    /**
     * Click on a date in a datepicker ui without being a nightmare
     *
     * @param string $inputId
     * @param int|string $date
     * @throws \Exception
     */
    protected function _clickDatepickerDate($inputId, $date)
    {
        if (is_int($date)) {
            $timestamp = $date;
        } else {
            $timestamp = strtotime($date);
        }
        if ($timestamp < strtotime(2001)) {
            throw new \InvalidArgumentException('invalid date requested: ' . date('Y-m-d', $timestamp));
        }
        $day = date('j', $timestamp); //no leading one on first 9 days of month
        $month = date('F', $timestamp); //the full name September
        $year = date('Y', $timestamp); //four digits
        self::byId($inputId); //make sure it exists

        //fire the JS directly so that selenium does not need to be in focus
        self::execute([
            'script' => '$("#' . $inputId . '").datepicker("show"); console.log("datepicker show");',
            'args' => []
        ]);
        self::waitUntil(function() {
            try {
                self::byId('ui-datepicker-div');
                return true;
            } catch (\Exception $e) {
                return null;
            }
        }, 10);

        //grab the now exposed calendar
        $calendar = self::byXPath("//div[@id='ui-datepicker-div']");

        //wait for it to render the tops of columns
        $windowRange = $calendar->byXPath("//div[contains(@class, 'ui-datepicker-title')]")->text();
        //move back to the right month
        while (strtotime($windowRange) > strtotime ($month . ' ' . $year)) {
            $nextButton = $calendar->byXPath("//*[contains(@class, 'ui-datepicker-prev')]");
            $nextButton->click();
            $windowRange = $calendar->byXPath("//div[contains(@class, 'ui-datepicker-title')]")->text();
        }
        //move forward to the right month
        while (strtotime($windowRange) < strtotime ($month . ' ' . $year)) {
            $nextButton = $calendar->byXPath("//*[contains(@class, 'ui-datepicker-next')]");
            $nextButton->click();
            $windowRange = $calendar->byXPath("//div[contains(@class, 'ui-datepicker-title')]")->text();
        }
        try {
            self::waitForSource('>'.$day.'<');
            //find our day and click it
            $day = $calendar->byXPath("//a[contains(@class, 'ui-state-default') and text()=$day]");
            $day->click();

            //maybe our day was not not exposed as clickable.
        } catch (\Exception $e) {
            throw new \Exception("Looking for [$day] in $month $year but could not find it. Check that date is enabled in picker config.");
        }
    }

    /**
     * @param int $facilityId
     */
    protected function _navigateToDashboard($facilityId = null)
    {
        if ($facilityId) {
            self::url('/dashboard?fid='.$facilityId);
        } else {
            self::url('/dashboard');
        }
        $this->waitForText('Dashboard');
    }
}
