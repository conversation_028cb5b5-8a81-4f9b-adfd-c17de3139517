<?php namespace MyfootTests\Account;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/15/14
 * Time: 1:45 PM
 */

class HelpWidgetTest extends AbstractAccount
{
    public static function setUpBeforeClass()
    {
        if (! \Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_ZENDESK_FEEDBACK)) {
            self::markTestSkipped('feature flag '.\Genesis_Entity_Feature::MYFOOT_RESIDUAL_ZENDESK_FEEDBACK.' is disabled');
        }

    }

    public function testResidualHelpWidget()
    {
        $obj = $this->_createAccountShell(\Genesis_Entity_Account::BID_TYPE_RESIDUAL);

        $this->_doLogin($obj->user->getEmail(), $obj->password);

        $this->waitForSource('launcher');
        //sleep(5); //let js fire
        $this->frame(self::byId('launcher')); //switch to the iFrame
        self::byCssSelector('div.Button.Button--launcher.Button--cta'); //find the button
        //self::byId('launcher'); //look for the widget that is green and says help that belongs to zendesk
    }
}