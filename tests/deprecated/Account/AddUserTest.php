<?php
namespace MyfootTests\Account;

class AddUserTest extends AbstractAccount
{
    public function testAddUser()
    {
        $object = $this->_createAccountShell();

        $user = $object->user;
        $password = $object->password;

        $this->_doQuickLogin($user->getEmail(), $password);

        self::url('/user');
        $this->waitForText('Users');
        self::byId('add_user_reveal');
        self::byId('add_user_reveal')->click();

        self::byId('email');
        self::byId('is_admin');
        self::byId('fname');
        self::byId('lname');
        self::byId('password');
        self::byId('password_confirm');
        self::byId('create_new');

        self::sendKeys(self::byId('email'), 'testAddUser' . uniqid() . '@sparefoot.com');
        self::byId('is_admin')->click();
        self::sendKeys(self::byId('fname'), 'Test');
        self::sendKeys(self::byId('lname'), 'Adduser');
        self::sendKeys(self::byId('password'), 't00ferapsyolo');
        self::sendKeys(self::byId('password_confirm'), 't00ferapsyolo');
        $successText = 'New User Created!';
        try {
            self::byId('create_new')->click();
        } catch (\PHPUnit_Extensions_Selenium2TestCase_WebDriverException $e) {
            if (stripos($e->getMessage(), $successText) === false) {
                throw new \Exception ('Pop-up message not found in exception: "' . $e->getMessage() . '"');
            }
        }
    }
}
