<?php
namespace MyfootTests;

use Genesis_Config_Server;

class TestSettings
{
    const CONNECTION_TIMEOUT_GRID   = 60; // seconds
    const CONNECTION_TIMEOUT_LOCAL  = 60; // seconds

    private static $settings;
    private static $envs;
    private static $selected_provider = 'browserstack';

    private static $ci_providers = [
        'localhost' => [
            'port'  => 4444,
            'browser' => 'firefox',
            'host' => 'my.sparefoot.local:8080',
            'user' => '',
            'key' => '',
            'desiredCapabilities' => []
        ],
        'sparefoot' => [
            'port'  => 4444,
            'browser' => 'firefox',
            'host' => 'selenium01.sparefoot.com',
            'user' => '',
            'key' => '',
            'desiredCapabilities' => []
        ],
        'browserstack' => [
            'host' => 'hub.browserstack.com',
            'port'  => 80,
            'browser' => 'firefox',
            'user' => 'rexmcconnell1',
            'key' => '********************',
            'desiredCapabilities' => [
                'browserstack.debug' => 'true',
                'browserstack.local' => 'true',
                'browserstack.localIdentifier' => 'MyFoot',
                'browserstack.user' => 'rexmcconnell1',
                'browserstack.key' => '********************',
                'project' => 'myfoot',
                'os' => 'Windows',
                'os_version' => '8.1'
            ]
        ]
    ];

    private static function getBaseUrl($environment) {
        if (getenv('BASE_URL')) {
            return getenv('BASE_URL');
        }

        switch($environment) {
            case 'prod':
            case 'production':
                return 'https://my.sparefoot.com';
            case 'stage':
            case 'staging':
                return 'https://my.sparefoot.extrameter.com';
            case 'dev':
            case 'development':
                return 'https://my.sparefoot.moreyard.com';
            case 'local':
            case 'localhost':
            default:
                return 'http://my.sparefoot.local:8080';
        }
    }
    /**
     * @return array
     */
    public static function get()
    {
        self::$envs = $_ENV;

        $settings = [];
        $settings['SF_BUILD_KEY'] = getenv('SF_BUILD_KEY') ? getenv('SF_BUILD_KEY') : '';

        if (\Genesis_Config_Server::isLocal()) {
            $hostname = shell_exec('hostname');
            $branch = shell_exec('git rev-parse --abbrev-ref HEAD');
            $build = "LOCAL {$hostname} {$branch}";
        } else {
            $build = $settings['SF_BUILD_KEY'].' '.$settings['build_number'];
        }

        $settings['provider'] = getenv('SELENIUM_PROVIDER') ? getenv('SELENIUM_PROVIDER') : self::$selected_provider;

        $settings['environment'] = getenv('SF_ENV') ? getenv('SF_ENV') : Genesis_Config_Server::getEnvironment();

        $settings['host'] = getenv('SELENIUM_HOST') ? getenv('SELENIUM_HOST') : self::$ci_providers[self::$selected_provider]['host'];

        $settings['port'] = (int) (getenv('SELENIUM_PORT') ? getenv('SELENIUM_PORT') : self::$ci_providers[self::$selected_provider]['port']);

        $settings['user'] = self::$ci_providers[self::$selected_provider]['user'];
        $settings['key'] =  self::$ci_providers[self::$selected_provider]['key'];

        $settings['browser'] = getenv('SELENIUM_BROWSER') ? getenv('SELENIUM_BROWSER') : self::$ci_providers[self::$selected_provider]['browser'];

        if (! in_array($settings['browser'], ['firefox', 'chrome', 'iexplore', 'safari'])) {
            throw new \InvalidArgumentException('invalid browser requested: '.$settings['browser']);
        }
        $settings['desiredCapabilities'] = self::$ci_providers[self::$selected_provider]['desiredCapabilities'];
        $settings['desiredCapabilities']['build'] = $build;

        $settings['timeout'] = Genesis_Config_Server::isLocal() ? self::CONNECTION_TIMEOUT_LOCAL : self::CONNECTION_TIMEOUT_GRID;

        $settings['grid_url'] = "http://" .($settings['user'] && $settings['key'] ? $settings['user'] . ':' .$settings['key'] . '@': '')
            . $settings['host'].':'.$settings['port']. "/wd/hub";

        $settings['base_url'] = self::getBaseUrl($settings['environment']);

        $settings['build_number'] = getenv('SF_BUILD_NUMBER') ? getenv('SF_BUILD_NUMBER') : '';

        $settings['database'] =  \Genesis_Config_Factory::getConfigs()['database']['master']['dsn'];

        $settings['debug'] = Genesis_Config_Server::getEnvironmentAsString();
        //die(print_r($settings));
        return $settings;
    }

    public function __toString()
    {
        $string = "\n--- Test Suite Settings ---\n";
        $string .= print_r(self::$settings, true);
        $string .= "\n--- Environment ---\n";
        $string .= print_r(self::$envs, true);
        return $string;
    }

    public function __construct()
    {
        self::$settings = self::get();
    }

    public static function isVagrant()
    {
        return stripos(shell_exec("cat /proc/version 2>&1"), "Linux version 3.13.0-55-generic (buildd@kapok)") !== false;
    }
}
if (stripos($_SERVER['SCRIPT_NAME'], 'tests/TestSettings.php') !== false) {
    require_once 'bootstrap.php';
    echo new TestSettings();
}
