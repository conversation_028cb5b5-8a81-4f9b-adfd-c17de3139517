<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/10/14
 * Time: 4:06 PM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;
use MyfootTests\MyAbstract;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;
class ResidualDisputeConfirmsTest extends AbstractStatement
{
    /**
     * @var $_statementResponse \GenesisTests\Service\Statement\FactoryResponse
     */
    private static $_statementResponse;
    /**
     * @group statements
     */
    public function testDisputes()
    {
        $this->featureflagStep('_' . __FUNCTION__);
    }

    protected function _testDisputes()
    {
        //setup the statement
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $request->setDisputedCount(1);
        $request->setConfirmedCount(1);
        $request->setPendingCount(1);
        $request->setTenantCount(1);

        self::$_statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            self::$_statementResponse->getUser()->getEmail(),
            self::$_statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement(self::$_statementResponse->getStatement()->getId());

        $this->_testDisputeAutoconfirmedReservation();
        $this->_testDisputePendingReservation();
        $this->_testDisputeDisputedReservation();
        $this->_testTenantDisputes();
    }

    private function _testDisputeAutoconfirmedReservation()
    {
        $confirmedTransactions = self::$_statementResponse->getTransactionsAutoConfirmed();
        /**
         * Scenario: Tests run through UI for disputing an autoconfirmed reservation and tenant:
         * Given a CPA statement with a reservation of autostate 'confirmed';
         * And a user clicks 'X' on a booking;
         * Assert the UI SpareFoot fee is $0;
         * Assert the UI "checkmark" is "X";
         * Assert the stored booking state is disputed;
         */

        /**
         * @var $confirmedTransaction Genesis_Entity_Transaction
         */
        $confirmedTransaction = $confirmedTransactions[0];
        $confirmationCode = $confirmedTransaction->getUniqueId();
        if (Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS)) {
            $this->waitForText('Auto-Matched');
        } else {
            $this->waitForText('Needs Your Review');
        }

        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-rent-collected')]")->click();

        //enter a reason in the popup
        $this->waitForText("Did the customer move in?");
        self::byId('customer-move-in-deny')->click(); //no

        //if we don't update the UI mostly
        self::waitUntil(function() use ($confirmationCode) {
            $amount = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]")->text();
            return ($amount == "$0") ? true : null;
        }, self::timeout());

        //check the sparefoot fee
        self::waitUntil(function() use ($confirmationCode) {
            $sparefootFee = self::byXPath("//td[@id='sparefootfee-$confirmationCode']")->text();
            return ("$0.00" == $sparefootFee) ? true : null;
        }, self::timeout());

        //check the stored booking state
        $transaction = \Genesis_Service_Transaction::loadById($confirmationCode);

        $this->assertEquals(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED, $transaction->getBookingState(), 'booking state should be disputed, but is not');
    }

    private function _testDisputePendingReservation()
    {
        $pendingTransactions = self::$_statementResponse->getTransactionsPending();
        /**
         * Scenario: Tests run through UI for disputing a pending reservation:
         * Given a CPA statement with a reservation of autostate 'null';
         * And a user clicks 'X' on a booking;
         * Assert the UI SpareFoot fee is $0;
         * Assert the UI "checkmark" is "X";
         * Assert the stored booking state is disputed;
         */

        /**
         * @var $pendingTransaction Genesis_Entity_Transaction
         */
        $pendingTransaction = $pendingTransactions[0];
        $confirmationCode = $pendingTransaction->getUniqueId();
        $this->waitForText('Needs Your Review');
        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-rent-collected')]")->click();

        //enter a reason in the popup
        $this->waitForText("Did the customer move in?");
        self::byId('customer-move-in-deny')->click(); //no

        //check the amount collected
        self::waitUntil(function() use ($confirmationCode) {
            $amountCollected = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]")->text();
            return ("$0" == $amountCollected) ? true : null;
        }, self::timeout());

        //check the sparefoot fee
        $sparefootFee = self::byXPath("//td[@id='sparefootfee-$confirmationCode']")->text();
        $this->assertEquals("$0.00", $sparefootFee, 'sparefoot fee did not match $0.00 after dispute');

        //check the stored booking state
        $transaction = \Genesis_Service_Transaction::loadById($confirmationCode);

        $this->assertEquals(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED, $transaction->getBookingState(), 'booking state should be disputed, but is not');
    }

    private function _testDisputeDisputedReservation()
    {
        $disputedTransactions = self::$_statementResponse->getTransactionsAutoDisputed();
        /**
         * Scenario: Tests run through UI for confirming an autodisputed reservation:
         * Given a CPA statement with a reservation of autostate 'disputed':
         * And a user clicks '$amount' on a booking;
         * Assert the UI SpareFoot fee to $<unit price x tiered bid amount>;
         * Assert the UI "X" is "checkmark";
         * Assert the stored booking state is pending;
         */

        /**
         * @var $disputedTransaction Genesis_Entity_Transaction
         */
        $disputedTransaction = $disputedTransactions[0];
        $confirmationCode = $disputedTransaction->getUniqueId();

        if (Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS)) {
            $this->waitForText('Unmatched');
        } else {
            $this->waitForText('Needs Your Review');
        }
        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-rent-collected')]")->click();


        //enter a reason in the popup
        $this->waitForText("Did the customer move in?");
        self::byId('customer-move-in-deny')->click(); //no

        //check the amount collected
        self::waitUntil(function() use ($confirmationCode) {
            $amountCollected = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]")->text();
            return ($amountCollected == "$0") ? true : null;
        }, self::timeout());

        //check the sparefoot fee
        $sparefootFee = self::byXPath("//td[@id='sparefootfee-$confirmationCode']");
        $this->assertEquals("$0.00", $sparefootFee->text(), 'sparefoot fee did not match $0.00 after dispute');

        //check the stored booking state
        $transaction = \Genesis_Service_Transaction::loadById($confirmationCode);

        $this->assertEquals(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED, $transaction->getBookingState(), 'booking state should be disputed, but is not');
    }

    protected function _testTenantDisputes()
    {
        $transactions = self::$_statementResponse->getTransactionsTenant();
        $transaction = $transactions[0];
        $confirmationCode = $transaction->getConfirmationCode();

        $this->waitForText('Tenants');

        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-tenant-rent-collected')]")->click();

        //enter a reason in the popup
        $this->waitForText("Did the Customer pay you rent in");
        self::byId('tenant-rent-collected-deny')->click(); //no

        $this->waitForText('This customer');
        self::byId('tenant-rent-moved-out')->click(); //will never move in

        self::byId('edit-tenant-rent-submit')->click();

        //check the amount collected
        self::waitUntil(function() use ($confirmationCode){
            $amountCollected = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]")->text();
            return ("$0" == $amountCollected) ? true : null;
        }, self::timeout());

        //check the sparefoot fee
        $sparefootFee = self::byXPath("//td[@id='sparefootfee-$confirmationCode']");
        $this->assertEquals("$0.00", $sparefootFee->text(), 'sparefoot fee did not match $0.00 after dispute');

        //check the stored booking state
        $transaction = \Genesis_Service_Transaction::loadById($confirmationCode);

        $this->assertEquals(Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED, $transaction->getBookingState(), 'booking state should be disputed, but is not');

        /**
         * lets check the BI here?
         */
    }
}