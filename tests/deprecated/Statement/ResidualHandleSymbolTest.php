<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/26/14
 * Time: 4:03 PM
 */

namespace MyfootTests\Statement;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;
use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;
class ResidualHandleSymbolTest extends AbstractStatement
{
    private function _clickEditAmountButton($confirmationCode)
    {

        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class, 'edit-rent-collected')]")->click();
        $this->waitForText('Did the customer move in?');
        self::byId('customer-move-in-confirm')->click();
        $this->waitForText('How much rent did you collect?');
    }

    private function _confirmAndGetNewValue($confirmationCode)
    {
        $oldValue = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]")->text();
        self::byId('edit-rent-submit')->click();

        self::waitUntil(function() use ($confirmationCode, $oldValue) {
            $newValue = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]")->text();
            return ($oldValue != $newValue) > 0 ? true : null;
        }, self::timeout());
        return self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]")->text();
    }
    /**
     * @group statements
     */
    public function testAcceptDollarInPriceChange()
    {
        $this->featureflagStep('_' . __FUNCTION__);
    }

    protected function _testAcceptDollarInPriceChange()
    {
        //setup the statement
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $request->setDisputedCount(0);
        $request->setConfirmedCount(0);
        $request->setPendingCount(1);
        $request->setFacilityCount(1);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());
        $pendingTransactions = $statementResponse->getTransactionsPending();
        $pendingTransaction = $pendingTransactions[0];
        /**
         * @var $pendingTransaction Genesis_Entity_Transaction
         */
        $confirmationCode = $pendingTransaction->getConfirmationCode();

        /**
         * make sure we can change the price, and that it persists after a hard refresh too
         * and that it will ignore if we are stupid and put in a $ symbol
         */
        $this->_clickEditAmountButton($confirmationCode);
        self::byId('rent-other')->value('$43.221');

        $text = $this->_confirmAndGetNewValue($confirmationCode);

        $this->assertEquals('$43.22', $text, 'value did not stick after change');
        $this->refresh();
        $this->waitForSource('statement-title');

        $text = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]")->text();

        $this->assertEquals('$43.22', $text, 'new value did not persist after refresh');

        /**
         * make sure we can change the price to zero
         * FAC-784
         */
        $this->_clickEditAmountButton($confirmationCode);
        self::byId('rent-other')->value('0');

        $text = $this->_confirmAndGetNewValue($confirmationCode);

        $this->assertEquals('$0.00', $text, 'did not accept 0 as a value');
sleep(5);
        /**
         * now make the user feel bad
         * if they put in a symbol other than $, reject their input
         */
        $this->_clickEditAmountButton($confirmationCode);
        self::byId('rent-other')->value('%43.221');

        self::byId('edit-rent-submit')->click();

        self::waitUntil(function() {
            return strlen($this->alertText()) > 0 ? true : null;
        }, self::timeout());

        $this->assertTrue(stripos($this->alertText(), 'Error: Enter a valid amount for the rent collected.') !== false);
        $this->acceptAlert();
    }
}
