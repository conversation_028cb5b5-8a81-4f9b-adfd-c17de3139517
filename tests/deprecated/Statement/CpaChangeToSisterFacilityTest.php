<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/10/14
 * Time: 4:06 PM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;
use MyfootTests\MyAbstract;

class CpaChangeToSisterFacilityTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testChangeMoveInDatesLate()
    {
        //setup the statement
//        $request = new FactoryRequest();
//        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
//        $request->setDisputedCount(0);
//        $request->setConfirmedCount(0);
//        $request->setPendingCount(1);
//        $request->setFacilityCount(2);
//
//        $statementResponse = Factory::buildStatement($request);
//
//        $this->_doLogin(
//            $statementResponse->getUser()->getEmail(),
//            $statementResponse->getUserPassword()
//        );
//
//        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());
//
//        $pendingTransactions = $statementResponse->getTransactionsPending();
//        /**
//         * first scenario: change the customer name of a pending transaction
//         * it should reappear in 'pending' on the statement with a new name
//         */
//        /**
//         * @var $pendingTransaction Genesis_Entity_Transaction
//         */
//        $pendingTransaction = $pendingTransactions[0];
//        $confirmationCode = $pendingTransaction->getUniqueId();
//        $xMark = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]");
//        $xMark->click();
//        $this->waitForSource("Why didn't the customer move in this month?");
//
//        //click for customer name change
//        self::byId('dispute-change-facility')->click();
//        $this->waitForSource("Which facility?");
//
//
//        /**
//         * @var $facility \Genesis_Entity_Facility
//         */
//        foreach ($statementResponse->getFacilities() as $facility) {
//            if ($facility->getId() != $pendingTransaction->getFacilityId()) {
//                break; //found a sister facility
//            }
//        }
//        //pick the sister facility
//        self::select(self::byId('new-facility-id'))->selectOptionByValue($facility->getId());
//
//        //click the save button
//        self::byId('dispute-submit')->click();
//
//        //let the save button finish
//        $this->waitForSource('Needs Your Review');
//        $this->refresh();
//
//        //make sure its still listed in pending
//        self::byXPath("//*[@id='pending']/*[@id='$confirmationCode']");
//
//        //make sure the facility name updated
//        $text = self::byId('facility-name-' . $confirmationCode)->text();
//        $this->assertNotFalse(stripos($text, $facility->getTitle()), "text: " . $text. PHP_EOL . "Looking for " . $facility->getTitle() );
//        $this->assertNotFalse(stripos($text, $facility->getCompanyCode()));
    }
}