<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 1/29/15
 * Time: 12:53 PM
 */

namespace MyfootTests\Statement;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;
use Genesis_Entity_Transaction;
use GenesisTests\Service\Statement\Factory;
use GenesisTests\Service\Statement\FactoryRequest;
use Genesis_Entity_Account;
class ResidualChangePriceTest extends AbstractStatement
{
    /**
     * @var $_statementResponse \GenesisTests\Service\Statement\FactoryResponse
     */
    private static $_statementResponse;

    /**
     * @group statements
     */
    public function testChangePrices()
    {
        //setup the statement
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $request->setDisputedCount(0);
        $request->setConfirmedCount(0);
        $request->setPendingCount(1);
        $request->setTenantCount(1);
        $request->setFacilityCount(1);

        self::$_statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            self::$_statementResponse->getUser()->getEmail(),
            self::$_statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement(self::$_statementResponse->getStatement()->getId());

        self::_testChangePendingAmount();

        self::_testChangeTenantAmount();

        self::_testUndoTenantMoveOut();
    }

    private function _testChangePendingAmount()
    {
        $transactions = self::$_statementResponse->getTransactionsPending();
        $transaction = $transactions[0];
        $confirmationCode = $transaction->getConfirmationCode();

        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-rent-collected')]")->click();

        //modal pops
        self::waitUntil(function() {
            return self::byId("edit-rent-collected-modal")->displayed() ? true : null;
        }, self::timeout());

        //enter a reason in the popup
        $this->waitForText("Did the customer move in?");
        self::byId('customer-move-in-confirm')->click(); //yes

        $this->waitForText('How much rent did you collect?');
        $value = round(rand(100,9999)/100, 2, PHP_ROUND_HALF_DOWN);
        self::byId('rent-other')->value($value);
        self::byId('edit-rent-submit')->click();
        //modal closes
        self::waitUntil(function() {
            return self::byId('edit-rent-collected-modal')->displayed() ? null : true;
        }, self::timeout());

        //check the amount collected
        $amountCollected = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]");
        $this->assertEquals("$" . number_format($value,2,'.',''), $amountCollected->text(), 'amount collected did not match the new amount after dispute');

        //check the sparefoot fee
        $sparefootFee = self::byXPath("//td[@id='sparefootfee-$confirmationCode']");
        $expectedAmount = round($value * self::$_statementResponse->getAccount()->getResidualPercent());
        $sparefootFee = round(str_replace("$", "", $sparefootFee->text()));

        $this->assertEquals($expectedAmount, $sparefootFee, 'sparefoot fee did not match after dispute ' . $value . ' ' . self::$_statementResponse->getAccount()->getResidualPercent());

        $this->refresh(); //make sure it persisted
        $this->waitForSource('statement-title');

        //check the amount collected
        $amountCollected = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]");
        $this->assertEquals("$" . number_format($value,2,'.',''), $amountCollected->text(), 'amount collected did not match the new amount after dispute');

        //check the sparefoot fee
        $sparefootFee = self::byXPath("//td[@id='sparefootfee-$confirmationCode']");
        $expectedAmount = round($value * self::$_statementResponse->getAccount()->getResidualPercent());
        $sparefootFee = round(str_replace("$", "", $sparefootFee->text()));
        $this->assertEquals($expectedAmount, $sparefootFee, 'sparefoot fee did not match after dispute');
    }

    private function _testChangeTenantAmount()
    {
        $transactions = self::$_statementResponse->getTransactionsTenant();
        $transaction = $transactions[0];
        $confirmationCode = $transaction->getConfirmationCode();

        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-tenant-rent-collected')]")->click();
        //modal pops in
        self::waitUntil(function() {
            return self::byId('edit-tenant-rent-collected-modal')->displayed() ? true : null;
        }, self::timeout());

        //enter a reason in the popup
        $this->waitForText("Did the Customer pay you rent in");
        self::byId('tenant-rent-collected-confirm')->click(); //yes

        $this->waitForText('Please enter how much rent the customer paid you during');
        $value = round(rand(100,9999)/100, 2, PHP_ROUND_HALF_DOWN);
        self::byId('rent-tenant-other')->value($value);
        self::byId('edit-tenant-rent-submit')->click();
        //modal leaves
        self::waitUntil(function() {
            return self::byId('edit-tenant-rent-collected-modal')->displayed() ? null : true;
        }, self::timeout());

        //check the amount collected
        $amountCollected = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]");
        $this->assertEquals("$" . number_format($value,2,'.',''), $amountCollected->text(), 'amount collected did not match the new amount after dispute');

        //check the sparefoot fee
        $sparefootFee = self::byXPath("//td[@id='sparefootfee-$confirmationCode']");
        $expectedAmount = round($value * self::$_statementResponse->getAccount()->getResidualPercent());
        $this->assertEquals($expectedAmount, round(str_replace("$", '', $sparefootFee->text())), 'sparefoot fee did not match after dispute ' . $value . ' ' . self::$_statementResponse->getAccount()->getResidualPercent());

        $this->refresh(); //make sure it persisted
        $this->waitForSource('statement-title');

        //check the amount collected
        $amountCollected = self::byXPath("//td[@id='action-$confirmationCode']//span[contains (@class, 'unit-price')]");
        $this->assertEquals("$" . number_format($value,2,'.',''), $amountCollected->text(), 'amount collected did not match the new amount after dispute');

        //check the sparefoot fee
        $sparefootFee = self::byXPath("//td[@id='sparefootfee-$confirmationCode']");
        $expectedAmount = round($value * self::$_statementResponse->getAccount()->getResidualPercent());
        $this->assertEquals($expectedAmount, round(str_replace('$', '', $sparefootFee->text())), 'sparefoot fee did not match after dispute');

    }

    private function _testUndoTenantMoveOut()
    {
        $transactions = self::$_statementResponse->getTransactionsTenant();
        $transaction = $transactions[0];
        $confirmationCode = $transaction->getConfirmationCode();
        $statementId = self::$_statementResponse->getStatement()->getId();

        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-tenant-rent-collected')]")->click();

        //modal pop up
        self::waitUntil(function() {
            return self::byId('edit-tenant-rent-collected-modal')->displayed() ? true : null;
        }, self::timeout());

        //enter a reason in the popup
        $this->waitForText("Did the Customer pay you rent in");
        self::byId('tenant-rent-collected-deny')->click(); //no
        $this->waitForText('This customer');
        self::byId('tenant-rent-moved-out')->click();
        self::byId('edit-tenant-rent-submit')->click();

        //modal pop down
        self::waitUntil(function() {
            return self::byId('edit-tenant-rent-collected-modal')->displayed() ? null : true;
        }, self::timeout());

        sleep(5); //weird bootstrap error. need this or the box won't be ready again to click opens
        //Check Billable Instance to see if reason is 'moved-out'
        $billableInstance = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($confirmationCode,$statementId);
        $reason = $billableInstance->getReason();
        $this->assertEquals($reason,\Genesis_Entity_BillableInstance::REASON_MOVED_OUT);

        //Undo the move-out
        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-tenant-rent-collected')]")->click();

        //enter a reason in the popup
        $this->waitForText("Did the Customer pay you rent in");
        self::byId('tenant-rent-collected-confirm')->click(); //yes

        $this->waitForText('Please enter how much rent the customer paid you during');
        $value = round(rand(100,9999)/100, 2, PHP_ROUND_HALF_DOWN);
        self::byId('rent-tenant-other')->value($value);
        self::byId('edit-tenant-rent-submit')->click();

        //modal pop down
        self::waitUntil(function() {
            return self::byId('edit-tenant-rent-collected-modal')->displayed() ? null : true;
        }, self::timeout());

        //Check Billable Instance to see if reason is 'moved-out'
        $billableInstance = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($confirmationCode,$statementId);
        $reason = $billableInstance->getReason();
        $this->assertEquals($reason, NULL);
    }
}