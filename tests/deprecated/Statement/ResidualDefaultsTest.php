<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 11/24/14
 * Time: 12:50 PM
 */

namespace MyfootTests\Statement;

use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;
class ResidualDefaultsTest extends AbstractStatement
{
    /**
     * @var $_statementResponse \GenesisTests\Service\Statement\FactoryResponse
     */
    private static $_statementResponse;

    /**
     * @group statements
     */
    public function testResidualStatement()
    {
         //test all combos
        $this->featureflagStep('_' . __FUNCTION__);
    }

    protected function _testResidualStatement()
    {
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $request->setDisputedCount(1);
        $request->setConfirmedCount(1);
        $request->setPendingCount(1);
        $request->setLateCount(1);
        $request->setTenantCount(1);

        self::$_statementResponse = Factory::buildStatement($request);

        //login
        $this->_doQuickLogin(
            self::$_statementResponse->getUser()->getEmail(),
            self::$_statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement(self::$_statementResponse->getStatement()->getId());

        //now do real stuff
        self::_testPendingDefaults();
        self::_testConfirmedDefaults();
        self::_testDisputedDefaults();
        self::_testLateDefaults();
        self::_testTenantDefaults();
    }

    protected function _testPendingDefaults()
    {
        /**
         * @var $pendingTransaction Genesis_Entity_Transaction
         */
        $pendingTransactions = self::$_statementResponse->getTransactionsPending();
        $pendingTransaction = $pendingTransactions[0];

        $confirmationCode = $pendingTransaction->getUniqueId();
        self::byId($confirmationCode); //its listed

        //listed in the statement section where it should be
        self::byXPath("//*[@id='pending']/*[@id='$confirmationCode']");

        //assert showing full price for unit price
        if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_STATEMENT_WIZARD)) {
            $paid = self::byXPath("//*[@id='action-$confirmationCode']//*[contains (@class, 'unit-price')]")->text();
        } else {
            $paid = self::byXPath("//*[@id='action-$confirmationCode']//*[contains (@class, 'btn-default') and contains(@class, 'active')]")->text();
        }
        $this->assertEquals('$'.$pendingTransaction->getPrice(), $paid, "the pending transaction's unit price is incorrect (This customer paid:)");
        //assert sparefoot fee is correct
        $amount = number_format(self::$_statementResponse->getAccount()->getResidualPercent() * $pendingTransaction->getPrice(),2);
        $this->assertEquals('$' . $amount, self::byId('sparefootfee-' . $confirmationCode)->text(), "the pending transaction's sparefoot fee is incorrect");
    }

    protected function _testConfirmedDefaults()
    {
        $confirmedTransactions = self::$_statementResponse->getTransactionsAutoConfirmed();
        $confirmedTransaction = $confirmedTransactions[0];

        $confirmationCode = $confirmedTransaction->getUniqueId();
        self::byId($confirmationCode); //its listed

        //listed in the statement section where it should be
        if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS)) {
            self::byXPath("//*[@id='confirmed']/*[@id='$confirmationCode']");
        } else {
            self::byXPath("//*[@id='pending']/*[@id='$confirmationCode']");
        }
        //assert showing full price for unit price
        if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_STATEMENT_WIZARD)) {
            $paid = self::byXPath("//*[@id='action-$confirmationCode']//*[contains (@class, 'unit-price')]")->text();
        } else {
            $paid = self::byXPath("//*[@id='action-$confirmationCode']//*[contains (@class, 'btn-default') and contains(@class, 'active')]")->text();
        }

        $this->assertEquals('$'.$confirmedTransaction->getPrice(), $paid, "the confirmed transaction's unit price is incorrect (This customer paid:)");
        //assert sparefoot fee is correct
        $amount = number_format(self::$_statementResponse->getAccount()->getResidualPercent() * $confirmedTransaction->getPrice(),2);
        $this->assertEquals('$' . $amount, self::byId('sparefootfee-' . $confirmationCode)->text(), "the confirmed transaction's sparefoot fee is incorrect");
    }

    protected function _testDisputedDefaults()
    {
        $disputedTransactions = self::$_statementResponse->getTransactionsAutoDisputed();
        $disputedTransaction = $disputedTransactions[0];
        $confirmationCode = $disputedTransaction->getUniqueId();
        self::byId($confirmationCode); //its listed

        //listed in the statement section where it should be
        if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS)) {
            self::byXPath("//*[@id='disputed']/*[@id='$confirmationCode']"); //listed where it should be
        } else {
            self::byXPath("//*[@id='pending']/*[@id='$confirmationCode']");
        }
        //assert showing zero for unit price
        if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_STATEMENT_WIZARD)) {
            $paid = self::byXPath("//*[@id='action-$confirmationCode']//*[contains (@class, 'unit-price')]")->text();
        } else {
            $paid = self::byXPath("//*[@id='action-$confirmationCode']//*[contains (@class, 'btn-default') and contains(@class, 'active')]")->text();
        }
        if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS)) {
            $this->assertEquals('$0', $paid, "the disputed transaction's unit price is incorrect (This customer paid:)");

            //assert sparefoot fee is zero
            $this->assertEquals('$0.00', self::byId('sparefootfee-' . $confirmationCode)->text(), "the disputed transaction's sparefoot fee is incorrect");
        } else {
            $this->assertEquals('$' . $disputedTransaction->getPrice(), $paid, "the disputed transaction's unit price is incorrect (This customer paid:)");
            $amount = number_format(self::$_statementResponse->getAccount()->getResidualPercent() * $disputedTransaction->getPrice(),2);
            //assert sparefoot fee is rate * amount
            $this->assertEquals('$' . $amount, self::byId('sparefootfee-' . $confirmationCode)->text(), "the disputed transaction's sparefoot fee is incorrect");
        }
    }

    protected function _testLateDefaults()
    {
        $lateTransactions = self::$_statementResponse->getTransactionsLate();
        $lateTransaction = $lateTransactions[0];

        $confirmationCode = $lateTransaction->getUniqueId();
        self::byId($confirmationCode); //its listed

        //listed in the statement section where it should be
        self::byXPath("//*[@id='late']/*[@id='$confirmationCode']");

        $paid = self::byXPath("//*[@id='action-$confirmationCode']//*[contains (@class, 'unit-price')]")->text();

        $this->assertEquals('$0', $paid, "the pending transaction's unit price is incorrect (This customer paid:)");

        //assert sparefoot fee is zero
        $this->assertEquals('$0.00', self::byId('sparefootfee-' . $confirmationCode)->text(), "the late transaction's sparefoot fee is incorrect");
    }

    protected function _testTenantDefaults()
    {
        $transactions = self::$_statementResponse->getTransactionsTenant();
        $transaction = $transactions[0];

        $confirmationCode = $transaction->getConfirmationCode();
        //confirm it exists in the UI
        self::byId('action-' . $confirmationCode);
        //*[@id='action-$confirmationCode']//*[contains (@class, 'unit-price')]
        //confirm it is in the tenants section
        self::byXPath("//*[@id='tenants']//tr[@id='$confirmationCode']");
        //confirm the price
        $paid = self::byXPath("//*[@id='action-$confirmationCode']//*[contains (@class, 'unit-price')]")->text();
        $this->assertEquals('$' . $transaction->getPrice(), $paid, 'the price did not match in the UI');
        //confirm correct facility
        $facilityName = self::byId('facility-name-' . $confirmationCode)->text();
        $this->assertContains($transaction->getFacility()->getTitle(), $facilityName, 'the facility name did not match in the UI');
        //confirm the tenant name
        $customerInfo = self::byId('customer-info-' . $confirmationCode)->text();
        $this->assertContains($transaction->getFirstName(), $customerInfo);
        $this->assertContains($transaction->getLastName(), $customerInfo);
        $this->assertContains(\Genesis_Util_Formatter::phoneToString($transaction->getPhone()), $customerInfo);
        //assert the sparefoot fee
        $sparefootFee = self::byId('sparefootfee-'.$confirmationCode)->text();
        $expectedAmount = number_format(self::$_statementResponse->getAccount()->getResidualPercent() * $transaction->getPrice(),2);
        $this->assertEquals('$' . $expectedAmount, $sparefootFee, 'the sparefoot fee did not match in the UI');
        //assert the LTV
        $this->assertEquals('$' . number_format($transaction->getPrice() * 2, 2, '.', ''), self::byId('lifetime-' . $confirmationCode)->text(), 'lifetime value did not match in the UI');
    }
}