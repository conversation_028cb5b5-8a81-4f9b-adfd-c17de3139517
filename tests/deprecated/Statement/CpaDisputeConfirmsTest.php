<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/10/14
 * Time: 4:06 PM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;
use MyfootTests\MyAbstract;

class CpaDisputeConfirmsTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testDisputeAutoconfirmedReservation()
    {
        //setup the statement
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
        $request->setDisputedCount(0);
        $request->setConfirmedCount(1);
        $request->setPendingCount(0);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());

        $confirmedTransactions = $statementResponse->getTransactionsAutoConfirmed();
        /**
         * Scenario: Tests run through UI for disputing an autoconfirmed reservation and tenant:
         * Given a CPA statement with a reservation of autostate 'confirmed';
         * And a user clicks 'X' on a booking;
         * Assert the UI SpareFoot fee is $0;
         * Assert the UI "checkmark" is "X";
         * Assert the stored booking state is disputed;
         */

        /**
         * @var $confirmedTransaction Genesis_Entity_Transaction
         */
        $confirmedTransaction = $confirmedTransactions[0];
        $confirmationCode = $confirmedTransaction->getUniqueId();

        //wait for decorators
        self::waitUntil(function() use ($confirmationCode) {
            $class = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'confirm-autoconfirmed-button')]")->attribute('class');
            return stripos($class, 'active') ? true : null;
        }, self::timeout());

        self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-autoconfirmed-button')]")->click();

        //enter a reason in the popup
        $this->waitForText("What's wrong with this auto-matched move-in?");

        //enter a reason in the text area
        $this->byCssSelector("#auto-dispute-modal [name='reason']")->value('testing dispute functionality in auto-matched transactions.');

        //click the submit button
        $this->byCssSelector('#auto-dispute-modal .ok')->click();

        //status is set
        $this->waitForText('Under Review By SpareFoot');

        //assert sparefoot fee
        self::waitUntil(function() use ($confirmationCode) {
            $tableDown = self::byXPath("//td[@id='amount-$confirmationCode']");
            return ($tableDown->text() == "$0.00") ? true : null;
        }, self::timeout());

        //assert the x is checked
        self::waitUntil(function() use ($confirmationCode) {
            $classes = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-autoconfirmed-button')]")->attribute('class');
            return stripos($classes, 'active') ? true : null;
        }, self::timeout());

        //check the stored booking state
        $transaction = \Genesis_Service_Transaction::loadById($confirmationCode);
        $this->assertEquals(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED, $transaction->getBookingState(), 'booking state should be disputed, but is not');
    }

    /**
     * @group statements
     */
    public function testDisputePendingReservation()
    {
        //setup the statement
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
        $request->setDisputedCount(0);
        $request->setConfirmedCount(0);
        $request->setPendingCount(1);


        $statementResponse = Factory::buildStatement($request);

//       $this->fail($statementResponse->getUser()->getEmail() . PHP_EOL . $statementResponse->getUserPassword(). PHP_EOL.
//        '/statement/view/' . $statementResponse->getStatement()->getId());
        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());

        $pendingTransactions = $statementResponse->getTransactionsPending();
        /**
         * Scenario: Tests run through UI for disputing a pending reservation:
         * Given a CPA statement with a reservation of autostate 'null';
         * And a user clicks 'X' on a booking;
         * Assert the UI SpareFoot fee is $0;
         * Assert the UI "checkmark" is "X";
         * Assert the stored booking state is disputed;
         */

        /**
         * @var $pendingTransaction Genesis_Entity_Transaction
         */
        $pendingTransaction = $pendingTransactions[0];
        $confirmationCode = $pendingTransaction->getUniqueId();

        //wait for buttons to load
        self::waitUntil(function() use ($confirmationCode) {
            $class = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'confirm-button')]")->attribute('class');
            return stripos($class, 'active') ? true : null;
        }, self::timeout());

        self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]")->click();

        $this->waitForText("Did Not Move In");

        //assert the x is checked
        self::waitUntil(function()  use ($confirmationCode) {
            $classes = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]")->attribute('class');
            return stripos($classes, 'active') ? true : null ;
        }, self::timeout());

        //assert sparefoot fee gets updated
        self::waitUntil(function() use ($confirmationCode) {
            return (self::byXPath("//td[@id='amount-$confirmationCode']")->text() == "$0.00") ? true : null;
        }, self::timeout());

        //check the stored booking state
        $transaction = \Genesis_Service_Transaction::loadById($confirmationCode);
        $this->assertEquals(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED, $transaction->getBookingState(), 'booking state should be disputed, but is not');

        //refresh page and do same assertions to make sure it persisted
        $this->refresh();

        $this->waitForSource('statement-title');

        //assert sparefoot fee
        $this->assertEquals("$0.00", self::byXPath("//td[@id='amount-$confirmationCode']")->text(), 'sparefoot fee did not match ui $0.00 for disputed (refresh)');

        //assert the x is checked
        self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button') and contains(@class, 'active')]");
    }

    /**
     * @group statements
     */
    public function testConfirmDisputedReservation()
    {
        //setup the statement
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
        $request->setDisputedCount(1);
        $request->setConfirmedCount(0);
        $request->setPendingCount(0);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());

        $disputedTransactions = $statementResponse->getTransactionsAutoDisputed();
        /**
         * Scenario: Tests run through UI for confirming an autodisputed reservation:
         * Given a CPA statement with a reservation of autostate 'disputed':
         * And a user clicks '$amount' on a booking;
         * Assert the UI SpareFoot fee to $<unit price x tiered bid amount>;
         * Assert the UI "X" is "checkmark";
         * Assert the stored booking state is pending;
         */

        /**
         * @var $disputedTransaction Genesis_Entity_Transaction
         */
        $disputedTransaction = $disputedTransactions[0];
        $confirmationCode = $disputedTransaction->getUniqueId();

        //wait for buttons to load
        self::waitUntil(function() use ($confirmationCode) {
            $class = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]")->attribute('class');
            return stripos($class, 'active') ? true : null;
        }, self::timeout());

        self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'confirm-button')]")->click();

        $this->waitForText('Moved In');

        //assert sparefoot fee
        self::waitUntil(function() use ($confirmationCode, $disputedTransaction) {
            $text = self::byXPath("//td[@id='amount-$confirmationCode']")->text();
            return ($text == "$" . $disputedTransaction->getBidAmount()) ? true : null;
        }, self::timeout());

        //assert the checke is selected
        self::waitUntil(function() use ($confirmationCode) {
            $classes = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'confirm-button')]")->attribute('class');
            return stripos($classes, 'active') ? true : null;
        }, self::timeout());

        //check the stored booking state
        $transaction = \Genesis_Service_Transaction::loadById($confirmationCode);
        $this->assertEquals(Genesis_Entity_Transaction::BOOKING_STATE_PENDING, $transaction->getBookingState(), 'booking state should be disputed, but is not');
    }
}
