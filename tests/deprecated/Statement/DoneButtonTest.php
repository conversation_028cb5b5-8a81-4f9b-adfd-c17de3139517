<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 6/16/15
 * Time: 9:12 AM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;

class DoneButtonTest extends AbstractStatement
{
    public function _testDoneButtonResidual()
    {
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $request->setDisputedCount(1);
        $request->setConfirmedCount(0);
        $request->setPendingCount(0);
        $request->setTenantCount(0);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());
        self::byId('confirm-statement-button')->click();
        $this->waitForText('Statement confirmed by');
        $currentDate = self::byId('confirmation-time')->text();
        self::byId('resubmit-statement-button')->click();
        self::waitUntil(function() use ($currentDate) {
            return self::byId('confirmation-time')->text() != $currentDate ? true : null;
        }, self::timeout());
        //now check the admin list
        self::refresh();
        $this->waitForText('Statement Confirmations');
        self::byId('confirmations-table');
    }

    public function _testDoneButtonCpa() {
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
        $request->setDisputedCount(1);
        $request->setConfirmedCount(0);
        $request->setPendingCount(0);
        $request->setLateCount(0);
        $request->setFacilityCount(1);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());
        self::byId('confirm-statement-button')->click();
        $this->waitForText('Statement confirmed by');
        $currentDate = self::byId('confirmation-time')->text();
        self::byId('resubmit-statement-button')->click();
        self::waitUntil(function() use ($currentDate) {
            return self::byId('confirmation-time')->text() != $currentDate ? true : null;
        }, self::timeout());
        //now check the admin list
        self::refresh();
        $this->waitForText('Statement Confirmations');
        self::byId('confirmations-table');
    }
}
