<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 2/10/15
 * Time: 11:14 AM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\Factory;
use GenesisTests\Service\Statement\FactoryRequest;
use Genesis_Entity_Account;
class ResidualMultiFacFilteredEditButtonTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testEditButton()
    {

        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $request->setDisputedCount(3);
        $request->setConfirmedCount(3);
        $request->setPendingCount(3);
        $request->setLateCount(1);
        //$request->setTenantCount(2);
        $request->setFacilityCount(2);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());

        /**
         * we need to load the facility with the late move in
         */
        $lateTransactions = $statementResponse->getTransactionsLate();
        /**
         * @var $lateTransaction = Genesis_Entity_Transaction
         */
        $lateTransaction = current($lateTransactions);

        /**
         * @var $facility \Genesis_Entity_Facility
         */
        foreach ($statementResponse->getFacilities() as $facility) {
            if ($facility->getId() == $lateTransaction->getFacility()->getId()) {
                break;
            }
        }
        
        $select = self::byId('statement-select-facility');
        //wait for banner if banner
//        self::waitUntil(function() {
//            try {
//                self::byId('new-residual-welcome')->displayed();
//                return true;
//            } catch (\Exception $e) {
//                return null;
//            }
//        }, self::timeout());
        self::select($select)->selectOptionByLabel($facility->getTitleWithCompanyCode());

        $this->waitForText('Late Move-Ins');

        //click on the trouble box
        $confirmationCode = $lateTransaction->getConfirmationCode();
        self::byXPath("//td[@id='action-$confirmationCode']//a[contains (@class,'edit-rent-collected')]")->click();
        $this->waitForText('Did the customer move in?');
    }
}