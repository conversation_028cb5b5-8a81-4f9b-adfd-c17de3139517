<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/10/14
 * Time: 3:57 PM
 */

namespace MyfootTests\Statement;
use MyfootTests\MyAbstract;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;
class AbstractStatement extends MyAbstract
{

    public static function setUpBeforeClass()
    {
        Genesis_Service_Feature::setValue('disable_email_for_ci', Genesis_Service_Feature::ACTIVE_KEY, Genesis_Service_Feature::ACTIVE_ALWAYS);
    }

    protected function _navigateToCurrentStatement($statementId = null)
    {
        if ($statementId) {
            self::url('/statement/view/' . $statementId);
        } else {
            self::byId('menu_statements')->click();
            $this->waitForText('OPEN STATEMENT');

            self::byId('open_statement')->click();
        }
        $this->waitForSource('statement-title'); //we made it
    }

    /**
     * @param $callbackFunction class method to call after setting feature flags
     * @throws \Exception
     */
    protected function featureflagStep($callbackFunction)
    {
        $keys = [Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS];
        //test all combos
        try {
            Genesis_Service_Feature::setValue(Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS, Genesis_Service_Feature::ACTIVE_KEY, 1);
            $this->$callbackFunction();

        } catch (\Exception $e) {
            $error = '';
            foreach ($keys  as $key) {
                $feature =  $feature = Genesis_Service_Feature::loadByNameAndKey($key, Genesis_Service_Feature::ACTIVE_KEY);
                $error .= PHP_EOL . "With " . $key . ' set to ' . (int) $feature->getValue();
            }

            $this->fail($e->getMessage() . PHP_EOL .  "Line:".$e->getLine() . PHP_EOL . "File:".$e->getFile(). PHP_EOL . $error . PHP_EOL . PHP_EOL . $e->getTraceAsString());
        }
    }
}