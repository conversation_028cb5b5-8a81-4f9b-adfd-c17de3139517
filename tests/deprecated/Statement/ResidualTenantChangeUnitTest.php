<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 2/6/15
 * Time: 11:24 AM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\Factory;
use GenesisTests\Service\Statement\FactoryRequest;
use Genesis_Entity_Account;
class ResidualTenantChangeUnitTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testChangeUnit()
    {
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $request->setDisputedCount(0);
        $request->setConfirmedCount(0);
        $request->setPendingCount(1);
        $request->setTenantCount(1);
        $request->setFacilityCount(2);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());

        /**
         * pending change unit number
         */
        $pendingTransactions = $statementResponse->getTransactionsPending();
        /**
         * @var $pendingTransaction \Genesis_Entity_Transaction
         */
        $pendingTransaction = $pendingTransactions[0];
        $confirmationCode = $pendingTransaction->getConfirmationCode();

        self::byXPath("//*[@id='facility-name-$confirmationCode']//*[contains (@class, 'edit-facility')]")->click();
        $this->waitForText('Edit Facility');
        $unitNumber = rand(1,231);
        self::byId('unit-number')->value($unitNumber);
        self::byId('sister-facility-select-submit')->click();

        self::waitUntil(function() use ($confirmationCode, $unitNumber) {
            $domUnitNumber = self::byXPath("//*[@id='facility-name-$confirmationCode']//*[contains (@class, 'unit-number')]")->text();
            return ('Unit #: ' . $unitNumber == $domUnitNumber) ? true : null;
        }, self::timeout());

        //refresh page, make sure its stored in db
        $this->refresh();
        $this->waitForSource('statement-title'); //we made it
        $domUnitNumber = self::byXPath("//*[@id='facility-name-$confirmationCode']//*[contains (@class, 'unit-number')]")->text();
        $this->assertContains('Unit #: ' . $unitNumber, $domUnitNumber, 'unit number was not set after ajax');

        /**
         * the tenant change unit number
         */
        $tenantTransactions = $statementResponse->getTransactionsTenant();
        /**
         * @var $tenantTransaction \Genesis_Entity_Transaction
         */
        $tenantTransaction = $tenantTransactions[0];
        $confirmationCode = $tenantTransaction->getConfirmationCode();

        self::byXPath("//*[@id='facility-name-$confirmationCode']//*[contains (@class, 'edit-unit-number')]")->click();
        $this->waitForText('Edit Unit Number');
        $unitNumber = rand(1,231);
        self::byId('edit-unit-number-value')->value($unitNumber);
        self::byId('edit-unit-number-submit')->click();

        self::waitUntil(function() use ($confirmationCode, $unitNumber) {
            $domUnitNumber = self::byXPath("//*[@id='facility-name-$confirmationCode']//*[contains (@class, 'unit-number')]")->text();
            return ('Unit #: ' . $unitNumber == $domUnitNumber) ? true : null;
        }, self::timeout());

        //refresh page, make sure its stored in db
        $this->refresh();
        $this->waitForSource('statement-title'); //we made it

        $domUnitNumber = self::byXPath("//*[@id='facility-name-$confirmationCode']//*[contains (@class, 'unit-number')]")->text();
        $this->assertContains('Unit #: ' . $unitNumber, $domUnitNumber, 'unit number was not set after ajax');

        //make sure we can still edit with pencil
        self::byXPath("//*[@id='facility-name-$confirmationCode']//*[contains (@class, 'edit-unit-number')]")->click();
        $this->waitForText('Edit Unit Number');
    }
}