<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/10/14
 * Time: 4:06 PM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;

class CpaLateMoveInTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testChangeMoveInDatesLate() {
        //setup the statement
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
        $request->setDisputedCount(0);
        $request->setConfirmedCount(0);
        $request->setPendingCount(1);
        $request->setLateCount(1);
        $request->setFacilityCount(1);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());

        $lateTransactions = $statementResponse->getTransactionsLate();
        /**
         * first scenario: move a late move-in onto the current statement
         * it should reappear in 'pending' on the statement
         */
        /**
         * @var $lateTransaction Genesis_Entity_Transaction
         */
        $lateTransaction = $lateTransactions[0]; //use first late transaction for first test
        $confirmationCode = $lateTransaction->getUniqueId();

        //wait for decorator to fire
        self::waitUntil(function() use ($confirmationCode) {
            $class = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]")->attribute('class');
            return stripos($class, 'active') ? true : null;
        }, self::timeout());

        $checkMark = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'change-move-in-date-button')]");
        $checkMark->click();
        $this->waitForText('Change move-in date');

        //move onto the current statement
        $this->_clickDatepickerDate('into-date', $statementResponse->getStatementBatch()->getStartDate());

        //click the save button
        self::byId('change-move-in-date-submit')->click();

        //let the save button finish
        $this->waitForText('Needs Your Review');
        $this->refresh();
        $this->waitForSource('statement-title');

        //make sure it moved to the needs review section
        $this->byCssSelector("[data-type='pending'] [data-id='$confirmationCode']");
    }
}
