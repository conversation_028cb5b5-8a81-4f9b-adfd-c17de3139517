<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/10/14
 * Time: 4:06 PM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;
use MyfootTests\MyAbstract;

class CpaChangeNameTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testChangeMoveInDatesLate()
    {

//        //setup the statement
//        $request = new FactoryRequest();
//        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
//        $request->setDisputedCount(0);
//        $request->setConfirmedCount(0);
//        $request->setPendingCount(1);
//        $request->setFacilityCount(1);
//
//        $statementResponse = Factory::buildStatement($request);
//
//        $this->_doLogin(
//            $statementResponse->getUser()->getEmail(),
//            $statementResponse->getUserPassword()
//        );
//
//        $this->_navigateToCurrentStatement();
//
//        $pendingTransactions = $statementResponse->getTransactionsPending();
//        /**
//         * first scenario: move a late move-in onto the current statement
//         * it should reappear in 'pending' on the statement
//         */
//        /**
//         * @var $pendingTransaction Genesis_Entity_Transaction
//         */
//        $pendingTransaction = $pendingTransactions[0];
//        $confirmationCode = $pendingTransaction->getUniqueId();
//        $xMark = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]");
//        $xMark->click();
//        $this->waitForSource("Why didn't the customer move in this month?");
//
//        //click for customer name change
//        self::byId('dispute-change-name')->click();
//        $this->waitForSource("Please provide us with accurate contact information to appear on your bill.");
//
//        //click on the name inputs
//        self::byId('change-first-name')->clear();
//        self::byId('change-first-name')->value('John');
//
//        self::byId('change-last-name')->clear();
//        self::byId('change-last-name')->value('Doe');
//
//        //click the save button
//        self::byId('dispute-submit')->click();
//
//        //let the save button finish
//        $this->waitForSource('Needs Your Review');
//        $this->refresh();
//
//        //make sure its still listed
//        self::byXPath("//*[@id='pending']/*[@id='$confirmationCode']");
//
//        //make sure its now confirmed
//        $this->assertEquals(self::byId('status-' . $confirmationCode)->text(),  'Moved In');
//
//        //make sure the name updated
//        $text = self::byId('customer-info-' . $confirmationCode)->text();
//        $this->assertNotFalse(stripos($text, 'John'));
//        $this->assertNotFalse(stripos($text, 'Doe'));
    }
}