<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/10/14
 * Time: 4:06 PM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;
use MyfootTests\MyAbstract;

class ResidualChangeToSisterFacilityTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testMoveToSisterFacility()
    {
        \Genesis_Service_Feature::setValue(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_STATEMENT_WIZARD, \Genesis_Service_Feature::ACTIVE_KEY, 1);
        //setup the statement
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $request->setDisputedCount(0);
        $request->setConfirmedCount(0);
        $request->setPendingCount(1);
        $request->setFacilityCount(2);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());


        /**
         * first scenario: change the customer name of a pending transaction
         * it should reappear in 'pending' on the statement with a new name
         */
        /**
         * @var $pendingTransaction Genesis_Entity_Transaction
         */
        $pendingTransaction = $statementResponse->getTransactionsPending()[0];
        $confirmationCode = $pendingTransaction->getUniqueId();

        $this->waitForText('Needs Your Review');
        self::byXPath("//td[@id='facility-name-$confirmationCode']//*[contains(@class,'edit-facility')]")->click();
        $this->waitForText("Edit Facility");

        /**
         * @var $facility \Genesis_Entity_Facility
         */
        foreach ($statementResponse->getFacilities() as $facility) {
            if ($facility->getId() != $pendingTransaction->getFacilityId()) {
                break; //found a sister facility
            }
        }
        //pick the sister facility
        self::select(self::byId('sister-facility-select'))->selectOptionByValue($facility->getId());

        //click the save button
        self::byId('sister-facility-select-submit')->click();
        //modal goes away
        self::waitUntil(function() {
            return self::byId('edit-facility-modal')->displayed() ? null : true;
        }, self::timeout());

        //make sure the facility name updated
        self::waitUntil(function() use ($confirmationCode, $facility) {
            $text = self::byId('facility-name-' . $confirmationCode)->text();
            return stripos(' '.$text, $facility->getTitle()) ? true: null;
        }, self::timeout());

        $this->assertNotFalse(stripos(self::byId('facility-name-' . $confirmationCode)->text(), $facility->getCompanyCode()));
    }
}