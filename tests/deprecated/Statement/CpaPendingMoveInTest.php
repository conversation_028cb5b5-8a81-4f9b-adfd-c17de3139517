<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 11/24/14
 * Time: 12:50 PM
 */

namespace MyfootTests\Statement;

use GenesisTests\Service\Statement\FactoryRequest;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Account;
use Genesis_Entity_Transaction;
use MyfootTests\MyAbstract;
class CpaPendingMoveInTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testChangeMoveInDatesPending()
    {
        //setup the statement
//        $request = new FactoryRequest();
//        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
//        $request->setDisputedCount(0);
//        $request->setConfirmedCount(0);
//        $request->setPendingCount(2);
//        $request->setFacilityCount(1);
//
//        $statementResponse = Factory::buildStatement($request);
//
//        $this->_doLogin(
//            $statementResponse->getUser()->getEmail(),
//            $statementResponse->getUserPassword()
//        );
//
//        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());
//
//        $pendingTransaction = $statementResponse->getTransactionsPending();
//        /**
//         * first scenario: change a MID for a pending within the current statement
//         * it should re-appear in 'pending' on the statement
//         */
//        /**
//         * @var $pendingTransaction Genesis_Entity_Transaction
//         */
//        $pendingTransaction = $pendingTransaction[0]; //use first late transaction for first test
//        $confirmationCode = $pendingTransaction->getUniqueId();
//        //click the X mark on a pending transaction
//        $xMark = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]");
//        $xMark->click();
//        $this->waitForSource("Why didn't the customer move in this month?");
//        //click for customer might move in the future
//        self::byId('dispute-change-move-in')->click();
//        $this->waitForSource("Please enter the customer's new move-in date");
//        //click on the date in datepicker
//        $this->_clickDatepickerDate(
//            'out-date',
//            strtotime("+3 days " . $statementResponse->getStatementBatch()->getEndDate())
//        );
//
//        //click the save button
//        self::byId('dispute-submit')->click();
//
//        //let the save button finish
//        $this->waitForSource('Needs Your Review');
//        $this->refresh();
//
//        //make sure it was removed from the needs review section
//        $this->setExpectedException('Exception');
//        self::byXPath("//*[@id='pending']/*[@id='$confirmationCode']");
    }
  }