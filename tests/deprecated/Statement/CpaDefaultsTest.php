<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/10/14
 * Time: 3:58 PM
 */

namespace MyfootTests\Statement;
use GenesisTests\Service\Statement\FactoryRequest;
use Genesis_Entity_Account;
use GenesisTests\Service\Statement\Factory;
use Genesis_Entity_Transaction;

class CpaDefaultsTest extends AbstractStatement
{
    /**
     * @group statements
     */
    public function testCpaStatementDefaults()
    {
        $request = new FactoryRequest();
        $request->setBidType(Genesis_Entity_Account::BID_TYPE_FLAT);
        $request->setDisputedCount(1);
        $request->setConfirmedCount(1);
        $request->setPendingCount(1);
        $request->setLateCount(1);
        $request->setFacilityCount(1);

        $statementResponse = Factory::buildStatement($request);

        $this->_doQuickLogin(
            $statementResponse->getUser()->getEmail(),
            $statementResponse->getUserPassword()
        );

        $this->_navigateToCurrentStatement($statementResponse->getStatement()->getId());

        /**
         * now do real stuff, like make sure all n transactions appear
         * and some other properties are surfaced correctly
         */

        /**
         * @var $pendingTransaction Genesis_Entity_Transaction
         */
        foreach ($statementResponse->getTransactionsPending() as $pendingTransaction) {
            $confirmationCode = $pendingTransaction->getUniqueId();
            $this->byCssSelector("[data-id='$confirmationCode']");
            //its listed in the pending section
            $this->byCssSelector("[data-type='pending'] [data-id='$confirmationCode']"); //listed where it should be in pending transactions
            //confirm the sparefoot fee is calculated as the bid amount
            $tableDown = self::byXPath("//td[@id='amount-$confirmationCode']");
            $this->assertEquals("$" . $pendingTransaction->getBidAmount(), $tableDown->text(), 'sparefoot fee did not match ui value for pending');
            //confirm the bid amount
            $tableDown = self::byXPath("//td[@id='baseBid-$confirmationCode']");
            $this->assertEquals("$" . $pendingTransaction->getBidAmount(), $tableDown->text(), 'bid did not match ui value for pending');
            //confirm the confirm checkbox is checked green
            self::waitUntil(function() use ($confirmationCode) {
                $class = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'confirm-button')]")->attribute('class');
                return stripos($class, 'active') ? true : null;
            }, self::timeout());
        }
        /**
         * @var $confirmedTransaction Genesis_Entity_Transaction
         */
        foreach ($statementResponse->getTransactionsAutoConfirmed() as $confirmedTransaction) {
            $confirmationCode = $confirmedTransaction->getUniqueId();
            $this->byCssSelector("[data-id='$confirmationCode']");
            //its listed in the confirmed section
            $this->byCssSelector("[data-type='confirmed'] [data-id='$confirmationCode']"); //listed where it should be in confirmed transactions
            //confirm the sparefoot fee is calculated as the bid amount
            $tableDown = self::byXPath("//td[@id='amount-$confirmationCode']");
            $this->assertEquals("$" . $pendingTransaction->getBidAmount(), $tableDown->text(), 'sparefoot fee did not match ui value for confirmed');
            //confirm the bid amount
            $tableDown = self::byXPath("//td[@id='baseBid-$confirmationCode']");
            $this->assertEquals("$" . $pendingTransaction->getBidAmount(), $tableDown->text(), 'bid did not match ui value for confirmed');
            //confirm the confirm checkbox is checked green
            self::waitUntil(function() use ($confirmationCode) {
                $class = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'confirm-autoconfirmed-button')]")->attribute('class');
                return stripos($class, 'active') ? true : null;
            }, self::timeout());
        }
        /**
         * @var $disputedTransaction Genesis_Entity_Transaction
         */
        foreach ($statementResponse->getTransactionsAutoDisputed() as $disputedTransaction) {
            $confirmationCode = $disputedTransaction->getUniqueId();
            $this->byCssSelector("[data-id='$confirmationCode']");
            //its listed in the disputed section
            $this->byCssSelector("[data-type='disputed'] [data-id='$confirmationCode']"); //listed where it should be in disputed transactions
            //confirm the sparefoot fee is calculated as 0
            $tableDown = self::byXPath("//td[@id='amount-$confirmationCode']");
            $this->assertEquals("$0.00", $tableDown->text(), 'sparefoot fee did not match ui value for disputed');
            //confirm the bid amount is displayed correctly
            $tableDown = self::byXPath("//td[@id='baseBid-$confirmationCode']");
            $this->assertEquals("$" . $pendingTransaction->getBidAmount(), $tableDown->text(), 'bid did not match ui value for disputed');
            //confirm the dispute checkbox is checked red
            self::waitUntil(function() use ($confirmationCode) {
                $class = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]")->attribute('class');
                return stripos($class, 'active') ? true : null;
            }, self::timeout());
        }

        /**
         * @var $lateTransaction Genesis_Entity_Transaction
         */
        foreach ($statementResponse->getTransactionsLate() as $lateTransaction) {
            $confirmationCode = $lateTransaction->getUniqueId();
            $this->byCssSelector("[data-id='$confirmationCode']");
            //its listed in the late move in section
            $this->byCssSelector("[data-type='late'] [data-id='$confirmationCode']"); //listed where it should be in disputed transactions
            //confirm the sparefoot fee is calculated as 0
            $tableDown = self::byXPath("//td[@id='amount-$confirmationCode']");
            $this->assertEquals("$0.00", $tableDown->text(), 'sparefoot fee did not match ui value for disputed');
            //confirm the bid amount is displayed correctly
            $tableDown = self::byXPath("//td[@id='baseBid-$confirmationCode']");
            $this->assertEquals("$" . $pendingTransaction->getBidAmount(), $tableDown->text(), 'bid did not match ui value for disputed');
            //confirm the dispute checkbox is checked red
            self::waitUntil(function() use ($confirmationCode) {
                $class = self::byXPath("//td[@id='action-$confirmationCode']//*[contains(@class,'dispute-button')]")->attribute('class');
                return stripos($class, 'active') ? true : null;
            }, self::timeout());
        }
    }
}
