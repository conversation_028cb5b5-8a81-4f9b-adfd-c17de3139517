<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/8/14
 * Time: 1:16 PM
 */

namespace MyfootTests\Signup;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;

/**
 * These tests verify that if a user uses the "Back" buttons available during the sign-up flow, then
 * their previously entered values are pulled from session data and re-input into form fields.
 */
class ValuesInSessionTest extends AbstractSignup
{
    public function testValuesInSession()
    {
        $this->_doCode('R3X3');
        self::byId('submit')->click();

        $this->waitForText('User Information');
        self::byId('back')->click();

        $code = self::byId('signup-code')->attribute('value');
        $this->assertEquals('R3X3', $code);

        self::byId('submit')->click();

        $this->waitForText('User Information');

        $formValues = array(
            'company-name' => uniqid('Test Values In Session'),
            'address' => '720 Brazos Street',
            'city' => 'Austin',
            'zip' => '78701',
            'first-name' => 'Test',
            'last-name' => 'Tester',
            'phone' => '5127056208',
            'email' => uniqid('test-values-in-session-') . '@sparefoot.com',
        );

        foreach ($formValues as $elementId => $elementValue) {
            self::byId($elementId)->clear();
            self::byId($elementId)->value($elementValue);
        }

        $this->_doPasswords();
        self::select(self::byId('state'))->selectOptionByLabel('TX');
        self::byId('submit')->click();

        $this->waitForText('Terms For');
        self::byId('back')->click();

        $this->waitForText('User Information');
        self::byId('back')->click();

        $code = self::byId('signup-code')->attribute('value');
        $this->assertEquals('R3X3', $code);

        self::byId('submit')->click();

        $this->waitForText('User Information');

        foreach ($formValues as $elementId => $elementValue) {
            $tValue = self::byId($elementId)->attribute('value');
            $this->assertEquals(
                $elementValue,
                $tValue,
                'value of ' . $elementId .' was not expected. Needed "' . $elementValue . '" and got "'. self::byId($elementId)->attribute('value') .'"'
            );
        }
        $this->_doPasswords();
        self::byId('submit')->click();


        $this->_doTerms(\Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        self::byId('submit')->click();

        $this->waitForText('Billing Information');
        self::byId('back')->click();

        $array = ['agree1', 'agree2', 'agree3', 'agree4'];
        foreach ($array as $checkbox) {
            $this->assertEquals('true', self::byId($checkbox)->attribute('checked'), 'Expected ' . $checkbox . ' to be checked.');
        }
    }
}
