<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/16/14
 * Time: 10:39 AM
 */

namespace MyfootTests\Signup;


class CpaPercentSignupTest extends AbstractSignup
{
    public function testCpaSignup()
    {
        $this->_doCode('C52P1');
        self::byId('submit')->click();

        $email = $this->_doUserInfo('test-cpa-percent');
        self::byId('submit')->click();

        $this->_doTerms(\Genesis_Entity_Account::BID_TYPE_PERCENT);
        self::byId('submit')->click();

        $this->_doBilling($email);

        // Skip billing info while netsuite is down
        #if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_TEST_NETSUITE_SANDBOX)) {
        #    self::byId('submit')->click();
        #} else {
            self::byId('do_this_later')->click();
        #}

        $this->_assertWeReachedWelcome();

        $user = \Genesis_Service_User::loadByEmail($email);
        $userAccess = $user->getUserAccess();
        $account = $userAccess->getAccount();
        $this->assertEquals(\Genesis_Entity_Account::BID_TYPE_PERCENT, $account->getBidType(), 'Expected account bid type to be ' . \Genesis_Entity_Account::BID_TYPE_PERCENT);
        $this->assertEquals('1.25', $account->getMinBid(), 'Expected account minBid to be 1.25');
    }
}
