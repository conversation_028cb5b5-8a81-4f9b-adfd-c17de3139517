<?php
namespace MyfootTests\Signup;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;

class SavePasswordResetsTest extends AbstractSignup
{
    /**
     * make sure we are _NOT_ resetting passwords
     */
    public function testNoResetPassword()
    {
        $this->_doCode('C5P1');
        self::byId('submit')->click();

        $this->_doUserInfo('no-password-reset');
        self::byId('submit')->click();

        $this->_doTerms();
        self::byId('back')->click();

        $this->_doPasswords('wrongPassword');
        self::byId('submit')->click();

        $this->waitForText('Error');
    }
}
