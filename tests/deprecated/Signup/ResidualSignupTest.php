<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/8/14
 * Time: 1:21 PM
 */

namespace MyfootTests\Signup;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;

class ResidualSignupTest extends AbstractSignup
{
    public function testResidualSignup() {

        $this->_doCode('R3X3');
        self::byId('submit')->click();

        $email = $this->_doUserInfo('test-residual');
        self::byId('submit')->click();

        $this->_doTerms(\Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        self::byId('submit')->click();

        $this->_doBilling($email);

        // skip billing info while netsuite is down
        if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_TEST_NETSUITE_SANDBOX)) {
            self::byId('submit')->click();
        } else {
            self::byId('do_this_later')->click();
        }

        $this->_assertWeReachedWelcome();

        $user = \Genesis_Service_User::loadByEmail($email);
        $userAccess = $user->getUserAccess();
        $account = $userAccess->getAccount();
        $this->assertEquals(\Genesis_Entity_Account::BID_TYPE_RESIDUAL, $account->getBidType(), 'Expected account bid type to be ' . \Genesis_Entity_Account::BID_TYPE_RESIDUAL);
        $this->assertEquals(null, $account->getMinBid(), 'Expected account minBid to be NULL');
    }
}
