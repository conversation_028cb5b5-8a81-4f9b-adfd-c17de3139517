<?php
namespace MyfootTests\Signup;
use Genesis_Service_Feature;
use Genesis_Entity_Feature;
use MyfootTests\MyAbstract;

class AbstractSignup extends MyAbstract
{
    // Default base URL for sign-up tests
    const BASE_URL = '/login/logout';

    // Default password used for creating new test accounts
    const DEFAULT_PASSWORD = 'testtesttest';

    // Test credit card account numbers
    const CC_AMEX = '***************';
    const CC_DISCOVER = '****************';
    const CC_JSB = '****************';
    const CC_MASTERCARD = '****************';
    const CC_VISA = '****************';

    protected function _doLogin($email, $password = self::DEFAULT_PASSWORD, $wait = false)
    {
        //wait for login form
        parent::_doLogin($email, $password, $wait);
    }

    protected function _doCode($code = 'C5P1')
    {
        self::url(self::BASE_URL);
        $this->waitForText('New to SpareFoot?');

        self::byId('signup-button')->click();
        $this->waitForText('Signup Code');
        self::byId('signup-code')->clear();
        self::byId('signup-code')->value($code);
    }

    protected function _doTerms($bidType = \Genesis_Entity_Account::BID_TYPE_PERCENT)
    {
        $this->waitForText('Terms For');
        if ($bidType !== \Genesis_Entity_Account::BID_TYPE_PERCENT) {
            self::byId('agree1')->click();
        }
        self::byId('agree2')->click();
        self::byId('agree3')->click();
        self::byId('agree4')->click();
    }

    protected function _doPasswords($password = self::DEFAULT_PASSWORD)
    {
        self::byId('password')->clear();
        self::byId('password')->value($password);
        self::byId('password-confirm')->clear();
        self::byId('password-confirm')->value($password);
    }

    protected function _doUserInfo($prefix = 'Test')
    {
        $this->waitForText('User Information');
        $email = uniqid($prefix) . '@sparefoot.com';

        self::sendKeys(self::byName('company_name'), uniqid('Company '.ucwords(str_replace("-", " ", $prefix))));
        self::sendKeys(self::byId('address'), '720 Brazos Street');
        self::sendKeys(self::byId('city'), 'Austin');
        self::select(self::byId('state'))->selectOptionByLabel('TX');
        self::sendKeys(self::byId('zip'), '78701');
        self::sendKeys(self::byId('first-name'), 'Test');
        self::sendKeys(self::byId('last-name'), ucwords(str_replace("-", " ", $prefix)));
        self::sendKeys(self::byId('phone'), '5127056208');
        self::sendKeys(self::byId('email'), $email);

        $this->_doPasswords();

        return $email;
    }

    protected function _doBilling($email)
    {
        $this->waitForText('Billing Information');
        self::byId('payment-type-nickname')->clear();
        self::byId('payment-type-nickname')->value(uniqid('Test Card'));
        self::byId('credit-card-number')->clear();
        self::byId('credit-card-number')->value(self::CC_VISA);
        self::byId('credit-card-name')->clear();
        self::byId('credit-card-name')->value('Test Tester');
        $nextMonth = strtotime('+1 month');
        self::select(self::byId('credit-card-expiration-month'))->selectOptionByLabel(date('m',$nextMonth));
        self::select(self::byId('credit-card-expiration-year'))->selectOptionByLabel(date('Y',$nextMonth));

        self::byId('billing-address-same')->click();
        sleep(1);

        self::byId('address')->clear();
        self::byId('address')->value('720 Brazos Street');
        self::byId('city')->clear();
        self::byId('city')->value('Austin');

        self::select(self::byId('state'))->selectOptionByLabel('TX');
        self::byId('zip')->clear();
        self::byId('zip')->value('78701');

        self::byId('emails')->clear();
        self::byId('emails')->value($email);
    }

    protected function _assertWeReachedWelcome()
    {
        $this->waitForText('Let\'s add your first facility.');
    }
}
