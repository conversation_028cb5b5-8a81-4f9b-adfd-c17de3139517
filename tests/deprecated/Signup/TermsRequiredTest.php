<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/16/14
 * Time: 10:38 AM
 */

namespace MyfootTests\Signup;


class TermsRequiredTest extends AbstractSignup
{
    public function testTermsRequired()
    {
        $this->_doCode();
        self::byId('submit')->click();

        $email = $this->_doUserInfo('terms-required');
        self::byId('submit')->click();

        $this->waitForText('Terms For');

        $this->_doLogin($email, self::DEFAULT_PASSWORD);

        $this->_doTerms();
        self::byId('submit')->click();

    }
}