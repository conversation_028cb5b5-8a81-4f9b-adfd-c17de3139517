<?php namespace MyfootTests\Settings;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 12/11/14
 * Time: 1:52 PM
 */
use MyfootTests\MyAbstract;
class AbstractSettings extends MyAbstract
{
    protected function _navigateToSettings()
    {
        self::_navigateToDashboard();
        self::byXPath("//div[contains(@class, 'right') and contains(@class, 'menu')]//div[contains(@class, 'ui') and contains(@class, 'dropdown') and contains(@class, 'item')]")
            ->click();
        self::byId('header-settings')->click();
        $this->waitForText('Change Your Personal Information'); //we made it
    }
}