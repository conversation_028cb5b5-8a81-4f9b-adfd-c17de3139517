security:
    enable_authenticator_manager: true
    
    providers:
        custom_provider:
            id: <PERSON><PERSON>foot\MyFootService\Security\CustomUserProvider

    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
        Sparefoot\MyFootService\Security\User:
            algorithm: auto

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|img|fonts|js)/
            security: false
        main:
            lazy: true
            provider: custom_provider
            custom_authenticator: Sparefoot\MyFootService\Security\CustomAuthenticator
            remember_me:
                secret: '%kernel.secret%'
                lifetime: 604800 # 1 week in seconds
                path: /
                always_remember_me: false
            logout:
               path: login_loign_logout
               target: login_index
    role_hierarchy:
       ROLE_GOD: [ROLE_FACILITYEDITOR, ROLE_SEARCHANALYST, ROLE_USER]
    access_control:
       - { path: ^/public, roles: PUBLIC_ACCESS }
       - { path: ^/login, roles: PUBLIC_ACCESS }
       - { path: ^/logout, roles: PUBLIC_ACCESS }
       - { path: ^/ping, roles: PUBLIC_ACCESS }
       - { path: ^/inventory, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/user, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/api/units, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/error, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/search, roles: [ROLE_SEARCHANALYST] }
       - { path: ^/, roles: ROLE_GOD } # Fallback
