{"friendsofphp/php-cs-fixer": {"version": "3.73", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "php-http/discovery": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "phpunit/phpunit": {"version": "10.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "10.0", "ref": "b786e93f0b38027289867faca488b8c511436fe0"}, "files": [".env.test", "phpunit.dist.xml", "tests/bootstrap.php"]}, "sparefoot/sf_service_bundle": {"version": "dev-php8"}, "squizlabs/php_codesniffer": {"version": "3.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.6", "ref": "1019e5c08d4821cb9b77f4891f8e9c31ff20ac6f"}, "files": ["phpcs.xml.dist"]}, "symfony/console": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/flex": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/framework-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/messenger": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "8bd5f27013fb1d7217191c548e340f0bdb11912c"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "07ce01a897311647520b43d4ddddad9537b99ba6"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "85de1d8ae45b284c3c84b668171d2615049e698f"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "98f1f2b0d635908c2b40f3675da2d23b1a069d30"}, "files": ["config/packages/security.yaml"]}, "symfony/twig-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "bb2178c57eee79e6be0b297aa96fc0c0def81387"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}}