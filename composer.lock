{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "7d4ba7d1929040441dedde7adce9a562", "packages": [{"name": "aws/aws-crt-php", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "d71d9906c7bb63a28295447ba12e74723bd3730e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/d71d9906c7bb63a28295447ba12e74723bd3730e", "reference": "d71d9906c7bb63a28295447ba12e74723bd3730e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.2.7"}, "time": "2024-10-18T22:15:13+00:00"}, {"name": "aws/aws-php-sns-message-validator", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/aws/aws-php-sns-message-validator.git", "reference": "d92d199179cbbf4d99601d4f23f61b9b8cc5da28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-php-sns-message-validator/zipball/d92d199179cbbf4d99601d4f23f61b9b8cc5da28", "reference": "d92d199179cbbf4d99601d4f23f61b9b8cc5da28", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">=8.1", "psr/http-message": "^2.0"}, "require-dev": {"guzzlehttp/psr7": "^2.4.5", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "squizlabs/php_codesniffer": "^2.3", "yoast/phpunit-polyfills": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Aws\\Sns\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "Amazon SNS message validation for PHP", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["SNS", "amazon", "aws", "cloud", "message", "sdk", "webhooks"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sns-message-validator/issues", "source": "https://github.com/aws/aws-php-sns-message-validator/tree/1.10.0"}, "time": "2025-03-05T22:22:58+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.337.3", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "06dfc8f76423b49aaa181debd25bbdc724c346d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/06dfc8f76423b49aaa181debd25bbdc724c346d6", "reference": "06dfc8f76423b49aaa181debd25bbdc724c346d6", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.2.3", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "mtdowling/jmespath.php": "^2.6", "php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}, "exclude-from-classmap": ["src/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.337.3"}, "time": "2025-01-21T19:10:05+00:00"}, {"name": "brick/math", "version": "0.13.1", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/fc7ed316430118cc7836bf45faff18d5dfc8de04", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.13.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-03-29T13:50:30+00:00"}, {"name": "bshaffer/oauth2-server-httpfoundation-bridge", "version": "v1.7.1", "source": {"type": "git", "url": "https://github.com/bshaffer/oauth2-server-httpfoundation-bridge.git", "reference": "ab0b702c8a036f44d72bf62f7d02574d610cd894"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bshaffer/oauth2-server-httpfoundation-bridge/zipball/ab0b702c8a036f44d72bf62f7d02574d610cd894", "reference": "ab0b702c8a036f44d72bf62f7d02574d610cd894", "shasum": ""}, "require": {"bshaffer/oauth2-server-php": "^1.13", "php": ">=8.0", "symfony/http-foundation": "^5.4||^6.0||^7.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-0": {"OAuth2\\HttpFoundationBridge": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://brentertainment.com"}], "description": "A bridge to HttpFoundation for oauth2-server-php", "homepage": "http://github.com/bshaffer/oauth2-server-httpfoundation-bridge", "keywords": ["auth", "httpfoundation", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/bshaffer/oauth2-server-httpfoundation-bridge/issues", "source": "https://github.com/bshaffer/oauth2-server-httpfoundation-bridge/tree/v1.7.1"}, "time": "2024-01-20T14:40:00+00:00"}, {"name": "bshaffer/oauth2-server-php", "version": "v1.14.2", "source": {"type": "git", "url": "https://github.com/bshaffer/oauth2-server-php.git", "reference": "1c715c2d2f71254d1c683b8c89a0f16b61ccd0d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bshaffer/oauth2-server-php/zipball/1c715c2d2f71254d1c683b8c89a0f16b61ccd0d6", "reference": "1c715c2d2f71254d1c683b8c89a0f16b61ccd0d6", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"aws/aws-sdk-php": "^2.8", "firebase/php-jwt": "^6.4", "phpunit/phpunit": "^7.5||^8.0", "predis/predis": "^1.1", "thobbs/phpcassa": "dev-master", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-sdk-php": "~2.8 is required to use DynamoDB storage", "firebase/php-jwt": "~v6.4 is required to use JWT features", "mongodb/mongodb": "^1.1 is required to use MongoDB storage", "predis/predis": "Required to use Redis storage", "thobbs/phpcassa": "Required to use Cassandra storage"}, "type": "library", "autoload": {"psr-0": {"OAuth2": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://brentertainment.com"}], "description": "OAuth2 Server for PHP", "homepage": "http://github.com/bshaffer/oauth2-server-php", "keywords": ["auth", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/bshaffer/oauth2-server-php/issues", "source": "https://github.com/bshaffer/oauth2-server-php/tree/v1.14.2"}, "time": "2025-03-28T21:13:45+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "datadog/dd-trace", "version": "0.97.0", "source": {"type": "git", "url": "https://github.com/DataDog/dd-trace-php.git", "reference": "e76850105e99ba5d5b36697b3e4553ee70cfdae9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DataDog/dd-trace-php/zipball/e76850105e99ba5d5b36697b3e4553ee70cfdae9", "reference": "e76850105e99ba5d5b36697b3e4553ee70cfdae9", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": "^5.4 || ^7.0 || ^8.0.0"}, "require-dev": {"ext-posix": "*", "g1a/composer-test-scenarios": "~3.0", "mockery/mockery": "*", "phpunit/phpunit": "<10", "squizlabs/php_codesniffer": "^3.3.0", "symfony/process": "<5"}, "type": "library", "extra": {"scenarios": {"opentracing10": {"require": {"opentracing/opentracing": "^1.0"}, "scenario-options": {"create-lockfile": false}}, "opentelemetry1": {"require": {"open-telemetry/sdk": "@stable", "open-telemetry/extension-propagator-b3": "@stable", "open-telemetry/opentelemetry-logger-monolog": "@stable"}, "scenario-options": {"create-lockfile": false}}, "opentracing_beta5": {"require": {"opentracing/opentracing": "1.0.0-beta5"}, "scenario-options": {"create-lockfile": false}}, "opentracing_beta6": {"require": {"opentracing/opentracing": "1.0.0-beta6"}, "scenario-options": {"create-lockfile": false}}}, "scenario-options": {"dependency-licenses": false}}, "autoload": {"files": ["./src/api/ComposerBootstrap.php"], "psr-4": {"DDTrace\\": "./src/api/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "DataDog", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP APM Client", "keywords": ["DataDog", "php", "tracing"], "support": {"issues": "https://github.com/DataDog/dd-trace-php/issues", "source": "https://github.com/DataDog/dd-trace-php/tree/0.97.0"}, "time": "2024-01-18T10:15:17+00:00"}, {"name": "developerforce/force.com-toolkit-for-php", "version": "v27.0.3", "source": {"type": "git", "url": "https://github.com/developerforce/Force.com-Toolkit-for-PHP.git", "reference": "14eadd0adae15216907dff5e76a922c0f8a3c9fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/developerforce/Force.com-Toolkit-for-PHP/zipball/14eadd0adae15216907dff5e76a922c0f8a3c9fa", "reference": "14eadd0adae15216907dff5e76a922c0f8a3c9fa", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["soapclient"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A wrapper for the Force.com Web Services SOAP API", "support": {"issues": "https://github.com/developerforce/Force.com-Toolkit-for-PHP/issues", "source": "https://github.com/developerforce/Force.com-Toolkit-for-PHP/tree/master"}, "abandoned": true, "time": "2015-03-23T21:27:56+00:00"}, {"name": "fluent/logger", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/fluent/fluent-logger-php.git", "reference": "c5a19ba8eb5e15f9ade72afeffba6b6e7eb51155"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fluent/fluent-logger-php/zipball/c5a19ba8eb5e15f9ade72afeffba6b6e7eb51155", "reference": "c5a19ba8eb5e15f9ade72afeffba6b6e7eb51155", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.3", "phpunit/phpunit-mock-objects": "2.3.0"}, "type": "library", "autoload": {"psr-4": {"Fluent\\Logger\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "DQNEO", "email": "<EMAIL>"}], "description": "a logging library for Fluentd", "homepage": "http://github.com/fluent/fluent-logger-php", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/fluent/fluent-logger-php/issues", "source": "https://github.com/fluent/fluent-logger-php/tree/v1.0.1"}, "time": "2017-02-15T07:14:53+00:00"}, {"name": "friendsofphp/proxy-manager-lts", "version": "v1.0.18", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/proxy-manager-lts.git", "reference": "2c8a6cffc3220e99352ad958fe7cf06bf6f7690f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/proxy-manager-lts/zipball/2c8a6cffc3220e99352ad958fe7cf06bf6f7690f", "reference": "2c8a6cffc3220e99352ad958fe7cf06bf6f7690f", "shasum": ""}, "require": {"laminas/laminas-code": "~3.4.1|^4.0", "php": ">=7.1", "symfony/filesystem": "^4.4.17|^5.0|^6.0|^7.0"}, "conflict": {"laminas/laminas-stdlib": "<3.2.1", "zendframework/zend-stdlib": "<3.2.1"}, "replace": {"ocramius/proxy-manager": "^2.1"}, "require-dev": {"ext-phar": "*", "symfony/phpunit-bridge": "^5.4|^6.0|^7.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/Ocramius/ProxyManager", "name": "ocramius/proxy-manager"}}, "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adding support for a wider range of PHP versions to ocramius/proxy-manager", "homepage": "https://github.com/FriendsOfPHP/proxy-manager-lts", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/FriendsOfPHP/proxy-manager-lts/issues", "source": "https://github.com/FriendsOfPHP/proxy-manager-lts/tree/v1.0.18"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2024-03-20T12:50:41+00:00"}, {"name": "geocoder-php/bing-maps-provider", "version": "4.4.0", "source": {"type": "git", "url": "https://github.com/geocoder-php/bing-maps-provider.git", "reference": "6eba4f69f6301cef9e1a7e008ca52c0afb776e83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/geocoder-php/bing-maps-provider/zipball/6eba4f69f6301cef9e1a7e008ca52c0afb776e83", "reference": "6eba4f69f6301cef9e1a7e008ca52c0afb776e83", "shasum": ""}, "require": {"geocoder-php/common-http": "^4.0", "php": "^8.0", "willdurand/geocoder": "^4.0|^5.0"}, "provide": {"geocoder-php/provider-implementation": "1.0"}, "require-dev": {"geocoder-php/provider-integration-tests": "^1.6.3", "php-http/message": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"psr-4": {"Geocoder\\Provider\\BingMaps\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Geocoder BigMaps adapter", "homepage": "http://geocoder-php.org/Geocoder/", "support": {"source": "https://github.com/geocoder-php/bing-maps-provider/tree/4.4.0"}, "time": "2025-04-15T13:24:32+00:00"}, {"name": "geocoder-php/common-http", "version": "4.7.0", "source": {"type": "git", "url": "https://github.com/geocoder-php/php-common-http.git", "reference": "4209be6c31946ed5a658f6240ab21faaf5413f61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/geocoder-php/php-common-http/zipball/4209be6c31946ed5a658f6240ab21faaf5413f61", "reference": "4209be6c31946ed5a658f6240ab21faaf5413f61", "shasum": ""}, "require": {"php": "^8.0", "php-http/discovery": "^1.17", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "willdurand/geocoder": "^4.0|^5.0"}, "require-dev": {"nyholm/psr7": "^1.0", "php-http/message": "^1.0", "php-http/mock-client": "^1.0", "phpunit/phpunit": "^9.5", "symfony/stopwatch": "~2.5 || ~5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"psr-4": {"Geocoder\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Common files for HTTP based Geocoders", "homepage": "http://geocoder-php.org", "keywords": ["http geocoder"], "support": {"source": "https://github.com/geocoder-php/php-common-http/tree/4.7.0"}, "time": "2025-04-15T12:38:11+00:00"}, {"name": "geocoder-php/google-maps-provider", "version": "4.8.0", "source": {"type": "git", "url": "https://github.com/geocoder-php/google-maps-provider.git", "reference": "587aac3647ebd2ce2090b613fc56c753b9dee9a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/geocoder-php/google-maps-provider/zipball/587aac3647ebd2ce2090b613fc56c753b9dee9a7", "reference": "587aac3647ebd2ce2090b613fc56c753b9dee9a7", "shasum": ""}, "require": {"geocoder-php/common-http": "^4.0", "php": "^8.0", "willdurand/geocoder": "^4.0|^5.0"}, "provide": {"geocoder-php/provider-implementation": "1.0"}, "require-dev": {"geocoder-php/provider-integration-tests": "^1.6.3", "php-http/message": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"psr-4": {"Geocoder\\Provider\\GoogleMaps\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Geocoder GoogleMaps adapter", "homepage": "http://geocoder-php.org/Geocoder/", "support": {"source": "https://github.com/geocoder-php/google-maps-provider/tree/4.8.0"}, "time": "2025-04-15T13:24:32+00:00"}, {"name": "geocoder-php/mapquest-provider", "version": "4.4.0", "source": {"type": "git", "url": "https://github.com/geocoder-php/mapquest-provider.git", "reference": "7d563294dfe1f68d847b08e9c17a13138e2a31ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/geocoder-php/mapquest-provider/zipball/7d563294dfe1f68d847b08e9c17a13138e2a31ed", "reference": "7d563294dfe1f68d847b08e9c17a13138e2a31ed", "shasum": ""}, "require": {"geocoder-php/common-http": "^4.6", "php": "^8.0", "willdurand/geocoder": "^4.0|^5.0"}, "provide": {"geocoder-php/provider-implementation": "1.0"}, "require-dev": {"geocoder-php/provider-integration-tests": "^1.6.3", "php-http/message": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"psr-4": {"Geocoder\\Provider\\MapQuest\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Geocoder MapQuest adapter", "homepage": "http://geocoder-php.org/Geocoder/", "support": {"source": "https://github.com/geocoder-php/mapquest-provider/tree/4.4.0"}, "time": "2025-04-15T13:24:32+00:00"}, {"name": "google/protobuf", "version": "v4.31.1", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "2b028ce8876254e2acbeceea7d9b573faad41864"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/2b028ce8876254e2acbeceea7d9b573faad41864", "reference": "2b028ce8876254e2acbeceea7d9b573faad41864", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": ">=5.0.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "support": {"source": "https://github.com/protocolbuffers/protobuf-php/tree/v4.31.1"}, "time": "2025-05-28T18:52:35+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "b964ca597e86b752cd994f27293e9fa6b6a95ed9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/b964ca597e86b752cd994f27293e9fa6b6a95ed9", "reference": "b964ca597e86b752cd994f27293e9fa6b6a95ed9", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-04-17T16:30:08+00:00"}, {"name": "guzzlehttp/oauth-subscriber", "version": "0.6.0", "source": {"type": "git", "url": "https://github.com/guzzle/oauth-subscriber.git", "reference": "8d6cab29f8397e5712d00a383eeead36108a3c1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/oauth-subscriber/zipball/8d6cab29f8397e5712d00a383eeead36108a3c1f", "reference": "8d6cab29f8397e5712d00a383eeead36108a3c1f", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.5|^7.2", "guzzlehttp/psr7": "^1.7|^2.0", "php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0|^9.3.3"}, "suggest": {"ext-openssl": "Required to sign using RSA-SHA1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Subscriber\\Oauth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle OAuth 1.0 subscriber", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "o<PERSON>h"], "support": {"issues": "https://github.com/guzzle/oauth-subscriber/issues", "source": "https://github.com/guzzle/oauth-subscriber/tree/0.6.0"}, "time": "2021-07-13T12:01:32+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-05-21T12:31:43+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "halaxa/json-machine", "version": "1.2.5", "source": {"type": "git", "url": "https://github.com/halaxa/json-machine.git", "reference": "d0f84abf79ac98145d478b66d2bcf363d706477c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/halaxa/json-machine/zipball/d0f84abf79ac98145d478b66d2bcf363d706477c", "reference": "d0f84abf79ac98145d478b66d2bcf363d706477c", "shasum": ""}, "require": {"php": "7.2 - 8.4"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-json": "To run JSON Machine out of the box without custom decoders.", "guzzlehttp/guzzle": "To run example with GuzzleHttp"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"JsonMachine\\": "src/"}, "exclude-from-classmap": ["src/autoloader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Efficient, easy-to-use and fast JSON pull parser", "support": {"issues": "https://github.com/halaxa/json-machine/issues", "source": "https://github.com/halaxa/json-machine/tree/1.2.5"}, "funding": [{"url": "https://ko-fi.com/G2G57KTE4", "type": "other"}], "time": "2025-07-07T13:38:34+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "kamermans/guzzle-oauth2-subscriber", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/kamermans/guzzle-oauth2-subscriber.git", "reference": "7c311987ce7dcb6bfcd8320cc64588ad8fca075a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kamermans/guzzle-oauth2-subscriber/zipball/7c311987ce7dcb6bfcd8320cc64588ad8fca075a", "reference": "7c311987ce7dcb6bfcd8320cc64588ad8fca075a", "shasum": ""}, "require": {"php": ">=7.1.0"}, "suggest": {"guzzlehttp/guzzle": "Guzzle ~4.0|~5.0|~6.0|~7.0"}, "type": "library", "autoload": {"psr-4": {"kamermans\\OAuth2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "steve<PERSON><PERSON><PERSON>@gmail.com"}], "description": "OAuth 2.0 client for Guzzle 4, 5, 6 and 7+", "keywords": ["Guzzle", "o<PERSON>h"], "support": {"issues": "https://github.com/kamermans/guzzle-oauth2-subscriber/issues", "source": "https://github.com/kamermans/guzzle-oauth2-subscriber/tree/v1.1.1"}, "time": "2025-05-13T19:40:57+00:00"}, {"name": "laminas/laminas-code", "version": "4.16.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-code.git", "reference": "1793e78dad4108b594084d05d1fb818b85b110af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-code/zipball/1793e78dad4108b594084d05d1fb818b85b110af", "reference": "1793e78dad4108b594084d05d1fb818b85b110af", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"doctrine/annotations": "^2.0.1", "ext-phar": "*", "laminas/laminas-coding-standard": "^3.0.0", "laminas/laminas-stdlib": "^3.18.0", "phpunit/phpunit": "^10.5.37", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.15.0"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "homepage": "https://laminas.dev", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-20T13:15:13+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.8.0"}, "time": "2024-09-04T18:46:31+00:00"}, {"name": "nategood/httpful", "version": "0.2.11", "source": {"type": "git", "url": "https://github.com/nategood/httpful.git", "reference": "0bf1423028abe2f630e1d2ef8d62486d6655b2f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nategood/httpful/zipball/0bf1423028abe2f630e1d2ef8d62486d6655b2f3", "reference": "0bf1423028abe2f630e1d2ef8d62486d6655b2f3", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-0": {"Httpful": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nategood.com"}], "description": "A Readable, Chainable, REST friendly, PHP HTTP Client", "homepage": "http://github.com/nategood/httpful", "keywords": ["api", "curl", "http", "requests", "rest", "restful"], "support": {"issues": "https://github.com/nategood/httpful/issues", "source": "https://github.com/nategood/httpful/tree/v0.2.11"}, "time": "2013-10-20T11:03:40+00:00"}, {"name": "nyholm/psr7", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "a71f2b11690f4b24d099d6b16690a90ae14fc6f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/a71f2b11690f4b24d099d6b16690a90ae14fc6f3", "reference": "a71f2b11690f4b24d099d6b16690a90ae14fc6f3", "shasum": ""}, "require": {"php": ">=7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/message-factory": "^1.0", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.8.2"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2024-09-09T07:06:30+00:00"}, {"name": "nyholm/psr7-server", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7-server.git", "reference": "4335801d851f554ca43fa6e7d2602141538854dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7-server/zipball/4335801d851f554ca43fa6e7d2602141538854dc", "reference": "4335801d851f554ca43fa6e7d2602141538854dc", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"nyholm/nsa": "^1.1", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^7.0 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Nyholm\\Psr7Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "Helper classes to handle PSR-7 server requests", "homepage": "http://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7-server/issues", "source": "https://github.com/Nyholm/psr7-server/tree/1.1.0"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2023-11-08T09:30:43+00:00"}, {"name": "open-telemetry/api", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/api.git", "reference": "b3a9286f9c1c8247c83493c5b1fa475cd0cec7f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/api/zipball/b3a9286f9c1c8247c83493c5b1fa475cd0cec7f7", "reference": "b3a9286f9c1c8247c83493c5b1fa475cd0cec7f7", "shasum": ""}, "require": {"open-telemetry/context": "^1.0", "php": "^8.1", "psr/log": "^1.1|^2.0|^3.0", "symfony/polyfill-php82": "^1.26"}, "conflict": {"open-telemetry/sdk": "<=1.0.8"}, "type": "library", "extra": {"spi": {"OpenTelemetry\\API\\Instrumentation\\AutoInstrumentation\\HookManagerInterface": ["OpenTelemetry\\API\\Instrumentation\\AutoInstrumentation\\ExtensionHookManager"]}, "branch-alias": {"dev-main": "1.4.x-dev"}}, "autoload": {"files": ["Trace/functions.php"], "psr-4": {"OpenTelemetry\\API\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "API for OpenTelemetry PHP.", "keywords": ["Metrics", "api", "apm", "logging", "opentelemetry", "otel", "tracing"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-06-19T23:36:51+00:00"}, {"name": "open-telemetry/context", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/context.git", "reference": "1eb2b837ee9362db064a6b65d5ecce15a9f9f020"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/context/zipball/1eb2b837ee9362db064a6b65d5ecce15a9f9f020", "reference": "1eb2b837ee9362db064a6b65d5ecce15a9f9f020", "shasum": ""}, "require": {"php": "^8.1", "symfony/polyfill-php82": "^1.26"}, "suggest": {"ext-ffi": "To allow context switching in Fibers"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.0.x-dev"}}, "autoload": {"files": ["fiber/initialize_fiber_handler.php"], "psr-4": {"OpenTelemetry\\Context\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "Context implementation for OpenTelemetry PHP.", "keywords": ["Context", "opentelemetry", "otel"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-05-07T23:36:50+00:00"}, {"name": "open-telemetry/exporter-otlp", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/exporter-otlp.git", "reference": "196f3a1dbce3b2c0f8110d164232c11ac00ddbb2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/exporter-otlp/zipball/196f3a1dbce3b2c0f8110d164232c11ac00ddbb2", "reference": "196f3a1dbce3b2c0f8110d164232c11ac00ddbb2", "shasum": ""}, "require": {"open-telemetry/api": "^1.0", "open-telemetry/gen-otlp-protobuf": "^1.1", "open-telemetry/sdk": "^1.0", "php": "^8.1", "php-http/discovery": "^1.14"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.0.x-dev"}}, "autoload": {"files": ["_register.php"], "psr-4": {"OpenTelemetry\\Contrib\\Otlp\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "OTLP exporter for OpenTelemetry.", "keywords": ["Metrics", "exporter", "gRPC", "http", "opentelemetry", "otel", "otlp", "tracing"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-06-16T00:24:51+00:00"}, {"name": "open-telemetry/exporter-zipkin", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/exporter-zipkin.git", "reference": "05ef43d6caa125b71774f9490b0cc7ca337394a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/exporter-zipkin/zipball/05ef43d6caa125b71774f9490b0cc7ca337394a0", "reference": "05ef43d6caa125b71774f9490b0cc7ca337394a0", "shasum": ""}, "require": {"open-telemetry/api": "^1.0", "open-telemetry/sdk": "^1.0", "php": "^8.1", "php-http/async-client-implementation": "^1.0", "php-http/discovery": "^1.14", "psr/http-factory-implementation": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.0.x-dev"}}, "autoload": {"files": ["_register.php"], "psr-4": {"OpenTelemetry\\Contrib\\Zipkin\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "Zipkin exporter for OpenTelemetry PHP.", "keywords": ["contrib", "exporter", "opentelemetry", "otel", "tracing", "zipkin"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2024-10-16T12:03:20+00:00"}, {"name": "open-telemetry/gen-otlp-protobuf", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/gen-otlp-protobuf.git", "reference": "585bafddd4ae6565de154610b10a787a455c9ba0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/gen-otlp-protobuf/zipball/585bafddd4ae6565de154610b10a787a455c9ba0", "reference": "585bafddd4ae6565de154610b10a787a455c9ba0", "shasum": ""}, "require": {"google/protobuf": "^3.22 || ^4.0", "php": "^8.0"}, "suggest": {"ext-protobuf": "For better performance, when dealing with the protobuf format"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Opentelemetry\\Proto\\": "Opentelemetry/Proto/", "GPBMetadata\\Opentelemetry\\": "GPBMetadata/Opentelemetry/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "PHP protobuf files for communication with OpenTelemetry OTLP collectors/servers.", "keywords": ["Metrics", "apm", "gRPC", "logging", "opentelemetry", "otel", "otlp", "protobuf", "tracing"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-01-15T23:07:07+00:00"}, {"name": "open-telemetry/opentelemetry", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/opentelemetry-meta.git", "reference": "8165bdfddc4c08556a1cd398a9f274bdf77e03c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/opentelemetry-meta/zipball/8165bdfddc4c08556a1cd398a9f274bdf77e03c4", "reference": "8165bdfddc4c08556a1cd398a9f274bdf77e03c4", "shasum": ""}, "require": {"nyholm/psr7": "^1.5", "open-telemetry/api": "^1", "open-telemetry/context": "^1", "open-telemetry/exporter-otlp": "^1", "open-telemetry/exporter-zipkin": "^1", "open-telemetry/sdk": "^1", "symfony/http-client": "^5|^6"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "OpenTelemetry metapackage", "homepage": "https://opentelemetry.io/docs/php", "keywords": ["Metrics", "logging", "open-telemetry", "opentelemetry", "otel", "tracing"], "support": {"source": "https://github.com/opentelemetry-php/opentelemetry-meta/tree/1.0.0"}, "time": "2023-10-25T12:26:35+00:00"}, {"name": "open-telemetry/opentelemetry-auto-symfony", "version": "dev-main", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/contrib-auto-symfony.git", "reference": "0ade1e2b40de0a6349c2976c392df4ac21657acb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/contrib-auto-symfony/zipball/0ade1e2b40de0a6349c2976c392df4ac21657acb", "reference": "0ade1e2b40de0a6349c2976c392df4ac21657acb", "shasum": ""}, "require": {"ext-opentelemetry": "*", "open-telemetry/api": "^1.0", "open-telemetry/sem-conv": "^1.32", "php": "^8.1", "symfony/http-client-contracts": "*", "symfony/http-kernel": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3", "open-telemetry/opentelemetry-propagation-server-timing": "*", "open-telemetry/opentelemetry-propagation-traceresponse": "*", "open-telemetry/sdk": "^1.0", "phan/phan": "^5.0", "php-http/mock-client": "*", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5", "psalm/plugin-phpunit": "^0.19.2", "symfony/http-client": "^5.4||^6.0", "symfony/messenger": "^5.4||^6.0", "vimeo/psalm": "6.4.0"}, "suggest": {"open-telemetry/opentelemetry-propagation-server-timing": "Automatically propagate the context to the client through server-timing headers.", "open-telemetry/opentelemetry-propagation-traceresponse": "Automatically propagate the context to the client through trace-response headers."}, "default-branch": true, "type": "library", "autoload": {"files": ["_register.php"], "psr-4": {"OpenTelemetry\\Contrib\\Instrumentation\\Symfony\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "OpenTelemetry auto-instrumentation for Symfony", "homepage": "https://opentelemetry.io/docs/php", "keywords": ["instrumentation", "open-telemetry", "opentelemetry", "otel", "symfony", "tracing"], "support": {"source": "https://github.com/opentelemetry-php/contrib-auto-symfony/tree/1.0.2"}, "time": "2025-07-09T00:12:10+00:00"}, {"name": "open-telemetry/sdk", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/sdk.git", "reference": "1c0371794e4c0700afd4a9d4d8511cb5e3f78e6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/sdk/zipball/1c0371794e4c0700afd4a9d4d8511cb5e3f78e6a", "reference": "1c0371794e4c0700afd4a9d4d8511cb5e3f78e6a", "shasum": ""}, "require": {"ext-json": "*", "nyholm/psr7-server": "^1.1", "open-telemetry/api": "~1.4.0", "open-telemetry/context": "^1.0", "open-telemetry/sem-conv": "^1.0", "php": "^8.1", "php-http/discovery": "^1.14", "psr/http-client": "^1.0", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.0.1|^2.0", "psr/log": "^1.1|^2.0|^3.0", "ramsey/uuid": "^3.0 || ^4.0", "symfony/polyfill-mbstring": "^1.23", "symfony/polyfill-php82": "^1.26", "tbachert/spi": "^1.0.1"}, "suggest": {"ext-gmp": "To support unlimited number of synchronous metric readers", "ext-mbstring": "To increase performance of string operations", "open-telemetry/sdk-configuration": "File-based OpenTelemetry SDK configuration"}, "type": "library", "extra": {"spi": {"OpenTelemetry\\API\\Configuration\\ConfigEnv\\EnvComponentLoader": ["OpenTelemetry\\API\\Instrumentation\\Configuration\\General\\ConfigEnv\\EnvComponentLoaderHttpConfig", "OpenTelemetry\\API\\Instrumentation\\Configuration\\General\\ConfigEnv\\EnvComponentLoaderPeerConfig"], "OpenTelemetry\\API\\Instrumentation\\AutoInstrumentation\\HookManagerInterface": ["OpenTelemetry\\API\\Instrumentation\\AutoInstrumentation\\ExtensionHookManager"]}, "branch-alias": {"dev-main": "1.0.x-dev"}}, "autoload": {"files": ["Common/Util/functions.php", "Logs/Exporter/_register.php", "Metrics/MetricExporter/_register.php", "Propagation/_register.php", "Trace/SpanExporter/_register.php", "Common/Dev/Compatibility/_load.php", "_autoload.php"], "psr-4": {"OpenTelemetry\\SDK\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "SDK for OpenTelemetry PHP.", "keywords": ["Metrics", "apm", "logging", "opentelemetry", "otel", "sdk", "tracing"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-06-19T23:36:51+00:00"}, {"name": "open-telemetry/sem-conv", "version": "1.32.1", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/sem-conv.git", "reference": "94daa85ea61a8e2b7e1b0af6be0e875bedda7c22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/sem-conv/zipball/94daa85ea61a8e2b7e1b0af6be0e875bedda7c22", "reference": "94daa85ea61a8e2b7e1b0af6be0e875bedda7c22", "shasum": ""}, "require": {"php": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"OpenTelemetry\\SemConv\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "opentelemetry-php contributors", "homepage": "https://github.com/open-telemetry/opentelemetry-php/graphs/contributors"}], "description": "Semantic conventions for OpenTelemetry PHP.", "keywords": ["Metrics", "apm", "logging", "opentelemetry", "otel", "semantic conventions", "semconv", "tracing"], "support": {"chat": "https://app.slack.com/client/T08PSQ7BQ/C01NFPCV44V", "docs": "https://opentelemetry.io/docs/php", "issues": "https://github.com/open-telemetry/opentelemetry-php/issues", "source": "https://github.com/open-telemetry/opentelemetry-php"}, "time": "2025-06-24T02:32:27+00:00"}, {"name": "open-telemetry/symfony-sdk-bundle", "version": "0.0.26", "source": {"type": "git", "url": "https://github.com/opentelemetry-php/contrib-sdk-bundle.git", "reference": "a49a97b1cdfe200d525f445ba83b488625ba321b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentelemetry-php/contrib-sdk-bundle/zipball/a49a97b1cdfe200d525f445ba83b488625ba321b", "reference": "a49a97b1cdfe200d525f445ba83b488625ba321b", "shasum": ""}, "require": {"ext-json": "*", "open-telemetry/api": "^1", "open-telemetry/context": "^1", "open-telemetry/exporter-otlp": "^1", "open-telemetry/exporter-zipkin": "^1", "open-telemetry/sem-conv": "^1.23", "php": "^8.1", "php-http/discovery": "^1.14", "php-http/message": "^1.12"}, "require-dev": {"composer/xdebug-handler": "^3.0", "dg/bypass-finals": "^1.3", "ext-grpc": "*", "friendsofphp/php-cs-fixer": "^3.0", "google/protobuf": "^3.23", "guzzlehttp/guzzle": "^7.3", "guzzlehttp/psr7": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "kriswallsmith/buzz": "^1.2", "matthiasnoback/symfony-dependency-injection-test": "^4.3", "mikey179/vfsstream": "^1.6", "nyholm/psr7": "^1.4", "open-telemetry/dev-tools": "dev-main", "open-telemetry/sdk": "^1.1", "phan/phan": "^4.1 || ^5", "phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-symfony": "^1.1", "phpunit/phpunit": "^9.5", "psalm/plugin-phpunit": "^0.18.4", "symfony/config": "^4.4|^5.0|^6.0", "symfony/http-client": "^5.3", "symfony/http-kernel": "^4.4|^5.3|^6.0", "symfony/options-resolver": "^4.4|^5.3|^6.0", "symfony/proxy-manager-bridge": "^4.4|^5.3|^6.0", "symfony/yaml": "^4.4|^5.3|^6.0", "vimeo/psalm": "^5.0"}, "suggest": {"symfony/config": "Needed to use otel-sdk-bundle", "symfony/dependency-injection": "Needed to use otel-sdk-bundle", "symfony/options-resolver": "Needed to use otel-sdk-bundle"}, "type": "library", "autoload": {"psr-4": {"OpenTelemetry\\Contrib\\Symfony\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "OpenTelemetry Symfony integration", "support": {"source": "https://github.com/opentelemetry-php/contrib-sdk-bundle/tree/0.0.26"}, "time": "2025-01-09T00:27:12+00:00"}, {"name": "php-http/curl-client", "version": "2.3.3", "source": {"type": "git", "url": "https://github.com/php-http/curl-client.git", "reference": "f3eb48d266341afec0229a7a37a03521d3646b81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/curl-client/zipball/f3eb48d266341afec0229a7a37a03521d3646b81", "reference": "f3eb48d266341afec0229a7a37a03521d3646b81", "shasum": ""}, "require": {"ext-curl": "*", "php": "^7.4 || ^8.0", "php-http/discovery": "^1.6", "php-http/httplug": "^2.0", "php-http/message": "^1.2", "psr/http-client": "^1.0", "psr/http-factory-implementation": "^1.0", "symfony/options-resolver": "^3.4 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"guzzlehttp/psr7": "^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/client-integration-tests": "^3.0", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^7.5 || ^9.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Curl\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "m.<PERSON><PERSON><PERSON>@yandex.ru"}], "description": "PSR-18 and HTTPlug Async client with cURL", "homepage": "http://php-http.org", "keywords": ["curl", "http", "psr-18"], "support": {"issues": "https://github.com/php-http/curl-client/issues", "source": "https://github.com/php-http/curl-client/tree/2.3.3"}, "time": "2024-10-31T07:36:58+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "php-http/guzzle7-adapter", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/guzzle7-adapter.git", "reference": "03a415fde709c2f25539790fecf4d9a31bc3d0eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle7-adapter/zipball/03a415fde709c2f25539790fecf4d9a31bc3d0eb", "reference": "03a415fde709c2f25539790fecf4d9a31bc3d0eb", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "php": "^7.3 | ^8.0", "php-http/httplug": "^2.0", "psr/http-client": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"php-http/client-integration-tests": "^3.0", "php-http/message-factory": "^1.1", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^8.0|^9.3"}, "type": "library", "autoload": {"psr-4": {"Http\\Adapter\\Guzzle7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 7 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "support": {"issues": "https://github.com/php-http/guzzle7-adapter/issues", "source": "https://github.com/php-http/guzzle7-adapter/tree/1.1.0"}, "time": "2024-11-26T11:14:36+00:00"}, {"name": "php-http/httplug", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/5cad731844891a4c282f3f3e1b582c46839d22f4", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.1"}, "time": "2024-09-23T11:39:58+00:00"}, {"name": "php-http/message", "version": "1.16.2", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.2"}, "time": "2024-10-02T11:34:13+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "predis/predis", "version": "v1.1.10", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.10"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-01-05T17:46:08+00:00"}, {"name": "psr/cache", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "213f9dbc5b9bfbc4f8db86d2838dc968752ce13b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/213f9dbc5b9bfbc4f8db86d2838dc968752ce13b", "reference": "213f9dbc5b9bfbc4f8db86d2838dc968752ce13b", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/2.0.0"}, "time": "2021-02-03T23:23:37+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/344572933ad0181accbf4ba763e85a0306a8c5e2", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "time": "2025-03-22T05:38:12+00:00"}, {"name": "ramsey/uuid", "version": "4.9.0", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "4e0e23cc785f0724a0e838279a9eb03f28b092a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/4e0e23cc785f0724a0e838279a9eb03f28b092a0", "reference": "4e0e23cc785f0724a0e838279a9eb03f28b092a0", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.9.0"}, "time": "2025-06-25T14:20:11+00:00"}, {"name": "rollbar/rollbar", "version": "v1.8.1", "source": {"type": "git", "url": "https://github.com/rollbar/rollbar-php.git", "reference": "8a57ad9574d85bd818eaedfc8049fdcb16795f31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rollbar/rollbar-php/zipball/8a57ad9574d85bd818eaedfc8049fdcb16795f31", "reference": "8a57ad9574d85bd818eaedfc8049fdcb16795f31", "shasum": ""}, "require": {"ext-curl": "*", "monolog/monolog": "^1", "psr/log": "^1"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "mockery/mockery": "0.9.*", "packfire/php5.3-compat": "*", "phpmd/phpmd": "@stable", "phpunit/phpunit": "4.8.*", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"fluent/logger": "Needed to use the 'fluent' handler for fluentd support", "packfire/php5.3-compat": "for backward compatibility with PHP 5.3"}, "type": "library", "autoload": {"psr-4": {"Rollbar\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Rollbar, Inc.", "email": "<EMAIL>", "role": "Developer"}], "description": "Monitors errors and exceptions and reports them to Rollbar", "homepage": "http://github.com/rollbar/rollbar-php", "keywords": ["debugging", "errors", "exceptions", "logging", "monitoring"], "support": {"email": "<EMAIL>", "issues": "https://github.com/rollbar/rollbar-php/issues", "source": "https://github.com/rollbar/rollbar-php/tree/v1.8.1"}, "time": "2019-05-06T11:31:11+00:00"}, {"name": "segmentio/analytics-php", "version": "3.8.1", "source": {"type": "git", "url": "https://github.com/segmentio/analytics-php.git", "reference": "195f7a37fcc8cb5210119840863f0a0baa703bdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/segmentio/analytics-php/zipball/195f7a37fcc8cb5210119840863f0a0baa703bdf", "reference": "195f7a37fcc8cb5210119840863f0a0baa703bdf", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "overtrue/phplint": "^3.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": "~9.5", "roave/security-advisories": "dev-latest", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.6"}, "suggest": {"ext-curl": "For using the curl HTTP client", "ext-zlib": "For using compression"}, "bin": ["bin/analytics"], "type": "library", "autoload": {"psr-4": {"Segment\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Segment.io <<EMAIL>>", "homepage": "https://segment.com/"}], "description": "Segment Analytics PHP Library", "homepage": "https://segment.com/libraries/php", "keywords": ["analytics", "analytics.js", "segment", "segmentio"], "support": {"issues": "https://github.com/segmentio/analytics-php/issues", "source": "https://github.com/segmentio/analytics-php/tree/3.8.1"}, "time": "2025-01-27T17:32:51+00:00"}, {"name": "solarium/solarium", "version": "6.3.7", "source": {"type": "git", "url": "https://github.com/solariumphp/solarium.git", "reference": "4f3cb22a4d98df2c8d5a621ad1beb93fad7b94c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/solariumphp/solarium/zipball/4f3cb22a4d98df2c8d5a621ad1beb93fad7b94c5", "reference": "4f3cb22a4d98df2c8d5a621ad1beb93fad7b94c5", "shasum": ""}, "require": {"composer-runtime-api": ">=2.0", "ext-json": "*", "halaxa/json-machine": "^1.1", "php": "^8.1", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "symfony/event-dispatcher-contracts": "^2.0 || ^3.0"}, "require-dev": {"escapestudios/symfony2-coding-standard": "^3.11", "ext-curl": "*", "ext-iconv": "*", "nyholm/psr7": "^1.8", "php-http/guzzle7-adapter": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5", "rawr/phpunit-data-provider": "^3.3", "roave/security-advisories": "dev-master", "symfony/event-dispatcher": "^5.0 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Solarium\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "See GitHub contributors", "homepage": "https://github.com/solariumphp/solarium/contributors"}], "description": "PHP Solr client", "homepage": "http://www.solarium-project.org", "keywords": ["php", "search", "solr"], "support": {"issues": "https://github.com/solariumphp/solarium/issues", "source": "https://github.com/solariumphp/solarium/tree/6.3.7"}, "time": "2025-02-20T10:29:08+00:00"}, {"name": "sparefoot/authorization", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/authorization.git", "reference": "c553fb500b30b6b21439110ad036760d2fe556d6"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fauthorization/repository/archive.zip?sha=c553fb500b30b6b21439110ad036760d2fe556d6", "reference": "c553fb500b30b6b21439110ad036760d2fe556d6", "shasum": ""}, "require": {"bshaffer/oauth2-server-httpfoundation-bridge": "^1.7", "bshaffer/oauth2-server-php": "^1.13", "datadog/dd-trace": "0.97.0", "php": "^8.2", "sparefoot/sf_service_bundle": "dev-php8", "symfony/serializer": "5.4.*", "symfony/string": "^5.4", "symfony/translation-contracts": "^2.5", "symfony/twig-bridge": "5.4.*"}, "conflict": {"symfony/symfony": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.22", "phpunit/phpunit": "10.4.*", "symfony/browser-kit": "*"}, "default-branch": true, "type": "library", "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}}, "autoload": {"psr-4": {"Sparefoot\\Authorization\\": "src/"}}, "autoload-dev": {"psr-4": {"Sparefoot\\Authorization\\Tests\\": "tests/"}}, "scripts": {"post-update-cmd": ["bash vendor/sparefoot/sf_service_bundle/Resources/bin/setup.sh"], "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}, "description": "Service for SpareFoot Authorization", "support": {"source": "https://gitlab.com/storable/sparefoot/authorization/-/tree/master", "issues": "https://gitlab.com/storable/sparefoot/authorization/-/issues"}, "time": "2025-04-17T11:31:33+05:30"}, {"name": "sparefoot/emails_service_client", "version": "dev-php8", "source": {"type": "git", "url": "**************:storable/sparefoot/emails_service_client.git", "reference": "1894006036f7afd476b30d6209e596c0e4f1ac0e"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Femails_service_client/repository/archive.zip?sha=1894006036f7afd476b30d6209e596c0e4f1ac0e", "reference": "1894006036f7afd476b30d6209e596c0e4f1ac0e", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~7.0", "php": ">=5.5.0"}, "type": "library", "autoload": {"psr-4": {"Sparefoot\\EmailsServiceClient\\": "src/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "SpareFoot emails_service_client library", "homepage": "https://gitlab.com/storable/sparefoot/emails_service_client", "support": {"source": "https://gitlab.com/storable/sparefoot/emails_service_client/-/tree/php8", "issues": "https://gitlab.com/storable/sparefoot/emails_service_client/-/issues"}, "time": "2025-05-28T15:12:34-05:00"}, {"name": "sparefoot/error-logging", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/error_logger.git", "reference": "24c9ed1171089c76652e06dc60f3debbc24c2e7d"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Ferror_logger/repository/archive.zip?sha=24c9ed1171089c76652e06dc60f3debbc24c2e7d", "reference": "24c9ed1171089c76652e06dc60f3debbc24c2e7d", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "4.7.*", "phpunit/phpunit-selenium": ">=1.2"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"SpareFoot\\ErrorLogging\\": "src/", "SpareFoot\\ErrorLogging\\Config\\": "config/", "SpareFoot\\ErrorLogging\\Lib\\": "lib/"}}, "license": ["proprietary"], "authors": [{"name": "Contributors", "email": "<EMAIL>"}], "description": "<PERSON>refoot Error Logging Library", "support": {"source": "https://gitlab.com/storable/sparefoot/error_logger/-/tree/master", "issues": "https://gitlab.com/storable/sparefoot/error_logger/-/issues"}, "time": "2020-11-17T22:09:05+00:00"}, {"name": "sparefoot/genesis", "version": "dev-php8", "source": {"type": "git", "url": "**************:storable/sparefoot/genesis.git", "reference": "ef6f75b0987dfaf8aaafa418c3f36df649557be3"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fgenesis/repository/archive.zip?sha=ef6f75b0987dfaf8aaafa418c3f36df649557be3", "reference": "ef6f75b0987dfaf8aaafa418c3f36df649557be3", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.22", "developerforce/force.com-toolkit-for-php": "^27.0", "ext-ftp": "*", "ext-json": "*", "ext-soap": "*", "geocoder-php/bing-maps-provider": "^4.3", "geocoder-php/google-maps-provider": "^4.7", "geocoder-php/mapquest-provider": "^4.3", "guzzlehttp/guzzle": "7.x", "guzzlehttp/oauth-subscriber": "0.6.*", "http-interop/http-factory-guzzle": "^1.2", "kamermans/guzzle-oauth2-subscriber": "~1.0", "nategood/httpful": "0.2.11", "php": "^8.2", "php-http/curl-client": "^2.3.0", "php-http/guzzle7-adapter": "^1.0", "solarium/solarium": "^6.2.8", "sparefoot/emails_service_client": "dev-php8", "sparefoot/pillar": "dev-master", "twilio/sdk": "^6.44", "willdurand/geocoder": "^4.6"}, "require-dev": {"brianium/paratest": "^7.1.4", "phpunit/phpunit": "^10.1.2"}, "type": "library", "autoload": {"psr-4": {"GenesisTests\\": "Tests/"}, "classmap": ["src", "lib/centaur-php"]}, "license": ["proprietary"], "authors": [{"name": "Contributors", "email": "<EMAIL>"}], "description": "Sparefoot monolith service libraries", "support": {"source": "https://gitlab.com/storable/sparefoot/genesis/-/tree/lt-sf-11925-fixing-with-pita-php8", "issues": "https://gitlab.com/storable/sparefoot/genesis/-/issues"}, "time": "2025-07-07T15:30:47+07:00"}, {"name": "sparefoot/phlow_client", "version": "dev-php8", "source": {"type": "git", "url": "**************:storable/sparefoot/phlow_client.git", "reference": "fb5135b6e098114a61746e0998d22965a7a6b79f"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fphlow_client/repository/archive.zip?sha=fb5135b6e098114a61746e0998d22965a7a6b79f", "reference": "fb5135b6e098114a61746e0998d22965a7a6b79f", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "10.4.*"}, "type": "library", "autoload": {"psr-4": {"Sparefoot\\PhlowClient\\": "src/"}}, "autoload-dev": {"psr-4": {"Sparefoot\\PhlowClientTest\\": "test/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "SpareFoot phlow_client skeleton", "homepage": "http://stash.sparefoot.com/cs/phlow_client", "support": {"source": "https://gitlab.com/storable/sparefoot/phlow_client/-/tree/php8", "issues": "https://gitlab.com/storable/sparefoot/phlow_client/-/issues"}, "time": "2024-09-09T20:47:00+05:30"}, {"name": "sparefoot/pillar", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/pillar_lib.git", "reference": "19b107f45ee6e42d8aaa2a0aa068d781e3348605"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fpillar_lib/repository/archive.zip?sha=19b107f45ee6e42d8aaa2a0aa068d781e3348605", "reference": "19b107f45ee6e42d8aaa2a0aa068d781e3348605", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.7"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"Pillar\\": "src/", "PillarTest\\": "test/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PDO bootstrapper for using pillar data", "support": {"source": "https://gitlab.com/storable/sparefoot/pillar_lib/-/tree/master", "issues": "https://gitlab.com/storable/sparefoot/pillar_lib/-/issues"}, "time": "2020-11-11T18:09:56+00:00"}, {"name": "sparefoot/reservation_rules", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/reservation_rules.git", "reference": "70718e5f0d30d8c7bbb518e154ebef44019c1da5"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Freservation_rules/repository/archive.zip?sha=70718e5f0d30d8c7bbb518e154ebef44019c1da5", "reference": "70718e5f0d30d8c7bbb518e154ebef44019c1da5", "shasum": ""}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"Sparefoot\\ReservationRules\\": "src/ReservationRules"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A place for reservation rules to live", "support": {"source": "https://gitlab.com/storable/sparefoot/reservation_rules/-/tree/master", "issues": "https://gitlab.com/storable/sparefoot/reservation_rules/-/issues"}, "time": "2020-12-10T16:31:00+00:00"}, {"name": "sparefoot/salesforce_client", "version": "dev-php8", "source": {"type": "git", "url": "**************:storable/sparefoot/salesforce_client.git", "reference": "889755d15417ad701a7aa3913abf525208dfb822"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fsalesforce_client/repository/archive.zip?sha=889755d15417ad701a7aa3913abf525208dfb822", "reference": "889755d15417ad701a7aa3913abf525208dfb822", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^7.0", "kamermans/guzzle-oauth2-subscriber": "^1.0", "php": "^8.2"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "autoload": {"psr-4": {"Sparefoot\\SalesForce\\": "SalesForce/"}}, "autoload-dev": {"psr-4": {"Sparefoot\\SalesForce\\Tests\\": "tests/"}}, "support": {"source": "https://gitlab.com/storable/sparefoot/salesforce_client/-/tree/php8", "issues": "https://gitlab.com/storable/sparefoot/salesforce_client/-/issues"}, "time": "2023-10-04T17:14:42-05:00"}, {"name": "sparefoot/sf_service_bundle", "version": "dev-php8", "source": {"type": "git", "url": "**************:storable/sparefoot/sf_service_bundle.git", "reference": "7a0d8fd5683b1a75c86d27c8368be10d9ab673cb"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fsf_service_bundle/repository/archive.zip?sha=7a0d8fd5683b1a75c86d27c8368be10d9ab673cb", "reference": "7a0d8fd5683b1a75c86d27c8368be10d9ab673cb", "shasum": ""}, "require": {"aws/aws-php-sns-message-validator": "^1.6", "aws/aws-sdk-php": "^3.87", "ext-json": "*", "fluent/logger": "^1.0", "guzzlehttp/guzzle": "7.5.1", "open-telemetry/exporter-otlp": "^1.1", "open-telemetry/opentelemetry": "^1.0", "open-telemetry/opentelemetry-auto-symfony": "dev-main", "open-telemetry/sdk": "^1.1", "open-telemetry/symfony-sdk-bundle": "^0.0.26", "php": "^8.2", "php-http/guzzle7-adapter": "^1.1", "php-http/message-factory": "^1.1", "predis/predis": "^1.1", "psr/simple-cache": "^1.0", "segmentio/analytics-php": "^3.6", "symfony/browser-kit": "~5.4", "symfony/cache": "~5.4", "symfony/config": "~5.4", "symfony/console": "~5.4", "symfony/dotenv": "~5.4", "symfony/flex": "^2.3", "symfony/framework-bundle": "~5.4", "symfony/http-client": "~5.4", "symfony/http-foundation": "~5.4", "symfony/messenger": "~5.4", "symfony/monolog-bundle": "~3.8", "symfony/phpunit-bridge": "~5.4", "symfony/proxy-manager-bridge": "~5.4", "symfony/security-bundle": "~5.4", "symfony/serializer": "~5.4", "symfony/yaml": "~5.4"}, "require-dev": {"phpunit/phpunit": "^10.1.2", "psr/log": "^1.0", "symfony/css-selector": "~5.4"}, "type": "bundle", "extra": {"symfony": {"allow-contrib": false, "require": "~5.4"}}, "autoload": {"psr-4": {"Sparefoot\\ServiceBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "autoload-dev": {"psr-4": {"Sparefoot\\ServiceBundle\\Tests\\": "tests/"}}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}, "description": "Provides common utilities such as analytics & caching for php projects.", "support": {"source": "https://gitlab.com/storable/sparefoot/sf_service_bundle/-/tree/php8", "issues": "https://gitlab.com/storable/sparefoot/sf_service_bundle/-/issues"}, "time": "2025-02-28T19:21:28+00:00"}, {"name": "symfony/amqp-messenger", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/amqp-messenger.git", "reference": "822ad5f425ef362580273a175c45aa765220fe73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/amqp-messenger/zipball/822ad5f425ef362580273a175c45aa765220fe73", "reference": "822ad5f425ef362580273a175c45aa765220fe73", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/messenger": "^5.3|^6.0"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Amqp\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony AMQP extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/amqp-messenger/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/asset", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "b7a18eaff1d717c321b4f13403413f8815bf9cb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/b7a18eaff1d717c321b4f13403413f8815bf9cb0", "reference": "b7a18eaff1d717c321b4f13403413f8815bf9cb0", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/http-foundation": "<5.3"}, "require-dev": {"symfony/http-client": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^5.3|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-22T13:05:35+00:00"}, {"name": "symfony/browser-kit", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "03cce39764429e07fbab9b989a1182a24578341d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/03cce39764429e07fbab9b989a1182a24578341d", "reference": "03cce39764429e07fbab9b989a1182a24578341d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/dom-crawler": "^4.4|^5.0|^6.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/css-selector": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-22T13:05:35+00:00"}, {"name": "symfony/cache", "version": "v5.4.46", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "0fe08ee32cec2748fbfea10c52d3ee02049e0f6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/0fe08ee32cec2748fbfea10c52d3ee02049e0f6b", "reference": "0fe08ee32cec2748fbfea10c52d3ee02049e0f6b", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<4.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6|^2.0", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v5.4.46"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-04T11:43:55+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "517c3a3619dadfa6952c4651767fcadffb4df65e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/517c3a3619dadfa6952c4651767fcadffb4df65e", "reference": "517c3a3619dadfa6952c4651767fcadffb4df65e", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0|^3.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/config", "version": "v5.4.46", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "977c88a02d7d3f16904a81907531b19666a08e78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/977c88a02d7d3f16904a81907531b19666a08e78", "reference": "977c88a02d7d3f16904a81907531b19666a08e78", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<4.4"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v5.4.46"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-30T07:58:02+00:00"}, {"name": "symfony/console", "version": "v5.4.47", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed", "reference": "c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-06T11:30:55+00:00"}, {"name": "symfony/css-selector", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "4f7f3c35fba88146b56d0025d20ace3f3901f097"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/4f7f3c35fba88146b56d0025d20ace3f3901f097", "reference": "4f7f3c35fba88146b56d0025d20ace3f3901f097", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/dependency-injection", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "e5ca16dee39ef7d63e552ff0bf0a2526a1142c92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/e5ca16dee39ef7d63e552ff0bf0a2526a1142c92", "reference": "e5ca16dee39ef7d63e552ff0bf0a2526a1142c92", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1.1", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<5.3", "symfony/finder": "<4.4", "symfony/proxy-manager-bridge": "<4.4", "symfony/yaml": "<4.4.26"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0|2.0"}, "require-dev": {"symfony/config": "^5.3|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4.26|^5.0|^6.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-20T10:51:57+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/doctrine-messenger", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-messenger.git", "reference": "3f5a6e1876fbf57e836ba0a02eb0a636e08c0d96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-messenger/zipball/3f5a6e1876fbf57e836ba0a02eb0a636e08c0d96", "reference": "3f5a6e1876fbf57e836ba0a02eb0a636e08c0d96", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/messenger": "^5.1|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"doctrine/dbal": "<2.13", "doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/dbal": "^2.13|^3|^4", "doctrine/persistence": "^1.3|^2|^3", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-messenger/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/dom-crawler", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "b57df76f4757a9a8dfbb57ba48d7780cc20776c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/b57df76f4757a9a8dfbb57ba48d7780cc20776c6", "reference": "b57df76f4757a9a8dfbb57ba48d7780cc20776c6", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T14:36:38+00:00"}, {"name": "symfony/dotenv", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "08013403089c8a126c968179179b817a552841ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/08013403089c8a126c968179179b817a552841ab", "reference": "08013403089c8a126c968179179b817a552841ab", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3"}, "require-dev": {"symfony/console": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-27T09:33:00+00:00"}, {"name": "symfony/error-handler", "version": "v5.4.46", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "d19ede7a2cafb386be9486c580649d0f9e3d0363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/d19ede7a2cafb386be9486c580649d0f9e3d0363", "reference": "d19ede7a2cafb386be9486c580649d0f9e3d0363", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "require-dev": {"symfony/deprecation-contracts": "^2.1|^3", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v5.4.46"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-05T14:17:06+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/72982eb416f61003e9bb6e91f8b3213600dcf9e9", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "57c8294ed37d4a055b77057827c67f9558c95c54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/57c8294ed37d4a055b77057827c67f9558c95c54", "reference": "57c8294ed37d4a055b77057827c67f9558c95c54", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/process": "^5.4|^6.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-22T13:05:35+00:00"}, {"name": "symfony/finder", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "63741784cd7b9967975eec610b256eed3ede022b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/63741784cd7b9967975eec610b256eed3ede022b", "reference": "63741784cd7b9967975eec610b256eed3ede022b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-28T13:32:08+00:00"}, {"name": "symfony/flex", "version": "v2.8.1", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "423c36e369361003dc31ef11c5f15fb589e52c01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/423c36e369361003dc31ef11c5f15fb589e52c01", "reference": "423c36e369361003dc31ef11c5f15fb589e52c01", "shasum": ""}, "require": {"composer-plugin-api": "^2.1", "php": ">=8.0"}, "conflict": {"composer/semver": "<1.7.2"}, "require-dev": {"composer/composer": "^2.1", "symfony/dotenv": "^5.4|^6.0", "symfony/filesystem": "^5.4|^6.0", "symfony/phpunit-bridge": "^5.4|^6.0", "symfony/process": "^5.4|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v2.8.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-05T07:45:19+00:00"}, {"name": "symfony/framework-bundle", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "3d70f14176422d4d8ee400b6acae4e21f7c25ca2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/3d70f14176422d4d8ee400b6acae4e21f7c25ca2", "reference": "3d70f14176422d4d8ee400b6acae4e21f7c25ca2", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=7.2.5", "symfony/cache": "^5.2|^6.0", "symfony/config": "^5.3|^6.0", "symfony/dependency-injection": "^5.4.44|^6.0.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/error-handler": "^4.4.1|^5.0.1|^6.0", "symfony/event-dispatcher": "^5.1|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^5.4.24|^6.2.11", "symfony/http-kernel": "^5.4|^6.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/routing": "^5.3|^6.0"}, "conflict": {"doctrine/annotations": "<1.13.1", "doctrine/cache": "<1.11", "doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/asset": "<5.3", "symfony/console": "<5.2.5|>=7.0", "symfony/dom-crawler": "<4.4", "symfony/dotenv": "<5.1", "symfony/form": "<5.2", "symfony/http-client": "<4.4", "symfony/lock": "<4.4", "symfony/mailer": "<5.2", "symfony/messenger": "<5.4", "symfony/mime": "<4.4", "symfony/property-access": "<5.3", "symfony/property-info": "<4.4", "symfony/runtime": "<5.4.45|>=6.0,<6.4.13|>=7.0,<7.1.6", "symfony/security-csrf": "<5.3", "symfony/serializer": "<5.2", "symfony/service-contracts": ">=3.0", "symfony/stopwatch": "<4.4", "symfony/translation": "<5.3", "symfony/twig-bridge": "<4.4", "symfony/twig-bundle": "<4.4", "symfony/validator": "<5.3.11", "symfony/web-profiler-bundle": "<4.4", "symfony/workflow": "<5.2"}, "require-dev": {"doctrine/annotations": "^1.13.1|^2", "doctrine/cache": "^1.11|^2.0", "doctrine/persistence": "^1.3|^2|^3", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^5.3|^6.0", "symfony/browser-kit": "^5.4|^6.0", "symfony/console": "^5.4.9|^6.0.9", "symfony/css-selector": "^4.4|^5.0|^6.0", "symfony/dom-crawler": "^4.4.30|^5.3.7|^6.0", "symfony/dotenv": "^5.1|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/form": "^5.2|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/mailer": "^5.2|^6.0", "symfony/messenger": "^5.4|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/notifier": "^5.4|^6.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/property-info": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0", "symfony/security-bundle": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/string": "^5.0|^6.0", "symfony/translation": "^5.3|^6.0", "symfony/twig-bundle": "^4.4|^5.0|^6.0", "symfony/validator": "^5.3.11|^6.0", "symfony/web-link": "^4.4|^5.0|^6.0", "symfony/workflow": "^5.2|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "twig/twig": "^2.10|^3.0.4"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-22T13:05:35+00:00"}, {"name": "symfony/http-client", "version": "v5.4.49", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "d77d8e212cde7b5c4a64142bf431522f19487c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/d77d8e212cde7b5c4a64142bf431522f19487c28", "reference": "d77d8e212cde7b5c4a64142bf431522f19487c28", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.5.4", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.4"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-28T08:37:04+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.5", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "48ef1d0a082885877b664332b9427662065a360c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/48ef1d0a082885877b664332b9427662065a360c", "reference": "48ef1d0a082885877b664332b9427662065a360c", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-28T08:37:04+00:00"}, {"name": "symfony/http-foundation", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "3f38b8af283b830e1363acd79e5bc3412d055341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/3f38b8af283b830e1363acd79e5bc3412d055341", "reference": "3f38b8af283b830e1363acd79e5bc3412d055341", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "^1.0|^2.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T18:58:02+00:00"}, {"name": "symfony/http-kernel", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "c2dbfc92b851404567160d1ecf3fb7d9b7bde9b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/c2dbfc92b851404567160d1ecf3fb7d9b7bde9b0", "reference": "c2dbfc92b851404567160d1ecf3fb7d9b7bde9b0", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^5.0|^6.0", "symfony/http-foundation": "^5.4.21|^6.2.7", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.0", "symfony/config": "<5.0", "symfony/console": "<4.4", "symfony/dependency-injection": "<5.3", "symfony/doctrine-bridge": "<5.0", "symfony/form": "<5.0", "symfony/http-client": "<5.0", "symfony/mailer": "<5.0", "symfony/messenger": "<5.0", "symfony/translation": "<5.0", "symfony/twig-bridge": "<5.0", "symfony/validator": "<5.0", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0", "symfony/config": "^5.0|^6.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/css-selector": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.3|^6.0", "symfony/dom-crawler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2|^3", "symfony/process": "^4.4|^5.0|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/translation": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2|^3", "symfony/var-dumper": "^4.4.31|^5.4", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-27T12:43:17+00:00"}, {"name": "symfony/messenger", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "c21d463ba813a3fe9833f46114310fac99bd66e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/c21d463ba813a3fe9833f46114310fac99bd66e0", "reference": "c21d463ba813a3fe9833f46114310fac99bd66e0", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/amqp-messenger": "^5.1|^6.0", "symfony/deprecation-contracts": "^2.1|^3", "symfony/doctrine-messenger": "^5.1|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/redis-messenger": "^5.1|^6.0"}, "conflict": {"symfony/event-dispatcher": "<4.4", "symfony/framework-bundle": "<4.4", "symfony/http-kernel": "<4.4", "symfony/serializer": "<5.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.3|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/serializer": "^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/validator": "^4.4|^5.0|^6.0"}, "suggest": {"enqueue/messenger-adapter": "For using the php-enqueue library as a transport."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps applications send and receive messages to/from other applications or via message queues", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/messenger/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/monolog-bridge", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "cf7d75d4d64a41fbb1c0e92301bec404134fa84b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/cf7d75d4d64a41fbb1c0e92301bec404134fa84b", "reference": "cf7d75d4d64a41fbb1c0e92301bec404134fa84b", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1|^2", "php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-kernel": "^5.3|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/console": "<4.4", "symfony/http-foundation": "<5.3"}, "require-dev": {"symfony/console": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/mailer": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/security-core": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Monolog with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-10T06:37:45+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.10.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1 || ^2.0 || ^3.0", "php": ">=7.2.5", "symfony/config": "^5.4 || ^6.0 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/http-kernel": "^5.4 || ^6.0 || ^7.0", "symfony/monolog-bridge": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/phpunit-bridge": "^6.3 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "https://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-06T17:08:13+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6", "reference": "74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/password-hasher", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/password-hasher.git", "reference": "6c5993b24505f98b90ca4896448012bbec54c7c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/password-hasher/zipball/6c5993b24505f98b90ca4896448012bbec54c7c8", "reference": "6c5993b24505f98b90ca4896448012bbec54c7c8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/security-core": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0", "symfony/security-core": "^5.3|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PasswordHasher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides password hashing utilities", "homepage": "https://symfony.com", "keywords": ["hashing", "password"], "support": {"source": "https://github.com/symfony/password-hasher/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "c6839a192ac526a7905980552d5997ea7f738eb2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/c6839a192ac526a7905980552d5997ea7f738eb2", "reference": "c6839a192ac526a7905980552d5997ea7f738eb2", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"phpunit/phpunit": "<7.5|9.1.2"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/error-handler": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"thanks": {"url": "https://github.com/sebastian<PERSON>mann/phpunit", "name": "phpunit/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/", "/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides utilities for PHPUnit, especially user deprecation notices management", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/phpunit-bridge/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-10T21:17:27+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php82", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php82.git", "reference": "5d2ed36f7734637dacc025f179698031951b1692"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php82/zipball/5d2ed36f7734637dacc025f179698031951b1692", "reference": "5d2ed36f7734637dacc025f179698031951b1692", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php82\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php82/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/property-access", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "111e7ed617509f1a9139686055d234aad6e388e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/111e7ed617509f1a9139686055d234aad6e388e0", "reference": "111e7ed617509f1a9139686055d234aad6e388e0", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/property-info": "^5.2|^6.0"}, "require-dev": {"symfony/cache": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/property-info", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "a0396295ad585f95fccd690bc6a281e5bd303902"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/a0396295ad585f95fccd690bc6a281e5bd303902", "reference": "a0396295ad585f95fccd690bc6a281e5bd303902", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/string": "^5.1|^6.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4|^2", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-25T16:14:41+00:00"}, {"name": "symfony/proxy-manager-bridge", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/proxy-manager-bridge.git", "reference": "e96cd37f3de0b75ff32f6b79c180ba77c4037eec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/proxy-manager-bridge/zipball/e96cd37f3de0b75ff32f6b79c180ba77c4037eec", "reference": "e96cd37f3de0b75ff32f6b79c180ba77c4037eec", "shasum": ""}, "require": {"friendsofphp/proxy-manager-lts": "^1.0.2", "php": ">=7.2.5", "symfony/dependency-injection": "^5.0|^6.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/config": "^4.4|^5.0|^6.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\ProxyManager\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for ProxyManager with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/proxy-manager-bridge/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/redis-messenger", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/redis-messenger.git", "reference": "a097e8c6529a7179a732161bd5368629c6319899"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/redis-messenger/zipball/a097e8c6529a7179a732161bd5368629c6319899", "reference": "a097e8c6529a7179a732161bd5368629c6319899", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/messenger": "^5.1|^6.0"}, "require-dev": {"symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Redis\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Redis extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/redis-messenger/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T13:58:00+00:00"}, {"name": "symfony/routing", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "dd08c19879a9b37ff14fd30dcbdf99a4cf045db1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/dd08c19879a9b37ff14fd30dcbdf99a4cf045db1", "reference": "dd08c19879a9b37ff14fd30dcbdf99a4cf045db1", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<5.3", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^5.3|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-12T18:20:21+00:00"}, {"name": "symfony/security-bundle", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "d6081d1b9118f944df90bb77444a8617eba01542"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/d6081d1b9118f944df90bb77444a8617eba01542", "reference": "d6081d1b9118f944df90bb77444a8617eba01542", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=7.2.5", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.4.43|^6.4.11", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher": "^5.1|^6.0", "symfony/http-foundation": "^5.3|^6.0", "symfony/http-kernel": "^5.3|^6.0", "symfony/password-hasher": "^5.3|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/security-core": "^5.4|^6.0", "symfony/security-csrf": "^4.4|^5.0|^6.0", "symfony/security-guard": "^5.3", "symfony/security-http": "^5.4.30|^6.3.6", "symfony/service-contracts": "^1.10|^2|^3"}, "conflict": {"symfony/browser-kit": "<4.4", "symfony/console": "<4.4", "symfony/framework-bundle": "<4.4", "symfony/ldap": "<5.1", "symfony/twig-bundle": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4|^2", "symfony/asset": "^4.4|^5.0|^6.0", "symfony/browser-kit": "^4.4|^5.0|^6.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/css-selector": "^4.4|^5.0|^6.0", "symfony/dom-crawler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/form": "^4.4|^5.0|^6.0", "symfony/framework-bundle": "^5.3|^6.0", "symfony/ldap": "^5.3|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0", "symfony/translation": "^4.4|^5.0|^6.0", "symfony/twig-bridge": "^4.4|^5.0|^6.0", "symfony/twig-bundle": "^4.4|^5.0|^6.0", "symfony/validator": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "twig/twig": "^2.13|^3.0.4"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Security component into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-bundle/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/security-core", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "cca947b1a74bdbc21c4d6288a4abb938d9a7eaba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/cca947b1a74bdbc21c4d6288a4abb938d9a7eaba", "reference": "cca947b1a74bdbc21c4d6288a4abb938d9a7eaba", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^1.1|^2|^3", "symfony/password-hasher": "^5.3|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1.6|^2|^3"}, "conflict": {"symfony/event-dispatcher": "<4.4", "symfony/http-foundation": "<5.3", "symfony/ldap": "<4.4", "symfony/security-guard": "<4.4", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3", "symfony/validator": "<5.2"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.0|^2.0", "psr/log": "^1|^2|^3", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^5.3|^6.0", "symfony/ldap": "^4.4|^5.0|^6.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3", "symfony/validator": "^5.2|^6.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/event-dispatcher": "", "symfony/expression-language": "For using the expression voter", "symfony/http-foundation": "", "symfony/ldap": "For using LDAP integration", "symfony/validator": "For using the user password constraint"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-core/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-27T08:58:20+00:00"}, {"name": "symfony/security-csrf", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "28dcafc3220f12264bb2aabe2389a2163458c1f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/28dcafc3220f12264bb2aabe2389a2163458c1f4", "reference": "28dcafc3220f12264bb2aabe2389a2163458c1f4", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/security-core": "^4.4|^5.0|^6.0"}, "conflict": {"symfony/http-foundation": "<5.3"}, "require-dev": {"symfony/http-foundation": "^5.3|^6.0"}, "suggest": {"symfony/http-foundation": "For using the class SessionTokenStorage."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-csrf/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/security-guard", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/security-guard.git", "reference": "f3da3dbec38aaedaf287ffeb4e3a90994af37faa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-guard/zipball/f3da3dbec38aaedaf287ffeb4e3a90994af37faa", "reference": "f3da3dbec38aaedaf287ffeb4e3a90994af37faa", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.15", "symfony/security-core": "^5.0", "symfony/security-http": "^5.3"}, "require-dev": {"psr/log": "^1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Guard\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Guard", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-guard/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/security-http", "version": "v5.4.47", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "cde02b002e0447075430e6a84482e38f2fd9268d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/cde02b002e0447075430e6a84482e38f2fd9268d", "reference": "cde02b002e0447075430e6a84482e38f2fd9268d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-foundation": "^5.3|^6.0", "symfony/http-kernel": "^5.3|^6.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/security-core": "^5.4.19|~6.0.19|~6.1.11|^6.2.5", "symfony/service-contracts": "^1.10|^2|^3"}, "conflict": {"symfony/event-dispatcher": "<4.3", "symfony/security-bundle": "<5.3", "symfony/security-csrf": "<4.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/security-csrf": "^4.4|^5.0|^6.0", "symfony/translation": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/security-csrf": "For using tokens to protect authentication/logout attempts"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-http/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-07T14:12:41+00:00"}, {"name": "symfony/serializer", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "460c5df9fb6c39d10d5b7f386e4feae4b6370221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/460c5df9fb6c39d10d5b7f386e4feae4b6370221", "reference": "460c5df9fb6c39d10d5b7f386e4feae4b6370221", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4.24|>=6,<6.2.11", "symfony/uid": "<5.3", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/form": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/property-access": "^5.4.26|^6.3", "symfony/property-info": "^5.4.24|^6.2.11", "symfony/uid": "^5.3|^6.0", "symfony/validator": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0", "symfony/var-exporter": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/mime": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/var-exporter": "For using the metadata compiler.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f37b419f7aea2e9abf10abd261832cace12e3300", "reference": "f37b419f7aea2e9abf10abd261832cace12e3300", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/string", "version": "v5.4.47", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "136ca7d72f72b599f2631aca474a4f8e26719799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/136ca7d72f72b599f2631aca474a4f8e26719799", "reference": "136ca7d72f72b599f2631aca474a4f8e26719799", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-10T20:33:58+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "450d4172653f38818657022252f9d81be89ee9a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/450d4172653f38818657022252f9d81be89ee9a8", "reference": "450d4172653f38818657022252f9d81be89ee9a8", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/twig-bridge", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "853a0c9aa40123a9feeb335c865b659d94e49e5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/853a0c9aa40123a9feeb335c865b659d94e49e5d", "reference": "853a0c9aa40123a9feeb335c865b659d94e49e5d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1|^2|^3", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<5.3", "symfony/form": "<5.4.21|>=6,<6.2.7", "symfony/http-foundation": "<5.3", "symfony/http-kernel": "<4.4", "symfony/translation": "<5.2", "symfony/workflow": "<5.2"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "egulias/email-validator": "^2.1.10|^3|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^4.4|^5.0|^6.0", "symfony/console": "^5.3|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/form": "^5.4.21|^6.2.7", "symfony/http-foundation": "^5.3|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/mime": "^5.2|^6.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^4.4|^5.0|^6.0", "symfony/security-csrf": "^4.4|^5.0|^6.0", "symfony/security-http": "^4.4|^5.0|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/translation": "^5.2|^6.0", "symfony/web-link": "^4.4|^5.0|^6.0", "symfony/workflow": "^5.2|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security-core": "For using the SecurityExtension", "symfony/security-csrf": "For using the CsrfExtension", "symfony/security-http": "For using the LogoutUrlExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-22T08:19:51+00:00"}, {"name": "symfony/twig-bundle", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "e1ca56e1dc7791eb19f0aff71d3d94e6a91cc8f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/e1ca56e1dc7791eb19f0aff71d3d94e6a91cc8f9", "reference": "e1ca56e1dc7791eb19f0aff71d3d94e6a91cc8f9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/config": "^4.4|^5.0|^6.0", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/twig-bridge": "^5.3|^6.0", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"symfony/dependency-injection": "<5.3", "symfony/framework-bundle": "<5.0", "symfony/service-contracts": ">=3.0", "symfony/translation": "<5.0"}, "require-dev": {"doctrine/annotations": "^1.10.4|^2", "doctrine/cache": "^1.0|^2.0", "symfony/asset": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.3|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/form": "^4.4|^5.0|^6.0", "symfony/framework-bundle": "^5.0|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/translation": "^5.0|^6.0", "symfony/web-link": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/42f18f170aa86d612c3559cfb3bd11a375df32c8", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-08T15:21:10+00:00"}, {"name": "symfony/var-exporter", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "f28cf841f5654955c9f88ceaf4b9dc29571988a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/f28cf841f5654955c9f88ceaf4b9dc29571988a9", "reference": "f28cf841f5654955c9f88ceaf4b9dc29571988a9", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-14T13:00:13+00:00"}, {"name": "symfony/yaml", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/a454d47278cc16a5db371fe73ae66a78a633371e", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "tbachert/spi", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/Nevay/spi.git", "reference": "e7078767866d0a9e0f91d3f9d42a832df5e39002"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nevay/spi/zipball/e7078767866d0a9e0f91d3f9d42a832df5e39002", "reference": "e7078767866d0a9e0f91d3f9d42a832df5e39002", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "composer/semver": "^1.0 || ^2.0 || ^3.0", "php": "^8.1"}, "require-dev": {"composer/composer": "^2.0", "infection/infection": "^0.27.9", "phpunit/phpunit": "^10.5", "psalm/phar": "^5.18"}, "type": "composer-plugin", "extra": {"class": "Nevay\\SPI\\Composer\\Plugin", "branch-alias": {"dev-main": "1.0.x-dev"}, "plugin-optional": true}, "autoload": {"psr-4": {"Nevay\\SPI\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Service provider loading facility", "keywords": ["service provider"], "support": {"issues": "https://github.com/Nevay/spi/issues", "source": "https://github.com/Nevay/spi/tree/v1.0.5"}, "time": "2025-06-29T15:42:06+00:00"}, {"name": "twig/twig", "version": "v3.21.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/285123877d4dd97dd7c11842ac5fb7e86e60d81d", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.21.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-05-03T07:21:55+00:00"}, {"name": "twilio/sdk", "version": "6.44.4", "source": {"type": "git", "url": "https://github.com/twilio/twilio-php.git", "reference": "08aad5f377e2245b9cd7508e7762d95e7392fa4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twilio/twilio-php/zipball/08aad5f377e2245b9cd7508e7762d95e7392fa4d", "reference": "08aad5f377e2245b9cd7508e7762d95e7392fa4d", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "phpunit/phpunit": ">=7.0 < 10"}, "suggest": {"guzzlehttp/guzzle": "An HTTP client to execute the API requests"}, "type": "library", "autoload": {"psr-4": {"Twilio\\": "src/<PERSON>wi<PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Twilio API Team", "email": "<EMAIL>"}], "description": "A PHP wrapper for Twilio's API", "homepage": "https://github.com/twilio/twilio-php", "keywords": ["api", "sms", "twi<PERSON>"], "support": {"issues": "https://github.com/twilio/twilio-php/issues", "source": "https://github.com/twilio/twilio-php/tree/6.44.4"}, "time": "2023-02-22T19:59:53+00:00"}, {"name": "voodoophp/handlebars", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/mardix/Handlebars.git", "reference": "74145cfe9a8abcfc0fd941a61c9bc2eaee4a8518"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mardix/Handlebars/zipball/74145cfe9a8abcfc0fd941a61c9bc2eaee4a8518", "reference": "74145cfe9a8abcfc0fd941a61c9bc2eaee4a8518", "shasum": ""}, "require": {"php": ">=5.4.0"}, "default-branch": true, "type": "library", "autoload": {"psr-0": {"Handlebars": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "fzerorubigd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> (everplays)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/mardix"}], "description": "Handlebars processor for php", "homepage": "http://voodoophp.org/docs/handlebars", "keywords": ["handlebars", "mustache", "templating"], "support": {"source": "https://github.com/mardix/Handlebars/tree/master"}, "time": "2017-05-25T13:06:38+00:00"}, {"name": "will<PERSON><PERSON>/geocoder", "version": "4.6.0", "source": {"type": "git", "url": "https://github.com/geocoder-php/php-common.git", "reference": "be3d9ed0fddf8c698ee079d8a07ae9520b4a49a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/geocoder-php/php-common/zipball/be3d9ed0fddf8c698ee079d8a07ae9520b4a49a1", "reference": "be3d9ed0fddf8c698ee079d8a07ae9520b4a49a1", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"nyholm/nsa": "^1.1", "phpunit/phpunit": "^9.5", "symfony/stopwatch": "~2.5"}, "suggest": {"symfony/stopwatch": "If you want to use the TimedGeocoder"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"psr-4": {"Geocoder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Common files for PHP Geocoder", "homepage": "http://geocoder-php.org", "keywords": ["abstraction", "geocoder", "geocoding", "geoip"], "support": {"source": "https://github.com/geocoder-php/php-common/tree/4.6.0"}, "time": "2022-07-30T11:09:43+00:00"}], "packages-dev": [{"name": "hamcrest/hamcrest-php", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0 || ^3.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.1.1"}, "time": "2025-04-30T06:54:44+00:00"}, {"name": "mockery/mockery", "version": "1.6.12", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2024-05-16T03:13:13+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.3", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "faed855a7b5f4d4637717c2b3863e277116beb36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/faed855a7b5f4d4637717c2b3863e277116beb36", "reference": "faed855a7b5f4d4637717c2b3863e277116beb36", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-07-05T12:25:42+00:00"}, {"name": "nikic/php-parser", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/ae59794362fe85e051a58ad36b289443f57be7a9", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.5.0"}, "time": "2025-05-31T08:24:38+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.16", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "7e308268858ed6baedc8704a304727d20bc07c77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7e308268858ed6baedc8704a304727d20bc07c77", "reference": "7e308268858ed6baedc8704a304727d20bc07c77", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-text-template": "^3.0.1", "sebastian/code-unit-reverse-lookup": "^3.0.0", "sebastian/complexity": "^3.2.0", "sebastian/environment": "^6.1.0", "sebastian/lines-of-code": "^2.0.2", "sebastian/version": "^4.0.1", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.16"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-08-22T04:31:57+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/e2a2d67966e740530f4a3343fe2e030ffdc1161d", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.5.48", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "6e0a2bc39f6fae7617989d690d76c48e6d2eb541"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/6e0a2bc39f6fae7617989d690d76c48e6d2eb541", "reference": "6e0a2bc39f6fae7617989d690d76c48e6d2eb541", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.3", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.16", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-invoker": "^4.0.0", "phpunit/php-text-template": "^3.0.1", "phpunit/php-timer": "^6.0.0", "sebastian/cli-parser": "^2.0.1", "sebastian/code-unit": "^2.0.0", "sebastian/comparator": "^5.0.3", "sebastian/diff": "^5.1.1", "sebastian/environment": "^6.1.0", "sebastian/exporter": "^5.1.2", "sebastian/global-state": "^6.0.2", "sebastian/object-enumerator": "^5.0.0", "sebastian/recursion-context": "^5.0.0", "sebastian/type": "^4.0.0", "sebastian/version": "^4.0.1"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.5.48"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-07-11T04:07:17+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/c34583b87e7b7a8055bf6c450c2c77ce32a24084", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:12:49+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-10-18T14:56:07+00:00"}, {"name": "sebastian/complexity", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68ff824baeae169ec9f2137158ee529584553799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/68ff824baeae169ec9f2137158ee529584553799", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.2.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:37:17+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "sebastian/environment", "version": "6.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/8074dbcd93529b357029f5cc5058fd3e43666984", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-23T08:47:14+00:00"}, {"name": "sebastian/exporter", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "955288482d97c19a372d3f31006ab3f37da47adf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/955288482d97c19a372d3f31006ab3f37da47adf", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:17:12+00:00"}, {"name": "sebastian/global-state", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:19:19+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/856e7f6a75a84e339195d48c556f23be2ebf75d0", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:38:20+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/05909fb5bc7df4c52992396d0116aed689f93712", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:05:40+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/462699a16464c3944eefc02ebdd77882bd3925bf", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-07T11:34:05+00:00"}, {"name": "symfony/stopwatch", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "fb2c199cf302eb207f8c23e7ee174c1c31a5c004"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/fb2c199cf302eb207f8c23e7ee174c1c31a5c004", "reference": "fb2c199cf302eb207f8c23e7ee174c1c31a5c004", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/service-contracts": "^1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"open-telemetry/opentelemetry-auto-symfony": 20, "sparefoot/authorization": 20, "sparefoot/emails_service_client": 20, "sparefoot/error-logging": 20, "sparefoot/genesis": 20, "sparefoot/phlow_client": 20, "sparefoot/pillar": 20, "sparefoot/reservation_rules": 20, "sparefoot/salesforce_client": 20, "voodoophp/handlebars": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^8.2", "ext-json": "*", "ext-pdo": "*", "ext-zip": "*", "ext-soap": "*", "ext-gd": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}