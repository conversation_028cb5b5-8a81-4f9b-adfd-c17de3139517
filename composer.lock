{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "e616eb63a9724a10b86a62f1342f1b9e", "packages": [{"name": "aws/aws-crt-php", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "2f1dc7b7eda080498be96a4a6d683a41583030e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/2f1dc7b7eda080498be96a4a6d683a41583030e9", "reference": "2f1dc7b7eda080498be96a4a6d683a41583030e9", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "time": "2023-07-20T16:49:55+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.277.7", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "906caee3dc8ddffeb992062c84f4552dc2e2da19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/906caee3dc8ddffeb992062c84f4552dc2e2da19", "reference": "906caee3dc8ddffeb992062c84f4552dc2e2da19", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.0.4", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "mtdowling/jmespath.php": "^2.6", "php": ">=5.5", "psr/http-message": "^1.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^4.8.35 || ^5.6.3 || ^9.5", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "time": "2023-08-02T18:04:32+00:00"}, {"name": "beber<PERSON>i/assert", "version": "v2.7.11", "source": {"type": "git", "url": "https://github.com/beberlei/assert.git", "reference": "53991547d6f0b8c81354fb2d098dcb71e81678cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beberlei/assert/zipball/53991547d6f0b8c81354fb2d098dcb71e81678cb", "reference": "53991547d6f0b8c81354fb2d098dcb71e81678cb", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.1.1", "phpunit/phpunit": "^4.8.35|^5.7"}, "type": "library", "autoload": {"files": ["lib/Assert/functions.php"], "psr-4": {"Assert\\": "lib/Assert"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Collaborator"}], "description": "Thin assertion library for input validation in business models.", "keywords": ["assert", "assertion", "validation"], "time": "2017-11-13T18:35:09+00:00"}, {"name": "bshaffer/oauth2-server-httpfoundation-bridge", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/bshaffer/oauth2-server-httpfoundation-bridge.git", "reference": "f2479fb24db24932962430deadac0e29ca2729f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bshaffer/oauth2-server-httpfoundation-bridge/zipball/f2479fb24db24932962430deadac0e29ca2729f7", "reference": "f2479fb24db24932962430deadac0e29ca2729f7", "shasum": ""}, "require": {"bshaffer/oauth2-server-php": ">=0.9", "php": ">=5.3.0", "symfony/http-foundation": ">=2.1"}, "type": "library", "autoload": {"psr-0": {"OAuth2\\HttpFoundationBridge": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://brentertainment.com"}], "description": "A bridge to HttpFoundation for oauth2-server-php", "homepage": "http://github.com/bshaffer/oauth2-server-httpfoundation-bridge", "keywords": ["auth", "httpfoundation", "o<PERSON>h", "oauth2"], "time": "2018-12-04T02:28:38+00:00"}, {"name": "bshaffer/oauth2-server-php", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/bshaffer/oauth2-server-php.git", "reference": "5a0c8000d4763b276919e2106f54eddda6bc50fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bshaffer/oauth2-server-php/zipball/5a0c8000d4763b276919e2106f54eddda6bc50fa", "reference": "5a0c8000d4763b276919e2106f54eddda6bc50fa", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"aws/aws-sdk-php": "~2.8", "firebase/php-jwt": "~2.2", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^4.0", "predis/predis": "dev-master", "thobbs/phpcassa": "dev-master"}, "suggest": {"aws/aws-sdk-php": "~2.8 is required to use DynamoDB storage", "firebase/php-jwt": "~2.2 is required to use JWT features", "mongodb/mongodb": "^1.1 is required to use MongoDB storage", "predis/predis": "Required to use Redis storage", "thobbs/phpcassa": "Required to use Cassandra storage"}, "type": "library", "autoload": {"psr-0": {"OAuth2": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://brentertainment.com"}], "description": "OAuth2 Server for PHP", "homepage": "http://github.com/bshaffer/oauth2-server-php", "keywords": ["auth", "o<PERSON>h", "oauth2"], "time": "2018-12-04T00:29:32+00:00"}, {"name": "clue/graph", "version": "v0.9.0", "source": {"type": "git", "url": "https://github.com/clue/graph.git", "reference": "0336a4d5229fa61a20ccceaeab25e52ac9542700"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/graph/zipball/0336a4d5229fa61a20ccceaeab25e52ac9542700", "reference": "0336a4d5229fa61a20ccceaeab25e52ac9542700", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": {"graphp/algorithms": "Common graph algorithms, such as <PERSON><PERSON><PERSON> and <PERSON> (shortest path), minimum spanning tree (MST), <PERSON><PERSON><PERSON>, Prim and many more..", "graphp/graphviz": "GraphViz graph drawing / DOT output"}, "type": "library", "autoload": {"psr-4": {"Fhaculty\\Graph\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A mathematical graph/network library written in PHP", "homepage": "https://github.com/clue/graph", "keywords": ["edge", "graph", "mathematical", "network", "vertex"], "time": "2015-03-07T18:11:31+00:00"}, {"name": "clue/stream-filter", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/d6169430c7731d8509da7aecd0af756a5747b78e", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/php-stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-21T13:15:14+00:00"}, {"name": "datadog/dd-trace", "version": "0.34.1", "source": {"type": "git", "url": "https://github.com/DataDog/dd-trace-php.git", "reference": "e96055ecf0f75fee408acaea626e4094a09d543f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DataDog/dd-trace-php/zipball/e96055ecf0f75fee408acaea626e4094a09d543f", "reference": "e96055ecf0f75fee408acaea626e4094a09d543f", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": "~5.4.0 || ~5.6.0 || ^7.0"}, "require-dev": {"g1a/composer-test-scenarios": "~3.0", "mockery/mockery": "*", "phpunit/phpunit": "^4", "squizlabs/php_codesniffer": "^3.3.0", "symfony/process": "*"}, "type": "library", "extra": {"scenarios": {"guzzle5": {"require": {"guzzlehttp/guzzle": "~5.0"}, "scenario-options": {"create-lockfile": false}}, "guzzle6": {"require": {"guzzlehttp/guzzle": "~6.0"}, "scenario-options": {"create-lockfile": false}}, "predis1": {"require": {"predis/predis": "^1.1"}, "scenario-options": {"create-lockfile": false}}, "elasticsearch1": {"require": {"elasticsearch/elasticsearch": "1.2.*"}, "scenario-options": {"create-lockfile": false}}}, "scenario-options": {"dependency-licenses": false}}, "autoload": {"psr-4": {"DDTrace\\": "./src/DDTrace/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "DataDog", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP APM Client", "keywords": ["DataDog", "php", "tracing"], "time": "2019-11-11T20:16:54+00:00"}, {"name": "datadog/php-datadogstatsd", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/DataDog/php-datadogstatsd.git", "reference": "148ba02f8d2310778750b0c7f7a60458f3ccb215"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DataDog/php-datadogstatsd/zipball/148ba02f8d2310778750b0c7f7a60458f3ccb215", "reference": "148ba02f8d2310778750b0c7f7a60458f3ccb215", "shasum": ""}, "require": {"ext-curl": "*", "lib-curl": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.8.36"}, "type": "library", "autoload": {"psr-4": {"DataDog\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "Datadog", "email": "<EMAIL>", "role": "Developer"}], "description": "This is an extremely simple PHP datadogstatsd client", "homepage": "https://www.datadoghq.com/", "keywords": ["DataDog", "php"], "time": "2019-08-13T19:23:10+00:00"}, {"name": "developerforce/force.com-toolkit-for-php", "version": "v27.0.3", "source": {"type": "git", "url": "https://github.com/developerforce/Force.com-Toolkit-for-PHP.git", "reference": "14eadd0adae15216907dff5e76a922c0f8a3c9fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/developerforce/Force.com-Toolkit-for-PHP/zipball/14eadd0adae15216907dff5e76a922c0f8a3c9fa", "reference": "14eadd0adae15216907dff5e76a922c0f8a3c9fa", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["soapclient"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A wrapper for the Force.com Web Services SOAP API", "abandoned": true, "time": "2015-03-23T21:27:56+00:00"}, {"name": "doctrine/cache", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "89a5c76c39c292f7798f964ab3c836c3f8192a55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/89a5c76c39c292f7798f964ab3c836c3f8192a55", "reference": "89a5c76c39c292f7798f964ab3c836c3f8192a55", "shasum": ""}, "require": {"php": "~7.1"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "riak", "xcache"], "time": "2019-11-15T14:31:57+00:00"}, {"name": "doctrine/inflector", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "90b2128806bfde671b6952ab8bea493942c1fdae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/90b2128806bfde671b6952ab8bea493942c1fdae", "reference": "90b2128806bfde671b6952ab8bea493942c1fdae", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Inflector\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2015-11-06T14:35:42+00:00"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2015-06-14T21:17:01+00:00"}, {"name": "facebook/webdriver", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/php-webdriver/php-webdriver-archive.git", "reference": "86b5ca2f67173c9d34340845dd690149c886a605"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-webdriver/php-webdriver-archive/zipball/86b5ca2f67173c9d34340845dd690149c886a605", "reference": "86b5ca2f67173c9d34340845dd690149c886a605", "shasum": ""}, "require": {"ext-curl": "*", "ext-zip": "*", "php": "^5.6 || ~7.0", "symfony/process": "^2.8 || ^3.1 || ^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "guzzle/guzzle": "^3.4.1", "php-coveralls/php-coveralls": "^1.0.2", "php-mock/php-mock-phpunit": "^1.1", "phpunit/phpunit": "^5.7", "sebastian/environment": "^1.3.4 || ^2.0 || ^3.0", "squizlabs/php_codesniffer": "^2.6", "symfony/var-dumper": "^3.3 || ^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-community": "1.5-dev"}}, "autoload": {"psr-4": {"Facebook\\WebDriver\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "A PHP client for Selenium WebDriver", "homepage": "https://github.com/facebook/php-webdriver", "keywords": ["facebook", "php", "selenium", "webdriver"], "abandoned": "php-webdriver/webdriver", "time": "2017-11-15T11:08:09+00:00"}, {"name": "florianwolters/component-core-stringutils", "version": "v0.3.1", "source": {"type": "git", "url": "https://github.com/FlorianWolters/PHP-Component-Core-StringUtils.git", "reference": "51978fa9a4d30104192036f0b1f11fc1c3bc4667"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FlorianWolters/PHP-Component-Core-StringUtils/zipball/51978fa9a4d30104192036f0b1f11fc1c3bc4667", "reference": "51978fa9a4d30104192036f0b1f11fc1c3bc4667", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "autoload": {"psr-0": {"FlorianWolters": "src/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.florianwolters.de", "role": "Developer"}], "description": "Offers operations on the data type string as a PHP component.", "homepage": "http://github.com/FlorianWolters/PHP-Component-Core-StringUtils", "keywords": ["helper", "language", "string", "wrapper"], "time": "2013-07-01T10:24:07+00:00"}, {"name": "florianwolters/component-util-reflection", "version": "v0.2.0", "source": {"type": "git", "url": "https://github.com/FlorianWolters/PHP-Component-Util-Reflection.git", "reference": "ffc94b62e2834d7d0306374d952eda7a5abd1844"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FlorianWolters/PHP-Component-Util-Reflection/zipball/ffc94b62e2834d7d0306374d952eda7a5abd1844", "reference": "ffc94b62e2834d7d0306374d952eda7a5abd1844", "shasum": ""}, "require": {"florianwolters/component-core-stringutils": ">=0.2-beta", "php": ">=5.4"}, "type": "library", "autoload": {"psr-0": {"FlorianWolters": "src/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.florianwolters.de", "role": "Developer"}], "description": "Provides operations for the PHP Reflection API as a PHP component.", "homepage": "http://github.com/FlorianWolters/PHP-Component-Util-Reflection", "keywords": ["reflection", "utility"], "time": "2013-03-19T16:42:41+00:00"}, {"name": "florianwolters/component-util-singleton", "version": "v0.3.2", "source": {"type": "git", "url": "https://github.com/FlorianWolters/PHP-Component-Util-Singleton.git", "reference": "ab39ba531a38c3b76b4babb0035ce840cde7f443"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FlorianWolters/PHP-Component-Util-Singleton/zipball/ab39ba531a38c3b76b4babb0035ce840cde7f443", "reference": "ab39ba531a38c3b76b4babb0035ce840cde7f443", "shasum": ""}, "require": {"florianwolters/component-core-stringutils": "0.3.*", "florianwolters/component-util-reflection": "0.2.*", "php": ">=5.4"}, "type": "library", "autoload": {"psr-0": {"FlorianWolters": ["src/php", "src/tests/unit-tests/php", "src/tests/mocks/php"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.florianwolters.de", "role": "Developer"}], "description": "The Singleton (and Registry of Singletons a.k.a. Multiton) design pattern as a PHP component.", "homepage": "http://github.com/FlorianWolters/PHP-Component-Util-Singleton", "keywords": ["creation", "pattern", "singleton", "utility"], "time": "2013-06-29T12:35:22+00:00"}, {"name": "gorkalaucirica/hipchat-v2-api-client", "version": "v1.5.1", "target-dir": "GorkaLaucirica/HipchatAPIv2Client", "source": {"type": "git", "url": "https://github.com/gorkalaucirica/HipchatAPIv2Client.git", "reference": "045521cd01aba26cdf304010bc3f66dc4f9fd37c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gorkalaucirica/HipchatAPIv2Client/zipball/045521cd01aba26cdf304010bc3f66dc4f9fd37c", "reference": "045521cd01aba26cdf304010bc3f66dc4f9fd37c", "shasum": ""}, "require": {"kriswallsmith/buzz": "0.10", "php": ">=5.3.3"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "1.0.*@dev", "phpspec/phpspec": "2.0.*@dev"}, "type": "library", "autoload": {"psr-0": {"GorkaLaucirica\\HipchatAPIv2Client": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://gorkalaucirica.net/"}], "description": "Hipchat v2 API client", "homepage": "https://github.com/gorkalaucirica/HipchatAPIv2Client", "keywords": ["api", "client", "hip<PERSON>t", "library", "v2"], "time": "2016-04-30T11:45:31+00:00"}, {"name": "graphp/algorithms", "version": "v0.8.1", "source": {"type": "git", "url": "https://github.com/graphp/algorithms.git", "reference": "81db4049c35730767ec8f97fb5c4844234b86cef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/graphp/algorithms/zipball/81db4049c35730767ec8f97fb5c4844234b86cef", "reference": "81db4049c35730767ec8f97fb5c4844234b86cef", "shasum": ""}, "require": {"clue/graph": "~0.9.0|~0.8.0", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Graphp\\Algorithms\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Common mathematical graph algorithms", "homepage": "https://github.com/graphp/algorithms", "keywords": ["Graph algorithms", "<PERSON><PERSON><PERSON>", "kruskal", "minimum spanning tree", "moore-bellman-ford", "prim", "shortest path"], "time": "2015-03-08T10:12:01+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.8", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:07+00:00"}, {"name": "guzzlehttp/oauth-subscriber", "version": "0.3.0", "source": {"type": "git", "url": "https://github.com/guzzle/oauth-subscriber.git", "reference": "04960cdef3cd80ea401d6b0ca8b3e110e9bf12cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/oauth-subscriber/zipball/04960cdef3cd80ea401d6b0ca8b3e110e9bf12cf", "reference": "04960cdef3cd80ea401d6b0ca8b3e110e9bf12cf", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~6.0", "php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Subscriber\\Oauth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle OAuth 1.0 subscriber", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "o<PERSON>h"], "time": "2015-08-15T19:44:28+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-05-21T12:31:43+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e4490cabc77465aaee90b20cfc9a770f8c04be6b", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-04-17T16:00:37+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "time": "2021-07-21T13:50:14+00:00"}, {"name": "igorw/get-in", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/igorw/get-in.git", "reference": "170ded831f49abc6a6061f655aba9bdbcf7b8111"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/get-in/zipball/170ded831f49abc6a6061f655aba9bdbcf7b8111", "reference": "170ded831f49abc6a6061f655aba9bdbcf7b8111", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/get_in.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Functions for for hash map (assoc array) traversal.", "keywords": ["assoc-array", "hash-map"], "time": "2014-12-15T23:03:51+00:00"}, {"name": "kamermans/guzzle-oauth2-subscriber", "version": "v1.0.13", "source": {"type": "git", "url": "https://github.com/kamermans/guzzle-oauth2-subscriber.git", "reference": "10b5cf242c5167afd16f1c1f243ac5be689ecd4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kamermans/guzzle-oauth2-subscriber/zipball/10b5cf242c5167afd16f1c1f243ac5be689ecd4c", "reference": "10b5cf242c5167afd16f1c1f243ac5be689ecd4c", "shasum": ""}, "require": {"php": ">=5.4.0"}, "suggest": {"guzzlehttp/guzzle": "Guzzle ~4.0|~5.0|~6.0|~7.0"}, "type": "library", "autoload": {"psr-4": {"kamermans\\OAuth2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "steve<PERSON><PERSON><PERSON>@gmail.com"}], "description": "OAuth 2.0 client for Guzzle 4, 5, 6 and 7+", "keywords": ["Guzzle", "o<PERSON>h"], "time": "2023-07-11T21:43:27+00:00"}, {"name": "kriswallsmith/buzz", "version": "v0.10", "source": {"type": "git", "url": "https://github.com/kriswallsmith/Buzz.git", "reference": "759432d66387e3433d3b06cb6f773b97225b9b17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kriswallsmith/Buzz/zipball/759432d66387e3433d3b06cb6f773b97225b9b17", "reference": "759432d66387e3433d3b06cb6f773b97225b9b17", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "suggest": {"ext-curl": "*"}, "type": "library", "autoload": {"psr-0": {"Buzz": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kriswallsmith.net/"}], "description": "Lightweight HTTP client", "homepage": "https://github.com/kriswallsmith/Buzz", "keywords": ["curl", "http client"], "time": "2013-05-19T03:41:15+00:00"}, {"name": "lmc/steward", "version": "2.3.4", "source": {"type": "git", "url": "https://github.com/lmc-eu/steward.git", "reference": "a4738179a6f3ccee72fa20957c8546c4c53c9ab9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lmc-eu/steward/zipball/a4738179a6f3ccee72fa20957c8546c4c53c9ab9", "reference": "a4738179a6f3ccee72fa20957c8546c4c53c9ab9", "shasum": ""}, "require": {"beberlei/assert": "^2.7", "clue/graph": "~0.9.0", "doctrine/inflector": "~1.0", "ext-curl": "*", "ext-zip": "*", "facebook/webdriver": "^1.4.0", "florianwolters/component-util-singleton": "0.3.2", "graphp/algorithms": "^0.8.1", "nette/reflection": "^2.4.2", "ondram/ci-detector": "^2.1", "php": "^5.6 || ~7.0", "phpunit/phpunit": "^5.7.11", "symfony/console": "^3.3.0", "symfony/event-dispatcher": "~3.0", "symfony/filesystem": "~3.0", "symfony/finder": "~3.0", "symfony/options-resolver": "^3.2", "symfony/process": "^3.2.0", "symfony/stopwatch": "^3.0", "symfony/yaml": "^3.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "php-coveralls/php-coveralls": "^1.0.2", "php-mock/php-mock-phpunit": "~1.0", "squizlabs/php_codesniffer": "^2.4.1", "symfony/var-dumper": "^3.2"}, "suggest": {"ext-posix": "For colored output", "ext-xdebug": "For remote tests debugging"}, "bin": ["bin/steward", "bin/steward.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Lmc\\Steward\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "LMC s.r.o.", "homepage": "https://github.com/lmc-eu"}], "description": "Steward - makes Selenium WebDriver + PHPUnit testing easy and robust", "keywords": ["phpunit", "selenium", "testing", "webdriver"], "time": "2018-07-26T22:03:36+00:00"}, {"name": "monolog/monolog", "version": "1.25.2", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "d5e2fb341cb44f7e2ab639d12a1e5901091ec287"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/d5e2fb341cb44f7e2ab639d12a1e5901091ec287", "reference": "d5e2fb341cb44f7e2ab639d12a1e5901091ec287", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2019-11-13T10:00:05+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2021-06-14T00:11:39+00:00"}, {"name": "myclabs/deep-copy", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^4.1"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2017-10-19T19:58:43+00:00"}, {"name": "nategood/httpful", "version": "0.2.11", "source": {"type": "git", "url": "https://github.com/nategood/httpful.git", "reference": "0bf1423028abe2f630e1d2ef8d62486d6655b2f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nategood/httpful/zipball/0bf1423028abe2f630e1d2ef8d62486d6655b2f3", "reference": "0bf1423028abe2f630e1d2ef8d62486d6655b2f3", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-0": {"Httpful": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nategood.com"}], "description": "A Readable, Chainable, REST friendly, PHP HTTP Client", "homepage": "http://github.com/nategood/httpful", "keywords": ["api", "curl", "http", "requests", "rest", "restful"], "time": "2013-10-20T11:03:40+00:00"}, {"name": "nette/caching", "version": "v2.5.6", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "1231735b5135ca02bd381b70482c052d2a90bdc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/1231735b5135ca02bd381b70482c052d2a90bdc9", "reference": "1231735b5135ca02bd381b70482c052d2a90bdc9", "shasum": ""}, "require": {"nette/finder": "^2.2 || ~3.0.0", "nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "^2.4", "nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "time": "2017-08-30T12:12:25+00:00"}, {"name": "nette/finder", "version": "v2.4.1", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "4d43a66d072c57d585bf08a3ef68d3587f7e9547"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/4d43a66d072c57d585bf08a3ef68d3587f7e9547", "reference": "4d43a66d072c57d585bf08a3ef68d3587f7e9547", "shasum": ""}, "require": {"nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Finder: Files Searching", "homepage": "https://nette.org", "time": "2017-07-10T23:47:08+00:00"}, {"name": "nette/reflection", "version": "v2.4.2", "source": {"type": "git", "url": "https://github.com/nette/reflection.git", "reference": "b12327e98ead74e87a1315e0d48182a702adf901"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/reflection/zipball/b12327e98ead74e87a1315e0d48182a702adf901", "reference": "b12327e98ead74e87a1315e0d48182a702adf901", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/caching": "^2.2 || ^3.0", "nette/utils": "^2.4 || ^3.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4 || ^3.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Reflection: docblock annotations parser and common reflection classes", "homepage": "https://nette.org", "keywords": ["annotation", "nette", "reflection"], "abandoned": true, "time": "2017-07-11T19:28:57+00:00"}, {"name": "nette/utils", "version": "v2.4.8", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "f1584033b5af945b470533b466b81a789d532034"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/f1584033b5af945b470533b466b81a789d532034", "reference": "f1584033b5af945b470533b466b81a789d532034", "shasum": ""}, "require": {"php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize() and toAscii()", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "time": "2017-08-20T17:32:29+00:00"}, {"name": "ocramius/package-versions", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/Ocramius/PackageVersions.git", "reference": "44af6f3a2e2e04f2af46bcb302ad9600cba41c7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/PackageVersions/zipball/44af6f3a2e2e04f2af46bcb302ad9600cba41c7d", "reference": "44af6f3a2e2e04f2af46bcb302ad9600cba41c7d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0.0", "php": "^7.1.0"}, "require-dev": {"composer/composer": "^1.6.3", "doctrine/coding-standard": "^5.0.1", "ext-zip": "*", "infection/infection": "^0.7.1", "phpunit/phpunit": "^7.5.17"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "time": "2019-11-15T16:17:10+00:00"}, {"name": "ocramius/proxy-manager", "version": "2.2.3", "source": {"type": "git", "url": "https://github.com/Ocramius/ProxyManager.git", "reference": "4d154742e31c35137d5374c998e8f86b54db2e2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/ProxyManager/zipball/4d154742e31c35137d5374c998e8f86b54db2e2f", "reference": "4d154742e31c35137d5374c998e8f86b54db2e2f", "shasum": ""}, "require": {"ocramius/package-versions": "^1.1.3", "php": "^7.2.0", "zendframework/zend-code": "^3.3.0"}, "require-dev": {"couscous/couscous": "^1.6.1", "ext-phar": "*", "humbug/humbug": "1.0.0-RC.0@RC", "nikic/php-parser": "^3.1.1", "padraic/phpunit-accelerator": "dev-master@DEV", "phpbench/phpbench": "^0.12.2", "phpstan/phpstan": "dev-master#856eb10a81c1d27c701a83f167dc870fd8f4236a as 0.9.999", "phpstan/phpstan-phpunit": "dev-master#5629c0a1f4a9c417cb1077cf6693ad9753895761", "phpunit/phpunit": "^6.4.3", "squizlabs/php_codesniffer": "^2.9.1"}, "suggest": {"ocramius/generated-hydrator": "To have very fast object to array to object conversion for ghost objects", "zendframework/zend-json": "To have the JsonRpc adapter (Remote Object feature)", "zendframework/zend-soap": "To have the Soap adapter (Remote Object feature)", "zendframework/zend-xmlrpc": "To have the XmlRpc adapter (Remote Object feature)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-0": {"ProxyManager\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.io/"}], "description": "A library providing utilities to generate, instantiate and generally operate with Object Proxies", "homepage": "https://github.com/Ocramius/ProxyManager", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "time": "2019-08-10T08:37:15+00:00"}, {"name": "ondram/ci-detector", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/OndraM/ci-detector.git", "reference": "be3410cb14443796122ca051f4224b5eae06aa76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/OndraM/ci-detector/zipball/be3410cb14443796122ca051f4224b5eae06aa76", "reference": "be3410cb14443796122ca051f4224b5eae06aa76", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.12", "phpunit/phpunit": "^5.5"}, "type": "library", "autoload": {"psr-4": {"OndraM\\CiDetector\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Detect current continuous integration server and provide unified access to properties of current build", "keywords": ["CircleCI", "Codeship", "adapter", "appveyor", "bamboo", "continuous integration", "gitlab", "interface", "jenkins", "teamcity", "travis"], "time": "2017-05-26T16:39:57+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "php-http/curl-client", "version": "v1.7.1", "source": {"type": "git", "url": "https://github.com/php-http/curl-client.git", "reference": "6341a93d00e5d953fc868a3928b5167e6513f2b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/curl-client/zipball/6341a93d00e5d953fc868a3928b5167e6513f2b6", "reference": "6341a93d00e5d953fc868a3928b5167e6513f2b6", "shasum": ""}, "require": {"ext-curl": "*", "php": "^5.5 || ^7.0", "php-http/discovery": "^1.0", "php-http/httplug": "^1.0", "php-http/message": "^1.2", "php-http/message-factory": "^1.0.2"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0"}, "require-dev": {"guzzlehttp/psr7": "^1.0", "php-http/client-integration-tests": "^0.6", "phpunit/phpunit": "^4.8.27", "zendframework/zend-diactoros": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Curl\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "m.<PERSON><PERSON><PERSON>@yandex.ru"}], "description": "cURL client for PHP-HTTP", "homepage": "http://php-http.org", "keywords": ["curl", "http"], "time": "2018-03-26T19:21:48+00:00"}, {"name": "php-http/discovery", "version": "1.19.1", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "57f3de01d32085fea20865f9b16fb0e69347c39e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/57f3de01d32085fea20865f9b16fb0e69347c39e", "reference": "57f3de01d32085fea20865f9b16fb0e69347c39e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "symfony/phpunit-bridge": "^6.2"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "time": "2023-07-11T07:02:26+00:00"}, {"name": "php-http/guzzle6-adapter", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/php-http/guzzle6-adapter.git", "reference": "a56941f9dc6110409cfcddc91546ee97039277ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle6-adapter/zipball/a56941f9dc6110409cfcddc91546ee97039277ab", "reference": "a56941f9dc6110409cfcddc91546ee97039277ab", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0", "php": ">=5.5.0", "php-http/httplug": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "php-http/adapter-integration-tests": "^0.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Http\\Adapter\\Guzzle6\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 6 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "abandoned": "guzzlehttp/guzzle or php-http/guzzle7-adapter", "time": "2016-05-10T06:13:32+00:00"}, {"name": "php-http/httplug", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "1c6381726c18579c4ca2ef1ec1498fdae8bdf018"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/1c6381726c18579c4ca2ef1ec1498fdae8bdf018", "reference": "1c6381726c18579c4ca2ef1ec1498fdae8bdf018", "shasum": ""}, "require": {"php": ">=5.4", "php-http/promise": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "^1.0", "phpspec/phpspec": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "time": "2016-08-31T08:30:17+00:00"}, {"name": "php-http/message", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "47a14338bf4ebd67d317bf1144253d7db4ab55fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/47a14338bf4ebd67d317bf1144253d7db4ab55fd", "reference": "47a14338bf4ebd67d317bf1144253d7db4ab55fd", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "time": "2023-05-17T06:43:38+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "4c4c1f9b7289a2ec57cde7f1e9762a5789506f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/4c4c1f9b7289a2ec57cde7f1e9762a5789506f88", "reference": "4c4c1f9b7289a2ec57cde7f1e9762a5789506f88", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2", "phpspec/phpspec": "^5.1.2 || ^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "time": "2020-07-07T09:29:14+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2017-09-11T18:02:19+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "bf329f6c1aadea3299f08ee804682b7c45b326a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/bf329f6c1aadea3299f08ee804682b7c45b326a2", "reference": "bf329f6c1aadea3299f08ee804682b7c45b326a2", "shasum": ""}, "require": {"php": "^5.6 || ^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}, "type": "library", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2017-11-10T14:09:06+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/9c977708995954784726e25d0cd1dddf4e65b0f7", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2017-07-14T14:27:02+00:00"}, {"name": "phpspec/prophecy", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "e4ed002c67da8eceb0eb8ddb8b3847bb53c5c2bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/e4ed002c67da8eceb0eb8ddb8b3847bb53c5c2bf", "reference": "e4ed002c67da8eceb0eb8ddb8b3847bb53c5c2bf", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0", "sebastian/comparator": "^1.1|^2.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2017-11-24T13:59:53+00:00"}, {"name": "phpunit/php-code-coverage", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-text-template": "^1.2", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "^1.0 || ^2.0"}, "require-dev": {"ext-xdebug": "^2.1.4", "phpunit/phpunit": "^5.7"}, "suggest": {"ext-xdebug": "^2.5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2017-04-02T07:44:40+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "1.4.11", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "e03f8f67534427a787e21a385a67ec3ca6978ea7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/e03f8f67534427a787e21a385a67ec3ca6978ea7", "reference": "e03f8f67534427a787e21a385a67ec3ca6978ea7", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "abandoned": true, "time": "2017-02-27T10:12:30+00:00"}, {"name": "phpunit/phpunit", "version": "5.7.27", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit/zipball/b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "~1.3", "php": "^5.6 || ^7.0", "phpspec/prophecy": "^1.6.2", "phpunit/php-code-coverage": "^4.0.4", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "sebastian/comparator": "^1.2.4", "sebastian/diff": "^1.4.3", "sebastian/environment": "^1.3.4 || ^2.0", "sebastian/exporter": "~2.0", "sebastian/global-state": "^1.1", "sebastian/object-enumerator": "~2.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "^1.0.6|^2.0.1", "symfony/yaml": "~2.1|~3.0|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.7.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2018-02-01T05:50:59+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "3.4.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/a23b761686d50a560cc56233b9ecf49597cc9118", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "sebastian/exporter": "^1.2 || ^2.0"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "abandoned": true, "time": "2017-06-30T09:13:00+00:00"}, {"name": "predis/predis", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "f0210e38881631afeafb56ab43405a92cafd9fd1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/f0210e38881631afeafb56ab43405a92cafd9fd1", "reference": "f0210e38881631afeafb56ab43405a92cafd9fd1", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/nrk/predis", "keywords": ["nosql", "predis", "redis"], "funding": [{"url": "https://www.paypal.me/tillkruss", "type": "custom"}, {"url": "https://github.com/tillkruss", "type": "github"}], "time": "2016-06-16T16:22:20+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2017-01-29T09:50:25+00:00"}, {"name": "sebastian/diff", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/7f066a26a962dbe58ddea9f72a4e82874a3975a4", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-05-22T07:24:03+00:00"}, {"name": "sebastian/environment", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-11-26T07:53:53+00:00"}, {"name": "sebastian/exporter", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~2.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-11-19T08:54:04+00:00"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12T03:26:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1311872ac850040a79c3c058bea3e22d0f09cbb7", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7", "shasum": ""}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-02-18T15:18:39+00:00"}, {"name": "sebastian/recursion-context", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2c3ba150cbec723aa057506e73a8d33bdb286c9a", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2016-11-19T07:33:16+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "segmentio/analytics-php", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/segmentio/analytics-php.git", "reference": "9513e3762ab24c9abd00078b0fb9e185aec10e07"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/segmentio/analytics-php/zipball/9513e3762ab24c9abd00078b0fb9e185aec10e07", "reference": "9513e3762ab24c9abd00078b0fb9e185aec10e07", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "bin": ["bin/analytics"], "type": "library", "autoload": {"files": ["lib/Segment.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Segment.io <<EMAIL>>", "homepage": "https://segment.com/"}], "description": "Segment Analytics PHP Library", "homepage": "https://segment.com/libraries/php", "keywords": ["analytics", "analytics.js", "segment", "segmentio"], "time": "2017-08-18T22:13:00+00:00"}, {"name": "solarium/solarium", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/solariumphp/solarium.git", "reference": "fcec6d7fa9d6704e45afed0d050ff069f3e0e86f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/solariumphp/solarium/zipball/fcec6d7fa9d6704e45afed0d050ff069f3e0e86f", "reference": "fcec6d7fa9d6704e45afed0d050ff069f3e0e86f", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1", "symfony/cache": "^3.1 || ^4.0", "symfony/event-dispatcher": "^3.1 || ^4.0"}, "require-dev": {"guzzlehttp/guzzle": "^3.8 || ^6.2", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^8.0", "squizlabs/php_codesniffer": "^3.4", "zendframework/zend-http": "^2.8"}, "suggest": {"minimalcode/search": "Query builder compatible with Solarium, allows simplified solr-query handling"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "autoload": {"psr-4": {"Solarium\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "See GitHub contributors", "homepage": "https://github.com/basdenooijer/solarium/contributors"}], "description": "PHP Solr client", "homepage": "http://www.solarium-project.org", "keywords": ["php", "search", "solr"], "time": "2019-07-05T14:34:48+00:00"}, {"name": "sparefoot/authorization", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/authorization.git", "reference": "6f56cf11825f3c82aa00eabda50030f9f3b1a545"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fauthorization/repository/archive.zip?sha=6f56cf11825f3c82aa00eabda50030f9f3b1a545", "reference": "6f56cf11825f3c82aa00eabda50030f9f3b1a545", "shasum": ""}, "require": {"bshaffer/oauth2-server-httpfoundation-bridge": "dev-master", "bshaffer/oauth2-server-php": "dev-master", "php": "^7.2", "sparefoot/sf_service_bundle": "dev-master", "symfony/twig-bridge": "2.1.*"}, "require-dev": {"symfony/browser-kit": "*", "symfony/phpunit-bridge": "*"}, "type": "library", "autoload": {"psr-4": {"Sparefoot\\Authorization\\": "src/"}}, "autoload-dev": {"psr-4": {"Sparefoot\\Authorization\\Tests\\": "tests/"}}, "scripts": {"post-update-cmd": ["bash vendor/sparefoot/sf_service_bundle/Resources/bin/setup.sh"], "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Service for SpareFoot Authorization", "time": "2019-11-15T21:55:12+00:00"}, {"name": "sparefoot/emails_service_client", "version": "dev-guzzle6-lock", "source": {"type": "git", "url": "**************:storable/sparefoot/emails_service_client.git", "reference": "f12b55baa35420a48371816ec282acbb6c4b8c91"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Femails_service_client/repository/archive.zip?sha=f12b55baa35420a48371816ec282acbb6c4b8c91", "reference": "f12b55baa35420a48371816ec282acbb6c4b8c91", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~6.0", "php": ">=5.5.0"}, "type": "library", "autoload": {"psr-4": {"Sparefoot\\EmailsServiceClient\\": "src/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "SpareFoot emails_service_client library", "homepage": "https://gitlab.com/storable/sparefoot/emails_service_client", "support": {"issues": "https://gitlab.com/api/v4/projects/22388589/issues"}, "time": "2023-08-09T18:27:15+00:00"}, {"name": "sparefoot/error-logging", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/error_logger.git", "reference": "24c9ed1171089c76652e06dc60f3debbc24c2e7d"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Ferror_logger/repository/archive.zip?sha=24c9ed1171089c76652e06dc60f3debbc24c2e7d", "reference": "24c9ed1171089c76652e06dc60f3debbc24c2e7d", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "4.7.*", "phpunit/phpunit-selenium": ">=1.2"}, "type": "library", "autoload": {"psr-4": {"SpareFoot\\ErrorLogging\\": "src/", "SpareFoot\\ErrorLogging\\Config\\": "config/", "SpareFoot\\ErrorLogging\\Lib\\": "lib/"}}, "license": ["proprietary"], "authors": [{"name": "Contributors", "email": "<EMAIL>"}], "description": "<PERSON>refoot Error Logging Library", "support": {"issues": "https://gitlab.com/api/v4/projects/22523058/issues"}, "time": "2020-11-17T22:09:05+00:00"}, {"name": "sparefoot/genesis", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/genesis.git", "reference": "7b91c7cda0a9aaf65f7e353a58c6f79532069351"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fgenesis/repository/archive.zip?sha=7b91c7cda0a9aaf65f7e353a58c6f79532069351", "reference": "7b91c7cda0a9aaf65f7e353a58c6f79532069351", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.22", "developerforce/force.com-toolkit-for-php": "^27.0", "ext-ftp": "*", "ext-json": "*", "ext-soap": "*", "gorkalaucirica/hipchat-v2-api-client": "^1.4", "guzzlehttp/guzzle": "6.x", "guzzlehttp/oauth-subscriber": "0.3.*", "http-interop/http-factory-guzzle": "^1.2", "kamermans/guzzle-oauth2-subscriber": "~1.0", "nategood/httpful": "0.2.11", "php": ">=7.1", "php-http/curl-client": "^1.7", "php-http/guzzle6-adapter": "^1.1", "solarium/solarium": "^5.0.0", "sparefoot/emails_service_client": "dev-guzzle6-lock", "sparefoot/geocoder": "dev-sparefoot-lock", "sparefoot/pillar": "dev-master", "sparefoot/reservation_rules": "dev-master", "twilio/sdk": "^6.44"}, "require-dev": {"brianium/paratest": "0.13.*", "phpunit/phpunit": "5.*"}, "type": "library", "autoload": {"psr-4": {"GenesisTests\\": "Tests/"}, "classmap": ["src", "lib/centaur-php"]}, "license": ["proprietary"], "authors": [{"name": "Contributors", "email": "<EMAIL>"}], "description": "Sparefoot monolith service libraries", "support": {"issues": "https://gitlab.com/api/v4/projects/22387573/issues"}, "time": "2025-04-14T16:11:36+00:00"}, {"name": "sparefoot/geocoder", "version": "dev-sparefoot-lock", "source": {"type": "git", "url": "https://github.com/artiegold-sparefoot/Geocoder.git", "reference": "11d175b14587250cea493cb74cbffd4832a946ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/artiegold-sparefoot/Geocoder/zipball/11d175b14587250cea493cb74cbffd4832a946ca", "reference": "11d175b14587250cea493cb74cbffd4832a946ca", "shasum": ""}, "require": {"igorw/get-in": "^1.0", "php": "^5.5 || ^7.0", "php-http/client-implementation": "^1.0", "php-http/discovery": "^1.0", "php-http/httplug": "^1.0", "php-http/message-factory": "^1.0.2", "psr/http-message-implementation": "^1.0"}, "require-dev": {"geoip2/geoip2": "~2.0", "php-http/guzzle6-adapter": "^1.0", "php-http/message": "^1.0", "php-http/mock-client": "^0.3.0", "phpunit/phpunit": "^4.8", "symfony/stopwatch": "~2.5"}, "suggest": {"ext-geoip": "Enabling the geoip extension allows you to use the MaxMindProvider.", "geoip/geoip": "If you are going to use the MaxMindBinaryProvider (conflict with geoip extension).", "geoip2/geoip2": "If you are going to use the GeoIP2DatabaseProvider.", "symfony/stopwatch": "If you want to use the TimedGeocoder"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"psr-4": {"Geocoder\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The almost missing Geocoder PHP 5.4 library.", "homepage": "http://geocoder-php.org", "keywords": ["abstraction", "geocoder", "geocoding", "geoip"], "time": "2017-05-03T03:55:04+00:00"}, {"name": "sparefoot/phlow_client", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/phlow_client.git", "reference": "619008ef1da39b7530628e919101f7b9142313cb"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fphlow_client/repository/archive.zip?sha=619008ef1da39b7530628e919101f7b9142313cb", "reference": "619008ef1da39b7530628e919101f7b9142313cb", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "library", "autoload": {"psr-4": {"Sparefoot\\PhlowClient\\": "src/"}}, "autoload-dev": {"psr-4": {"Sparefoot\\PhlowClientTest\\": "test/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "SpareFoot phlow_client skeleton", "homepage": "http://stash.sparefoot.com/cs/phlow_client", "support": {"issues": "https://gitlab.com/api/v4/projects/22388624/issues"}, "time": "2020-12-10T17:11:41+00:00"}, {"name": "sparefoot/pillar", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/pillar_lib.git", "reference": "19b107f45ee6e42d8aaa2a0aa068d781e3348605"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fpillar_lib/repository/archive.zip?sha=19b107f45ee6e42d8aaa2a0aa068d781e3348605", "reference": "19b107f45ee6e42d8aaa2a0aa068d781e3348605", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.7"}, "type": "library", "autoload": {"psr-4": {"Pillar\\": "src/", "PillarTest\\": "test/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PDO bootstrapper for using pillar data", "support": {"issues": "https://gitlab.com/api/v4/projects/22388608/issues"}, "time": "2020-11-11T18:09:56+00:00"}, {"name": "sparefoot/reservation_rules", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/reservation_rules.git", "reference": "70718e5f0d30d8c7bbb518e154ebef44019c1da5"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Freservation_rules/repository/archive.zip?sha=70718e5f0d30d8c7bbb518e154ebef44019c1da5", "reference": "70718e5f0d30d8c7bbb518e154ebef44019c1da5", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"Sparefoot\\ReservationRules\\": "src/ReservationRules"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A place for reservation rules to live", "support": {"issues": "https://gitlab.com/api/v4/projects/22388636/issues"}, "time": "2020-12-10T16:31:00+00:00"}, {"name": "sparefoot/salesforce_client", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/salesforce_client.git", "reference": "f2880d6e950646824b8ba6dac04f09a3d544a6ba"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fsalesforce_client/repository/archive.zip?sha=f2880d6e950646824b8ba6dac04f09a3d544a6ba", "reference": "f2880d6e950646824b8ba6dac04f09a3d544a6ba", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.5", "kamermans/guzzle-oauth2-subscriber": "^1.0", "php": "^7.2"}, "require-dev": {"phpunit/phpunit": "~6.5"}, "type": "library", "autoload": {"psr-4": {"Sparefoot\\SalesForce\\": "SalesForce/"}}, "autoload-dev": {"psr-4": {"Sparefoot\\SalesForce\\Tests\\": "tests/"}}, "support": {"issues": "https://gitlab.com/api/v4/projects/22523075/issues"}, "time": "2022-05-03T20:41:55+00:00"}, {"name": "sparefoot/sf_service_bundle", "version": "dev-master", "source": {"type": "git", "url": "**************:storable/sparefoot/sf_service_bundle.git", "reference": "1d3f63b4e6bf727c54c91531fe4748c5a8bd40b0"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/storable%2Fsparefoot%2Fsf_service_bundle/repository/archive.zip?sha=1d3f63b4e6bf727c54c91531fe4748c5a8bd40b0", "reference": "1d3f63b4e6bf727c54c91531fe4748c5a8bd40b0", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.87", "datadog/dd-trace": "0.34.1", "datadog/php-datadogstatsd": "^1.4", "ext-json": "*", "guzzlehttp/guzzle": "^6.4", "php": ">=7.2", "predis/predis": "^1.1", "psr/simple-cache": "^1.0", "segmentio/analytics-php": "^1.5", "symfony/browser-kit": "*", "symfony/cache": "*", "symfony/config": "*", "symfony/console": "*", "symfony/dotenv": "*", "symfony/flex": "*", "symfony/framework-bundle": "*", "symfony/http-foundation": "*", "symfony/messenger": "*", "symfony/monolog-bundle": "*", "symfony/phpunit-bridge": "*", "symfony/proxy-manager-bridge": "*", "symfony/security-bundle": "*", "symfony/serializer": "*", "symfony/test-pack": "^1.0", "symfony/yaml": "*"}, "require-dev": {"phpunit/php-code-coverage": "^7", "phpunit/phpunit": "^8", "psr/log": "^1.0", "sebastian/global-state": "^3"}, "type": "bundle", "extra": {"symfony": {"allow-contrib": false, "require": "4.3.*"}}, "autoload": {"psr-4": {"Sparefoot\\ServiceBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "autoload-dev": {"psr-4": {"Sparefoot\\ServiceBundle\\Tests\\": "tests/"}}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}, "description": "Provides common utilities such as analytics & caching for php projects.", "time": "2019-11-21T01:37:59+00:00"}, {"name": "symfony/browser-kit", "version": "v4.3.8", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "b14fa08508afd152257d5dcc7adb5f278654d972"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/b14fa08508afd152257d5dcc7adb5f278654d972", "reference": "b14fa08508afd152257d5dcc7adb5f278654d972", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/dom-crawler": "~3.4|~4.0"}, "require-dev": {"symfony/css-selector": "~3.4|~4.0", "symfony/http-client": "^4.3", "symfony/mime": "^4.3", "symfony/process": "~3.4|~4.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "time": "2019-10-28T17:07:32+00:00"}, {"name": "symfony/cache", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "a7a14c4832760bd1fbd31be2859ffedc9b6ff813"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/a7a14c4832760bd1fbd31be2859ffedc9b6ff813", "reference": "a7a14c4832760bd1fbd31be2859ffedc9b6ff813", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/cache": "~1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "symfony/polyfill-apcu": "~1.1"}, "conflict": {"symfony/var-dumper": "<3.3"}, "provide": {"psr/cache-implementation": "1.0", "psr/simple-cache-implementation": "1.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6", "doctrine/dbal": "^2.4|^3.0", "predis/predis": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Cache component with PSR-6, PSR-16, and tags", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/class-loader", "version": "v3.4.35", "source": {"type": "git", "url": "https://github.com/symfony/class-loader.git", "reference": "e212b06996819a2bce026a63da03b7182d05a690"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/class-loader/zipball/e212b06996819a2bce026a63da03b7182d05a690", "reference": "e212b06996819a2bce026a63da03b7182d05a690", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/finder": "~2.8|~3.0|~4.0", "symfony/polyfill-apcu": "~1.1"}, "suggest": {"symfony/polyfill-apcu": "For using ApcClassLoader on HHVM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ClassLoader\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ClassLoader Component", "homepage": "https://symfony.com", "abandoned": true, "time": "2019-08-20T13:31:17+00:00"}, {"name": "symfony/config", "version": "v3.4.35", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "c3a30587de97263d2813a3c81b74126c58b67a4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/c3a30587de97263d2813a3c81b74126c58b67a4f", "reference": "c3a30587de97263d2813a3c81b74126c58b67a4f", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/filesystem": "~2.8|~3.0|~4.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/dependency-injection": "<3.3", "symfony/finder": "<3.3"}, "require-dev": {"symfony/dependency-injection": "~3.3|~4.0", "symfony/event-dispatcher": "~3.3|~4.0", "symfony/finder": "~3.3|~4.0", "symfony/yaml": "~3.0|~4.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2019-11-08T08:28:59+00:00"}, {"name": "symfony/console", "version": "v3.3.13", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "63cd7960a0a522c3537f6326706d7f3b8de65805"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/63cd7960a0a522c3537f6326706d7f3b8de65805", "reference": "63cd7960a0a522c3537f6326706d7f3b8de65805", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3", "symfony/dependency-injection": "~3.3", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/filesystem": "~2.8|~3.0", "symfony/process": "~2.8|~3.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/filesystem": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2017-11-16T15:24:32+00:00"}, {"name": "symfony/css-selector", "version": "v4.3.8", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "f4b3ff6a549d9ed28b2b0ecd1781bf67cf220ee9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/f4b3ff6a549d9ed28b2b0ecd1781bf67cf220ee9", "reference": "f4b3ff6a549d9ed28b2b0ecd1781bf67cf220ee9", "shasum": ""}, "require": {"php": "^7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2019-10-02T08:36:26+00:00"}, {"name": "symfony/debug", "version": "v3.4.35", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "f72e33fdb1170b326e72c3157f0cd456351dd086"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/f72e33fdb1170b326e72c3157f0cd456351dd086", "reference": "f72e33fdb1170b326e72c3157f0cd456351dd086", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "abandoned": "symfony/error-handler", "time": "2019-10-24T15:33:53+00:00"}, {"name": "symfony/dependency-injection", "version": "v3.3.18", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "54243abc4e1a1a15e274e391bd6f7090b44711f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/54243abc4e1a1a15e274e391bd6f7090b44711f1", "reference": "54243abc4e1a1a15e274e391bd6f7090b44711f1", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/container": "^1.0"}, "conflict": {"symfony/config": "<3.3.7", "symfony/finder": "<3.3", "symfony/yaml": "<3.3"}, "provide": {"psr/container-implementation": "1.0"}, "require-dev": {"symfony/config": "~3.3", "symfony/expression-language": "~2.8|~3.0", "symfony/yaml": "~3.3"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "time": "2018-01-29T09:02:23+00:00"}, {"name": "symfony/dom-crawler", "version": "v4.3.8", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "4b9efd5708c3a38593e19b6a33e40867f4f89d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/4b9efd5708c3a38593e19b6a33e40867f4f89d72", "reference": "4b9efd5708c3a38593e19b6a33e40867f4f89d72", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "~3.4|~4.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "time": "2019-10-28T17:07:32+00:00"}, {"name": "symfony/dotenv", "version": "v4.3.8", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "62d93bf07edd0d76f033d65a7fd1c1ce50d28b50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/62d93bf07edd0d76f033d65a7fd1c1ce50d28b50", "reference": "62d93bf07edd0d76f033d65a7fd1c1ce50d28b50", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"symfony/process": "^3.4.2|^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "time": "2019-10-18T11:23:15+00:00"}, {"name": "symfony/event-dispatcher", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "31fde73757b6bad247c54597beef974919ec6860"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/31fde73757b6bad247c54597beef974919ec6860", "reference": "31fde73757b6bad247c54597beef974919ec6860", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/debug": "~3.4|~4.4", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/filesystem", "version": "v3.3.13", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "77db266766b54db3ee982fe51868328b887ce15c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/77db266766b54db3ee982fe51868328b887ce15c", "reference": "77db266766b54db3ee982fe51868328b887ce15c", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2017-11-07T14:12:55+00:00"}, {"name": "symfony/finder", "version": "v3.3.13", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "138af5ec075d4b1d1bd19de08c38a34bb2d7d880"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/138af5ec075d4b1d1bd19de08c38a34bb2d7d880", "reference": "138af5ec075d4b1d1bd19de08c38a34bb2d7d880", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2017-11-05T15:47:03+00:00"}, {"name": "symfony/flex", "version": "v1.4.8", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "f5bfc79c1f5bed6b2bb4ca9e49a736c2abc03e8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/f5bfc79c1f5bed6b2bb4ca9e49a736c2abc03e8f", "reference": "f5bfc79c1f5bed6b2bb4ca9e49a736c2abc03e8f", "shasum": ""}, "require": {"composer-plugin-api": "^1.0", "php": "^7.0"}, "require-dev": {"composer/composer": "^1.0.2", "symfony/dotenv": "^3.4|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4.19|^4.1.8|^5.0", "symfony/process": "^2.7|^3.0|^4.0|^5.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex", "branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "time": "2019-11-14T09:25:51+00:00"}, {"name": "symfony/framework-bundle", "version": "v3.3.18", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "2407a3de0048373f85484d7089258e1f1bad4692"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/2407a3de0048373f85484d7089258e1f1bad4692", "reference": "2407a3de0048373f85484d7089258e1f1bad4692", "shasum": ""}, "require": {"doctrine/cache": "~1.0", "ext-xml": "*", "php": "^5.5.9|>=7.0.8", "symfony/cache": "~3.3", "symfony/class-loader": "~3.2", "symfony/config": "~3.3", "symfony/dependency-injection": "~3.3", "symfony/event-dispatcher": "^3.3.1", "symfony/filesystem": "~2.8|~3.0", "symfony/finder": "~2.8|~3.0", "symfony/http-foundation": "^3.3.11", "symfony/http-kernel": "~3.3", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "~3.3", "symfony/stopwatch": "~2.8|~3.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0", "phpdocumentor/type-resolver": "<0.2.1", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/asset": "<3.3", "symfony/console": "<3.3", "symfony/form": "<3.3", "symfony/property-info": "<3.3", "symfony/serializer": "<3.3", "symfony/translation": "<3.2", "symfony/validator": "<3.3", "symfony/workflow": "<3.3"}, "require-dev": {"doctrine/annotations": "~1.0", "fig/link-util": "^1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "sensio/framework-extra-bundle": "^3.0.2", "symfony/asset": "~3.3", "symfony/browser-kit": "~2.8|~3.0", "symfony/console": "~3.3", "symfony/css-selector": "~2.8|~3.0", "symfony/dom-crawler": "~2.8|~3.0", "symfony/expression-language": "~2.8|~3.0", "symfony/form": "~3.3", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "~2.8|~3.0", "symfony/property-info": "~3.3", "symfony/security": "~2.8|~3.0", "symfony/security-core": "~3.2", "symfony/security-csrf": "~2.8|~3.0", "symfony/serializer": "~3.3", "symfony/templating": "~2.8|~3.0", "symfony/translation": "~3.2", "symfony/validator": "~3.3", "symfony/web-link": "~3.3", "symfony/workflow": "~3.3", "symfony/yaml": "~3.2", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony FrameworkBundle", "homepage": "https://symfony.com", "time": "2018-01-29T09:02:23+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.35", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "9e4b3ac8fa3348b4811674d23de32d201de225ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/9e4b3ac8fa3348b4811674d23de32d201de225ce", "reference": "9e4b3ac8fa3348b4811674d23de32d201de225ce", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2019-11-11T12:53:10+00:00"}, {"name": "symfony/http-kernel", "version": "v3.3.18", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "ad99c00584863e677c6c6fa2b1badd26890f6d26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/ad99c00584863e677c6c6fa2b1badd26890f6d26", "reference": "ad99c00584863e677c6c6fa2b1badd26890f6d26", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0", "symfony/debug": "~2.8|~3.0", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/http-foundation": "^3.3.11"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.3", "symfony/var-dumper": "<3.3", "twig/twig": "<1.34|<2.4,>=2"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "~2.8|~3.0", "symfony/class-loader": "~2.8|~3.0", "symfony/config": "~2.8|~3.0", "symfony/console": "~2.8|~3.0", "symfony/css-selector": "~2.8|~3.0", "symfony/dependency-injection": "~3.3", "symfony/dom-crawler": "~2.8|~3.0", "symfony/expression-language": "~2.8|~3.0", "symfony/finder": "~2.8|~3.0", "symfony/process": "~2.8|~3.0", "symfony/routing": "~2.8|~3.0", "symfony/stopwatch": "~2.8|~3.0", "symfony/templating": "~2.8|~3.0", "symfony/translation": "~2.8|~3.0", "symfony/var-dumper": "~3.3"}, "suggest": {"symfony/browser-kit": "", "symfony/class-loader": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/finder": "", "symfony/var-dumper": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2018-08-01T14:04:30+00:00"}, {"name": "symfony/inflector", "version": "v4.3.8", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "f97c69c132c08e31d291689d2d77bb0878094acb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/f97c69c132c08e31d291689d2d77bb0878094acb", "reference": "f97c69c132c08e31d291689d2d77bb0878094acb", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Inflector Component", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "abandoned": "EnglishInflector from the String component", "time": "2019-11-05T19:58:22+00:00"}, {"name": "symfony/messenger", "version": "v4.2.12", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "b7eb1cb7f5ad4451565971afd92912d376548d01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/b7eb1cb7f5ad4451565971afd92912d376548d01", "reference": "b7eb1cb7f5ad4451565971afd92912d376548d01", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"psr/log": "~1.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4.19|^4.1.8", "symfony/http-kernel": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "symfony/property-access": "~3.4|~4.0", "symfony/serializer": "~3.4|~4.0", "symfony/stopwatch": "~3.4|~4.0", "symfony/validator": "~3.4|~4.0", "symfony/var-dumper": "~3.4|~4.0"}, "suggest": {"enqueue/messenger-adapter": "For using the php-enqueue library as a transport."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Messenger Component", "homepage": "https://symfony.com", "time": "2019-07-24T07:56:23+00:00"}, {"name": "symfony/monolog-bridge", "version": "v3.4.35", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "a2c763498f3fa69bd65a615ad4b89d6bd1d9ce20"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/a2c763498f3fa69bd65a615ad4b89d6bd1d9ce20", "reference": "a2c763498f3fa69bd65a615ad4b89d6bd1d9ce20", "shasum": ""}, "require": {"monolog/monolog": "~1.19", "php": "^5.5.9|>=7.0.8", "symfony/http-kernel": "~2.8|~3.0|~4.0"}, "conflict": {"symfony/console": "<2.8", "symfony/http-foundation": "<3.3"}, "require-dev": {"symfony/console": "~2.8|~3.0|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/security-core": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings. You need version ^2.8 of the console for it.", "symfony/event-dispatcher": "Needed when using log messages in console commands.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Monolog Bridge", "homepage": "https://symfony.com", "time": "2019-08-07T11:38:48+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "8781649349fe418d51d194f8c9d212c0b97c40dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/8781649349fe418d51d194f8c9d212c0b97c40dd", "reference": "8781649349fe418d51d194f8c9d212c0b97c40dd", "shasum": ""}, "require": {"monolog/monolog": "~1.22", "php": ">=5.3.2", "symfony/config": "~2.7|~3.0|~4.0", "symfony/dependency-injection": "~2.7|~3.0|~4.0", "symfony/http-kernel": "~2.7|~3.0|~4.0", "symfony/monolog-bridge": "~2.7|~3.0|~4.0"}, "require-dev": {"symfony/console": "~2.3|~3.0|~4.0", "symfony/phpunit-bridge": "^3.3|^4.0", "symfony/yaml": "~2.3|~3.0|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "time": "2018-03-05T14:51:36+00:00"}, {"name": "symfony/options-resolver", "version": "v3.3.13", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "623d9c210a137205f7e6e98166105625402cbb2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/623d9c210a137205f7e6e98166105625402cbb2f", "reference": "623d9c210a137205f7e6e98166105625402cbb2f", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2017-11-05T15:47:03+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v4.3.8", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "c216b32261358a820bb4217eb3a20e3f437a484e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/c216b32261358a820bb4217eb3a20e3f437a484e", "reference": "c216b32261358a820bb4217eb3a20e3f437a484e", "shasum": ""}, "require": {"php": ">=5.5.9"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "suggest": {"symfony/debug": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"thanks": {"url": "https://github.com/sebastian<PERSON>mann/phpunit", "name": "phpunit/phpunit"}, "branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PHPUnit Bridge", "homepage": "https://symfony.com", "time": "2019-10-30T12:58:49+00:00"}, {"name": "symfony/polyfill-apcu", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-apcu.git", "reference": "6e7f6ed2168779a2b3927e606a9768860a8bdfa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-apcu/zipball/6e7f6ed2168779a2b3927e606a9768860a8bdfa0", "reference": "6e7f6ed2168779a2b3927e606a9768860a8bdfa0", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Apcu\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting apcu_* functions to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["apcu", "compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "550ebaac289296ce228a706d0867afc34687e3f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/550ebaac289296ce228a706d0867afc34687e3f4", "reference": "550ebaac289296ce228a706d0867afc34687e3f4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2019-08-06T08:03:45+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "639084e360537a19f9ee352433b84ce831f3d2da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/639084e360537a19f9ee352433b84ce831f3d2da", "reference": "639084e360537a19f9ee352433b84ce831f3d2da", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/19bd1e4fcd5b91116f14d8533c57831ed00571b6", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "0e3b212e96a51338639d8ce175c046d7729c3403"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/0e3b212e96a51338639d8ce175c046d7729c3403", "reference": "0e3b212e96a51338639d8ce175c046d7729c3403", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-util": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php56\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-08-06T08:03:45+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "54b4c428a0054e254223797d2713c31e08610831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/54b4c428a0054e254223797d2713c31e08610831", "reference": "54b4c428a0054e254223797d2713c31e08610831", "shasum": ""}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-08-06T08:03:45+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/869329b1e9894268a8a61dabb69153029b7a8c97", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-util", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-util.git", "reference": "4317de1386717b4c22caed7725350a8887ab205c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-util/zipball/4317de1386717b4c22caed7725350a8887ab205c", "reference": "4317de1386717b4c22caed7725350a8887ab205c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Util\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony utilities for portability of PHP codes", "homepage": "https://symfony.com", "keywords": ["compat", "compatibility", "polyfill", "shim"], "time": "2019-08-06T08:03:45+00:00"}, {"name": "symfony/process", "version": "v3.3.13", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "a56a3989fb762d7b19a0cf8e7693ee99a6ffb78d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/a56a3989fb762d7b19a0cf8e7693ee99a6ffb78d", "reference": "a56a3989fb762d7b19a0cf8e7693ee99a6ffb78d", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2017-11-13T15:31:11+00:00"}, {"name": "symfony/property-access", "version": "v4.3.8", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "bb0c302375ffeef60c31e72a4539611b7f787565"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/bb0c302375ffeef60c31e72a4539611b7f787565", "reference": "bb0c302375ffeef60c31e72a4539611b7f787565", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/inflector": "~3.4|~4.0"}, "require-dev": {"symfony/cache": "~3.4|~4.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PropertyAccess Component", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "time": "2019-08-26T08:26:39+00:00"}, {"name": "symfony/proxy-manager-bridge", "version": "v3.3.18", "source": {"type": "git", "url": "https://github.com/symfony/proxy-manager-bridge.git", "reference": "82b06001ba31dc4930eff1dd73fad54ee47edb22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/proxy-manager-bridge/zipball/82b06001ba31dc4930eff1dd73fad54ee47edb22", "reference": "82b06001ba31dc4930eff1dd73fad54ee47edb22", "shasum": ""}, "require": {"ocramius/proxy-manager": "~0.4|~1.0|~2.0", "php": "^5.5.9|>=7.0.8", "symfony/dependency-injection": "~2.8|~3.0"}, "require-dev": {"symfony/config": "~2.8|~3.0"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\ProxyManager\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ProxyManager Bridge", "homepage": "https://symfony.com", "time": "2018-01-03T07:37:11+00:00"}, {"name": "symfony/routing", "version": "v3.3.18", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "3329b5bf114779dcea6f9d0c04cd55e2d7553067"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/3329b5bf114779dcea6f9d0c04cd55e2d7553067", "reference": "3329b5bf114779dcea6f9d0c04cd55e2d7553067", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.3"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/common": "~2.2", "psr/log": "~1.0", "symfony/config": "~2.8|~3.0", "symfony/dependency-injection": "~3.3", "symfony/expression-language": "~2.8|~3.0", "symfony/http-foundation": "~2.8|~3.0", "symfony/yaml": "~3.3"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/dependency-injection": "For loading routes from a service", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2018-01-16T18:03:02+00:00"}, {"name": "symfony/security", "version": "v3.4.35", "source": {"type": "git", "url": "https://github.com/symfony/security.git", "reference": "007dca771e024ac9279e2b5ad16b059f291d1970"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security/zipball/007dca771e024ac9279e2b5ad16b059f291d1970", "reference": "007dca771e024ac9279e2b5ad16b059f291d1970", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "^2.8.31|~3.3.13|~3.4|~4.0", "symfony/http-kernel": "~3.3|~4.0", "symfony/polyfill-php56": "~1.0", "symfony/polyfill-php70": "~1.0", "symfony/property-access": "~2.8|~3.0|~4.0"}, "replace": {"symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-guard": "self.version", "symfony/security-http": "self.version"}, "require-dev": {"psr/container": "^1.0", "psr/log": "~1.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/ldap": "~3.1|~4.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "~2.8|~3.0|~4.0", "symfony/validator": "^3.2.5|~4.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/expression-language": "For using the expression voter", "symfony/form": "", "symfony/ldap": "For using the LDAP user and authentication providers", "symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/validator": "For using the user password constraint"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Security\\": ""}, "exclude-from-classmap": ["/Core/Tests/", "/Csrf/Tests/", "/Guard/Tests/", "/Http/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component", "homepage": "https://symfony.com", "abandoned": true, "time": "2019-10-24T15:33:53+00:00"}, {"name": "symfony/security-bundle", "version": "v3.3.18", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "d0da0b5145daea8b5f4a49dfbcd25b938830ae75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/d0da0b5145daea8b5f4a49dfbcd25b938830ae75", "reference": "d0da0b5145daea8b5f4a49dfbcd25b938830ae75", "shasum": ""}, "require": {"ext-xml": "*", "php": "^5.5.9|>=7.0.8", "symfony/dependency-injection": "~3.3", "symfony/http-kernel": "~3.3", "symfony/polyfill-php70": "~1.0", "symfony/security": "~3.3.17|~3.4.11"}, "conflict": {"symfony/var-dumper": "<3.3"}, "require-dev": {"doctrine/doctrine-bundle": "~1.4", "symfony/asset": "~2.8|~3.0", "symfony/browser-kit": "~2.8|~3.0", "symfony/console": "~3.2", "symfony/css-selector": "~2.8|~3.0", "symfony/dom-crawler": "~2.8|~3.0", "symfony/expression-language": "~2.8|~3.0", "symfony/form": "^2.8.18|^3.2.5", "symfony/framework-bundle": "^3.2.8", "symfony/http-foundation": "~2.8|~3.0", "symfony/process": "~2.8|~3.0", "symfony/security-acl": "~2.8|~3.0", "symfony/translation": "~2.8|~3.0", "symfony/twig-bridge": "~2.8|~3.0", "symfony/twig-bundle": "~2.8|~3.0", "symfony/validator": "^3.2.5", "symfony/var-dumper": "~3.3", "symfony/yaml": "~2.8|~3.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"symfony/security-acl": "For using the ACL functionality of this bundle"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony SecurityBundle", "homepage": "https://symfony.com", "time": "2018-05-24T18:59:55+00:00"}, {"name": "symfony/serializer", "version": "v3.3.18", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "49a429eba5a2f96c170e8b4b834ce612c257fb32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/49a429eba5a2f96c170e8b4b834ce612c257fb32", "reference": "49a429eba5a2f96c170e8b4b834ce612c257fb32", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"phpdocumentor/type-resolver": "<0.2.1", "symfony/dependency-injection": "<3.2", "symfony/property-access": ">=3.0,<3.0.4|>=2.8,<2.8.4", "symfony/property-info": "<3.1", "symfony/yaml": "<3.3"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "~3.1", "symfony/config": "~2.8|~3.0", "symfony/dependency-injection": "~3.2", "symfony/http-foundation": "~2.8|~3.0", "symfony/property-access": "~2.8|~3.0", "symfony/property-info": "~3.1", "symfony/yaml": "~3.3"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "To use the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Serializer Component", "homepage": "https://symfony.com", "time": "2018-01-17T08:02:20+00:00"}, {"name": "symfony/stopwatch", "version": "v3.3.13", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "1e93c3139ef6c799831fe03efd0fb1c7aecb3365"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/1e93c3139ef6c799831fe03efd0fb1c7aecb3365", "reference": "1e93c3139ef6c799831fe03efd0fb1c7aecb3365", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2017-11-10T19:02:53+00:00"}, {"name": "symfony/test-pack", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/symfony/test-pack.git", "reference": "ff87e800a67d06c423389f77b8209bc9dc469def"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/test-pack/zipball/ff87e800a67d06c423389f77b8209bc9dc469def", "reference": "ff87e800a67d06c423389f77b8209bc9dc469def", "shasum": ""}, "require": {"php": "^7.0", "symfony/browser-kit": "*", "symfony/css-selector": "*", "symfony/phpunit-bridge": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for functional and end-to-end testing within a Symfony app", "time": "2019-06-21T06:27:32+00:00"}, {"name": "symfony/twig-bridge", "version": "v2.1.13", "target-dir": "Symfony/Bridge/Twig", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "e3a48a99971c35ad8d1c5bf36b6b3d22b15df306"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/e3a48a99971c35ad8d1c5bf36b6b3d22b15df306", "reference": "e3a48a99971c35ad8d1c5bf36b6b3d22b15df306", "shasum": ""}, "require": {"php": ">=5.3.3", "twig/twig": ">=1.9.1,<2.0-dev"}, "require-dev": {"symfony/form": "2.1.*", "symfony/routing": "2.1.*", "symfony/security": "2.1.*", "symfony/templating": "2.1.*", "symfony/translation": "2.1.*", "symfony/yaml": "2.1.*"}, "suggest": {"symfony/form": "2.1.*", "symfony/routing": "2.1.*", "symfony/security": "2.1.*", "symfony/templating": "2.1.*", "symfony/translation": "2.1.*", "symfony/yaml": "2.1.*"}, "type": "symfony-bridge", "autoload": {"psr-0": {"Symfony\\Bridge\\Twig": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony Twig Bridge", "homepage": "http://symfony.com", "time": "2013-04-22T04:28:40+00:00"}, {"name": "symfony/yaml", "version": "v3.3.13", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "0938408c4faa518d95230deabb5f595bf0de31b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/0938408c4faa518d95230deabb5f595bf0de31b9", "reference": "0938408c4faa518d95230deabb5f595bf0de31b9", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/console": "~2.8|~3.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2017-11-10T18:26:04+00:00"}, {"name": "twig/twig", "version": "v1.42.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "e587180584c3d2d6cb864a0454e777bb6dcb6152"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/e587180584c3d2d6cb864a0454e777bb6dcb6152", "reference": "e587180584c3d2d6cb864a0454e777bb6dcb6152", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/debug": "^3.4|^4.2", "symfony/phpunit-bridge": "^4.4@dev|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.42-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "homepage": "https://twig.symfony.com/contributors", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2019-11-11T16:49:32+00:00"}, {"name": "twilio/sdk", "version": "6.44.4", "source": {"type": "git", "url": "https://github.com/twilio/twilio-php.git", "reference": "08aad5f377e2245b9cd7508e7762d95e7392fa4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twilio/twilio-php/zipball/08aad5f377e2245b9cd7508e7762d95e7392fa4d", "reference": "08aad5f377e2245b9cd7508e7762d95e7392fa4d", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "phpunit/phpunit": ">=7.0 < 10"}, "suggest": {"guzzlehttp/guzzle": "An HTTP client to execute the API requests"}, "type": "library", "autoload": {"psr-4": {"Twilio\\": "src/<PERSON>wi<PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Twilio API Team", "email": "<EMAIL>"}], "description": "A PHP wrapper for Twilio's API", "homepage": "https://github.com/twilio/twilio-php", "keywords": ["api", "sms", "twi<PERSON>"], "time": "2023-02-22T19:59:53+00:00"}, {"name": "webmozart/assert", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "2db61e59ff05fe5126d152bd0655c9ea113e550f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/2db61e59ff05fe5126d152bd0655c9ea113e550f", "reference": "2db61e59ff05fe5126d152bd0655c9ea113e550f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2016-11-23T20:04:58+00:00"}, {"name": "zendframework/zend-code", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/zendframework/zend-code.git", "reference": "46feaeecea14161734b56c1ace74f28cb329f194"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-code/zipball/46feaeecea14161734b56c1ace74f28cb329f194", "reference": "46feaeecea14161734b56c1ace74f28cb329f194", "shasum": ""}, "require": {"php": "^7.1", "zendframework/zend-eventmanager": "^2.6 || ^3.0"}, "require-dev": {"doctrine/annotations": "^1.0", "ext-phar": "*", "phpunit/phpunit": "^7.5.16 || ^8.4", "zendframework/zend-coding-standard": "^1.0", "zendframework/zend-stdlib": "^2.7 || ^3.0"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "zendframework/zend-stdlib": "Zend\\Stdlib component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev", "dev-develop": "3.5.x-dev"}}, "autoload": {"psr-4": {"Zend\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "keywords": ["ZendFramework", "code", "zf"], "abandoned": "laminas/laminas-code", "time": "2019-10-05T23:18:22+00:00"}, {"name": "zendframework/zend-eventmanager", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-eventmanager.git", "reference": "a5e2583a211f73604691586b8406ff7296a946dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-eventmanager/zipball/a5e2583a211f73604691586b8406ff7296a946dd", "reference": "a5e2583a211f73604691586b8406ff7296a946dd", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"athletic/athletic": "^0.1", "container-interop/container-interop": "^1.1.0", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2", "zendframework/zend-coding-standard": "~1.0.0", "zendframework/zend-stdlib": "^2.7.3 || ^3.0"}, "suggest": {"container-interop/container-interop": "^1.1.0, to use the lazy listeners feature", "zendframework/zend-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev", "dev-develop": "3.3-dev"}}, "autoload": {"psr-4": {"Zend\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://github.com/zendframework/zend-eventmanager", "keywords": ["event", "eventmanager", "events", "zf2"], "abandoned": "laminas/laminas-eventmanager", "time": "2018-04-25T15:33:34+00:00"}, {"name": "zendframework/zendframework1", "version": "1.12.20", "source": {"type": "git", "url": "https://github.com/zendframework/zf1.git", "reference": "737ef159654fbbef37cf9af742b2c8f9690c2ece"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zf1/zipball/737ef159654fbbef37cf9af742b2c8f9690c2ece", "reference": "737ef159654fbbef37cf9af742b2c8f9690c2ece", "shasum": ""}, "require": {"php": ">=5.2.11"}, "require-dev": {"phpunit/dbunit": "1.3.*", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12.x-dev"}}, "autoload": {"psr-0": {"Zend_": "library/"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["library/"], "license": ["BSD-3-<PERSON><PERSON>"], "description": "Zend Framework 1", "homepage": "http://framework.zend.com/", "keywords": ["ZF1", "framework"], "abandoned": "zendframework/zendframework", "time": "2016-09-08T14:50:34+00:00"}, {"name": "zordius/lightncandy", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/zordius/lightncandy.git", "reference": "f96eb18212a0b4e04360b84323f3e8f23fd018c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zordius/lightncandy/zipball/f96eb18212a0b4e04360b84323f3e8f23fd018c7", "reference": "f96eb18212a0b4e04360b84323f3e8f23fd018c7", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "4.8.35"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.1-dev"}}, "autoload": {"psr-4": {"LightnCandy\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An extremely fast PHP implementation of handlebars ( http://handlebarsjs.com/ ) and mustache ( http://mustache.github.io/ ).", "homepage": "https://github.com/zordius/lightncandy", "keywords": ["handlebars", "logicless", "mustache", "php", "template"], "time": "2017-10-13T08:42:32+00:00"}], "packages-dev": [{"name": "andreas-weber/php-junit-merge", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/andreas-weber/php-junit-merge.git", "reference": "065a624faf5f72a7d33b8cdd10839ca3dd1ab943"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/andreas-weber/php-junit-merge/zipball/065a624faf5f72a7d33b8cdd10839ca3dd1ab943", "reference": "065a624faf5f72a7d33b8cdd10839ca3dd1ab943", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/console": "~2.4|^3.0", "symfony/finder": "~2.4|^3.0", "theseer/fdomdocument": "1.6.*"}, "require-dev": {"phploc/phploc": "2.0.*", "phpmd/phpmd": "2.0.*", "phpunit/phpunit": "4.1.*", "sebastian/phpcpd": "2.0.*", "squizlabs/php_codesniffer": "1.5.*"}, "bin": ["phpjunitmerge"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "PHP-Unit J-Unit XML File Merger", "homepage": "https://github.com/andreas-weber/php-junit-merge", "time": "2016-07-19T13:16:13+00:00"}, {"name": "browserstack/browserstack-local", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/browserstack/browserstack-local-php.git", "reference": "491c6e31960ce8111d2cb70cb84d03e73f270dbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/browserstack/browserstack-local-php/zipball/491c6e31960ce8111d2cb70cb84d03e73f270dbb", "reference": "491c6e31960ce8111d2cb70cb84d03e73f270dbb", "shasum": ""}, "require": {"php": ">=5.3.19"}, "require-dev": {"phpunit/phpunit": "4.6.*"}, "suggest": {"phpdocumentor/phpdocumentor": "2.*"}, "type": "library", "autoload": {"psr-4": {"BrowserStack\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHP bindings for BrowserStack Local", "homepage": "https://github.com/browserstack/browserstack-local-php", "keywords": ["BrowserStack", "browserstacklocal", "local", "php", "selenium"], "time": "2016-09-19T13:39:06+00:00"}, {"name": "doctrine/annotations", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "b17c5014ef81d212ac539f07a1001832df1b6d3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/b17c5014ef81d212ac539f07a1001832df1b6d3b", "reference": "b17c5014ef81d212ac539f07a1001832df1b6d3b", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/cache": "1.*", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^9.1.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2021-02-21T21:00:45+00:00"}, {"name": "doctrine/collections", "version": "1.6.7", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "55f8b799269a1a472457bd1a41b4f379d4cfba4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/55f8b799269a1a472457bd1a41b4f379d4cfba4a", "reference": "55f8b799269a1a472457bd1a41b4f379d4cfba4a", "shasum": ""}, "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan-shim": "^0.9.2", "phpunit/phpunit": "^7.0", "vimeo/psalm": "^3.8.1"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "time": "2020-07-27T17:53:49+00:00"}, {"name": "doctrine/common", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/f3812c026e557892c34ef37f6ab808a6b567da7f", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2020-06-05T16:46:05+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/lexer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2020-05-25T17:44:05+00:00"}, {"name": "doctrine/persistence", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2020-06-20T12:56:16+00:00"}, {"name": "doctrine/reflection", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "fa587178be682efe90d005e3a322590d6ebb59a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/fa587178be682efe90d005e3a322590d6ebb59a5", "reference": "fa587178be682efe90d005e3a322590d6ebb59a5", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^6.0 || ^8.2.0", "doctrine/common": "^2.10", "phpstan/phpstan": "^0.11.0 || ^0.12.20", "phpstan/phpstan-phpunit": "^0.11.0 || ^0.12.16", "phpunit/phpunit": "^7.5 || ^9.1.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "abandoned": "roave/better-reflection", "time": "2020-10-27T21:46:55+00:00"}, {"name": "markbaker/complex", "version": "1.4.7", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "1ea674a8308baf547cbcbd30c5fcd6d301b7c000"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/1ea674a8308baf547cbcbd30c5fcd6d301b7c000", "reference": "1ea674a8308baf547cbcbd30c5fcd6d301b7c000", "shasum": ""}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.3", "phpcompatibility/php-compatibility": "^8.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.4.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.3.0"}, "type": "library", "autoload": {"files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"], "psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2018-10-13T23:28:42+00:00"}, {"name": "markbaker/matrix", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "6ea97472b5baf12119b4f31f802835b820dd6d64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/6ea97472b5baf12119b4f31f802835b820dd6d64", "reference": "6ea97472b5baf12119b4f31f802835b820dd6d64", "shasum": ""}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.3", "phpcompatibility/php-compatibility": "^8.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.4.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.3.0"}, "type": "library", "autoload": {"files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"], "psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2018-11-04T22:12:12+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "bf00f0cc5f55c354018f9a9ef15e6e3e1a229051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/bf00f0cc5f55c354018f9a9ef15e6e3e1a229051", "reference": "bf00f0cc5f55c354018f9a9ef15e6e3e1a229051", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.1", "php": "^5.6|^7.0", "psr/simple-cache": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.0", "dompdf/dompdf": "^0.8.0", "friendsofphp/php-cs-fixer": "@stable", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^7.0.0", "phpcompatibility/php-compatibility": "^8.0", "phpunit/phpunit": "^5.7", "squizlabs/php_codesniffer": "^3.3", "tecnickcom/tcpdf": "^6.2"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2019-01-02T04:42:54+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.1.6", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "bf4940572e43af679aaa13be98f3446a1c237bd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/bf4940572e43af679aaa13be98f3446a1c237bd8", "reference": "bf4940572e43af679aaa13be98f3446a1c237bd8", "shasum": ""}, "require": {"doctrine/common": "^2.2", "symfony/config": "^3.3|^4.0", "symfony/dependency-injection": "^3.3|^4.0", "symfony/framework-bundle": "^3.3|^4.0", "symfony/http-kernel": "^3.3|^4.0"}, "require-dev": {"doctrine/doctrine-bundle": "^1.6", "doctrine/orm": "^2.5", "symfony/browser-kit": "^3.3|^4.0", "symfony/dom-crawler": "^3.3|^4.0", "symfony/expression-language": "^3.3|^4.0", "symfony/finder": "^3.3|^4.0", "symfony/phpunit-bridge": "^3.3|^4.0", "symfony/psr-http-message-bridge": "^0.3", "symfony/security-bundle": "^3.3|^4.0", "symfony/twig-bundle": "^3.3|^4.0", "symfony/yaml": "^3.3|^4.0", "twig/twig": "~1.12|~2.0", "zendframework/zend-diactoros": "^1.3"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.1.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "abandoned": "Symfony", "time": "2018-02-14T08:40:54+00:00"}, {"name": "symfony/property-info", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "a5f1e77c881342a5b1e05fdc12642650853bd112"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/a5f1e77c881342a5b1e05fdc12642650853bd112", "reference": "a5f1e77c881342a5b1e05fdc12642650853bd112", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/inflector": "~3.1|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0||>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0", "symfony/dependency-injection": "<3.3"}, "require-dev": {"doctrine/annotations": "~1.7", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "~3.1|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/serializer": "~2.8|~3.0|~4.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Property Info Component", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "theseer/fdomdocument", "version": "1.6.6", "source": {"type": "git", "url": "https://github.com/theseer/fDOMDocument.git", "reference": "6e8203e40a32a9c770bcb62fe37e68b948da6dca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/fDOMDocument/zipball/6e8203e40a32a9c770bcb62fe37e68b948da6dca", "reference": "6e8203e40a32a9c770bcb62fe37e68b948da6dca", "shasum": ""}, "require": {"ext-dom": "*", "lib-libxml": "*", "php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The classes contained within this repository extend the standard DOM to use exceptions at all occasions of errors instead of PHP warnings or notices. They also add various custom methods and shortcuts for convenience and to simplify the usage of DOM.", "homepage": "https://github.com/theseer/fDOMDocument", "abandoned": true, "time": "2017-06-30T11:53:12+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"sparefoot/emails_service_client": 20, "sparefoot/error-logging": 20, "sparefoot/phlow_client": 20, "sparefoot/pillar": 20, "sparefoot/reservation_rules": 20, "sparefoot/authorization": 20, "sparefoot/salesforce_client": 20, "sparefoot/genesis": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=7.4", "ext-zip": "^1.15", "ext-soap": "^7.4", "ext-gd": "^7.4", "ext-pdo": "^7.2", "ext-json": "*"}, "platform-dev": [], "plugin-api-version": "1.1.0"}