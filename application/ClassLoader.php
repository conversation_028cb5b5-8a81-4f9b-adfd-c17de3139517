<?php
/**
 * Class Loader
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR>
 * @package genesis.util
 */
class AccountMgmt_ClassLoader
{
    /**
     * Dynamically load class
     *
     * @static
     * @param String $class
     */
    public static function load($className)
    {
            $absolutePath =  APPLICATION_PATH .
                DIRECTORY_SEPARATOR . self::_transformToClassPath($className);

            require_once $absolutePath;
    }

    /**
     * Convenience method to transform a class name in the form:
     * Psearch_Package_ClassName to psearch/package/ClassName.class.php
     *
     * @param  String $className
     * @return String
     */
    private static function _transformToClassPath($className)
    {
            $pathArray = explode('_', $className);
            array_shift($pathArray);

            $fileName = $pathArray[count($pathArray) - 1];

            $className = implode('_', $pathArray);

            $srcPath = dirname(str_replace('_', DIRECTORY_SEPARATOR,
                    strtolower($className)));

            return $srcPath . DIRECTORY_SEPARATOR . $fileName . '.php';
    }

}
