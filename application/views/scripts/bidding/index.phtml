<h1>Unit-level bids</h1>

<h2>Facility Average Bid: <?=$this->facility->getBidFlat()?></h2>

<form method="post">
<table border="1">
    <tr>
    <th>Unit ID</th>
    <th>Unit</th>
    <th>Bid Amount</th>
    </tr>
<?php
foreach ($this->units as $unit) {
    ?>
    <tr>
        <td><?=$unit->getId()?></td>
        <td><?=$unit->stringUnitDescription(false)?></td>
        <td><input type="text" name="bid_amount_<?=$unit->getId()?>" value="<?=($unit->getUnitBidAmount() ? $unit->getUnitBidAmount() : Genesis_Entity_StorageSpace::MINIMUM_UNIT_BID_AMOUNT)?>" /></td>
        
    </tr>
    <?php
}
?>
</table>

<input type="hidden" name="submit" value="1" />
<input type="submit" />

</form>

