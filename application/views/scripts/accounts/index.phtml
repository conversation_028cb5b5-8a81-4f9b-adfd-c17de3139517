<?php if ($this->loggedUser->isMyfootGod()): ?>
<form action="<?=$this->action?>" id="accounts-form">
    <select name="account_id">
    <?php foreach($this->accounts as $account): ?>
       <option <?=($this->accountId === $account['account_id']) ? ' selected="selected" ' : null ?> value="<?=$account['account_id']?>">
                <?=$account['name']?> &mdash; <?=str_replace(',',', ',$account['integration_names'])?> (<?=$account['num_facilities']?>)
                <?=($account['status'] && $account['status'] !== "Live") ? " &mdash; ".strtoupper($account['status']) : null ?>

        </option>
    <?php endforeach; ?>
    </select>
</form>
<?php endif;
