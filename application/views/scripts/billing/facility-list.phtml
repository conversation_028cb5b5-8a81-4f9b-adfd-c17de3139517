// The '<li ' bits are intentional, they get closed eventually.
$this->view->leftSideBarContent =
    '<h2>Billing</h2><ul><li '
    . (($this->view->selectedAction == 'statements' || $this->view->selectedAction == 'viewstatement') ? 'class="selected"' : '')
    . '><a href="/billing/statements">Statements</a></li></ul>'
    . '<h2>Facilities</h2><div class="ui-layout-content" style="overflow-x: hidden;"><ul><li '
    . (!$this->view->facilityId ? 'class="selected"' : '')
    . '><a href="/billing/viewstatement/id/'
    . $this->view->statementId
    . '" class="allfacilities">All Facilities</a></li>';

foreach ($this->view->tableList as $tableInfo) {
    $this->view->leftSideBarContent .=
    '<li '
     . ($this->view->facilityId == $tableInfo['FACILITY']->getId() ? 'class="selected"' : '')
     . '><a href="/billing/viewstatement/id/'
     . $this->view->statementId
     . '/facility/'
     . $tableInfo['FACILITY']->getId()
     . '" class="facility">'
     . $tableInfo['FACILITY']->getTitle()
     . '</a></li>';
}

$this->view->leftSideBarContent .= '</ul></div>';
