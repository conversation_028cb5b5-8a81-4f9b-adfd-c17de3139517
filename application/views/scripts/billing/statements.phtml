<?php

    if(count($this->statementPairs) < 1) { ?>
<p style="margin:1em;">You have no statements at this time.
</p>

<?php } else { ?>

<?php foreach($this->statementPairs as $pair) { ?>

    <?php if($pair['BATCH']->getStatus() == 'OPEN') {?>
        <div class="well form-inline">
                    <h6>Open Statement</h6> <h3><?=date('F j',strtotime($pair['BATCH']->getStartDate()))?>-<?=date('j',strtotime($pair['BATCH']->getEndDate()))?>
                <span style="color:#888; font-weight:normal;"><?php echo ($this->account->getBidType() == 'RESIDUAL' ? 'Rent Collected' : 'Move-Ins');?></span></h3>
            <br />
            <div class="row">
                <div class="col-md-5">
                    <table class="table">
                        <tr>
                            <th>Reconciliation Deadline</th>
                            <td><?=date('F j', strtotime($pair['BATCH']->getReconciliationEndDate()))?></td>
                        </tr>
                        <tr>
                            <th><?echo ($this->account->getBidType() == 'RESIDUAL' ? 'Current Tenants' : 'Current Move-Ins');?></th>
                            <td><?=($this->userId) ?
                            ($pair['STATEMENT']->getTotalPendingMinusDelayedByUserId($this->userId) + $pair['STATEMENT']->getTotalConfirmedByUserId($this->userId)) :
                            ($pair['STATEMENT']->getTotalPendingMinusDelayed() + $pair['STATEMENT']->getTotalConfirmed())
                    ?> out of <?= (($this->userId) ?
                            $pair['STATEMENT']->getTotalDisputedByUserId($this->userId) :
                            $pair['STATEMENT']->getTotalDisputed()) + (($this->userId) ?
                            ($pair['STATEMENT']->getTotalPendingMinusDelayedByUserId($this->userId) + $pair['STATEMENT']->getTotalConfirmedByUserId($this->userId)) :
                            ($pair['STATEMENT']->getTotalPendingMinusDelayed() + $pair['STATEMENT']->getTotalConfirmed()))?></td>
                        </tr>
                        <tr>
                            <th><?echo ($this->account->getBidType() == 'RESIDUAL' ? 'Current SpareFoot Fees (not including taxes)' : 'Current Move-In Fees (not including taxes)');?></th>
                            <td>$<?= ($this->userId) ?
                            (($pair['STATEMENT']->getTotalAmountByUserId($this->userId)) ? $pair['STATEMENT']->getTotalAmountByUserId($this->userId) : '0.00') :
                            (($pair['STATEMENT']->getDynamicTotalAmount()) ? $pair['STATEMENT']->getDynamicTotalAmount() : '0.00')
                            ?></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="btn-toolbar">
                <div class="btn-group">
                    <a href="/billing/viewstatement/id/<?=$pair['STATEMENT']->getId()?>" class="ui primary button" id="open_statement">Reconcile Statement</a>
                </div>
                <div class="btn-group">
                    <a class="ui button large dropdown-toggle" data-toggle="dropdown" href="#">Download <span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li><a href="/billing/pdf/statement_id/<?=$pair['STATEMENT']->getId()?><?=($this->userId) ? "/user_id/{$this->userId}" : ''?>"><img src="/images/small_pdf_icon.gif" width="14" height="14" alt="Download a PDF of your statement" /> PDF</a></li>
                        <li><a href="/billing/csv/statement_id/<?=$pair['STATEMENT']->getId()?><?=($this->userId) ? "/user_id/{$this->userId}" : ''?>"><img src="/images/xls_icon.gif" width="14" height="14" alt="Download a CSV of your statement" /> Excel</a></li>
                    </ul>
                </div>
            </div>

		    <?php if ($this->account->getBidType() != 'RESIDUAL') { ?>
				<br />
		    	<?php if ( stristr($this->loggedUser->getAccount()->getInfoString(), 'SLA') ) { ?>
					<p><span class="label label-info">How To</span> <a href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">Use SiteLink to reconcile my statement</a></p>
				<?php } ?>
			    <p><span class="label label-info">How To</span> <a href="https://www.youtube.com/v/gzR0y7IrjFo" class="video-link">Reconcile my statement</a></p>

		    <?php } else {?>
		    <p><i class="fa fa-play-circle"></i> <a href="https://www.youtube.com/v/zQbKQ2mk3hE" class="video-link">How do I reconcile my statement?</a></p>
		    <?php } ?>

        </div>
    <?php } ?>

<?php } ?>

<h3>Statement Archive</h3>
<?php if ($this->account->getBidType() != 'RESIDUAL') { ?>
<table id="statements" class="data-grid">
    <thead>
        <tr>
           <th></th>
           <th>Move-In Fee Total</th>
           <th>Moved In</th>
           <th>Did Not Move In</th>
           <th>Move In Rate</th>
           <th></th>
        </tr>
    </thead>
    <tbody>
        <?php foreach($this->statementPairs as $pair) {
            $didNotMoveIn = ($this->userId) ?
                        $pair['STATEMENT']->getTotalDisputedByUserId($this->userId) :
                        $pair['STATEMENT']->getTotalDisputed();

            $movedIn = ($this->userId) ?
                        ($pair['STATEMENT']->getTotalPendingByUserId($this->userId) + $pair['STATEMENT']->getTotalConfirmedByUserId($this->userId)) :
                        ($pair['STATEMENT']->getTotalPending() + $pair['STATEMENT']->getTotalConfirmed());
        ?>
            <?php if($pair['BATCH']->getStatus() !== 'OPEN') {?>
            <tr>
                <td><?=date('F j',strtotime($pair['BATCH']->getStartDate()))?>-<?=date('d, Y',strtotime($pair['BATCH']->getEndDate()))?></td>
                <td>$<?= ($this->userId) ?
                        (($pair['STATEMENT']->getTotalAmountByUserId($this->userId)) ? $pair['STATEMENT']->getTotalAmountByUserId($this->userId) : '0.00') :
                        (($pair['STATEMENT']->getDynamicTotalAmount()) ? $pair['STATEMENT']->getDynamicTotalAmount() : '0.00')
                ?></td>
                <td><?= $movedIn
                ?></td>
                <td><?= $didNotMoveIn
                ?></td>
                <td><?= round(($movedIn / ($movedIn + $didNotMoveIn)) * 100, 2)
                ?>%</td>
                <td>
                    <div class="btn-group">
                        <a class="btn dropdown-toggle" data-toggle="dropdown" href="#">Download <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="/billing/pdf/statement_id/<?=$pair['STATEMENT']->getId()?><?=($this->userId) ? "/user_id/{$this->userId}" : ''?>"><img src="/images/small_pdf_icon.gif" width="14" height="14" alt="Download a PDF of your statement" />&nbsp;PDF</a></li>
                            <li><a href="/billing/csv/statement_id/<?=$pair['STATEMENT']->getId()?><?=($this->userId) ? "/user_id/{$this->userId}" : ''?>"><img src="/images/xls_icon.gif" width="14" height="14" alt="Download a CSV of your statement" />&nbsp;Excel</a></li>
                        </ul>
                    </div>
                </td>
            </tr>
            <?php } ?>
        <?php } ?>
    </tbody>
</table>
<?php } else {?>
<table id="statements" class="data-grid">
    <thead>
        <tr>
           <th></th>
           <th>Tenant Fees</th>
           <th>Tenants</th>
           <th></th>
        </tr>
    </thead>
    <tbody>
        <?php foreach($this->statementPairs as $pair) {
            $confirmedTenants = ($this->userId) ?
                        ($pair['STATEMENT']->getTotalConfirmedByUserId($this->userId)) :
                        ($pair['STATEMENT']->getTotalConfirmed());

            $existingTenants = $pair['STATEMENT']->getTotalExistingTenants();

            $didNotMoveIn = ($this->userId) ?
                        $pair['STATEMENT']->getTotalDisputedByUserId($this->userId) :
                        $pair['STATEMENT']->getTotalDisputed(); //contains moved out and disputed
        ?>

            <?php if($pair['BATCH']->getStatus() !== 'OPEN') {?>
            <tr>
                <td><?=date('F j',strtotime($pair['BATCH']->getStartDate()))?>-<?=date('d, Y',strtotime($pair['BATCH']->getEndDate()))?></td>
                <td>$<?= ($this->userId) ?
                        (($pair['STATEMENT']->getTotalAmountByUserId($this->userId)) ? $pair['STATEMENT']->getTotalAmountByUserId($this->userId) : '0.00') :
                        (($pair['STATEMENT']->getDynamicTotalAmount()) ? $pair['STATEMENT']->getDynamicTotalAmount() : '0.00')
                ?></td>
                <td><?=$confirmedTenants?></td>
                <td>
                    <div class="btn-group">
                        <a class="btn dropdown-toggle" data-toggle="dropdown" href="#">Download <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="/billing/pdf/statement_id/<?=$pair['STATEMENT']->getId()?><?=($this->userId) ? "/user_id/{$this->userId}" : ''?>"><img src="/images/small_pdf_icon.gif" width="14" height="14" alt="Download a PDF of your statement" />&nbsp;PDF</a></li>
                            <li><a href="/billing/csv/statement_id/<?=$pair['STATEMENT']->getId()?><?=($this->userId) ? "/user_id/{$this->userId}" : ''?>"><img src="/images/xls_icon.gif" width="14" height="14" alt="Download a CSV of your statement" />&nbsp;Excel</a></li>
                        </ul>
                    </div>
                </td>
            </tr>
            <?php } ?>
        <?php } ?>
    </tbody>
</table>
<?php }?>


<?php } ?>
