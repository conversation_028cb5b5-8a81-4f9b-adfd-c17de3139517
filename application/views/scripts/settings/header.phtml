<h1>Settings</h1>

<?php if($this->loggedUser && ($this->loggedUser->getMyfootRole() === Genesis_Entity_UserAccess::ROLE_GOD || $this->loggedUser->getMyfootRole() === Genesis_Entity_UserAccess::ROLE_ADMIN )): ?>
<ul class="nav nav-tabs">
    <li<?= 'personal'      == $this->active  ? ' class="active"'       : '' ?>><a href="<?=$this->url(array('action' => 'myaccount'), 'settings')?>" id="settings_menu_myaccount">Your Account</a></li>
    <li<?= 'corporate'     == $this->active  ? ' class="active"'       : '' ?>><a href="<?=$this->url(array('action' => 'corporate'), 'settings')?>" id="settings_menu_corporate">Corporate Account</a></li>
    <?php if($this->loggedUser && ($this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD || $this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN )) { ?>
        <li<?= 'user'          == $this->active  ? ' class="active"'       : '' ?>><a href="<?=$this->url(array(), 'user')?>" id="settings_menu_user">Users</a></li>
    <?php } ?>
    <?php if($this->loggedUser && ($this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD || $this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN )) { ?>
        <li<?= 'payment'       == $this->active  ? ' class="active"'       : '' ?>><a href="<?=$this->url(array(), 'payment')?>" id="settings_menu_payment">Payment</a></li>
    <?php } ?>
    <?php if ($this->loggedUser->getAccount() && $this->loggedUser->getAccount()->getHostedSite()) { ?>
         <li<?= 'sites'       == $this->active  ? ' class="active"'       : '' ?>><a href="<?=$this->url(array('action' => 'setup'), 'sites')?>" id="settings_menu_geopages">GeoPages</a></li>
    <?php } ?>
</ul>
<?php endif; ?>
