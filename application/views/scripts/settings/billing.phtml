<div class="ui-layout-content">

    <h1>Billing</h1>

        <p style="margin:1em 2em;"> <br /><strong>Note:</strong> Before your units appear on SpareFoot.com, you must supply us with a billing method for your account.</p>

        <p class="form-section">
            Download the appropriate form and email it back to us.  Once we have verified your billing information, we will fully activate your account.<br/>
            <a href="/pdf/SpareFoot_CreditCard_Auth.pdf" target="_blank">Credit Card</a><br/>
            <a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">Bank Draft</a><br/><br/>
            Tel: (*************<br/>Email: <EMAIL>
        </p>

    <!--<form name="billing" id="billing" method="POST" action="/settings/verifybilling">
                <div class="form-section">
                    <table>
                        <tr>
                            <td>Credit Card:</td>
                            <td><select id="cardType" name="cardType" tabindex="1">
                              <option value="visa" selected>Visa</option>
                              <option value="mastercard">MasterCard</option>
                              <option value="amex">American Express</option>
                              <option value="discover">Discover</option>
                            </select></td>
                        </tr>
                        <tr>
                            <td>Number:</td>
                            <td><input size="50" type="text" id="number" name="number" tabindex="2"/></td>
                        </tr>
                        <tr>
                            <td>Security Code (CVV2):</td>
                            <td><input size="5" maxlength="4" type="text" id="cvv2" name="cvv2" tabindex="3"/></td>
                        </tr>
                        <tr>
                            <td>Name on Card:</td>
                            <td><input size="50" type="text" id="name" name="name" tabindex="4"/></td>
                        </tr>
                        <tr>
                            <td>Expiration Date:</td>
                            <td><input size="3" maxlength="2" type="text" id="month" name="month" value="mm"  tabindex="4"/> <input maxlength="5" size="5" type="text" id="year" name="year" value="yyyy"  tabindex="6"/></td>
                        </tr>
                        <tr>
                            <td>Billing Address:</td>
                            <td><input type="text" tabindex="7" maxlength="30" size="30" id="address1" name="address1"><br />
                            <input type="text" tabindex="8" maxlength="30" size="30" id="address2" name="address2"></td>
                        </tr>
                        <tr>
                            <td>City:</td>
                            <td><input type="text" tabindex="9" maxlength="30" size="30" id="city" name="city"></td>
                        </tr>
                        <tr>
                            <td>State:</td>
                            <td><select name="state" tabindex="10">
                                <option selected>Select State</option>
                                    <option value="AK">Alaska</option>
                                    <option value="AL">Alabama</option>
                                    <option value="AR">Arkansas</option>
                                    <option value="AZ">Arizona</option>
                                    <option value="CA">California</option>
                                    <option value="CO">Colorado</option>
                                    <option value="CT">Connecticut</option>
                                    <option value="DC">District of Columbia</option>
                                    <option value="DE">Delaware</option>
                                    <option value="FL">Florida</option>
                                    <option value="GA">Georgia</option>
                                    <option value="HI">Hawaii</option>
                                    <option value="IA">Iowa</option>
                                    <option value="ID">Idaho</option>
                                    <option value="IL">Illinois</option>
                                    <option value="IN">Indiana</option>
                                    <option value="KS">Kansas</option>
                                    <option value="KY">Kentucky</option>
                                    <option value="LA">Louisiana</option>
                                    <option value="MA">Massachusetts</option>
                                    <option value="MD">Maryland</option>
                                    <option value="ME">Maine</option>
                                    <option value="MI">Michigan</option>
                                    <option value="MN">Minnesota</option>
                                    <option value="MO">Missouri</option>
                                    <option value="MS">Mississippi</option>
                                    <option value="MT">Montana</option>
                                    <option value="NC">North Carolina</option>
                                    <option value="ND">North Dakota</option>
                                    <option value="NE">Nebraska</option>
                                    <option value="NH">New Hampshire</option>
                                    <option value="NJ">New Jersey</option>
                                    <option value="NM">New Mexico</option>
                                    <option value="NV">Nevada</option>
                                    <option value="NY">New York</option>
                                    <option value="OH">Ohio</option>
                                    <option value="OK">Oklahoma</option>
                                    <option value="OR">Oregon</option>
                                    <option value="PA">Pennsylvania</option>
                                    <option value="PR">Puerto Rico</option>
                                    <option value="RI">Rhode Island</option>
                                    <option value="SC">South Carolina</option>
                                    <option value="SD">South Dakota</option>
                                    <option value="TN">Tennessee</option>
                                    <option value="TX">Texas</option>
                                    <option value="UT">Utah</option>
                                    <option value="VA">Virginia</option>
                                    <option value="VT">Vermont</option>
                                    <option value="WA">Washington</option>
                                    <option value="WI">Wisconsin</option>
                                    <option value="WV">West Virginia</option>
                                    <option value="WY">Wyoming</option>
                            </select></td>
                        </tr>
                        <tr>
                            <td>Zip:</td>
                            <td><input type="text" tabindex="11" maxlength="5" size="8" id="zip" name="zip"></td>
                        </tr>
                    </table>
                </div>
        <p style="text-align:center;"><input type="submit" id="submit" name="submit" value="Submit" /></p>
        </form>-->

    <?php if ($this->account->getNumBillableEntities() > 0) { ?>
        <p class="messages-success" style="margin:1em 1.5em;">
            Billing verified!  Your listings are currently appearing on SpareFoot.com
        </p>
    <?php } else { ?>
        <p class="messages-error" style="margin:1em 1.5em;">
            Billing NOT VERIFIED. Your listings are not currently appearing on SpareFoot.com
        </p>
    <?php } ?>
</div>
