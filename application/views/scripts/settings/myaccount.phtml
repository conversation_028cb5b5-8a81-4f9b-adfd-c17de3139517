<?=$this->partial('settings/header.phtml', array('loggedUser' => $this->loggedUser, 'active' => 'personal'))?>

<h3>Change Your Personal Information</h3>
<br />

<?php if($this->alert){ ?>
    <p class="alert<?=($this->alertClass?' '.$this->alertClass:'')?>">
        <?=$this->alert?>
    </p>
<?php } ?>

<div class="ui grid form">
    <div class="sixteen wide tablet six wide computer column">
        <form method="post" action="/settings/myaccount" class="form-horizontal">
            <div class="two fields">
                <div class="field">
                    <label>First Name</label>
                    <input id="fname" name="fname" type="text" class="form-control" value="<?=$this->user->getFirstName()?>"/>
                </div>
                <div class="field">
                    <label>Last Name</label>
                    <input id="lname" name="lname" type="text" class="form-control" value="<?=$this->user->getLastName()?>"/>

                </div>
            </div>

            <div class="two fields">
                <div class="field">
                    <label>Email</label>
                    <input id="email" name="email" type="text" class="form-control" value="<?=$this->user->getEmail()?>"/>
                </div>
                <div class="field">
                    <label>Phone Number</label>
                    <input id="phone" name="phone" type="text" class="form-control" value="<?=$this->user->getPhone()?>"/>

                </div>
            </div>
            <div class="field">
                <label>About Me</label>
                <textarea rows="2" cols="20" id="aboutme" name="aboutme" size="30" type="text" class="form-control"><?=$this->user->getAboutMe()?></textarea>
            </div>


            <p>To change your password, enter your current password first.  Then type your new password.<br/><strong>Note:</strong> Passwords must be at least 6 characters long.  Leave blank to remain unchanged.</p>
            <div class="ui grid internal form">
                <div class="eight wide computer sixteen wide tablet column">
                    <div class="field">
                        <label>Current Password</label>
                        <input id="old_password" name="old_password" type="password" class="form-control" />
                    </div>
                    <div class="field">
                        <label>New Password</label>
                        <input id="new_password" name="new_password" type="password" class="form-control" />
                    </div>
                    <div class="field">
                        <label>Confirm New Password</label>
                        <input id="new_password_confirm" name="new_password_confirm" type="password" class="form-control" />
                    </div>
                </div>

                <div class="form-actions">
                    <div class="right">
                        <input id="submit_button" class="ui primary button" name="commit" type="submit" value="Save Changes" />
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

