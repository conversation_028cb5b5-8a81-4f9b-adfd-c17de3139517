<?=$this->partial('settings/header.phtml', array('loggedUser' => $this->loggedUser, 'active' => 'corporate'))?>

<?php if($this->loggedUser && ($this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD || $this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN )) { ?>

<h3>Change Your Corporate Information</h3>

    <form method="post" action="/settings/corporate" class="form-horizontal">
        <br />

        <?php if($this->alert){ ?>
            <p class="alert<?=($this->alertClass?' '.$this->alertClass:'')?>">
                <?=$this->alert?>
            </p>
        <?php } ?>

        <div class="form-group<?=(in_array('company_name', $this->erroredFields))?' error':''?>">
            <label class="col-lg-2 control-label" for="company_name">Company Name</label>
            <div class="col-lg-10">
                <input id="company_name" name="company_name" type="text" class="form-control" readonly="readonly" value="<?=$this->account->getName()?>" />
            </div>
        </div>

        <div class="form-group<?=(in_array('address', $this->erroredFields))?' error':''?>">
            <label class="col-lg-2 control-label" for="address">Address</label>
            <div class="col-lg-10">
                <input id="address" name="address" type="text" class="form-control" value="<?=($this->account->getLocation())?$this->account->getLocation()->getAddress1():''?>" />
            </div>
        </div>

        <div class="form-group<?=(in_array('city', $this->erroredFields))?' error':''?>">
            <label class="col-lg-2 control-label" for="city">City</label>
            <div class="col-lg-10">
                <input id="city" name="city" type="text" class="form-control" value="<?=($this->account->getLocation())?$this->account->getLocation()->getCity():''?>" />
            </div>
        </div>

        <div class="form-group<?=(in_array('state', $this->erroredFields))?' error':''?>">
            <label class="col-lg-2 control-label" for="state">State</label>
            <div class="col-lg-10">
                <select id="state" name="state" class="form-control">
                    <option value=""></option>
                    <option value="AK"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='AK'?' selected="selected"':''?>>AK</option>
                    <option value="AL"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='AL'?' selected="selected"':''?>>AL</option>
                    <option value="AR"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='AR'?' selected="selected"':''?>>AR</option>
                    <option value="AZ"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='AZ'?' selected="selected"':''?>>AZ</option>
                    <option value="CA"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='CA'?' selected="selected"':''?>>CA</option>
                    <option value="CO"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='CO'?' selected="selected"':''?>>CO</option>
                    <option value="CT"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='CT'?' selected="selected"':''?>>CT</option>
                    <option value="DC"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='DC'?' selected="selected"':''?>>DC</option>
                    <option value="DE"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='DE'?' selected="selected"':''?>>DE</option>
                    <option value="FL"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='FL'?' selected="selected"':''?>>FL</option>
                    <option value="GA"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='GA'?' selected="selected"':''?>>GA</option>
                    <option value="HI"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='HI'?' selected="selected"':''?>>HI</option>
                    <option value="IA"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='IA'?' selected="selected"':''?>>IA</option>
                    <option value="ID"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='ID'?' selected="selected"':''?>>ID</option>
                    <option value="IL"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='IL'?' selected="selected"':''?>>IL</option>
                    <option value="IN"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='IN'?' selected="selected"':''?>>IN</option>
                    <option value="KS"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='KS'?' selected="selected"':''?>>KS</option>
                    <option value="KY"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='KY'?' selected="selected"':''?>>KY</option>
                    <option value="LA"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='LA'?' selected="selected"':''?>>LA</option>
                    <option value="MA"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='MA'?' selected="selected"':''?>>MA</option>
                    <option value="MD"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='MD'?' selected="selected"':''?>>MD</option>
                    <option value="ME"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='ME'?' selected="selected"':''?>>ME</option>
                    <option value="MI"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='MI'?' selected="selected"':''?>>MI</option>
                    <option value="MN"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='MN'?' selected="selected"':''?>>MN</option>
                    <option value="MO"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='MO'?' selected="selected"':''?>>MO</option>
                    <option value="MS"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='MS'?' selected="selected"':''?>>MS</option>
                    <option value="MT"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='MT'?' selected="selected"':''?>>MT</option>
                    <option value="NC"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='NC'?' selected="selected"':''?>>NC</option>
                    <option value="ND"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='ND'?' selected="selected"':''?>>ND</option>
                    <option value="NE"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='NE'?' selected="selected"':''?>>NE</option>
                    <option value="NH"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='NH'?' selected="selected"':''?>>NH</option>
                    <option value="NJ"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='NJ'?' selected="selected"':''?>>NJ</option>
                    <option value="NM"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='NM'?' selected="selected"':''?>>NM</option>
                    <option value="NV"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='NV'?' selected="selected"':''?>>NV</option>
                    <option value="NY"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='NY'?' selected="selected"':''?>>NY</option>
                    <option value="OH"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='OH'?' selected="selected"':''?>>OH</option>
                    <option value="OK"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='OK'?' selected="selected"':''?>>OK</option>
                    <option value="OR"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='OR'?' selected="selected"':''?>>OR</option>
                    <option value="PA"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='PA'?' selected="selected"':''?>>PA</option>
                    <option value="RI"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='RI'?' selected="selected"':''?>>RI</option>
                    <option value="SC"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='SC'?' selected="selected"':''?>>SC</option>
                    <option value="SD"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='SD'?' selected="selected"':''?>>SD</option>
                    <option value="TN"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='TN'?' selected="selected"':''?>>TN</option>
                    <option value="TX"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='TX'?' selected="selected"':''?>>TX</option>
                    <option value="UT"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='UT'?' selected="selected"':''?>>UT</option>
                    <option value="VA"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='VA'?' selected="selected"':''?>>VA</option>
                    <option value="VT"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='VT'?' selected="selected"':''?>>VT</option>
                    <option value="WA"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='WA'?' selected="selected"':''?>>WA</option>
                    <option value="WI"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='WI'?' selected="selected"':''?>>WI</option>
                    <option value="WV"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='WV'?' selected="selected"':''?>>WV</option>
                    <option value="WY"<?=$this->account->getLocation() && $this->account->getLocation()->getState()=='WY'?' selected="selected"':''?>>WY</option>
                </select>
            </div>
        </div>

        <div class="form-group<?=(in_array('zip', $this->erroredFields))?' error':''?>">
            <label class="col-lg-2 control-label" for="zip">Zip Code</label>
            <div class="col-lg-10">
                <input id="zip" name="zip" type="text" class="form-control" value="<?=($this->account->getLocation())?$this->account->getLocation()->getZip():''?>" />
            </div>
        </div>
    <div class="form-group">
        <label class="col-lg-2 control-label"></label>
        <div class="col-lg-10">
            Account Payment Terms:
            <?php if($this->account->getBidType() == Genesis_Entity_Account::BID_TYPE_RESIDUAL) : ?>
                <?= $this->account->getResidualPercent() * 100 ?>% of rent for a move-in.
            <?php elseif($this->account->getBidType() == Genesis_Entity_Account::BID_TYPE_FLAT) : ?>
                One-time move in fee <?= ($this->account->getMinBid() !== NULL) ? 'of ' . $this->account->getMinBid() : '' ?> for a move-in.
            <?php elseif($this->account->getBidType() == Genesis_Entity_Account::BID_TYPE_TIERED) : ?>
                Middle-tier, one-time move in fee <?= ($this->account->getMinBid() !== NULL) ? 'of ' . $this->account->getMinBid() : '' ?> for a move-in.
            <?php endif; ?>
        </div>
    </div>
        <div class="form-group">

            <?php if($this->display_terms_download == true) { ?>
                <label class="col-lg-2 control-label"></label>
                <div class="col-lg-10">
                    Download your Sparefoot Terms Agreement <a href="/accounts/download-terms" target="_blank"><img src="/images/small_pdf_icon.gif" width="14" height="14" alt="Download a PDF of your Terms Agreement" /> PDF</a>
                </div>
            <?php } ?>
            <label class="col-lg-2 control-label"></label>
            <div class="col-lg-10">
                <a target="_blank" href="<?=$this->terms_link?>">Your Terms of Service</a>
                <?php if($this->termsAddendumLink): ?>
                    <br /><a target="_blank" href="<?= $this->termsAddendumLink ?>">Your Terms of Service Addendum</a>
                <?php endif ?>
            </div>
        </div>


        <div class="form-actions">
            <div class="pull-right">
                <input id="submit_button" class="ui primary button" name="commit" type="submit" value="Save Changes" />
            </div>
        </div>

<?php } else { ?>
    <p>You don't have permission to edit this information.</p>
<?php } ?>
