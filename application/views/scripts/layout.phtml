<?=$this->partial('sitetop.phtml') ?>
<?php
$bodyClassNames = [];
if(isset($this->banner) && count($this->banner) > 0) {
    $bodyClassNames[] = 'banner-showing';
}
?>
<body data-path="<?= $_SERVER['REQUEST_URI'] ?>" class="<?=implode(' ', $bodyClassNames)?>">
    <?php if (AccountMgmt_Service_User::getLoggedUser()): ?>
        <?=$this->partial('new-ui/sidebar.phtml', $this)?>
    <?php endif; ?>

    <div id="wrapper" class="page-container pusher">
        <?php if (AccountMgmt_Service_User::getLoggedUser()): ?>
            <?=$this->partial('new-ui/headerbar.phtml', $this)?>
        <?php endif; ?>

        <div id="page-content-wrapper">
            <?php
            if($this->banner['showCovidMsgBanner']) {
                require_once('new-ui/covid-banner.phtml');
            }
            if($this->banner['showMoveInsBanner']) {
                require_once('new-ui/move-ins-banner.phtml');
            }
            if($this->banner['showNotificationBanner']) {
                require_once('new-ui/notification-banner.phtml');
            }
            ?>
            <div class="master-layout">
                <?php if(isset($this->welcomeMessage) && $this->welcomeMessage): ?>
                <div class="alert alert-block alert-success">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <h4 class="alert-heading">Welcome to SpareFoot</h4>
                    This is MySpareFoot, your tool for managing your listings on the SpareFoot AdNetwork. If you have questions, click "Help" in the toolbar. We can't wait to send you some tenants!
                </div>
                <?php endif; ?>

                <?php if (AccountMgmt_Service_User::getLoggedUser() && ($this->loggedUser->isMyFootAdmin() || $this->loggedUser->isMyFootGod())):
                    $account = $this->loggedUser->getAccount();
                    $unassignedFacs = $account->getUnassignedFacilities();
                    $accountId = $this->accountId;

                    if (strpos($_SERVER['REQUEST_URI'],'payment') == 1) {
                        $alertMsg = 'add a payment method below.';
                    } else {
                        $alertMsg = '<a href="' . $this->url(array(), 'payment') . '">add a payment method</a> to activate.';
                    }

                    if (count($unassignedFacs) > 0) : ?>
                        <div class="ui error message">You have <?=count($unassignedFacs) > 1 ? 'unactivated facilities' : 'an unactivated facility' ?> - <?=$alertMsg?></div>
                    <?php endif;

                    if ($accountId && $accountId != $this->loggedUser->getAccountId()): ?>
                        <div class="ui error message">Account ID <?=$accountId?> does not exist.</div>
                    <?php endif; ?>
                <?php endif; ?>

                <?php if (is_array($this->errorMessages) && count($this->errorMessages) > 0): ?>
                <div class="ui error message">
                    <ul>
                        <?php foreach($this->errorMessages as $message): ?>
                            <li><?=$message?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <?php if (is_array($this->successMessages) && count($this->successMessages) > 0): ?>
                <div class="ui success message">
                    <ul>
                        <?php foreach($this->successMessages as $message): ?>
                            <li><?=$message?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <!-- YIELD View Content -->
                <?= $this->layout()->content ?>

                <footer id="page-footer">
                    <p>&copy; <?=date('Y');?> SpareFoot. Got questions? Check out our <a href="https://support.sparefoot.com/hc/en-us" target="_blank">Help Center</a>.</p>
                </footer>
            </div>
        </div><!--/#page-content-wrapper-->

        <div id="message-modal" class="modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title"></h4>
                    </div>
                    <div class="modal-body">
                    </div>
                    <div class="modal-footer">
                        <a href="#" id="message-modal-ok" data-dismiss="modal" class="ui primary button">OK</a>
                    </div>
                </div>
            </div>
        </div>
    </div> <!--/.page-container-->

    <?php if ( ! Genesis_Config_Server::isProduction()):
    switch (strtolower(Genesis_Config_Server::getEnvironmentAsString())) {
        case 'staging':
            $color = 'orange';
            break;
        case 'dev':
            $color = 'purple';
            break;
        case 'local':
            $color = 'teal';
            break;
        default:
    } ?>
    <div id="env-label" class="ui label <?=$color?>"><?=Genesis_Config_Server::getEnvironmentAsString()?></div>
    <?php endif ?>

    <script type="text/javascript">
        //Global variables used within the App
        App.authBearerToken = '<?= $this->authBearerToken ?>';
        App.servicesBaseUrl = '<?= $this->servicesBaseUrl ?>' + (location.port ? ':'+location.port : '');
    </script>

    <script src="/dist/init.js"></script>

    <script type="text/javascript">
        var CONFIG = { featureFlags: {} };
        // Feature Flags
        CONFIG.featureFlags['<?=AccountMgmt_Models_Features::UNIT_DELETE?>'] = <?=(AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::UNIT_DELETE) ? 'true' : 'false')?>;

        SF.tools.setReadOnly(CONFIG, {
            appUrl: '//<?=$_SERVER['HTTP_HOST']?>',
            cdnUrl: '//<?=$_SERVER['HTTP_HOST']?>',
            env: location.hostname.match('sparefoot.com') ? 'live' : location.hostname.match('localhost') ? 'dev' : 'staging'
        });

        <?php $fields = ['email', 'firstName', 'id', 'lastName', 'phone', 'pictureExt', 'username'];
        $user = $this->loggedUser->toArray($fields) ?>
        var USER = {};
        SF.tools.setReadOnly(USER, <?=json_encode($user)?>);

        <?php $fields = ['accountId', 'bidType', 'cpa', 'infoString', 'integrationsString', 'locationId', 'name', 'numFacilities', 'phone', 'sfAccountId', 'status'];
        $account = $this->loggedUser->getAccount()->toArray($fields) ?>
        var ACCOUNT = {};
        SF.tools.setReadOnly(ACCOUNT, <?=json_encode($account)?>);
    </script>


    <script type="text/javascript">
        $(document).ready(() => {
            $('#contact-us-button').click(() => {
                $('#contact-us-modal').modal('show')
            })
        })
    </script>

<?=$this->partial('sitebottom.phtml', [
    'userId'=>$this->loggedUser->getId(),
    'loggedUser' => $this->loggedUser,
    'scripts'=>$this->scripts
    ]);
?>
