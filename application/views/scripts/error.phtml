<?=$this->partial('sitetop.phtml',['head'=> '<link href="/css/signup.css" rel="stylesheet"/>'])?>

<body style="min-width: 0;" class="signup">

<nav class="navbar navbar-fixed-top">
    <?php if (Genesis_Config_Server::isStaging()) : ?>
        <div class="preheader preheader-staging"><span>Staging</span><div class="clear"></div></div>
    <?php elseif (Genesis_Config_Server::isDev()) : ?>
        <div class="preheader preheader-dev"><span>Dev</span><div class="clear"></div></div>
    <?php elseif (Genesis_Config_Server::isLocal()) : ?>
        <div class="preheader preheader-local"><span>Local</span><div class="clear"></div></div>
    <?php endif; ?>

    <div class="ruler-header">
        <div class="navbar-header">
            <a href="/"><span class="brand"><img src="<?=CDN('/images/mysparefootx2.png')?>" alt="MySpareFoot" class="mysparefootx2" /></span></a>
        </div>

        <div class="collapse navbar-collapse">
            <div class="nav pull-right">
                <span class="navbar-text">Need assistance? We're here to help. <strong><?=AccountMgmt_Service_Constants::SUPPORT_PHONE_NUMBER?></strong></span>
            </div>
        </div>
    </div>
</nav>

<div class="container">
    <div class="row">
        <div>
            <div class="panel panel-default">
                <div class="panel-body setup-content-panel-body">
                    <?php echo $this->layout()->content ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="message-modal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal">×</button>
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body">
            </div>
            <div class="modal-footer">
                <a href="#" id="message-modal-ok" data-dismiss="modal" class="btn btn-primary">OK</a>
            </div>
        </div>
    </div>
</div>
<script src="<?=CDN('/dist/init.js')?>"></script>
<script type="text/javascript">
    var CONFIG = {
        appUrl: '//<?=$_SERVER['HTTP_HOST']?>',
        cdnUrl: '//<?=$_SERVER['HTTP_HOST']?>'
    };
</script>

<?=$this->partial('sitebottom.phtml', [
    'userId'=>isset($this->loggedUser) ? $this->loggedUser->getId() : 0,
    'loggedUser'=>isset($this->loggedUser) ? $this->loggedUser : null,
    'scripts'=>$this->scripts
   ]);
?>
