<script type="text/javascript">
    App.context = {
        facility_id: <?=($this->facility ? $this->facility->getId() : 'null')?>
    };
</script>

<?php if (isset($this->facility) && !empty($this->facility)): ?>
    <?php if (!$this->facility->getActive()): ?>
        <div id="inactive-facility-message" class="ui warning message" style="overflow:auto">
            <div class="header">
                This facility is currently inactive and will not receive reservations on the SpareFoot network.
                <button id="activate-facility" class="ui button compact primary pull-right" />Activate Facility</button>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>

<div class="ui stackable two column grid">
    <div class="column">
        <div class="ui segment dashboard-widget welcome-widget <?=$this->showWelcomeWidget ? '' : 'hidden'?>">
            <div class="head">
                <h2 class="widget-title">Welcome to SpareFoot!</h2>
                <p>
                    Adding photos, requesting reviews, and adding additional unit types will help drive customers to your facility listing and improve conversion rates.
                </p>
            </div>
            <div class="body">
                <a href="<?=$this->url(['action'=>'photos'], 'features')?>?fid=<?=$this->facility->getId()?>" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="photo icon"></i>
                        <h3>Add Photos</h3>
                    </div>
                </a>
                <a href="<?=$this->url(['action'=>'request'], 'reviews')?>?fid=<?=$this->facility->getId()?>" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="star icon"></i>
                        <h3>Request Reviews</h3>
                    </div>
                </a>
                <a href="<?=$this->url(['action'=>'units'], 'features')?>?fid=<?=$this->facility->getId()?>" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="building outline icon"></i>
                        <h3>Add Unit Types</h3>
                    </div>
                </a>
            </div>
        </div>
        <?php if(AccountMgmt_Service_User::isFeatureActive('myfoot.dashboard-widget.submitRate')): ?>
        <div class="ui segment dashboard-widget submit-rate-widget"></div>
        <?php endif ?>

        <?php if(AccountMgmt_Service_User::isFeatureActive('myfoot.dashboard-widget.moveInRate')): ?>
        <div class="ui segment dashboard-widget move-in-rate-chart <?=$this->showWelcomeWidget ? 'hidden' : ''?>"></div>
        <?php endif ?>
    </div>
    <div class="column">
        <?php if(AccountMgmt_Service_User::isFeatureActive('myfoot.dashboard-widget.inventory')): ?>
        <div class="ui segment dashboard-widget inventory-widget <?=$this->showWelcomeWidget ? 'hidden' : ''?>"></div>
        <?php endif ?>

        <?php if(AccountMgmt_Service_User::isFeatureActive('myfoot.dashboard-widget.currentBidRanking')
        && $this->account->getBidType() !== Genesis_Entity_Account::BID_TYPE_RESIDUAL): ?>
        <div class="ui segment dashboard-widget current-bid-widget <?=$this->showWelcomeWidget ? 'hidden' : ''?>"></div>
        <?php endif ?>

        <?php if(AccountMgmt_Service_User::isFeatureActive('myfoot.dashboard-widget.customerReviews')): ?>
        <div class="ui segment dashboard-widget customer-reviews-widget <?=$this->showWelcomeWidget ? 'hidden' : ''?>"></div>
        <?php endif ?>
    </div>
</div>

<div class="bottom-more-row">
    <a href="<?=$this->url([], 'reports')?>">View More Reports &gt;</a>
</div>

<div class="clear"></div>
