<tr class="cpa-item" data-id="<?=$this->clientItem->getConfirmationCode()?>" data-facility-id="<?= $this->clientItem->getFacility()->getId() ?>">
    <?php if (! $this->clientItem->isFreeBooking()): ?>
    <td id="action-<?=$this->clientItem->getConfirmationCode()?>" class="statement-actions">
        <div class="ui basic icon buttons" data-toggle="buttons">
            <?php
                $yesClasses = '';
                $noClasses = '';
                if ($this->clientItem->isAutoConfirmed()) {
                    $yesClasses = 'confirm-autoconfirmed-button ';
                    $noClasses = 'dispute-autoconfirmed-button ';
                    if ( ! $this->clientItem->getReviewStatus()) {
                        $yesClasses .= $this->clientItem->isDisputed() ? '' : 'active';
                        $noClasses .= $this->clientItem->isDisputed() ? 'active' : '';
                    } elseif ($this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) {
                        $yesClasses .= $this->clientItem->isDisputed() ? '' : 'active';
                        $noClasses .= $this->clientItem->isDisputed() ? 'active' : '';
                    } elseif ($this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED) {
                        if ($this->clientItem->isDisputed()) {
                            $noClasses .= ' active';
                        } else {
                            $yesClasses .= ' active';
                            $noClasses .= ' hidden';
                        }
                    }
                } elseif ($this->clientItem->isEarly()) {
                    $yesClasses = 'change-move-in-date-button';
                    $noClasses = 'disabled';
                } elseif ($this->clientItem->isLate()) {
                    $yesClasses = 'change-move-in-date-button';
                    $noClasses = 'dispute-button active';
                } else {
                    $yesClasses = 'confirm-button '.($this->clientItem->isDisputed() ? '' : 'active');
                    $noClasses = 'dispute-button '.($this->clientItem->isDisputed() ? 'active' : '');
                }
            ?>
            <button class="ui basic green compact button <?=$yesClasses?>">
                <i class="checkmark icon"></i>
            </button>
            <button class="ui basic red compact button <?=$noClasses?>">
                <i class="remove icon"></i>
            </button>
        </div>
        <input type="hidden" name="options_<?=$this->clientItem->getConfirmationCode()?>"/>
    </td>
    <td id="status-<?=$this->clientItem->getConfirmationCode()?>" class="status-cell">
        <?php if ($this->clientItem->isAutoConfirmed()) { ?>
            <?php if ($this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) { ?>
                <span class="under-review">Under Review by SpareFoot</span>
                <span class="error is-hidden">Did Not Move In</span>
                <span class="success is-hidden">Moved In</span>
            <?php } else { ?>
                <span class="under-review is-hidden">Under Review by SpareFoot</span>
                <span class="error<?=$this->clientItem->isDisputed()?'':' is-hidden'?>">Did Not Move In <?=$this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED ? ' (Reviewed)' : ''?></span>
                <span class="success<?=$this->clientItem->isDisputed()?' is-hidden':''?>">Moved In <?=$this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED ? ' (Reviewed)' : ''?></span>
            <?php } ?>
        <?php } elseif ($this->clientItem->isEarly()) { ?>
            <span>Move In Next Month</span>
            <span class="success is-hidden">Moved In</span>
        <?php } elseif ($this->clientItem->isLate()) { ?>
            <span class="error">Did Not Move In</span><img id="activity-indicator-<?=$this->clientItem->getConfirmationCode()?>" class="is-hidden" src="/images/loading.gif" alt="loading"/>
            <span class="success is-hidden">Moved In</span>
        <?php } else { ?>
            <span class="error <?=($this->clientItem->isDisputed() ? '' : 'is-hidden')?>">Did Not Move In</span>
            <span class="success <?=($this->clientItem->isDisputed() ? 'is-hidden' : '')?>">Moved In</span>
        <?php } ?>

        <?php // Support notes
        if ($this->clientItem->getSupportNotes()): ?>
            <br /><a class="ui popup-text" data-title="Support Notes" data-content="<?=$this->clientItem->getSupportNotes()?>"><strong>NOTES</strong></a>
        <?php endif ?>
    </td>
    <td class="statement-verification">
        <?php if(array_key_exists('consumer', $this->clientItem->getMoveInVerifiedBy())){ ?>
            <img src="/images/customer-verified.gif" width="19" height="20" alt="" />  Customer
        <?php } ?>
        <?php if(count($this->clientItem->getMoveInVerifiedBy()) > 1){ ?>
            <br /><br />
        <?php } ?>
        <?php if(array_key_exists('facility', $this->clientItem->getMoveInVerifiedBy())){ ?>
            <img src="/images/facility-verified.gif" width="19" height="20" alt="" />  Facility
        <?php } ?>
    </td>
    <?php endif ?>

    <?php if (! $this->clientStatement->isFilteredByFacility()): ?>
        <td id="facility-name-<?=$this->clientItem->getConfirmationCode()?>"><a href="<?=$this->url(['action' => 'view', 'id'=> $this->clientStatement->getStatementId()], 'statement')?>?facility=<?=$this->clientItem->getFacility()->getId()?>"><?=htmlspecialchars($this->clientItem->stringFacilityName())?></a></td>
    <?php endif ?>

    <td id="customer-<?=$this->clientItem->getConfirmationCode()?>">
        <span id="customer-info-<?=$this->clientItem->getConfirmationCode()?>">
            <?=nl2br(htmlspecialchars($this->clientItem->stringCustomerInfo()))?>
        </span>
        <?=($this->clientItem->getBookingUnitNumber() ? '<br/>Unit '.$this->clientItem->getBookingUnitNumber() : '')?>
        <?php if ($this->clientItem->isOffline()): ?>
            <a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
        <?php endif; ?>

        <?php if ($this->clientItem->hasDuplicate()): ?>
            <a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant.">Multiple Reservations</a>
        <?php endif; ?>
        <?php if ($this->clientItem->isAutoConfirmed()): ?>
            <div id="tenant-<?=$this->clientItem->getConfirmationCode()?>" class="is-hidden">
                <?=nl2br($this->clientItem->stringTenantInfo())?>
                <?=($this->clientItem->getUnitNumber() ? '<br/>Unit '.$this->clientItem->getUnitNumber() : '')?>
            </div>
        <?php endif; ?>
    </td>
    <td id="date-<?=$this->clientItem->getConfirmationCode()?>"><?=$this->clientItem->stringMoveInDate()?></td>
    <td id="reservation-<?=$this->clientItem->getConfirmationCode()?>"><?=$this->clientItem->stringReservationDate()?></td>
    <td id="baseBid-<?=$this->clientItem->getConfirmationCode()?>"><?=$this->clientItem->stringBaseBid()?></td>
    <td id="amount-<?=$this->clientItem->getConfirmationCode()?>"><?=$this->clientItem->stringSparefootFee()?></td>
    <td id="lifetime-<?=$this->clientItem->getConfirmationCode()?>"><a href="#" class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of <?=$this->clientItem->stringUnitPrice()?>."><?=$this->clientItem->stringLifetimeValue()?></a><br />
        <a href="#" class="ui popup-text" data-content="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant."><?=$this->clientItem->stringLifetimeValueROI()?> <abbr data-content="Return on Investment">ROI</abbr></a></td>
</tr>
