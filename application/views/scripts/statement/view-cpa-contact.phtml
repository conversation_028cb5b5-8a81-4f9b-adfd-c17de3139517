<tr id="<?=$this->consumerContact->getId()?>" class="cpa-contact">
    <td id="action-<?=$this->consumerContact->getId()?>">
        <div class="statement-reservation-actions">
            <div class="ui basic icon buttons" data-toggle="buttons">
                <button class="ui basic green compact button confirm-consumercontact-button">
                    <i class="checkmark icon"></i>
                </button>
                <button class="ui basic red compact button dispute-consumercontact-button active">
                    <i class="remove icon"></i>
                </button>
            </div>
        </div>
    </td>
    <?php if (!$this->clientStatement->isFilteredByFacility()): ?>
    <td id="facility-name-<?=$this->consumerContact->getId()?>">
        <a href="<?=$this->url(['action' => 'view', 'id'=> $this->clientStatement->getStatementId()], 'statement')?>?facility=<?=$this->consumerContact->getListingAvailId()?>"><?=$this->consumerContact->getFacility()->getTitle()?></a>
    </td>
    <?php endif ?>
    <td id="customer-<?=$this->consumerContact->getId()?>">
        <span id="customer-info-<?=$this->consumerContact->getId()?>">
            <?=nl2br(htmlspecialchars($this->consumerContact->stringCustomerInfo()))?>
        </span>
    </td>
    <td><?=date('Y-m-d', strtotime($this->consumerContact->getTimestamp()))?></td>
</tr>
