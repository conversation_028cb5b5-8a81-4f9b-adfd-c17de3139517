<div id="dispute-modal" class="ui modal">
    <i class="close icon"></i>
    <div class="header">Why didn't the customer move in this month?</div>
    <div class="content">
        <div class="dispute-customer-info ui card">
            <div class="content"></div>
        </div>
        <form class="ui form" method="post">
            <div class="grouped fields">
                <div class="field">
                    <div class="ui radio checkbox dispute-reason">
                        <input name="dispute-reason" type="radio" value="delay"/>
                        <label>Customer might move in at a future date</label>
                    </div>
                    <div class="reason-specifics ui segment">
                        <label>Please enter the customer's new move-in date (pick any date this month if you don't know it)</label>
                        <input type="text"/>
                    </div>
                </div>

                <div class="field">
                    <div class="ui radio checkbox dispute-reason">
                        <input name="dispute-reason" type="radio" value="name-change"/>
                        <label>Customer moved in under a different name</label>
                    </div>
                    <div class="reason-specifics ui segment" data-for="name-change">
                        <label>This is still a valid move-in. Please provide us with accurate contact information to appear on your bill.</label>
                        <div class="two fields">
                            <div class="field name">
                                <input type="text" placeholder="First Name"/>
                            </div>
                            <div class="field name">
                                <input type="text" placeholder="Last Name"/>
                            </div>
                        </div>
                    </div>
                </div>

                <?php // customer moved into sister facility?
                if(count($this->sisterFacilityList) > 1): ?>
                <div class="field">
                    <div class="ui radio checkbox dispute-reason">
                        <input name="dispute-reason" type="radio" name="facility-change"/>
                        <label>Customer moved into a sister facility</label>
                    </div>
                    <div class="reason-specifics ui segment">
                        <label>Which facility?</label>
                        <select>
                            <?php foreach($this->sisterFacilityList as $facility): ?>
                                <?php if($facility->getApproved() && $facility->getPublished() && $facility->getBillableEntityId()): ?>
                                    <option value="<?=$facility->getId()?>"><?=$facility->getTitle()?> <?=($facility->getCompanyCode())?'('.$facility->getCompanyCode().')':''?></option>
                                <?php endif ?>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
                <?php endif ?>

                <div class="field">
                    <div class="ui radio checkbox dispute-reason">
                        <input name="dispute-reason" type="radio" name="canceled"/>
                        <label>Customer will never move in</label>
                    </div>
                    <div class="reason-specifics ui segment">
                        <p>
                            <strong>Why didn't this customer move in?</strong><br/>
                            Please let us know! We'll use this information to follow up with the customer, and to improve the quality of customers we send you in the future.
                        </p>
                        <div class="grouped fields">
                            <label>We tried to contact this customer, but could not reach them because</label>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="invalid-contact"/>
                                    <label>Customer's contact information was invalid</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="no-response"/>
                                    <label>Customer did not respond to our contact attempts</label>
                                </div>
                            </div>
                        </div>
                        <div class="grouped fields">
                            <label>We spoke with the customer, but they</label>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="no-show"/>
                                    <label>Didn't show up on date they said they would</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="no-need"/>
                                    <label>No longer needed to rent a storage unit</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="rent-elsewhere"/>
                                    <label>Rented storage elsewhere</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="misinformed"/>
                                    <label>Felt misinformed by SpareFoot or the rental process</label>
                                </div>
                            </div>
                        </div>
                        <div class="grouped fields">
                            <label>We could not accomodate this customer because</label>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="invalid-reserved"/>
                                    <label>There reserved unit did not meet their needs</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="no-avail"/>
                                    <label>We did not have their reserved unit available</label>
                                </div>
                            </div>
                        </div>
                        <div class="grouped fields">
                            <label>Other</label>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="duplicate"/>
                                    <label>This is a duplicate reservation</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="no-attempt"/>
                                    <label>We did not attempt to contact this customer</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui radio checkbox">
                                    <input name="dispute-reason-other" type="radio" value="not-sparefoot-lead"/>
                                    <label>I don't think SpareFoot generated this lead</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <input name="confirmation_code" type="hidden"/>
        </form>
    </div>
    <div class="actions">
        <button class="cancel ui button default">Cancel</button>
        <button class="ok ui button primary">Save</button>
    </div>
</div>
