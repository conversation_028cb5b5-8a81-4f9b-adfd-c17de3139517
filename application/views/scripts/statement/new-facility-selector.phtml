<?php if($this->facilityCount > 1): ?>
    <select class="form-control" js-change-facility id="statement-select-facility">
    <option value="<?=$this->url(['action' => 'dispute', 'id'=> $this->statementId], 'statement')?>">All Facilities</option>
        <?php foreach($this->facilities as $facility): ?>
        <option value="<?=$this->url(['action' => 'dispute', 'id'=> $this->statementId], 'statement')?>?facility=<?=$facility->getId()?>" <?=(($facility->getId() == $this->facilityId)?" selected='selected'":null)?>><?=$facility->getTitleWithCompanyCode();?></option>
        <?php endforeach ?>
    </select>

    <?php if (! $this->hasItems): ?>
    <p>There are no customers for this facility.</p>
    <?php endif ?>
<?php endif ?>
