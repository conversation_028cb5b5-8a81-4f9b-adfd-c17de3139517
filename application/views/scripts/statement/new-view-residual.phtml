<?php
    /*
     * lets make it easier to use this in the view, and always get it right
     */
    define('RESIDUAL_AUTO_CONFIRM', Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS, array('account_id' => $this->account->getId())) ? true : false);
?>
<script>
var cdpEnabled = <?=$this->account->isPartiallyCdpEnabled() ? "true" : "false"?>;
App.context = {facility_id:<?=($this->facilityId) ? $this->facilityId : 'null'?>,statement_id:<?=$this->statementId?>};
</script>

<div class="view-residual">
    <?php $statementType = $this->isHybrid ? 'cpa' : 'residual'; ?>
    <script type="text/javascript">
        var statementId = <?=$this->statementId?>;
        <?php $startDate = strtotime($this->statementStartDate); ?>
        var startDate = new Date(<?=1000*date('U', $startDate)?>);
        <?php $endDate = strtotime($this->statementEndDate); ?>
        var endDate = new Date(<?=1000*date('U', $endDate )?>);
        <?php $nextStatementStartDate = strtotime("+1 days " . $this->statementEndDate); ?>
        var nextStatementStartDate = new Date(<?=1000 * date('U', $nextStatementStartDate)?>);
        <?php $nextStatementEndDate = strtotime("+61 days " . $this->statementEndDate); ?>
        var nextStatementEndDate = new Date(<?=1000*date('U', $nextStatementEndDate)?>);
        var defaultDate = '<?=date("m-d-Y", strtotime($this->statementStartDate))?>';
        var statementType = '<?=$this->statementType?>';
    </script>

    <?php if (!$this->isHybrid) : ?>
        <h1 class="ui header" id="statement-title">
            <?php
                if ($this->facility) {
                    echo $this->facility->getTitle() . " - " . date('F',strtotime($this->statementStartDate)) . ' ' . date('Y',strtotime($this->statementStartDate));
                } else {
                    echo date('F Y',strtotime($this->statementStartDate));
                }

            ?>
        </h1>

        <?=$this->partial(
            'new-facility-selector.phtml', array(
                'facilityCount' => $this->facilityCount,
                'facilityId' => $this->facilityId,
                'facilities' => $this->facilities,
                'statementId' => $this->statementId,
                'hasItems' =>
                    count($this->newLateLtvItems) ||
                    count($this->nonAutoconfirmedLtvItems) ||
                    count($this->autoConfirmedLtvItems) ||
                    count($this->autoDisputedLtvItems) ||
                    count($this->ltvBookings) ||
                    $this->numExistingLtvItems
            )
        );?>

        <div id="js-message-box"></div>
    <?php endif; ?>

    <?php function printColumnHeaders($isFilteredByFacility, $account) { ?>
        <thead>
        <tr>
            <th data-sort="string" class="residual-status-header">Status</th>
            <th data-sort="string" class="verified-header"><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In&nbsp;Verified&nbsp;By <i class="fa fa-info-circle"></i></a></th>
            <?=(!$isFilteredByFacility) ? '<th data-sort="string" class="facility-header">Facility</th>' : null ?>
            <th data-sort="string" class="customer-header">Customer</th>
            <th data-sort="string" class="scheduled-header">Scheduled Move-In</th>
            <th data-sort="string" class="reserved-header">Reserved</th>
            <th data-sort="string" class="sparefoot-fee-header"><a class="ui popup-text" data-content="<?= ($account->getResidualPercent() * 100) . '%'?> of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
        </tr>
        </thead>
    <?php } ?>
    <?php function printClientItemRow(Genesis_Entity_Transaction $item, $isFilteredByFacility, $statementStartDate, $statementId, $isEarly, $isLate, $bookingExtra) { ?>
        <?php if ($item->getFree() == 1) { return; } ?>

        <tr id="<?=$item->getConfirmationCode()?>">
            <td id="action-<?=$item->getConfirmationCode()?>">
                <div class="statement-reservation-actions-residual">
                    <?php
                    $billableInstance = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($item->getConfirmationCode(), $statementId);
                    $wasReviewed = ($item->getReviewStatus() === Genesis_Entity_Transaction::STATUS_REVIEWED) ? true : false;
                    $underReview = ($item->getReviewStatus() === Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) ? true : false;
                    ?>
                        <input type="hidden" id="delinquent-<?=$item->getConfirmationCode()?>" value="0" />
                        <p>This customer paid</p>
                        <h4><span class="unit-price"><?php
                                if ($isLate): ?>
                                    $0
                                <?php elseif ($billableInstance->getAmountCollected() > 0): ?>
                                    <?= "$".number_format($billableInstance->getAmountCollected(), 2)?>
                                <?php else:  ?>
                                    $0
                                <?php endif; ?>
                    </span></h4>
                        <p>for rent in <?=date('F Y',strtotime($statementStartDate))?></p>
                    <div id="status-<?=$item->getConfirmationCode()?>">
                        <h6 class="under-review <?=$underReview ? '' : 'is-hidden'?>">Under Review By SpareFoot</h6>
                        <h6 class="was-reviewed <?=$wasReviewed ? '' : 'is-hidden'?>">Reviewed</h6>
                    </div>

                </div>
                <div class="statement-customer-paid-edit">
                    <a class="edit-rent-collected ui button secondary default">Edit</a>
                </div>
            </td>
            <td class="statement-verification">
                <?php if(array_key_exists('consumer', $item->getMoveInVerifiedBy())){ ?>
                    <img src="/images/customer-verified.gif" width="19" height="20" alt="" />&nbsp;&nbsp;Customer
                <?php } ?>
                <?php if(count($item->getMoveInVerifiedBy()) > 1){ ?>
                    <br /><br />
                <?php } ?>
                <?php if(array_key_exists('facility', $item->getMoveInVerifiedBy())){ ?>
                    <img src="/images/facility-verified.gif" width="19" height="20" alt="" />&nbsp;&nbsp;Facility
                <?php } ?>
            </td>
            <?php if (! $isFilteredByFacility): ?>
                <td id="facility-name-<?=$item->getConfirmationCode()?>">
                    <?php if (! $isLate): ?> <a class="edit-facility"><i class="pull-right fa fa-pencil"></i></a> <?php endif; ?>
                    <span class="facility-name"><?=$item->getFacility()->getTitleWithCompanyCode()?></span><br/>
                    <span class="unit-number-span">
                    <?php if (! $isLate): ?>
                        <?php if($item->getUnitNumber()): ?>
                            Unit #: <span class="unit-number"><?=$item->getUnitNumber()?> </span>
                        <?php else: ?>
                            <a class="edit-facility">Add unit number</a>
                        <?php endif; ?>
                    <?php endif; ?>
                    </span>
                </td>
            <?php endif; ?>
            <td>
                <span id="customer-<?=$item->getConfirmationCode()?>">
                <?php if (!$isLate): ?>
                    <a class="edit-customer-name"><i class="pull-right fa fa-pencil"></i></a>
                <?php endif; ?>
                <?=nl2br($bookingExtra->customerInfo)?>
                </span>
                <?php if ($item->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED): ?>
                    <div id="tenant-<?=$item->getConfirmationCode()?>" class="is-hidden">
                        <?= nl2br($bookingExtra->tenantInfo) ?>
                        <?=($item->getUnitNumber() ? '<br/>Unit '.$item->getUnitNumber() : '')?>
                    </div>
                <?php endif; ?>


                <?php if ($bookingExtra->hasDuplicates): ?>
                    <br/><a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant."><h6>Multiple Reservations</h6></a>
                <?php endif;
                if (($item->getBookingType() === Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE)): ?>
                    <br/><a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
                <?php endif;
                if ($item->stringStatementSupportNotes()): ?>
                    <br/><a class="notes" data-original-title="Support Notes" data-html="<?=$item->stringStatementSupportNotes()?>" data-position="right center"><strong>NOTES</strong></a>
                <?php endif; ?>
            </td>
            <td id="date-<?=$item->getConfirmationCode()?>">
                <?php if (!$isLate): ?>
                    <a class="edit-move-in-date"><i class="pull-right fa fa-pencil"></i></a>
                <?php endif; ?>
                    <?=date("m-d-Y", strtotime($item->getMoveIn()))?>

            </td>
            <td><?=date("m-d-Y", strtotime($item->getTimestamp()))?></td>
            <td id="sparefootfee-<?=$item->getConfirmationCode()?>"><?="$".number_format($billableInstance->getSparefootCharge(), 2)?></td>
        </tr>
    <?php } ?>

    <?php if ($this->newLateLtvItems) : ?>
        <h3 class="ui header">
            Late Move-Ins
            <div class="sub header">These customers had move-in dates during the last 10 days of <?=date ( 'F', strtotime ( '-1 month' . $this->statementStartDate) )?>. If any of these customers moved in late, change their move-in date below to improve your move-in rate and search ranking. If none of last month's customers moved in late, you don't need to do anything. We'll only bill you for customers you move to this month.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="late_reservations">
                <?php printColumnHeaders($this->facility, $this->account); ?>
                <tbody id="late">
                    <?php
                    foreach ($this->newLateLtvItems as $item) {
                        printClientItemRow($item, (bool) $this->facility, $this->statementStartDate, $this->statementId, false, false, $this->bookingExtraInfo[$item->getConfirmationCode()]);
                    } ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php $reservationItems = RESIDUAL_AUTO_CONFIRM ? $this->nonAutoconfirmedLtvItems : $this->ltvBookings; ?>
    <?php if ($reservationItems): ?>
        <h3 class="ui header">
            Needs Your Review
            <div class="sub header">For the following reservations, please edit the customer details if needed, and let us know how much rent you collected in <?=date('F Y',strtotime($this->statementStartDate))?>. If the reservation moved in under a different name or at another one of your facilities, update it to be consistent with your records.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="new_reservations">
                <?php printColumnHeaders($this->facility, $this->account); ?>
                <tbody id="pending">
                    <?php
                    foreach ($reservationItems as $item) {
                        printClientItemRow($item, (bool) $this->facility, $this->statementStartDate, $this->statementId, false, false, $this->bookingExtraInfo[$item->getConfirmationCode()]);
                    } ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if (RESIDUAL_AUTO_CONFIRM): ?>
        <?php if ($this->autoConfirmedLtvItems): ?>
            <h3 class="ui header">
                Auto-Matched
                <div class="sub header">These customers were automatically matched to tenants in your management software.</div>
            </h3>
            <div class="table-responsive">
                <table class="ui table striped cell-headers sortable" id="auto_confirmed_reservations">
                    <?php printColumnHeaders($this->facility, $this->account); ?>
                    <tbody id="confirmed">
                        <?php foreach ($this->autoConfirmedLtvItems as $item) {
                            printClientItemRow($item, (bool) $this->facility, $this->statementStartDate, $this->statementId, false ,false, $this->bookingExtraInfo[$item->getConfirmationCode()]);
                        } ?>
                    </tbody>
                </table>
            </div>
        <?php endif ?>

        <?php if ($this->autoDisputedLtvItems): ?>
            <h3 class="ui header">
                Unmatched
                <div class="sub header">We weren't able to match these customers to tenants in your management software. To save you time, we marked these customers as "did not move in".</div>
            </h3>
            <div class="table-responsive">
                <table class="ui table striped cell-headers sortable" id="auto_disputed_reservations">
                    <?php printColumnHeaders($this->facility, $this->account); ?>
                    <tbody id="disputed">
                        <?php
                        foreach ($this->autoDisputedLtvItems as $item) {
                            printClientItemRow($item, (bool) $this->facility, $this->statementStartDate, $this->statementId, false ,false, $this->bookingExtraInfo[$item->getConfirmationCode()]);
                        } ?>
                    </tbody>
                </table>
            </div>
        <?php endif ?>
    <?php endif ?>

    <?php if ($this->numExistingLtvItems > 0): ?>
        <h3 class="ui header">
            Tenants
            <div class="sub header">You've previously received rent from each of these AdNetwork tenants. Let us know how much you collected from them during <?=date('F Y',strtotime($this->statementStartDate))?>.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="existing_tenants">
                <thead>
                    <tr>
                        <th data-sort="string" class="residual-status-header">Status</th>
                        <?php if (! $this->facility) { ?><th data-sort="string" class="facility-header">Facility</th><?php } ?>
                        <th data-sort="string" class="customer-header">Tenant</th>
                        <th data-sort="string" class="sparefoot-fee-header"><a class="ui popup-text" data-content="<?=($this->account->getResidualPercent() * 100) . '%'?> of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
                        <th data-sort="string" class="total-rent-header">Total Rent Collected</th>
                    </tr>
                </thead>
                <tbody id="tenants">
                <?php
                $filtered = $this->facility ? true : false;

                /** @var Genesis_Entity_Transaction $item */
                foreach ($this->existingLtvItems as $item) {
                    $billableInstance = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($item->getConfirmationCode(), $this->statementId);

                    $wasDelinquent = (bool) Genesis_Service_BillableInstance::load(
                        Genesis_Db_Restriction::and_(
                                Genesis_Db_Restriction::equal('confirmationCode', $item->getConfirmationCode()),
                                Genesis_Db_Restriction::equal('statementBatchId', $billableInstance->getStatementBatchId() - 1),
                                Genesis_Db_Restriction::equal('reason', Genesis_Entity_BillableInstance::REASON_DELIQUENCY)
                        )
                    )->uniqueResult();

                    $amountCollected = $billableInstance->getAmountCollected();
                    ?>
                    <tr id="<?=$item->getConfirmationCode()?>">
                        <td id="action-<?=$item->getConfirmationCode()?>">
                            <div class="statement-reservation-actions-residual">
                                <p>This customer paid</p>
                                <h4><span class="unit-price">
                                    <?=($amountCollected > 0) ? "$".number_format($amountCollected, 2) : '$0'?>
                                </span></h4>
                                <p>for rent in <?=date('F Y',strtotime($this->statementStartDate))?></p>
                                <div id="status-<?=$item->getConfirmationCode()?>">
                                    <?=($wasDelinquent ? '<h6>Previously Delinquent</h6>' : '')?>
                                </div>
                            </div>
                            <div class="statement-customer-paid-edit">
                                <a class="edit-tenant-rent-collected ui button secondary default">Edit</a>
                            </div>

                        </td>
                        <?php if(!$filtered): ?>
                            <td id="facility-name-<?=$item->getConfirmationCode()?>"><?=trim($item->getFacility()->getTitleWithCompanyCode())?><br/>
                            <span class="unit-number-span">
                            <?php if($item->getUnitNumber()) { ?>
                                Unit #: <span class="unit-number"><?=$item->getUnitNumber()?> </span><a style="padding-left:15px" class="edit-unit-number"><i class="fa fa-pencil"></i></a>
                            <?php } else { ?>
                                <a class="edit-unit-number">Add unit number</a>
                            <?php } ?>
                            </span>
                            </td>
                        <?php endif; ?>
                        <td>
                            <span id="customer-info-<?=$item->getConfirmationCode()?>"><?=nl2br($this->bookingExtraInfo[$item->getConfirmationCode()]->customerInfo)?></span>
                            <span id="tenant-<?=$item->getConfirmationCode()?>" class="is-hidden"><?=nl2br($this->bookingExtraInfo[$item->getConfirmationCode()]->tenantInfo)?></span>
                            <?=($item->stringStatementSupportNotes() ? '<br/><a class="notes" data-original-title="Support Notes" data-html="'.$item->stringStatementSupportNotes().'" data-position="right center"><strong>NOTES</strong></a>' : '')?>
                        </td>

                        <td id="sparefootfee-<?=$item->getConfirmationCode()?>"><?="$".number_format($billableInstance->getSparefootCharge(), 2)?></td>
                        <td id="lifetime-<?=$item->getConfirmationCode()?>">$<?=Genesis_Service_BillableInstance::getAmountCollectedSumByConfCode($item->getConfirmationCode());?></td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?=$this->partial('done-form.phtml',
        ['loggedUser'=>$this->loggedUser,
            'confirmedTime'=>$this->confirmedTime,
            'confirmations'=>$this->confirmations,
            'allowChanges'=>$this->allowChanges
        ]);?>

    <input type="hidden" id="residual_percent" name="residual_percent" value="<?=$this->account->getResidualPercent()?>" />

    <form id="edit-rent-collected-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title" id="edit-rent-collected-title">Edit</h4>
                </div>
                <div class="modal-body" style="min-height:300px;">
                    <div id="edit-rent-step-1" style="text-align:center;padding-top:10%;">
                        <h4>Did the customer move in?</h4>
                        <div style="width:30%;margin:0 auto;padding-top:20px;">
                            <a class="ui button default pull-left" id="customer-move-in-deny" >No</a>
                            <a class="ui button primary pull-right" id="customer-move-in-confirm">Yes</a>

                        </div>
                    </div>
                    <div id="edit-rent-step-2-A" style="display:none">
                        <input type="hidden" id="early_late" name="early_late" value="0" />
                        <input type="hidden" id="into_date" name="into_date" value="<?=$this->statementStartDate;?>" />
                        <p>How much rent did you collect?</p>
                        <div class="indent-left">
                            Enter new amount:
                            <div class="input-group">
                                <span class="input-group-addon">$</span><input type="text" id="rent-other" name="rent_other" value="" class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="id" value="<?=$this->statementId?>" />
                    <input type="hidden" name="residual_percent" value="<?=$this->account->getResidualPercent()?>" />

                    <a class="ui button default pull-left"  id="edit-rent-cancel" style="display:none" data-dismiss="modal" >Cancel</a>
                    <a class="ui button primary pull-right" id="edit-rent-submit" style="display:none">Submit</a>

                </div>

            </div>
        </div>
    </form>

    <form id="edit-facility-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-facility-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Facility</h4>
                </div>
                <div class="modal-body">
                    <p>Facility</p>
                    <select id="sister-facility-select" name="facility_id" class="form-control">
                        <?php foreach ($this->account->getFacilities()->toArray() as $facility): ?>
                            <?php if($facility->getApproved() && $facility->getPublished() && $facility->getBillableEntityId()):?>
                                <option value="<?=$facility->getId()?>"><?=$facility->getTitleWithCompanyCode()?></option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                    <p style="margin-top:30px">Unit Number (optional)</p>
                    <input type="text" id="unit-number" name="unit_number" value="" class="form-control" />

                </div>
                <div class="modal-footer">
                    <a id="edit-facility-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" id="sister-facility-select-submit"/>
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="<?=$this->statementId?>" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-unit-number-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-unit-number-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Unit Number</h4>
                </div>
                <div class="modal-body">
                    <p>Unit Number</p>
                    <input type="text" name="unit_number" value="" class="form-control unit-number" id="edit-unit-number-value" />

                </div>
                <div class="modal-footer">
                    <a id="edit-unit-number-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" id="edit-unit-number-submit" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="<?=$this->statementId?>" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-customer-name-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-customer-name-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Customer Name</h4>
                </div>
                <div class="modal-body">
                    <p>Please provide us with accurate contact information to appear on your bill.</p>
                    <p><input type="text" id="change-first-name" name="first_name" value="" placeholder="First Name" class="form-control" /></p>
                    <p><input type="text" id="change-last-name" name="last_name" value="" placeholder="Last Name" class="form-control" /></p>

                </div>
                <div class="modal-footer">
                    <a id="edit-customer-name-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="<?=$this->statementId?>" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-tenant-rent-collected-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title" id="edit-tenant-rent-collected-title">Edit</h4>
                </div>
                <div class="modal-body" style="min-height:300px;">
                    <div id="edit-tenant-rent-step-1" style="text-align:center;padding-top:10%;">
                        <h4>Did the Customer pay you rent in <?=date('F \of Y',strtotime($this->statementStartDate));?>?</h4>
                        <div style="width:30%;margin:0 auto;padding-top:20px;">
                            <a class="ui button default pull-left" id="tenant-rent-collected-deny" >No</a>
                            <a class="ui button primary pull-right" id="tenant-rent-collected-confirm">Yes</a>
                        </div>
                    </div>
                    <div id="edit-tenant-rent-step-2-A" style="display:none">
                        <p>Please enter how much rent the customer paid you during <?=date('F Y',strtotime($this->statementStartDate));?></p>
                        <div class="indent-left">
                            Enter new amount:
                            <div class="input-group">
                                <span class="input-group-addon">$</span><input type="text" id="rent-tenant-other" name="rent_other" value="" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div id="edit-tenant-rent-step-2-B" style="display:none">
                        This customer
                        <div class="radio controls only-existing-tenants">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_PREPAID?>" id="tenant-rent-zero" /> customer has previously pre-paid</label>
                        </div>
                        <div class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_DELIQUENCY?>" id="tenant-rent-deliquent" /> is delinquent on their rent payments</label>
                        </div>
                        <div class="radio controls only-existing-tenants">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_MOVED_OUT?>" id="tenant-rent-moved-out" /> moved out (this means the customer last paid you rent in <?=date('F Y',strtotime($this->statementStartDate . ' -1 month'))?>)</label>
                        </div>
                        <div id="lien-sale-option" class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_MOVED_OUT?>" id="tenant-rent-lien"/> lien sale of delinquent unit</label>
                        </div>
                        <div id="lien-sale-option" class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_OTHER?>" id="tenant-rent-other"/> other</label>
                        </div>

                    </div>

                </div>

                <div class="modal-footer">
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="id"  value="<?=$this->statementId?>" />
                    <input type="hidden" name="residual_percent" value="<?=$this->account->getResidualPercent()?>" />
                    <input type="hidden" name="is_tenant" value="1"/>
                    <a class="ui button default pull left"  id="edit-tenant-rent-cancel" style="display:none" data-dismiss="modal" >Cancel</a>
                    <a class="ui button primary pull-right" id="edit-tenant-rent-submit" style="display:none">Submit</a>

                </div>
            </div>
        </div>
    </form>

    <form id="edit-move-in-date-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-move-in-date-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Move-In Date</h4>
                </div>
                <div class="modal-body">
                    <p>Please enter the customer's new move-in date (pick any date this month if you don't know it):</p>
                    <input type="text" id="edit-move-in-date" name="into_date" value="" placeholder="YYYY-MM-DD" readonly="readonly" class="form-control datepicker-field" />
                </div>
                <div class="modal-footer">
                    <a id="edit-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui primary button" value="Save" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="<?=$this->statementId?>" />
                </div>
            </div>
        </div>
    </form>
</div>
