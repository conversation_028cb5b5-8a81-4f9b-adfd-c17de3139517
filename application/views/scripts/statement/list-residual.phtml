<?php if (isset($_SERVER['HTTP_USER_AGENT']) && (strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)) : ?>
    <div class="ui negative message">
        <i class="close icon"></i>
        <div class="header">
            Make sure your popup blocker is turned off.
        </div>
    </div>
<?php endif ?>

<?php
/** @var Genesis_Entity_Statement_Client $clientStatement */
foreach ($this->openStatements as $clientStatement) : ?>
    <div class="ui segment">
        <div class="ui top left attached label">Open Statement</div>
        <h2 class="ui header">
            <?= date('F j', strtotime($clientStatement->getStatementStartDate())) ?>-<?= date('j', strtotime($clientStatement->getStatementEndDate())) ?>
            <div class="sub header">Rent Collected</div>
        </h2>

        <div class="table-responsive">
            <table class="ui table very basic cell-headers">
                <tr>
                    <td>Reconciliation Deadline</td>
                    <td><?= date('F j', strtotime($clientStatement->getReconciliationEndDate())) ?></td>
                </tr>
                <tr>
                    <td>Current Tenants</td>
                    <td><?= $clientStatement->getNumLtvItemsGettingBill() ?> out of <?= $clientStatement->getNumBookingItems() ?></td>
                </tr>
                <tr>
                    <td>Current SpareFoot Fees</td>
                    <td><?= $clientStatement->stringTotalBookingCharge() ?></td>
                </tr>
            </table>
        </div>

        <div>
            <?php if (!Genesis_Service_Feature::isActive('myfoot.disable_reconcile_statement_button')) { ?>
                <a id="open_statement" class="ui button blue" href="<?= $this->url(['id' => $clientStatement->getStatementId(), 'action' => $this->reconcileStatementAction], 'statement') ?>">Reconcile Statement</a>
            <?php } ?>
            <div class="ui dropdown button right">
                <div class="text">Download</div>
                <i class="dropdown icon"></i>
                <div class="menu">
                    <a class="item" href="<?= $this->url(['action' => 'viewPdf', 'id' => $clientStatement->getStatementId()], 'statement') ?>">
                        <i class="file pdf outline icon"></i> PDF</a>
                    <a class="item" href="<?= $this->url(['action' => 'viewCsv', 'id' => $clientStatement->getStatementId()], 'statement') ?>">
                        <i class="file excel outline icon"></i> Excel</a>
                </div>
            </div>
        </div>

        <div class="ui divided list">
            <?php if (stristr($this->loggedUser->getAccount()->getInfoString(), 'SLA')) : ?>
                <a class="item" href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">
                    <div class="ui blue horizontal label">How To</div>
                    Use SiteLink to reconcile my statement
                </a>
            <?php endif ?>
        </div>
    </div>
<?php endforeach ?>

<?php echo $this->partial('billing-history.phtml'); ?>

<table id="statements" data-type="residual" class="table ui striped cell-headers sortable">
    <thead>
        <tr>
            <th></th>
            <th>Tenant Fees</th>
            <th>Tenants</th>
            <th class="no-sort"></th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td colspan="4">&nbsp;</td>
        </tr>
        <tr id="payload-error">
            <td colspan="4">Error</td>
        </tr>
        <tr id="no-content">
            <td colspan="4">No receipts found</td>
        </tr>
    </tbody>
</table>

<?php
if (
    $this->loggedUser
    && ($this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN || $this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD)
) :
?>

    <h2 id="billing-history">Billing History</h2>
    <div class="ui segment basic">
        <a class="ui button primary" href="<?= $this->url(['action' => 'receipts']) ?>">View Billing History</a>
    </div>

<?php endif; ?>