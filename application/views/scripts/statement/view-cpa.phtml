<?php
/*
    Cpa Statement:<br/>
    Name: <?=$this->clientStatement->getStatementTitle();?><br/>
    Num Bookings: <?=$this->clientStatement->getNumBookingItems();?><br/>
    Moved-In Bookings: <?=$this->clientStatement->getNumMovedInCpaItems();?><br/>
    Move-In Rate: <?=$this->clientStatement->stringMoveInRate();?><br/>
    Bookings Fee: <?=$this->clientStatement->stringTotalBookingCharge();?><br/>
    Products Fee: <?=$this->clientStatement->stringTotalProductCharge();?><br/>
    Total Fee: <?=$this->clientStatement->stringTotalCharge();?><br/>
    <br/>
    */
?>

<script>
    App.context = {
        facility_id: <?= ($this->facilityId) ? $this->facilityId : 'null' ?>,
        statement_id: <?= $this->clientStatement->getStatementId() ?>
    };
</script>

<div class="view-cpa">
    <?= $this->partial('move-in-rate.phtml', [
        'clientStatement' => $this->clientStatement,
        'showInterstitial' => $this->showInterstitial,
        'statementType' => 'cpa'
    ]); ?>
    <?php
    //prepare for later on page
    $isFree = false; // TODO compute isFree edge case? = appears to be true if every entry in regular bookings is free, or just remove isFree checks
    $autoConfirmedBookings = array();
    /** @var Genesis_Entity_Statement_Item_Booking $item */
    foreach ($this->clientStatement->getAutoConfirmedBookingItems() as $item) {
        if ($item->isBidTypePercent()) {
            $autoConfirmedBookings[] = $item;
        }
    }
    $autoDisputedBookings = array();
    /** @var Genesis_Entity_Statement_Item_Booking $item */
    foreach ($this->clientStatement->getAutoDisputedBookingItems() as $item) {
        if ($item->isBidTypePercent()) {
            $autoDisputedBookings[] = $item;
        }
    }
    $regularBookings = $this->clientStatement->getNoAutoStateBookingCpaItems();
    $reviewedBookings = $this->clientStatement->getReviewedBookingCpaItems();
    $lateBookings = $this->clientStatement->getLateCpaItems();
    $freeBookings = $this->clientStatement->getFreeCpaItems();

    // if all facilities are manual, there should only be one section
    $allManual = true;
    foreach ($this->facilities as $facility) {
        if ($facility->getCorporation()->getSourceId() != Genesis_Entity_Source::ID_MANUAL) {
            $allManual = false;
            break; // found one, can stop looking
        }
    }
    if ($allManual) {
        $regularBookings = array_merge($regularBookings, $autoDisputedBookings);
        $autoDisputedBookings = null;
    }
    ?>

    <?php // modal guide
    if (count($this->arr_softwares) > 0 && array_intersect([0 => 21, 1 => 23], $this->arr_softwares)): ?>
        <div id="guides-modal" class="modal fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">Do you HATE reconciling your statement?</h4>
                    </div>
                    <div class="modal-body">
                        <div class="modal-guides">
                            <p>Yeah, we know...</p>
                            <p>So we figured out the best way to use your management software for a more efficient reconciliation. Simply view the guide below and follow the steps. You'll enjoy more move-ins, which means higher rankings in our search results, which means EVEN MORE move-ins. Pretty cool cycle, huh?<br /><br /></p>

                            <?php if ($this->arr_softwares[21]) { ?>
                                <p><a class="ui button primary" href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">View Guide</a> <a href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">Reconciling with SiteLink Stand Alone</a></p>
                            <?php } ?>
                            <?php if ($this->arr_softwares[23]) { ?>
                                <p><a class="ui button primary" href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">View Guide</a> <a href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">Reconciling with WebSelfStorage</a></p>
                            <?php } ?>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="#" data-dismiss="modal" class="ui button">Close</a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif ?>

    <h1 class="ui header" id="statement-title">
        <?php if (! $this->clientStatement->isFilteredByFacility()): ?>
            <?= date('F Y', strtotime($this->clientStatement->getStatementStartDate())) ?>
        <?php else: ?>
            <?= $this->clientStatement->getFilteredFacility()->getTitle() ?> - <?= date('F Y', strtotime($this->clientStatement->getStatementStartDate())) ?>
        <?php endif ?>
    </h1>

    <?php if (isset($_SERVER['HTTP_USER_AGENT']) && (strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)): ?>
        <div class="ui negative message">
            <i class="close icon"></i>
            <div class="header">Make sure your popup blocker is turned off.</div>
        </div>
    <?php endif ?>

    <?php if (Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_SHOW_WSS_UPLOADER, ['account_id' => $this->clientStatement->getAccountId()])): ?>
        <?php
        $hasManualCpaFacility = false;
        foreach ($this->facilities as $facility) {
            if (
                $facility->getPublished()
                && $facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_MANUAL
                && $facility->getCorporation()->getAccount()->getCpa()
            ) {
                $hasManualCpaFacility = true;
                break;
            }
        }
        ?>
        <?php if ($hasManualCpaFacility): ?>
            <div class="ui warning message">
                <i class="close icon"></i>
                <div class="header">Save time! We'll reconcile your statement for you.</div>
                <p>
                    <strong>Do you use WebSelfStorage? </strong>
                    <a href="<?= $this->url(['action' => 'upload-mil', 'id' => $this->clientStatement->getStatementId()], 'statement') ?>">Upload your move-in list here</a> for automatic reconciliation.
                </p>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php
    $hasItems = $regularBookings ||
        $autoConfirmedBookings ||
        $lateBookings ||
        $freeBookings;

    if ($this->clientStatement->isCpaWithLtv()) {
        $hasItems = $hasItems ||
            // Add LTV Item conditions (same as in view-residual.phtml)
            $this->clientStatement->getNumExistingLtvItems() ||
            $this->clientStatement->getNumNewNotFreeLtvItems() ||
            $this->clientStatement->getNewEarlyLtvItems() ||
            $this->clientStatement->getNewLateLtvItems();
    }
    ?>
    <?= $this->partial('facility-selector.phtml', [
        'facilityCount' => $this->facilityCount,
        'facilityId' => $this->facilityId,
        'facilities' => $this->facilities,
        'statementId' => $this->clientStatement->getStatementId(),
        'hasItems' => $hasItems
    ]); ?>

    <div id="js-message-box"></div>

    <?php if ($autoConfirmedBookings): ?>
        <div class="ui message">
            <i class="close icon"></i>
            <div class="header"><a href="http://blog.sparefoot.com/sparefoot-reconciliation/" target="_blank">Read more</a> about your new, automated SpareFoot statement.</div>
        </div>
    <?php endif ?>

    <?php if ($this->isMIRFElegible && $regularBookings): ?>

        <h3 class="ui header">
            Estimated Minimum Move-In Rate Fee
            <?php if ($this->mirfPercentage === (Genesis_Util_NewMirfCalculation::MIR_FLOOR * 100)): ?>
                <div class="sub header">Non-integrated facilities will be charged an additional fee if their move-in rate is less than 50%. The estimated Minimum Move-In Rate Fee is shown below. <a href="https://support.sparefoot.com/hc/en-us/articles/115015444207#MinimumMIR" target="_blank">Learn more</a></div>
            <?php endif; ?>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="mirf-table">
                <thead>
                    <tr>
                        <th>Facility</th>
                        <th>Move-Ins</th>
                        <th><a class="ui popup-text" data-html='SpareFoot only counts one reservation per<br/>customer when calculating move-in rates<br/>for a given statement period, even if that<br/>customer has made multiple reservations.'>Unique Reservations <i class="fa fa-info-circle"></i></a></th>
                        <th>Move-In Rate</th>
                        <th>Estimated Move-In Rate Floor Charge</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->MIRFData['facilities'] as $facilityData): ?>
                        <?php if (($this->clientStatement->isFilteredByFacility() && $this->clientStatement->getFilteredFacility()->getId() == $facilityData['facility_id']) || !$this->clientStatement->isFilteredByFacility()): ?>
                            <tr data-id="<?= $facilityData['facility_id'] ?>">
                                <td>
                                    <a href="<?= $this->url(['action' => 'view', 'id' => $this->clientStatement->getStatementId()], 'statement') ?>?facility=<?= $facilityData['facility_id'] ?>">
                                        <?= htmlspecialchars($facilityData['facility_name']) ?>
                                    </a>
                                </td>
                                <td><?= $facilityData['total_pending'] ?></td>
                                <td><?= $facilityData['total_bookings'] ?></td>
                                <td><?= $facilityData['reservation_rate'] . " %" ?></td>
                                <td data-sort-value="<?= $facilityData['mirf'] ?>">
                                    <span>
                                        <?= "$" . number_format($facilityData['mirf'], 2) ?>
                                    </span>
                                    <span id="loading-spinner" style="display:none">
                                        <img id="loading-spinner" src="/images/loading.gif" width="15" height="15" />
                                        Loading Charge...
                                    </span>
                                </td>
                            </tr>
                        <?php endif ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($regularBookings): ?>
        <?php $this->clientStatement->sortItems($regularBookings); ?>
        <h3 class="ui header">
            Needs Your Review
            <div class="sub header">Please select the "X" next to each customer who did not move in.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="all-statements" data-type="pending">
                <thead>
                    <tr>
                        <?php if (!$isFree): ?>
                            <th class="no-sort"><!-- √ or X --></th>
                            <th class="no-sort status-col">Status</th>
                            <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        <?php endif ?>

                        <?php if (! $this->clientStatement->isFilteredByFacility()): ?>
                            <th>Facility</th>
                        <?php endif ?>

                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($regularBookings as $clientItem): ?>
                        <?= $this->partial('view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'clientStatement' => $this->clientStatement
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($autoConfirmedBookings): ?>
        <?php $this->clientStatement->sortItems($autoConfirmedBookings); ?>

        <h3 class="ui header">
            Auto-Matched
            <div class="sub header">These customers were automatically matched to tenants in your management software.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="auto-statements" data-type="confirmed">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>

                        <?php if (! $this->clientStatement->isFilteredByFacility()): ?>
                            <th>Facility</th>
                        <?php endif ?>

                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($autoConfirmedBookings as $clientItem): ?>
                        <?= $this->partial('view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'clientStatement' => $this->clientStatement
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($autoDisputedBookings): ?>
        <?php $this->clientStatement->sortItems($autoDisputedBookings); ?>
        <h3 class="ui header">
            Unmatched
            <div class="sub header">We weren't able to match these customers to tenants in your management software. To save you time, we marked these customers as "did not move in".</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="auto-disputed" data-type="disputed">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>

                        <?php if (! $this->clientStatement->isFilteredByFacility()): ?>
                            <th>Facility</th>
                        <?php endif ?>

                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($autoDisputedBookings as $clientItem): ?>
                        <?= $this->partial('view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'clientStatement' => $this->clientStatement
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($lateBookings): ?>
        <?php $this->clientStatement->sortItems($lateBookings); ?>
        <h3 class="ui header">
            Late Move-Ins
            <div class="sub header">These customers had move-in dates during the last 10 days of <?= date('F', strtotime('-1 month' . $this->clientStatement->getStatementStartDate())) ?>. If any of these customers moved in late, change their move-in date below to improve your move-in rate and search ranking. If none of last month's customers moved in late, you don’t need to do anything. We'll only bill you for customers you move to this month.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="late-move-ins" data-type="late">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>

                        <?php if (! $this->clientStatement->isFilteredByFacility()): ?>
                            <th>Facility</th>
                        <?php endif ?>


                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($lateBookings as $clientItem): ?>
                        <?= $this->partial('view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'clientStatement' => $this->clientStatement
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($reviewedBookings): ?>
        <?php $this->clientStatement->sortItems($reviewedBookings); ?>
        <h3 class="ui header">
            Previously Disputed Move-ins
            <div class="sub header">
                Our support team has reviewed your reservations disputed from the previous month.
                The disputed reservations that have been over ruled will show up on this months billing cycle.
            </div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="reviewed-statements" data-type="reviewed">
                <thead>
                    <tr>
                        <?php if (!$isFree): ?>
                            <th class="no-sort"><!-- √ or X --></th>
                            <th class="no-sort status-col">Status</th>
                        <?php endif ?>

                        <th>Reason for ruling</th>

                        <?php if (! $this->clientStatement->isFilteredByFacility()): ?>
                            <th>Facility</th>
                        <?php endif ?>

                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($reviewedBookings as $clientItem): ?>
                        <?= $this->partial('view-disputed-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'clientStatement' => $this->clientStatement
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($freeBookings): ?>
        <?php $this->clientStatement->sortItems($freeBookings); ?>
        <h3 class="ui header">
            No-Fee Reservations
            <div class="sub header">We sent you these reservations from SpareFoot products without transaction fees (GeoPages, SiteBuilder, and Booking Widgets).</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="no-fee-reservations" data-type="free">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        <?php if (! $this->clientStatement->isFilteredByFacility()): ?>
                            <th>Facility</th>
                        <?php endif ?>
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($freeBookings as $clientItem): ?>
                        <?= $this->partial('view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'clientStatement' => $this->clientStatement
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
        <?php endif;

    if (Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::CPA_STATEMENT_CONSUMER_CONTACTS, array('account_id' => $this->clientStatement->getAccountId()))):
        if ($this->clientStatement->getConsumerContacts() && $this->clientStatement->getConsumerContacts()->count() > 0): ?>
            <h3 class="ui header">
                We told these customers about your facility. Did they move in?
                <div class="sub header">If they did, it's a good idea to let us know. This will improve your move-in rate, so you'll rank higher in SpareFoot search results and get even more new customers. You won't be charged unless you mark a customer as moved in. Then we'll charge your standard unit size move-in fee.</div>
            </h3>
            <div class="table-responsive">
                <table class="ui cell-headers sortable striped table" id="no-fee-reservations">
                    <thead>
                        <tr>
                            <th class="no-sort"><!-- √ or X --></th>

                            <?php if (!$this->clientStatement->isFilteredByFacility()): ?>
                                <th>Facility</th>
                            <?php endif ?>

                            <th>Customer</th>
                            <th>Contact Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($this->clientStatement->getConsumerContacts() as $consumerContact): ?>
                            <?php if (Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::CPA_STATEMENT_CONSUMER_CONTACTS, array('account_id' => $this->clientStatement->getAccountId(), 'consumer_contact' => $consumerContact))) : ?>
                                <?= $this->partial('view-cpa-contact.phtml', [
                                    'clientItem' => $clientItem,
                                    'clientStatement' => $this->clientStatement,
                                    'consumerContact' => $consumerContact
                                ]) ?>
                            <?php endif ?>
                        <?php endforeach ?>
                    </tbody>
                </table>
            </div>
        <?php endif ?>
    <?php endif ?>

    <?php if (!$this->clientStatement->isCpaWithLtv()) : ?>
        <?= $this->partial(
            'done-form.phtml',
            [
                'loggedUser' => $this->loggedUser,
                'confirmedTime' => $this->confirmedTime,
                'confirmations' => $this->confirmations,
                'allowChanges' => $this->allowChanges
            ]
        ); ?>
    <?php endif; ?>

    <div id="interstitial-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title">Introducing the SpareFoot Bidding Tool!</h4>
                </div>
                <div class="modal-body">
                    <img src="/images/bidding-interstitial.jpg" width="400" height="370" style="float:right; margin-left:12px;" alt="interstitial" />
                    <h3>Make your AdNetwork listings perform better.</h3>
                    <p>Our bidding tool is the perfect way to generate more bookings for facilities that need a little extra help,
                        while ensuring that you don't pay more than necessary. See how your facilities are currently ranking, and
                        adjust your bid up or down to get the level of exposure you want for a price you are comfortable with.</p>
                    <br />
                    <h3>See where your facilities are ranking now: </h3>
                    <ul>
                        <?php foreach ($this->interstitialFacilities as $facility) { ?>
                            <li><a href="<?= $this->url(['action' => 'bid'], 'features') ?>?fid=<?= $facility->getId() ?>"><?= $facility->getTitle() ?></a></li>
                        <?php } ?>
                    </ul>
                    <a href="<?= $this->url(['action' => 'list'], 'features') ?>" style="margin-left:2.5em;">See all facilities</a>
                    <span class="clear"></span>
                </div>
            </div>
        </div>
    </div>

    <form id="change-move-in-date-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="change-move-in-date-modal-close" class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title">Change move-in date</h4>
                </div>
                <div class="modal-body">
                    <p id="change-move-in-date-customer-info"></p>
                    <label for="into-date">Please enter the customer's (approximate) new move-in date:</label>
                    <input type="text" id="into-date" name="into_date" value="" placeholder="" readonly="readonly" class="form-control datepicker-field" />
                </div>
                <div class="modal-footer">
                    <a id="change-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                    <input type="hidden" name="confirmation_code" />
                    <input type="submit" class="ui primary button" value="Save" id="change-move-in-date-submit" />
                </div>
            </div>
        </div>
    </form>

    <?= $this->partial('dispute-modal.phtml', ['clientStatement' => $this->clientStatement]); ?>
    <?= $this->partial('auto-dispute-modal.phtml'); ?>
</div>