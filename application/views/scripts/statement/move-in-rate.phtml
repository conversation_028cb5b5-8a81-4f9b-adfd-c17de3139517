<?php if (! isset($this->clientStatement)) { die('no statement'); } ?>
<?php
/**
 * the dates look complex, but we are passing them to the JS date constructor
 * as an int, which Date accepts directly as one param.
 * These also work for statements batch windows that are not exactly a month. Which is every test case...
 */
?>
<script type="text/javascript">
    var statementId = <?=$this->clientStatement->getStatementId()?>;
    <?php $startDate = strtotime($this->clientStatement->getStatementStartDate()); ?>
    var startDate = new Date(<?=1000*date('U', $startDate)?>);               /*<?=date('r',$startDate);?>*/
    <?php $endDate = strtotime($this->clientStatement->getStatementEndDate()); ?>
    var endDate = new Date(<?=1000*date('U', $endDate )?>);                 /*<?=date('r', $endDate); ?>*/
    <?php $nextStatementStartDate = strtotime("+1 days " . $this->clientStatement->getStatementEndDate()); ?>
    var nextStatementStartDate = new Date(<?=1000 * date('U', $nextStatementStartDate)?>);  /*<?=date('r', $nextStatementStartDate); ?>*/
    <?php $nextStatementEndDate = strtotime("+61 days " . $this->clientStatement->getStatementEndDate()); ?>
    var nextStatementEndDate = new Date(<?=1000*date('U', $nextStatementEndDate)?>);    /*<?=date('r', $nextStatementEndDate); ?>*/
    var defaultDate = '<?=date("m-d-Y", strtotime($this->clientStatement->getStatementStartDate()))?>';
    var showInterstitial = <?=($this->showInterstitial) ? 'true' : 'false'?>;
    var statementType = '<?=$this->statementType?>';
</script>