<tr class="cpa-item" data-id="<?=$this->clientItem->getConfirmationCode()?>" data-facility-id="<?= $this->clientItem->getFacility()->getId() ?>">
    <?php if (!$this->clientItem->getFree()): ?>
    <td id="action-<?=$this->clientItem->getConfirmationCode()?>" class="statement-actions">
        <div class="ui basic icon buttons" data-toggle="buttons">
            <?php
                $yesClasses = '';
                $noClasses = '';
                $isDisputed = in_array(
                    $this->bookingExtra->status, [
                        Genesis_Entity_Statement_Item_Cpa::STATUS_NEVER_MOVED_IN,
                        Genesis_Entity_Statement_Item_Cpa::STATUS_NEVER_MOVED_IN_REVIEWED,
                        Genesis_Entity_Statement_Item_Cpa::STATUS_UNDER_REVIEW
                    ]
                );
                if ($this->clientItem->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) {
                    $yesClasses = 'confirm-autoconfirmed-button ';
                    $noClasses = 'dispute-autoconfirmed-button ';

                    if (!$this->clientItem->getReviewStatus()) {
                        $yesClasses .= $isDisputed ? '' : 'active';
                        $noClasses .= $isDisputed ? 'active' : '';
                    } elseif ($this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) {
                        $yesClasses .= $isDisputed ? '' : 'active';
                        $noClasses .= $isDisputed ? 'active' : '';
                    } elseif ($this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED) {
                        if ($isDisputed) {
                            $noClasses .= ' active';
                        } else {
                            $yesClasses .= ' active';
                            $noClasses .= ' hidden';
                        }
                    }
                } elseif ($this->isLate) {
                    $yesClasses = 'change-move-in-date-button';
                    $noClasses = 'dispute-button active';
                } else {
                    $yesClasses = 'confirm-button '.($isDisputed ? '' : 'active');
                    $noClasses = 'dispute-button '.($isDisputed ? 'active' : '');
                }
            ?>
            <button class="ui basic green compact button <?=$yesClasses?>">
                <i class="checkmark icon"></i>
            </button>
            <button class="ui basic red compact button <?=$noClasses?>">
                <i class="remove icon"></i>
            </button>
        </div>
        <input type="hidden" name="options_<?=$this->clientItem->getConfirmationCode()?>"/>
    </td>
    <td id="status-<?=$this->clientItem->getConfirmationCode()?>" class="status-cell">
        <?php if ($this->clientItem->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) { ?>
            <?php if ($this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) { ?>
                <span class="under-review">Under Review by SpareFoot</span>
                <span class="error is-hidden">Did Not Move In</span>
                <span class="success is-hidden">Moved In</span>
            <?php } else { ?>
                <span class="under-review is-hidden">Under Review by SpareFoot</span>
                <span class="error<?=$isDisputed?'':' is-hidden'?>">Did Not Move In <?=$this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED ? ' (Reviewed)' : ''?></span>
                <span class="success<?=$isDisputed?' is-hidden':''?>">Moved In <?=$this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED ? ' (Reviewed)' : ''?></span>
            <?php } ?>
        <?php } elseif ($this->isLate) { ?>
            <span class="error">Did Not Move In</span><img id="activity-indicator-<?=$this->clientItem->getConfirmationCode()?>" class="is-hidden" src="/images/loading.gif" alt="loading"/>
            <span class="success is-hidden">Moved In</span>
        <?php } else { ?>
            <span class="error <?=($isDisputed ? '' : 'is-hidden')?>">Did Not Move In</span>
            <span class="success <?=($isDisputed ? 'is-hidden' : '')?>">Moved In</span>
        <?php } ?>

        <?php // Support notes
        if ($this->clientItem->stringStatementSupportNotes()): ?>
            <br /><a class="ui popup-text" data-title="Support Notes" data-content="<?=$this->clientItem->stringStatementSupportNotes()?>"><strong>NOTES</strong></a>
        <?php endif ?>
    </td>
    <td class="statement-verification">
        <?php if(array_key_exists('consumer', $this->clientItem->getMoveInVerifiedBy())){ ?>
            <img src="/images/customer-verified.gif" width="19" height="20" alt="" />  Customer
        <?php } ?>
        <?php if(count($this->clientItem->getMoveInVerifiedBy()) > 1){ ?>
            <br /><br />
        <?php } ?>
        <?php if(array_key_exists('facility', $this->clientItem->getMoveInVerifiedBy())){ ?>
            <img src="/images/facility-verified.gif" width="19" height="20" alt="" />  Facility
        <?php } ?>
    </td>
    <?php endif ?>

    <?php if (!$this->facility): ?>
        <td id="facility-name-<?=$this->clientItem->getConfirmationCode()?>"><a href="<?=$this->url(['action' => 'view', 'id'=> $this->statementId], 'statement')?>?facility=<?=$this->clientItem->getFacility()->getId()?>"><?=htmlspecialchars($this->clientItem->getFacility()->getTitleWithCompanyCode())?></a></td>
    <?php endif; ?>

    <td id="customer-<?=$this->clientItem->getConfirmationCode()?>">
        <span id="customer-info-<?=$this->clientItem->getConfirmationCode()?>">
            <?=nl2br(htmlspecialchars($this->bookingExtra->customerInfo))?>
        </span>
        <?=($this->clientItem->getUnitNumber() ? '<br/>Unit '.$this->clientItem->getUnitNumber() : '')?>
        <?php if ($this->clientItem->getBookingType() === Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE): ?>
            <a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
        <?php endif; ?>

        <?php if ($this->bookingExtra->hasDuplicates): ?>
            <a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant.">Multiple Reservations</a>
        <?php endif; ?>
        <?php if ($this->clientItem->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED): ?>
            <div id="tenant-<?=$this->clientItem->getConfirmationCode()?>" class="is-hidden">
                <?=nl2br($this->bookingExtra->tenantInfo)?>
                <?=($this->clientItem->getUnitNumber() ? '<br/>Unit '.$this->clientItem->getUnitNumber() : '')?>
            </div>
        <?php endif; ?>
    </td>
    <td id="date-<?=$this->clientItem->getConfirmationCode()?>"><?=date("m-d-Y", strtotime($this->clientItem->getMoveIn()))?></td>
    <td id="reservation-<?=$this->clientItem->getConfirmationCode()?>"><?= date("m-d-Y", strtotime($this->clientItem->getTimestamp()))?></td>
    <td id="baseBid-<?=$this->clientItem->getConfirmationCode()?>"><?=$this->clientItem->stringBaseBid()?></td>
    <td id="amount-<?=$this->clientItem->getConfirmationCode()?>">
        <?php
        #TODO: Optimize this (or at least move to controller)
        $billableInstance = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($this->clientItem->getConfirmationCode(), $this->statementId);
        echo (!$billableInstance) ? "$0.00" : ("$" .number_format($billableInstance->getSparefootCharge(), 2, '.', ','));
        ?>
    </td>
    <td id="lifetime-<?=$this->clientItem->getConfirmationCode()?>">
        <a href="#" class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of <?= "$" . number_format($this->clientItem->getLatestBillableInstance()->getUnitPrice(), 2, '.', ',') ?>.">
            <?= "$".number_format(($this->clientItem->getLatestBillableInstance()->getUnitPrice() * 12), 2, '.', ',')?>
        </a>
        <br />
        <a href="#" class="ui popup-text" data-content="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant.">
            <?php
                if ($this->clientItem->getBidAmount() > 0) {
                    $lifetimeValueROI = (($this->clientItem->getLatestBillableInstance()->getUnitPrice() * 12)/ $this->clientItem->getBidAmount()) * 100;
                    echo number_format($lifetimeValueROI, 0, '.', ',') . '%';
                }
            ?>
            <abbr data-content="Return on Investment">ROI</abbr>
        </a>
    </td>
</tr>
