<a class="ui icon button" href="<?=$this->url(['action'=>'index'])?>"><i class="icon left arrow"></i> Back</a>

<div class="row ui segment" style="margin-top:2%;margin-bottom:2%;">
    <div class="col-md-12" style="margin-bottom:1%;">
        <h2>Billing History</h2>
    </div>
    <div class="col-md-12" style="margin-bottom:1%;">
        <p>
            Select a statement period range below to view your billing history.
        </p>
    </div>
    <div>
        <div class="col-md-3">
            <label for="frmSaveOffice_startdt">Statement Period From</label>
            <div class="input-group date" id="dateFrom">
                <input type="text" class="form-control" id="inputDateFrom" required>
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar">
                    </span>
                </span>
            </div>
        </div>
        <div class="col-md-3">
            <label for="frmSaveOffice_startdt">Statement Period To</label>
            <div class="input-group date" id="dateTo">
                <input type="text" class="form-control" id="inputDateTo" required>
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar">
                    </span>
                </span>
            </div>
        </div>
        <div class="col-md-2">
        <label class="invisible" for="submitReceiptsRange">View History</label>
            <div class="input-group">
                <button type="submit" id="submitReceiptsRange" class="ui secondary button right">View History</button>
            </div>
        </div>
    </div>
    <div class="col-md-12" style="margin-top: 1%;" id="alert-container">
    </div>
</div>

<div class="table-responsive">
    <table id="receipts-table" class="ui table striped cell-headers sortable">
        <thead>
            <tr>
                <th>Statement Period</th>
                <th>Name</th>
                <th>Amount</th>
                <th>Transaction Date</th>
                <th class="no-sort"></th>
            </tr>
        </thead>
        <tbody id="receipts-body-table">
        <?php if (count($this->netsuite_receipts) > 0): ?>
            <?php foreach($this->netsuite_receipts as $receipt): ?>
            <tr>
                <td data-sort-value="<?=strtotime($receipt['statement_period'])?>"><?= $receipt['statement_period'] ?></td>
                <td><?= $receipt['name'] ?></td>
                <td data-sort-value="<?=$receipt['amount']?>">$<?= number_format($receipt['amount'], 2) ?></td>
                <td><?= $receipt['trandate']  ?></td>
                <td><a class="ui secondary compact button right" href="<?= $receipt['link']?>" target="_blank"><i class="file outline icon pdf"></i> PDF</a></td>
            </tr>
            <?php endforeach ?>
        <?php elseif (!empty($this->payload_error)): ?>
            <tr id="payload-error">
                <td>No receipts found</td>
            </tr>
        <?php else: ?>
            <tr>
                <td>Loading receipts...</td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>

