<?php if ($this->loggedUser->isMyfootAdmin() && (count($this->confirmations))): ?>
    <h4>Statement Confirmations</h4>
    <div class="table-responsive">
        <table class="ui table striped cell-headers sortable" id="confirmations-table">
            <thead>
                <tr>
                <th data-sort="string">User</th>
                <th data-sort="string">Email</th>
                <th data-sort="string">Role</th>
                <th data-sort="string">Confirmation Date</th>
                </tr>
            </thead>
            <tbody>
                <?php
                /**
                * @var $confirmation Genesis_Entity_StatementConfirmation
                */
                foreach ($this->confirmations as $confirmation): ?>
                <tr>
                <td><?=$confirmation->getUser()->getFullName()?></td>
                <td><a href="mailto:<?=$confirmation->getUser()->getEmail()?>"><?=$confirmation->getUser()->getEmail()?></a></td>
                <td><?=ucfirst($confirmation->getUser()->getMyfootRole())?></td>
                <td><?=date('H:ia \o\n F j, Y ',strtotime($confirmation->getConfirmationTime()))?></td>
                </tr>
                <?php endforeach ?>
            </tbody>
        </table>
    </div>
<?php endif; ?>

<div id="statement-confirmed-message" class="ui message info" style="<?=$this->confirmedTime ? null : 'display: none' ?>">
 Statement confirmed by <b><?=$this->loggedUser->getFullName()?></b> <em>(<a href="mailto:<?=$this->loggedUser->getEmail()?>"><?=$this->loggedUser->getEmail()?>)</a></em>  at <span id="confirmation-time" style="font-weight: bold"><?=date('H:ia \o\n F j, Y ',strtotime($this->confirmedTime))?></span>.<br/>
    <?php if($this->allowChanges): ?>
    You can still edit and resubmit this statement until <?=date('F j, Y ',strtotime($this->allowChanges))?>.<br/><br/>
    <a id="resubmit-statement-button" class="ui button primary" href="#" >Resubmit</a>
    <?php endif; ?>
</div>
<?php if($this->allowChanges): ?>
    <a id="confirm-statement-button" class="ui button primary" href="#" style="<?=$this->confirmedTime ? 'display: none' : null ?>">Confirm</a>
<?php endif; ?>
