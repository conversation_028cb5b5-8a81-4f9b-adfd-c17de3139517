<?php if (isset($_SERVER['HTTP_USER_AGENT']) && (strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)) : ?>
    <div class="ui negative message">
        <i class="close icon"></i>
        <div class="header">
            Make sure your popup blocker is turned off.
        </div>
    </div>
<?php endif ?>

<?php foreach ($this->openStatements as $clientStatement) : ?>

    <?php if ($clientStatement->isCpa() || $clientStatement->isCpaWithLtv()) : ?>
        <div class="ui segment">
            <div class="ui top left attached label">Open Statement</div>
            <h2 class="ui header">
                <?= date('F j', strtotime($clientStatement->getStatementStartDate())) ?>-<?= date('j', strtotime($clientStatement->getStatementEndDate())) ?>
                <div class="sub header">Move-Ins</div>
            </h2>

            <div class="table-responsive">
                <table class="ui table very basic">
                    <tr>
                        <td>Reconciliation Deadline</td>
                        <td><?= date('F j', strtotime($clientStatement->getReconciliationEndDate())) ?></td>
                    </tr>
                    <tr>
                        <td>Current Move-Ins</td>
                        <td><?= $clientStatement->getNumMovedInCpaItems() ?> out of <?= $clientStatement->getNumBookingItems() ?></td>
                    </tr>
                    <tr>
                        <td>Current Move-In Fees</td>
                        <td><?= $clientStatement->stringTotalBookingCharge() ?></td>
                    </tr>
                    <?php if ($this->isMIRFElegible) : ?>
                        <tr>
                            <td id="mirf_estimated">
                                <?php if ($this->mirfPercentage === (Genesis_Util_NewMirfCalculation::MIR_FLOOR * 100)) : ?>
                                    <a id="mirf_popup" href="#" data-html="Non-integrated facilities will be charged an additional fee if their move-in rate<br/>is less than 50%. The charge shown here is an estimate for the open<br/>statement period. It will be adjusted based on your reconciliation and move-in<br/>rate when the statement closes.<br/><br/><a href='https://support.sparefoot.com/hc/en-us/articles/115015444207#MinimumMIR' target='_blank'>Learn more</a>">
                                        Current Estimated Minimum Move-In Rate Fees for Non-Integrated Facilities
                                        <i class="fa fa-info-circle"></i>
                                    </a>
                                <?php else: ?>
                                    Current Estimated Minimum Move-In Rate Fees
                                <?php endif; ?>
                            </td>
                            <td><?= $this->totalMIRF ?></td>
                        </tr>
                    <?php endif; ?>
                </table>
            </div>
            <div>
                <?php if (!Genesis_Service_Feature::isActive('myfoot.disable_reconcile_statement_button')) { ?>
                    <a class="ui button blue" id="open_statement" href="<?= $this->url(['id' => $clientStatement->getStatementId(), 'action' => $this->reconcileStatementAction], 'statement') ?>">Reconcile Statement</a>
                <?php } ?>
                <div class="ui dropdown button right">
                    <div class="text">Download</div>
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a class="item" href="<?= $this->url(['id' => $clientStatement->getStatementId(), 'action' => 'viewpdf', 'user_id' => $this->userId], 'statement') ?>">
                            <i class="file pdf outline icon"></i> PDF</a>
                        <a class="item" href="<?= $this->url(['id' => $clientStatement->getStatementId(), 'action' => 'viewcsv', 'user_id' => $this->userId], 'statement') ?>">
                            <i class="file excel outline icon"></i> Excel</a>
                    </div>
                </div>
            </div>

            <div class="ui divided list">
                <?php if (stristr($this->loggedUser->getAccount()->getInfoString(), 'SLA')) : ?>
                    <a class="item" href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">
                        <div class="ui blue horizontal label">How To</div>
                        Use SiteLink to reconcile my statement
                    </a>
                <?php endif ?>
            </div>
        </div>
    <?php endif ?>
<?php endforeach ?>
<?php if ($this->isMIRFElegible && $this->mirfPercentage === (Genesis_Util_NewMirfCalculation::MIR_FLOOR * 100)): ?>
    <div class="ui segment">
        <div id="mirf_grid" class="ui grid">
            <div class="sixteen wide column">
                <h2 class="ui header">Minimum Move-In Rate Fees</h2>
            </div>
            <div class="twelve wide computer eight wide tablet column ">
                <p><b>Non-Integrated Facilities will be charged an additional fee if their move-in rate is less than 50%.</b> If you are using a Facility Management Software that we integrate with, you can avoid this fee by integrating your facilities. </p>
            </div>
            <div class="four wide computer eight wide tablet column">
                <a class="ui button fluid blue" target="_blank" href="https://info.storable.com/sparefoot-integration">
                    Integrate Facilities
                </a>
            </div>
            <div class="sixteen wide column">
                <a class="learn_about_integrations" href="https://support.sparefoot.com/hc/en-us/articles/115015444207#MinimumMIR" target="_blank">
                    Learn how the Minimum Move-In Rate Fee Is Calculated →
                </a>
            </div>
        </div>
    </div>
<?php endif; ?>


<?php echo $this->partial('billing-history.phtml'); ?>

<table id="statements" data-type="cpa" class="ui table striped cell-headers sortable">
    <thead>
        <tr>
            <th></th>
            <?php if ($this->isMIRFElegible) : ?>
                <th id="total_fees" data-html="This column may display a combination of<br/>Move-In Fee Totals and Minimum Move-In<br/> Rate Fees<?php if ($this->mirfPercentage === (Genesis_Util_NewMirfCalculation::MIR_FLOOR * 100)): ?> if you have facilities on your<br/>account that are not integrated. <?php endif; ?>">
                    Total Fees
                    <i class="fa fa-info-circle"></i>
                </th>
            <?php else: ?>
                <th>Move-In Fee Total</th>
            <?php endif; ?>
            <th>Moved In</th>
            <th>Did Not Move In</th>
            <th>Move In Rate</th>
            <th class="no-sort"></th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td colspan="4">&nbsp;</td>
        </tr>
    </tbody>
</table>


<?php
if (
    $this->loggedUser
    && ($this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN || $this->loggedUser->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD)
) :
?>

    <h2 id="billing-history">Billing History</h2>
    <div class="ui segment basic">
        <a class="ui button primary" href="<?= $this->url(['action' => 'receipts']) ?>">View Billing History</a>
    </div>

<?php endif; ?>