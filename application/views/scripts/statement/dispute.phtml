<form id="dispute-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal" id="dispute-modal-close">×</button>
                <h4 class="modal-title">Why didn't the customer move in this month?</h4>
            </div>
            <div class="modal-body">
                <p id="dispute-customer-info"></p>
                <input type="radio" name="dispute-reason" value="false" style="display: none" />
                <div class="controls">
                    <div class="radio">
                        <label><input type="radio" name="dispute-reason" value="delay" id="dispute-change-move-in" /> Customer might move in at a future date</label>
                    </div>
                    <div class="is-hidden customer-reason-specifics">
                        <p>Please enter the customer's new move-in date (pick any date this month if you don't know it):</p>
                        <input type="text" id="out-date" name="out_date" value="" placeholder="" readonly="readonly" class="form-control datepicker-field" />
                    </div>
                </div>
                <div class="controls">
                    <div class="radio">
                        <label><input type="radio" name="dispute-reason" value="name-change" id="dispute-change-name"/> Customer moved in under a different name</label>
                    </div>
                    <div class="is-hidden customer-reason-specifics">
                        <p>This is still a valid move-in. Please provide us with accurate contact information to appear on your bill.</p>
                        <input type="text" id="change-first-name" name="first_name" value="" placeholder="First Name" class="form-control" /><br />
                        <input type="text" id="change-last-name" name="last_name" value="" placeholder="Last Name" class="form-control" />
                    </div>
                </div>
                <?php if(count($this->clientStatement->getSisterFacilityList()) > 1){ ?>
                    <div class="controls">
                        <div class="radio">
                            <label><input type="radio" name="dispute-reason" value="facility-change" id="dispute-change-facility"/> Customer moved into a sister facility</label>
                        </div>
                        <div id="facility-change-input" class="is-hidden customer-reason-specifics">
                            <p>Which facility?</p>
                            <select id="new-facility-id" name="facility_id" class="form-control">
                                <?php foreach($this->clientStatement->getSisterFacilityList() as $facility){ ?>
                                    <?php if($facility->getApproved() && $facility->getPublished() && $facility->getBillableEntityId()) { ?>
                                        <option value="<?=$facility->getId()?>"><?=$facility->getTitle()?> <?=($facility->getCompanyCode())?'('.$facility->getCompanyCode().')':''?></option>
                                    <?php } ?>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                <?php } ?>
                <div class="controls">
                    <div class="radio">
                        <label><input id="rescancel" type="radio" name="dispute-reason" value="canceled" id="dispute-cancel" /> Customer will never move in</label>
                    </div>
                    <div class="is-hidden customer-reason-specifics">
                        <div class="form-group">
                            <p><strong>Why didn’t this customer move in?</strong><br/>
                                Please let us know! We’ll use this information to follow up with the customer, and to improve the quality of customers we send you in the future.</p>
                        </div>

                        <div class="form-group">
                            We tried to contact this customer, but could not reach them because:
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="Customer's contact information was invalid"> Customer’s contact information was invalid</label>
                            </div>
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="Customer did not respond to our contact attempts"> Customer did not respond to our contact attempts</label>
                            </div>
                        </div>

                        <div class="form-group">
                            We spoke with this customer, but they:
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="Didn't show up on date they said they would"> Didn’t show up on date they said they would</label>
                            </div>
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="No longer needed to rent a storage unit"> No longer needed to rent a storage unit</label>
                            </div>
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="Rented storage elsewhere"> Rented storage elsewhere</label>
                            </div>
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="Felt misinformed by SpareFoot or the rental process"> Felt misinformed by SpareFoot or the rental process</label>
                            </div>
                        </div>

                        <div class="form-group">
                            We could not accommodate this customer because:
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="Their reserved unit did not meet their needs"> Their reserved unit did not meet their needs</label>
                            </div>
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="We did not have their reserved unit available " id="rescancel-no-unit"> We did not have their reserved unit available</label>
                            </div>
                        </div>

                        <div class="form-group">
                            Other:
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="This is a duplicate reservation"> This is a duplicate reservation</label>
                            </div>
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="We did not attempt to contact this customer "> We did not attempt to contact this customer</label>
                            </div>
                            <div class="radio">
                                <label><input type="radio" name="dispute-reason-other" value="I don’t think SpareFoot generated this lead"> I don’t think SpareFoot generated this lead</label>
                            </div>
                            <!--<div class="radio">
                                <label><input id="other-reason" type="radio" name="dispute-reason-other" value="Other"> Other (please specify):</label>
                            </div>
                                <textarea id="dispute-text-other" name="customer-dispute-reason" class="form-control"></textarea>-->
                            <textarea style="display:none" id="dispute-text" name="customer-dispute-reason"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="ui button" data-dismiss="modal" id="dispute-modal-cancel">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="submit" class="ui button primary" value="Save" id="dispute-submit" />
            </div>
        </div>
    </div>
</form>
