<?php //improve performance
    /**
     * @var $this->clientStatement Genesis_Entity_LtvStatementInterface
     */
    $numExistingLtvItems = $this->clientStatement->getNumExistingLtvItems();
    $newLateLtvItems = $this->clientStatement->getNewLateLtvItems();

    /*
     * lets make it easier to use this in the view, and always get it right
     */
    define('RESIDUAL_AUTO_CONFIRM', Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS, array('account_id' => $this->clientStatement->getAccountId())) ? true : false);
    // can remove the script below after 6-10-2015, FAC-916
?>
<script>
var cdpEnabled = <?=$this->clientStatement->getAccount()->isPartiallyCdpEnabled() ? "true" : "false"?>;
App.context = {facility_id:<?=($this->facilityId) ? $this->facilityId : 'null'?>,statement_id:<?=$this->clientStatement->getStatementId()?>};
</script>

<div class="view-residual">
    <?php $statementType = $this->clientStatement->isCpaWithLtv() ? 'cpa' : 'residual'; ?>
    <?= $this->partial('move-in-rate.phtml', [
        'clientStatement' => $this->clientStatement,
        'showInterstitial' => $this->showInterstitial,
        'statementType' => $statementType
    ]); ?>

    <?php if (!$this->clientStatement->isCpaWithLtv()) : ?>
        <h1 class="ui header" id="statement-title"><?=$this->clientStatement->getStatementTitle()?></h1>

        <?=$this->partial(
            'facility-selector.phtml', array(
                'facilityCount' => $this->facilityCount,
                'facilityId' => $this->facilityId,
                'facilities' => $this->facilities,
                'statementId' => $this->clientStatement->getStatementId(),
                'hasItems' =>
                    $numExistingLtvItems ||
                    $this->clientStatement->getNumNewNotFreeLtvItems() ||
                    $this->clientStatement->getNewEarlyLtvItems() ||
                    $newLateLtvItems
            )
        );?>

        <div id="js-message-box"></div>
    <?php endif; ?>

    <?php function printColumnHeaders($clientStatement) { ?>
        <thead>
        <tr>
            <th data-sort="string" class="residual-status-header">Status</th>
            <th data-sort="string" class="verified-header"><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In&nbsp;Verified&nbsp;By <i class="fa fa-info-circle"></i></a></th>
            <?=(! $clientStatement->isFilteredByFacility()) ? '<th data-sort="string" class="facility-header">Facility</th>' : null ?>
            <th data-sort="string" class="customer-header">Customer</th>
            <th data-sort="string" class="scheduled-header">Scheduled Move-In</th>
            <th data-sort="string" class="reserved-header">Reserved</th>
            <th data-sort="string" class="sparefoot-fee-header"><a class="ui popup-text" data-content="<?=$clientStatement->stringAcctResidualPercent()?> of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
        </tr>
        </thead>
    <?php } ?>
    <?php function printClientItemRow(Genesis_Entity_Statement_Item_Ltv $item, Genesis_Entity_Statement_Client $clientStatement) { ?>
        <?php if ($item->isFreeBooking()) { return; } ?>

        <tr id="<?=$item->getConfirmationCode()?>">
            <td id="action-<?=$item->getConfirmationCode()?>">
                <div class="statement-reservation-actions-residual">
                    <?php
                    $wasReviewed = ($item->getReviewStatus() === Genesis_Entity_Transaction::STATUS_REVIEWED) ? true : false; //used twice!
                    $underReview = ($item->getReviewStatus() === Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) ? true : false; //used twice!
                    if (1==2 && RESIDUAL_AUTO_CONFIRM && $item->isAutoConfirmed()):
                        if ($wasReviewed) {
                            $checked = true;
                        } elseif ($underReview) {
                            $checked = false;
                        } else {
                            $checked = true;
                        }?>

                    <?php else: ?>
                        <input type="hidden" id="delinquent-<?=$item->getConfirmationCode()?>" value="0" />
                        <p>This customer paid</p>
                        <h4><span class="unit-price"><?php
                                if ($item->isLate()): ?>
                                    $0
                                <?php elseif ($item->getAmountCollected() > 0): ?>
                                    <?=$item->stringAmountCollected()?>
                                <?php else:  ?>
                                    $0
                                <?php endif; ?>
                    </span></h4>
                        <p>for rent in <?=date('F Y',strtotime($clientStatement->getStatementStartDate()))?></p>
                    <?php endif; ?>
                    <div id="status-<?=$item->getConfirmationCode()?>">
                        <h6 class="under-review <?=$underReview ? '' : 'is-hidden'?>">Under Review By SpareFoot</h6>
                        <h6 class="was-reviewed <?=$wasReviewed ? '' : 'is-hidden'?>">Reviewed</h6>
                    </div>

                </div>
                <div class="statement-customer-paid-edit">
                    <a class="edit-rent-collected ui button secondary default">Edit</a>
                </div>
            </td>
            <?php if (2 == 1 && RESIDUAL_AUTO_CONFIRM && $item->isAutoConfirmed()): ?>
                <td id="status-<?=$item->getConfirmationCode()?>">
                    <?php

                    if ($item->isAutoConfirmed()) {
                        if ($wasReviewed) {
                            $mode = 'success';
                        } elseif ($underReview) {
                            $mode = 'review';
                        } elseif ($item->isAutoDisputed()) {
                            $mode = 'error';
                        } else {
                            $mode = 'success';
                        }
                    } elseif ($item->isEarly()) {
                        $mode = 'early';
                    } elseif ($item->isLate()) {
                        $mode = 'error';
                    } else {
                        $mode = 'error';
                    } ?>

                    <span class="early <?=$mode === 'early' ? '' : 'is-hidden'?>">Move&nbsp;In&nbsp;Next&nbsp;Month</span>
                    <span class="under-review <?=$mode === 'review' ? '' : 'is-hidden'?>">Under&nbsp;Review&nbsp;by&nbsp;SpareFoot</span>
                    <span class="error <?=$mode === 'error' ? '' : 'is-hidden'?>">Did&nbsp;Not&nbsp;Move&nbsp;In <?=$wasReviewed ? '(Reviewed)' : ''?></span>
                    <span class="success <?=$mode === 'success' ? '' : 'is-hidden'?>">Moved&nbsp;In <?=$wasReviewed ? '(Reviewed)' : ''?></span>
                    <img id="activity-indicator-<?=$item->getConfirmationCode()?>" class="is-hidden" src="/images/loading.gif" />
                    <?php if ($item->getSupportNotes()): ?>
                        <br /><a class="notes" data-original-title="Support Notes" data-html="<?=$item->getSupportNotes()?>" data-position="right center"><strong>NOTES</strong></a>
                    <?php endif; ?>
                </td>
            <?php endif; ?>
            <td class="statement-verification">
                <?php if(array_key_exists('consumer', $item->getMoveInVerifiedBy())){ ?>
                    <img src="/images/customer-verified.gif" width="19" height="20" alt="" />&nbsp;&nbsp;Customer
                <?php } ?>
                <?php if(count($item->getMoveInVerifiedBy()) > 1){ ?>
                    <br /><br />
                <?php } ?>
                <?php if(array_key_exists('facility', $item->getMoveInVerifiedBy())){ ?>
                    <img src="/images/facility-verified.gif" width="19" height="20" alt="" />&nbsp;&nbsp;Facility
                <?php } ?>
            </td>
            <?php if (! $clientStatement->isFilteredByFacility()): ?>
                <td id="facility-name-<?=$item->getConfirmationCode()?>">
                    <?php if (! $item->isLate()): ?> <a class="edit-facility"><i class="pull-right fa fa-pencil"></i></a> <?php endif; ?>
                    <span class="facility-name"><?=$item->getFacilityName()?></span><br/>
                    <span class="unit-number-span">
                    <?php if (! $item->isLate()): ?>
                        <?php if($item->getBookingUnitNumber()): ?>
                            Unit #: <span class="unit-number"><?=$item->getBookingUnitNumber()?> </span>
                        <?php else: ?>
                            <a class="edit-facility">Add unit number</a>
                        <?php endif; ?>
                    <?php endif; ?>
                    </span>
                </td>
            <?php endif; ?>
            <td>
                <span id="customer-<?=$item->getConfirmationCode()?>">
                <?php if (! $item->isLate()): ?>
                    <a class="edit-customer-name"><i class="pull-right fa fa-pencil"></i></a>
                <?php endif; ?>

                    <?=nl2br($item->stringCustomerInfo())?>
                </span>
                <?php if ($item->isAutoConfirmed()): ?>
                    <div id="tenant-<?=$item->getConfirmationCode()?>" class="is-hidden">
                        <?=nl2br($item->stringTenantInfo())?>
                        <?=($item->getUnitNumber() ? '<br/>Unit '.$item->getUnitNumber() : '')?>
                    </div>
                <?php endif; ?>

                <?php if ($item->hasDuplicate()): ?>
                    <br/><a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant."><h6>Multiple Reservations</h6></a>
                <?php endif;
                if ($item->isOffline()): ?>
                    <br/><a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
                <?php endif;
                if ($item->getSupportNotes()): ?>
                    <br/><a class="notes" data-original-title="Support Notes" data-html="<?=$item->getSupportNotes()?>" data-position="right center"><strong>NOTES</strong></a>
                <?php endif; ?>
            </td>
            <td id="date-<?=$item->getConfirmationCode()?>">
                <?php if (! $item->isLate()): ?>
                    <a class="edit-move-in-date"><i class="pull-right fa fa-pencil"></i></a>
                <?php endif; ?>
                    <?=$item->stringMoveInDate()?>

            </td>
            <td><?=$item->stringReservationDate()?></td>
            <td id="sparefootfee-<?=$item->getConfirmationCode()?>"><?=$item->stringSparefootFee()?></td>
        </tr>
    <?php } ?>

    <?php if ($newLateLtvItems) : ?>
        <h3 class="ui header">
            Late Move-Ins
            <div class="sub header">These customers had move-in dates during the last 10 days of <?=date ( 'F', strtotime ( '-1 month' . $this->clientStatement->getStatementStartDate() ) )?>. If any of these customers moved in late, change their move-in date below to improve your move-in rate and search ranking. If none of last month's customers moved in late, you don't need to do anything. We'll only bill you for customers you move to this month.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="late_reservations">
                <?php printColumnHeaders($this->clientStatement); ?>
                <tbody id="late">
                    <?php
                    $this->clientStatement->sortItems($newLateLtvItems);
                    foreach ($newLateLtvItems as $item) {
                        printClientItemRow($item, $this->clientStatement);
                    } ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php $reservationItems = RESIDUAL_AUTO_CONFIRM ? $this->clientStatement->getNonAutoConfirmedLtvItems() : $this->clientStatement->getNewLtvItems(); ?>
    <?php if ($reservationItems): ?>
        <h3 class="ui header">
            Needs Your Review
            <div class="sub header">For the following reservations, please edit the customer details if needed, and let us know how much rent you collected in <?=date('F Y',strtotime($this->clientStatement->getStatementStartDate()))?>. If the reservation moved in under a different name or at another one of your facilities, update it to be consistent with your records.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="new_reservations">
                <?php printColumnHeaders($this->clientStatement); ?>
                <tbody id="pending">
                    <?php
                    $this->clientStatement->sortItems($reservationItems);
                    foreach ($reservationItems as $item) {
                        printClientItemRow($item, $this->clientStatement);
                    } ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if (RESIDUAL_AUTO_CONFIRM): ?>
        <?php if ($this->clientStatement->getAutoConfirmedLtvItems()): ?>
            <h3 class="ui header">
                Auto-Matched
                <div class="sub header">These customers were automatically matched to tenants in your management software.</div>
            </h3>
            <div class="table-responsive">
                <table class="ui table striped cell-headers sortable" id="auto_confirmed_reservations">
                    <?php printColumnHeaders($this->clientStatement); ?>
                    <tbody id="confirmed">
                        <?php foreach ($this->clientStatement->getAutoConfirmedLtvItems() as $item) {
                            printClientItemRow($item, $this->clientStatement);
                        } ?>
                    </tbody>
                </table>
            </div>
        <?php endif ?>

        <?php if ($this->clientStatement->getAutoDisputedLtvItems()): ?>
            <h3 class="ui header">
                Unmatched
                <div class="sub header">We weren't able to match these customers to tenants in your management software. To save you time, we marked these customers as "did not move in".</div>
            </h3>
            <div class="table-responsive">
                <table class="ui table striped cell-headers sortable" id="auto_disputed_reservations">
                    <?php printColumnHeaders($this->clientStatement); ?>
                    <tbody id="disputed">
                        <?php
                        foreach ($this->clientStatement->getAutoDisputedLtvItems() as $item) {
                            printClientItemRow($item, $this->clientStatement);
                        } ?>
                    </tbody>
                </table>
            </div>
        <?php endif ?>
    <?php endif ?>

    <?php if ($numExistingLtvItems > 0): ?>
        <h3 class="ui header">
            Tenants
            <div class="sub header">You've previously received rent from each of these AdNetwork tenants. Let us know how much you collected from them during <?=date('F Y',strtotime($this->clientStatement->getStatementStartDate()))?>.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="existing_tenants">
                <thead>
                    <tr>
                        <th data-sort="string" class="residual-status-header">Status</th>
                        <?php if (! $this->clientStatement->isFilteredByFacility()) { ?><th data-sort="string" class="facility-header">Facility</th><?php } ?>
                        <th data-sort="string" class="customer-header">Tenant</th>
                        <th data-sort="string" class="sparefoot-fee-header"><a class="ui popup-text" data-content="<?=$this->clientStatement->stringAcctResidualPercent()?> of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
                        <th data-sort="string" class="total-rent-header">Total Rent Collected</th>
                    </tr>
                </thead>
                <tbody id="tenants">
                <?php
                $filtered = $this->clientStatement->isFilteredByFacility() ? true : false;

                foreach ($this->clientStatement->getExistingLtvItems() as $item) {
                    $wasDelinquent = (method_exists($item, 'wasDelinquentLastStatement') && $item->wasDelinquentLastStatement()) ? 1 : 0; ?>

                    <tr id="<?=$item->getConfirmationCode()?>">
                        <td id="action-<?=$item->getConfirmationCode()?>">
                            <div class="statement-reservation-actions-residual">
                                <p>This customer paid</p>
                                <h4><span class="unit-price">
                                    <?=($item->getAmountCollected() > 0) ? $item->stringAmountCollected() : '$0'?>
                                </span></h4>
                                <p>for rent in <?=date('F Y',strtotime($this->clientStatement->getStatementStartDate()))?></p>
                                <div id="status-<?=$item->getConfirmationCode()?>">
                                    <?=($wasDelinquent ? '<h6>Previously Delinquent</h6>' : '')?>
                                </div>
                            </div>
                            <div class="statement-customer-paid-edit">
                                <a class="edit-tenant-rent-collected ui button secondary default">Edit</a>
                            </div>

                        </td>
                        <?php if(!$filtered): ?>
                            <td id="facility-name-<?=$item->getConfirmationCode()?>"><?=$item->getFacilityName()?><br/>
                            <span class="unit-number-span">
                            <?php if($item->getBookingUnitNumber() != '' AND $item->getBookingUnitNumber() !== NULL) { ?>
                                Unit #: <span class="unit-number"><?=$item->getBookingUnitNumber()?> </span><a style="padding-left:15px" class="edit-unit-number"><i class="fa fa-pencil"></i></a>
                            <?php } else { ?>
                                <a class="edit-unit-number">Add unit number</a>
                            <?php } ?>
                            </span>
                            </td>
                        <?php endif; ?>
                        <td>
                            <span id="customer-info-<?=$item->getConfirmationCode()?>"><?=nl2br($item->stringCustomerInfo())?></span>
                            <span id="tenant-<?=$item->getConfirmationCode()?>" class="is-hidden"><?=nl2br($item->stringTenantInfo())?></span>
                            <?=($item->getSupportNotes() ? '<br/><a class="notes" data-original-title="Support Notes" data-html="'.$item->getSupportNotes().'" data-position="right center"><strong>NOTES</strong></a>' : '')?>
                        </td>

                        <td id="sparefootfee-<?=$item->getConfirmationCode()?>"><?=$item->stringSparefootFee()?></td>
                        <td id="lifetime-<?=$item->getConfirmationCode()?>">$<?=Genesis_Service_BillableInstance::getAmountCollectedSumByConfCode($item->getConfirmationCode());?></td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?=$this->partial('done-form.phtml',
        ['loggedUser'=>$this->loggedUser,
            'confirmedTime'=>$this->confirmedTime,
            'confirmations'=>$this->confirmations,
            'allowChanges'=>$this->allowChanges
        ]);?>

    <input type="hidden" id="residual_percent" name="residual_percent" value="<?=$this->clientStatement->getAcctResidualPercent()?>" />

    <form id="edit-rent-collected-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title" id="edit-rent-collected-title">Edit</h4>
                </div>
                <div class="modal-body" style="min-height:300px;">
                    <div id="edit-rent-step-1" style="text-align:center;padding-top:10%;">
                        <h4>Did the customer move in?</h4>
                        <div style="width:30%;margin:0 auto;padding-top:20px;">
                            <a class="ui button default pull-left" id="customer-move-in-deny" >No</a>
                            <a class="ui button primary pull-right" id="customer-move-in-confirm">Yes</a>

                        </div>
                    </div>
                    <div id="edit-rent-step-2-A" style="display:none">
                        <input type="hidden" id="early_late" name="early_late" value="0" />
                        <input type="hidden" id="into_date" name="into_date" value="<?=$this->clientStatement->getStatementStartDate();?>" />
                        <p>How much rent did you collect?</p>
                        <div class="indent-left">
                            Enter new amount:
                            <div class="input-group">
                                <span class="input-group-addon">$</span><input type="text" id="rent-other" name="rent_other" value="" class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="id" value="<?=$this->clientStatement->getStatementId()?>" />
                    <input type="hidden" name="residual_percent" value="<?=$this->clientStatement->getAcctResidualPercent()?>" />

                    <a class="ui button default pull-left"  id="edit-rent-cancel" style="display:none" data-dismiss="modal" >Cancel</a>
                    <a class="ui button primary pull-right" id="edit-rent-submit" style="display:none">Submit</a>

                </div>

            </div>
        </div>
    </form>

    <form id="edit-facility-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-facility-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Facility</h4>
                </div>
                <div class="modal-body">
                    <p>Facility</p>
                    <select id="sister-facility-select" name="facility_id" class="form-control">
                        <?php foreach ($this->clientStatement->getSisterFacilityList() as $facility): ?>
                            <?php if($facility->getApproved() && $facility->getPublished() && $facility->getBillableEntityId()):?>
                                <option value="<?=$facility->getId()?>"><?=$facility->getTitleWithCompanyCode()?></option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                    <p style="margin-top:30px">Unit Number (optional)</p>
                    <input type="text" id="unit-number" name="unit_number" value="" class="form-control" />

                </div>
                <div class="modal-footer">
                    <a id="edit-facility-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" id="sister-facility-select-submit"/>
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="<?=$this->clientStatement->getStatementId()?>" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-unit-number-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-unit-number-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Unit Number</h4>
                </div>
                <div class="modal-body">
                    <p>Unit Number</p>
                    <input type="text" name="unit_number" value="" class="form-control unit-number" id="edit-unit-number-value" />

                </div>
                <div class="modal-footer">
                    <a id="edit-unit-number-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" id="edit-unit-number-submit" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="<?=$this->clientStatement->getStatementId()?>" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-customer-name-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-customer-name-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Customer Name</h4>
                </div>
                <div class="modal-body">
                    <p>Please provide us with accurate contact information to appear on your bill.</p>
                    <p><input type="text" id="change-first-name" name="first_name" value="" placeholder="First Name" class="form-control" /></p>
                    <p><input type="text" id="change-last-name" name="last_name" value="" placeholder="Last Name" class="form-control" /></p>

                </div>
                <div class="modal-footer">
                    <a id="edit-customer-name-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="<?=$this->clientStatement->getStatementId()?>" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-tenant-rent-collected-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title" id="edit-tenant-rent-collected-title">Edit</h4>
                </div>
                <div class="modal-body" style="min-height:300px;">
                    <div id="edit-tenant-rent-step-1" style="text-align:center;padding-top:10%;">
                        <h4>Did the Customer pay you rent in <?=date('F \of Y',strtotime($this->clientStatement->getStatementStartDate()));?>?</h4>
                        <div style="width:30%;margin:0 auto;padding-top:20px;">
                            <a class="ui button default pull-left" id="tenant-rent-collected-deny" >No</a>
                            <a class="ui button primary pull-right" id="tenant-rent-collected-confirm">Yes</a>
                        </div>
                    </div>
                    <div id="edit-tenant-rent-step-2-A" style="display:none">
                        <p>Please enter how much rent the customer paid you during <?=date('F Y',strtotime($this->clientStatement->getStatementStartDate()));?></p>
                        <div class="indent-left">
                            Enter new amount:
                            <div class="input-group">
                                <span class="input-group-addon">$</span><input type="text" id="rent-tenant-other" name="rent_other" value="" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div id="edit-tenant-rent-step-2-B" style="display:none">
                        This customer
                        <div class="radio controls only-existing-tenants">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_PREPAID?>" id="tenant-rent-zero" /> customer has previously pre-paid</label>
                        </div>
                        <div class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_DELIQUENCY?>" id="tenant-rent-deliquent" /> is delinquent on their rent payments</label>
                        </div>
                        <div class="radio controls only-existing-tenants">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_MOVED_OUT?>" id="tenant-rent-moved-out" /> moved out (this means the customer last paid you rent in <?=date('F Y',strtotime($this->clientStatement->getStatementStartDate() . ' -1 month'))?>)</label>
                        </div>
                        <div id="lien-sale-option" class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_MOVED_OUT?>" id="tenant-rent-lien"/> lien sale of delinquent unit</label>
                        </div>
                        <div id="lien-sale-option" class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="<?=Genesis_Entity_BillableInstance::REASON_OTHER?>" id="tenant-rent-other"/> other</label>
                        </div>

                    </div>

                </div>

                <div class="modal-footer">
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="id"  value="<?=$this->clientStatement->getStatementId()?>" />
                    <input type="hidden" name="residual_percent" value="<?=$this->clientStatement->getAcctResidualPercent()?>" />
                    <input type="hidden" name="is_tenant" value="1"/>
                    <a class="ui button default pull left"  id="edit-tenant-rent-cancel" style="display:none" data-dismiss="modal" >Cancel</a>
                    <a class="ui button primary pull-right" id="edit-tenant-rent-submit" style="display:none">Submit</a>

                </div>
            </div>
        </div>
    </form>

    <form id="edit-move-in-date-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-move-in-date-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Move-In Date</h4>
                </div>
                <div class="modal-body">
                    <p>Please enter the customer's new move-in date (pick any date this month if you don't know it):</p>
                    <input type="text" id="edit-move-in-date" name="into_date" value="" placeholder="YYYY-MM-DD" readonly="readonly" class="form-control datepicker-field" />
                </div>
                <div class="modal-footer">
                    <a id="edit-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui primary button" value="Save" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="<?=$this->clientStatement->getStatementId()?>" />
                </div>
            </div>
        </div>
    </form>
</div>
