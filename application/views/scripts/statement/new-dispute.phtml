<script>
    App.context = {
        facility_id: <?= ($this->facilityId) ? $this->facilityId : 'null' ?>,
        statement_id: <?= $this->statementId ?>
    };
</script>
<script type="text/javascript">
    var statementId = <?= $this->statementId ?>;
    <?php $startDate = strtotime($this->statementStartDate); ?>
    var startDate = new Date(<?= 1000 * date('U', $startDate) ?>); /*<?= date('r', $startDate); ?>*/
    <?php $endDate = strtotime($this->statementEndDate); ?>
    var endDate = new Date(<?= 1000 * date('U', $endDate) ?>); /*<?= date('r', $endDate); ?>*/
    <?php $nextStatementStartDate = strtotime("+1 days " . $this->statementEndDate); ?>
    var nextStatementStartDate = new Date(<?= 1000 * date('U', $nextStatementStartDate) ?>); /*<?= date('r', $nextStatementStartDate); ?>*/
    <?php $nextStatementEndDate = strtotime("+61 days " . $this->statementEndDate); ?>
    var nextStatementEndDate = new Date(<?= 1000 * date('U', $nextStatementEndDate) ?>); /*<?= date('r', $nextStatementEndDate); ?>*/
    var defaultDate = '<?= date("m-d-Y", strtotime($this->statementStartDate)) ?>';
    var statementType = '<?= $this->statementType ?>';
</script>
<div class="view-cpa">

    <?php // modal guide
    if (count($this->arr_softwares) > 0 && array_intersect([0 => 21, 1 => 23], $this->arr_softwares)): ?>
        <div id="guides-modal" class="modal fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">Do you HATE reconciling your statement?</h4>
                    </div>
                    <div class="modal-body">
                        <div class="modal-guides">
                            <p>Yeah, we know...</p>
                            <p>So we figured out the best way to use your management software for a more efficient reconciliation. Simply view the guide below and follow the steps. You'll enjoy more move-ins, which means higher rankings in our search results, which means EVEN MORE move-ins. Pretty cool cycle, huh?<br /><br /></p>

                            <?php if ($this->arr_softwares[21]) { ?>
                                <p><a class="ui button primary" href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">View Guide</a> <a href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">Reconciling with SiteLink Stand Alone</a></p>
                            <?php } ?>
                            <?php if ($this->arr_softwares[23]) { ?>
                                <p><a class="ui button primary" href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">View Guide</a> <a href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">Reconciling with WebSelfStorage</a></p>
                            <?php } ?>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="#" data-dismiss="modal" class="ui button">Close</a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif ?>

    <h1 class="ui header" id="statement-title">
        <?php if (!$this->facility): ?>
            <?= date('F Y', strtotime($this->statementStartDate)) ?>
        <?php else: ?>
            <?= $this->facility->getTitle() ?> - <?= date('F', strtotime($this->statementStartDate)) ?> <?= date('Y', strtotime($this->statementStartDate)) ?>
        <?php endif ?>
    </h1>

    <?php if (isset($_SERVER['HTTP_USER_AGENT']) && (strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)): ?>
        <div class="ui negative message">
            <i class="close icon"></i>
            <div class="header">Make sure your popup blocker is turned off.</div>
        </div>
    <?php endif ?>

    <?php if (Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_SHOW_WSS_UPLOADER, ['account_id' => $this->loggedUser->getAccountId()])): ?>
        <?php
        $hasManualCpaFacility = false;
        foreach ($this->facilities as $facility) {
            if (
                $facility->getPublished()
                && $facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_MANUAL
                && $facility->getCorporation()->getAccount()->getCpa()
            ) {
                $hasManualCpaFacility = true;
                break;
            }
        }
        ?>
        <?php if ($hasManualCpaFacility): ?>
            <div class="ui warning message">
                <i class="close icon"></i>
                <div class="header">Save time! We'll reconcile your statement for you.</div>
                <p>
                    <strong>Do you use WebSelfStorage? </strong>
                    <a href="<?= $this->url(['action' => 'upload-mil', 'id' => $this->statementId], 'statement') ?>">Upload your move-in list here</a> for automatic reconciliation.
                </p>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php
    $hasItems = $this->regularBookings ||
        $this->autoConfirmedBookings ||
        $this->lateBookings ||
        $this->freeBookings;
    ?>
    <?= $this->partial('new-facility-selector.phtml', [
        'facilityCount' => $this->facilityCount,
        'facilityId' => $this->facilityId,
        'facilities' => $this->facilities,
        'statementId' => $this->statementId,
        'hasItems' => $hasItems
    ]); ?>

    <div id="js-message-box"></div>

    <?php if ($this->autoConfirmedBookings): ?>
        <div class="ui message">
            <i class="close icon"></i>
            <div class="header"><a href="http://blog.sparefoot.com/sparefoot-reconciliation/" target="_blank">Read more</a> about your new, automated SpareFoot statement.</div>
        </div>
    <?php endif ?>

    <?php if ($this->isMIRFElegible && $this->regularBookings): ?>
        <h3 class="ui header">
            Estimated Minimum Move-In Rate Fee
            <?php if ($this->mirfPercentage === (Genesis_Util_NewMirfCalculation::MIR_FLOOR * 100)) : ?>
                <div class="sub header">Non-integrated facilities will be charged an additional fee if their move-in rate is less than 50%. The estimated Minimum Move-In Rate Fee is shown below. <a href="https://support.sparefoot.com/hc/en-us/articles/115015444207#MinimumMIR" target="_blank">Learn more</a></div>
            <?php endif; ?>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="mirf-table">
                <thead>
                    <tr>
                        <th>Facility</th>
                        <th>Move-Ins</th>
                        <th><a class="ui popup-text" data-html='SpareFoot only counts one reservation per<br/>customer when calculating move-in rates<br/>for a given statement period, even if that<br/>customer has made multiple reservations.'>Unique Reservations <i class="fa fa-info-circle"></i></a></th>
                        <th>Move-In Rate</th>
                        <th>Estimated Move-In Rate Floor Charge</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->MIRFData['facilities'] as $facilityData): ?>
                        <?php if (($this->facilityId && $this->facilityId == $facilityData['facility_id']) || !$this->facilityId): ?>
                            <tr data-id="<?= $facilityData['facility_id'] ?>">
                                <td>
                                    <a href="<?= $this->url(['action' => 'view', 'id' => $this->statementId], 'statement') ?>?facility=<?= $facilityData['facility_id'] ?>">
                                        <?= htmlspecialchars($facilityData['facility_name']) ?>
                                    </a>
                                </td>
                                <td><?= $facilityData['total_pending'] ?></td>
                                <td><?= $facilityData['total_bookings'] ?></td>
                                <td><?= $facilityData['reservation_rate'] . " %" ?></td>
                                <td data-sort-value="<?= $facilityData['mirf'] ?>">
                                    <span>
                                        <?= "$" . number_format($facilityData['mirf'], 2) ?>
                                    </span>
                                    <span id="loading-spinner" style="display:none">
                                        <img id="loading-spinner" src="/images/loading.gif" width="15" height="15" />
                                        Loading Charge...
                                    </span>
                                </td>
                            </tr>
                        <?php endif ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($this->regularBookings): ?>
        <h3 class="ui header">
            Needs Your Review
            <div class="sub header">Please select the "X" next to each customer who did not move in.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="all-statements" data-type="pending">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        <?php if (!$this->facility): ?>
                            <th>Facility</th>
                        <?php endif; ?>
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->regularBookings as $clientItem): ?>
                        <?= $this->partial('new-view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'statementId' => $this->statementId,
                            'bookingExtra' => $this->bookingExtraInfo[$clientItem->getConfirmationCode()],
                            'isLate' => false,
                            'facility' => $this->facility
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($this->autoConfirmedBookings): ?>
        <h3 class="ui header">
            Auto-Matched
            <div class="sub header">These customers were automatically matched to tenants in your management software.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="auto-statements" data-type="confirmed">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        <?php if (!$this->facility): ?>
                            <th>Facility</th>
                        <?php endif; ?>
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->autoConfirmedBookings as $clientItem): ?>
                        <?= $this->partial('new-view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'statementId' => $this->statementId,
                            'bookingExtra' => $this->bookingExtraInfo[$clientItem->getConfirmationCode()],
                            'isLate' => false,
                            'facility' => $this->facility
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($this->autoDisputedBookings): ?>
        <h3 class="ui header">
            Unmatched
            <div class="sub header">We weren't able to match these customers to tenants in your management software. To save you time, we marked these customers as "did not move in".</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="auto-disputed" data-type="disputed">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        <?php if (!$this->facility): ?>
                            <th>Facility</th>
                        <?php endif; ?>
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->autoDisputedBookings as $clientItem): ?>
                        <?= $this->partial('new-view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'statementId' => $this->statementId,
                            'bookingExtra' => $this->bookingExtraInfo[$clientItem->getConfirmationCode()],
                            'isLate' => false,
                            'facility' => $this->facility
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if (count($this->lateBookings)): ?>
        <h3 class="ui header">
            Late Move-Ins
            <div class="sub header">These customers had move-in dates during the last 10 days of <?= date('F', strtotime('-1 month' . $this->statementStartDate)) ?>. If any of these customers moved in late, change their move-in date below to improve your move-in rate and search ranking. If none of last month's customers moved in late, you don’t need to do anything. We'll only bill you for customers you move to this month.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="late-move-ins" data-type="late">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        <?php if (!$this->facility): ?>
                            <th>Facility</th>
                        <?php endif; ?>
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->lateBookings as $clientItem): ?>
                        <?= $this->partial('new-view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'statementId' => $this->statementId,
                            'bookingExtra' => $this->bookingExtraInfo[$clientItem->getConfirmationCode()],
                            'isLate' => true,
                            'facility' => $this->facility
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($this->reviewedBookings): ?>
        <h3 class="ui header">
            Previously Disputed Move-ins
            <div class="sub header">
                Our support team has reviewed your reservations disputed from the previous month.
                The disputed reservations that have been over ruled will show up on this months billing cycle.
            </div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="reviewed-statements" data-type="reviewed">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th>Reason for ruling</th>
                        <?php if (!$this->facility): ?>
                            <th>Facility</th>
                        <?php endif; ?>
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->reviewedBookings as $clientItem): ?>
                        <?= $this->partial('new-view-disputed-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'isEarly' => false,
                            'isLate' => false,
                            'isFilteredByFacility' => (bool) $this->facility,
                            'bookingExtra' => $this->bookingExtraInfo[$clientItem->getConfirmationCode()],
                            'statementId' => $this->statementId,
                        ]) ?>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    <?php endif ?>

    <?php if ($this->freeBookings): ?>
        <h3 class="ui header">
            No-Fee Reservations
            <div class="sub header">We sent you these reservations from SpareFoot products without transaction fees (GeoPages, SiteBuilder, and Booking Widgets).</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="no-fee-reservations" data-type="free">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        <?php if (!$this->facility): ?>
                            <th>Facility</th>
                        <?php endif; ?>
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->freeBookings as $clientItem): ?>
                        <?= $this->partial('new-view-cpa-item.phtml', [
                            'clientItem' => $clientItem,
                            'facility' => $this->facility
                        ]) ?>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>

    <?php if (!$this->isHybrid) : ?>
        <?= $this->partial(
            'done-form.phtml',
            [
                'loggedUser' => $this->loggedUser,
                'confirmedTime' => $this->confirmedTime,
                'confirmations' => $this->confirmations,
                'allowChanges' => $this->allowChanges
            ]
        ); ?>
    <?php endif; ?>

    <form id="change-move-in-date-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="change-move-in-date-modal-close" class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title">Change move-in date</h4>
                </div>
                <div class="modal-body">
                    <p id="change-move-in-date-customer-info"></p>
                    <label for="into-date">Please enter the customer's (approximate) new move-in date:</label>
                    <input type="text" id="into-date" name="into_date" value="" placeholder="" readonly="readonly" class="form-control datepicker-field" />
                </div>
                <div class="modal-footer">
                    <a id="change-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                    <input type="hidden" name="confirmation_code" />
                    <input type="submit" class="ui primary button" value="Save" id="change-move-in-date-submit" />
                </div>
            </div>
        </div>
    </form>

    <?= $this->partial('new-dispute-modal.phtml', ['sisterFacilityList' => $this->sisterFacilityList]); ?>
    <?= $this->partial('auto-dispute-modal.phtml'); ?>
</div>