<?php
    $billableInstance = $this->clientItem->getLatestBillableInstance();
    if (!$billableInstance) {
        echo "<h1>" . $this->clientItem->getConfirmationCode() . "<h1>";
    }
    $reviewRuling = $this->clientItem->getReviewRuling();
    if ($reviewRuling) {
        $wasDenied = $reviewRuling->getValue() === Genesis_Entity_BookingMeta::REVIEW_RULING_DENY;
    } else {
        $wasDenied = false;
    }
    $rulingReason = $this->clientItem->getReviewRulingReason();
?>

<tr class="cpa-item" data-id="<?=$this->clientItem->getConfirmationCode()?>">
    <?php if (! $this->clientItem->getFree() == 1): ?>
    <td id="dispute-action-<?=$this->clientItem->getConfirmationCode()?>" class="statement-actions dispute-icon">
        <?php if ($wasDenied) { ?>
            <i class="remove icon"></i>
        <?php } else { ?>
            <i class="checkmark icon"></i>
        <?php } ?>
    </td>

    <td id="dispute-status-<?=$this->clientItem->getConfirmationCode()?>" class="status-cell">
        <?php if ($wasDenied) { ?>
            <span class="dispute-status">Dispute Denied</span>
        <?php } else { ?>
            <span class="dispute-status">Dispute Approved</span>
        <?php } ?>

        <?php if ($this->clientItem->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) { ?>
            <span class="error<?=!$wasDenied?'':' is-hidden'?>">Did Not Move In <?=$this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED ? ' (Reviewed)' : ''?></span>
            <span class="success<?=!$wasDenied?' is-hidden':''?>">Moved In <?=$this->clientItem->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED ? ' (Reviewed)' : ''?></span>
        <?php } elseif ($this->clientItem->isEarly()) { ?>
            <span>Move In Next Month</span>
            <span class="success is-hidden">Moved In</span>
        <?php } elseif ($this->clientItem->isLate()) { ?>
            <span class="error">Did Not Move In</span><img id="activity-indicator-<?=$this->clientItem->getConfirmationCode()?>" class="is-hidden" src="/images/loading.gif" alt="loading"/>
            <span class="success is-hidden">Moved In</span>
        <?php } else { ?>
            <span class="error <?=($this->clientItem->isDisputed() ? '' : 'is-hidden')?>">Did Not Move In</span>
            <span class="success <?=($this->clientItem->isDisputed() ? 'is-hidden' : '')?>">Moved In</span>
        <?php } ?>

        <?php // Support notes
        if ($this->clientItem->getSupportNotes()): ?>
            <br /><a class="ui popup-text" data-title="Support Notes" data-content="<?=$this->clientItem->getSupportNotes()?>"><strong>NOTES</strong></a>
        <?php endif ?>
    </td>

    <?php if ($rulingReason) { ?>
        <td id="ruling-reason-<?=$this->clientItem->getConfirmationCode()?>">
            <?=$rulingReason->getValue()?>
        </td>
    <?php } else { ?>
            <td id="ruling-reason-<?=$this->clientItem->getConfirmationCode()?>">
                <span>N/A</span>
            </td>
    <?php } ?>
    <?php endif ?>

    <?php if (! $this->isFilteredByFacility): ?>
        <td id="facility-name-<?=$this->clientItem->getConfirmationCode()?>"><a href="<?=$this->url(['action' => 'view', 'id'=> $this->statementId], 'statement')?>?facility=<?=$this->clientItem->getFacility()->getId()?>"><?=htmlspecialchars($this->clientItem->getFacility()->getTitleWithCompanyCode())?></a></td>
    <?php endif ?>

    <td id="customer-<?=$this->clientItem->getConfirmationCode()?>">
        <span id="customer-info-<?=$this->clientItem->getConfirmationCode()?>">
            <?=nl2br(htmlspecialchars($this->bookingExtra->customerInfo))?>
        </span>
        <?=($this->clientItem->getUnitNumber() ? '<br/>Unit '.$this->clientItem->getUnitNumber() : '')?>
        <?php if ($this->clientItem->getBookingType() === Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE): ?>
            <a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
        <?php endif; ?>

    <?php if ($this->bookingExtra->hasDuplicates): ?>
            <a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant.">Multiple Reservations</a>
        <?php endif; ?>
        <?php if ($this->clientItem->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED): ?>
            <div id="tenant-<?=$this->clientItem->getConfirmationCode()?>" class="is-hidden">
                <?=nl2br($this->bookingExtra->tenantInfo)?>
                <?=($this->clientItem->getUnitNumber() ? '<br/>Unit '.$this->clientItem->getUnitNumber() : '')?>
            </div>
        <?php endif; ?>
    </td>
    <td id="date-<?=$this->clientItem->getConfirmationCode()?>"><?= date("m-d-Y", strtotime($this->clientItem->getMoveIn())) ?></td>
    <td id="reservation-<?=$this->clientItem->getConfirmationCode()?>"><?=date("m-d-Y", strtotime($this->clientItem->getTimestamp()))?></td>
    <td id="baseBid-<?=$this->clientItem->getConfirmationCode()?>"><?=$this->clientItem->stringBaseBid()?></td>
    <td id="amount-<?=$this->clientItem->getConfirmationCode()?>"><?= (!$billableInstance) ? "$0.00" : ("$" .number_format($billableInstance->getSparefootCharge(), 2, '.', ',')) ?></td>
    <td id="lifetime-<?=$this->clientItem->getConfirmationCode()?>">
        <a href="#" class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of <?= "$" . $billableInstance->getUnitPrice()?>.">
        <?= "$".number_format(($billableInstance->getUnitPrice() * 12), 2, '.', ',')?>
        </a>
        <br />
        <a href="#" class="ui popup-text" data-content="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant.">
            <?php
                if ($this->clientItem->getBidAmount() > 0) {
                    $lifetimeValueROI = (($billableInstance->getUnitPrice() * 12)/ $this->clientItem->getBidAmount()) * 100;
                    echo number_format($lifetimeValueROI, 0, '.', ',') . '%';
                }
            ?>
            <abbr data-content="Return on Investment">ROI</abbr>
        </a>
    </td>
</tr>
