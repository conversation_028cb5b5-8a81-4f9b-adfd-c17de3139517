<script type="text/javascript">
    var myFootLink = '<?=$this->myFootLink?>';
</script>

<div class="content-row">
    <h2>Select the storage management software you use.</h2>
    <p>We'll use this to customize your MySpareFoot experience. Select all that apply.</p>
</div>

<form id="signup-integration-form" action="/signup-end/record-software" method="post">

    <div class="input-row">
    
        <div class="alert alert-danger hide"></div>
    
        <div class="row">
        
            <div class="col-md-4">

                <div class="form-group">
                    <div class="checkbox">
                        <label for="sl">
                            <input type="checkbox" id="sl" name="integration_type[]" value="<?=Genesis_Entity_Source::ID_SITELINK?>" /><?=Genesis_Service_Source::loadById(Genesis_Entity_Source::ID_SITELINK)->buildLogoTag();?><br />Web edition
                        </label>
                    </div>
                </div>
                
            </div>
            <div class="col-md-4">
            
                <div class="form-group">
                    <div class="checkbox">
                        <label for="cs4">
                            <input type="checkbox" id="cs4" name="integration_type[]" value="<?=Genesis_Entity_Source::ID_CENTERSHIFT4?>" /><?=Genesis_Service_Source::loadById(Genesis_Entity_Source::ID_CENTERSHIFT4)->buildLogoTag();?> 4.0
                        </label>
                    </div>
                </div>
                
            </div>
            <div class="col-md-4">
            
                <div class="form-group">
                    <div class="checkbox">
                        <label for="qs">
                            <input type="checkbox" id="qs" name="integration_type[]" value="<?=Genesis_Entity_Source::ID_QUIKSTOR?>" /><?=Genesis_Service_Source::loadById(Genesis_Entity_Source::ID_QUIKSTOR)->buildLogoTag();?>
                        </label>
                    </div>
                </div>
            
                <div class="form-group">
                    <div class="checkbox">
                        <label for="ssm">
                            <input type="checkbox" id="ssm" name="integration_type[]" value="<?=Genesis_Entity_Source::ID_SELFSTORAGEMANAGER?>" /><?=Genesis_Service_Source::loadById(Genesis_Entity_Source::ID_SELFSTORAGEMANAGER)->buildLogoTag();?>
                        </label>
                    </div>
                </div>
            </div>

        </div>
        
        <div class="row" id="manual-software-options">
            <div class="col-md-4">
                
        	   <div class="form-group">
                    <div class="checkbox">
                        <label for="othersoft">
                            <input type="checkbox" id="othersoft" name="integration_type[]" value="<?=Genesis_Entity_Source::ID_OTHER?>" />Other software
                        </label>
                    </div>
                </div>
        
                <div class="form-group">
                    <div class="checkbox">
                        <label for="nosoftware">
                            <input type="checkbox" id="nosoftware" name="integration_type[]" value="<?=Genesis_Entity_Source::ID_MANUAL?>" />I don't use software
                        </label>
                    </div>
                </div>
                
        	</div>		
        </div>
    </div>
    
    <div class="input-row hide" id="man-first-facility">
        <div class="form-horizontal">
        
            <h4>Let's set up your first facility.</h4>
        
            <div class="checkbox">
                <label for="address-same">
                    <input type="checkbox" name="address_same" id="address-same" />
                    My facility address is the same as my company address.
                </label>
            </div><br />
            
            <div class="form-group">
                <label for="address" class="col-lg-2 control-label">Facility Name</label>
                <div class="col-lg-10">
                    <input id="facility-name" name="facility_name" type="text" class="form-control" value="<?=$this->facilityName?>"/>
                </div>
            </div>
    
            <div class="form-group">
                <label for="address" class="col-lg-2 control-label">Address</label>
                <div class="col-lg-10">
                    <input id="address" name="address" type="text" class="form-control" value="<?=$this->address?>"/>
                </div>
            </div>
        
            <div class="form-group">
                <label for="city" class="col-lg-2 control-label">City</label>
                <div class="col-lg-10">
                    <input id="city" name="city" type="text" class="form-control" value="<?=$this->city?>"/>
                </div>
            </div>
        
            <div class="form-group">
                <label for="state" class="col-lg-2 control-label">State</label>
                <div class="col-lg-10">
                    <select id="state" name="state" class="form-control">
                        <option value=""></option>
                        <option value="AL">AL</option>
                        <option value="AK">AK</option>
                        <option value="AZ">AZ</option>
                        <option value="AR">AR</option>
                        <option value="CA">CA</option>
                
                        <option value="CO">CO</option>
                        <option value="CT">CT</option>
                        <option value="DE">DE</option>
                        <option value="DC">DC</option>
                        <option value="FL">FL</option>
                        <option value="GA">GA</option>
                
                        <option value="HI">HI</option>
                        <option value="ID">ID</option>
                        <option value="IL">IL</option>
                        <option value="IN">IN</option>
                        <option value="IA">IA</option>
                        <option value="KS">KS</option>
                
                        <option value="KY">KY</option>
                        <option value="LA">LA</option>
                        <option value="ME">ME</option>
                        <option value="MD">MD</option>
                        <option value="MA">MA</option>
                        <option value="MI">MI</option>
                
                        <option value="MN">MN</option>
                        <option value="MS">MS</option>
                        <option value="MO">MO</option>
                        <option value="MT">MT</option>
                        <option value="NE">NE</option>
                        <option value="NV">NV</option>
                
                        <option value="NH">NH</option>
                        <option value="NJ">NJ</option>
                        <option value="NM">NM</option>
                        <option value="NY">NY</option>
                        <option value="NC">NC</option>
                        <option value="ND">ND</option>
                
                        <option value="OH">OH</option>
                        <option value="OK">OK</option>
                        <option value="OR">OR</option>
                        <option value="PA">PA</option>
                        <option value="RI">RI</option>
                        <option value="SC">SC</option>
                
                        <option value="SD">SD</option>
                        <option value="TN">TN</option>
                        <option value="TX">TX</option>
                        <option value="UT">UT</option>
                        <option value="VT">VT</option>
                        <option value="VA">VA</option>
                
                        <option value="WA">WA</option>
                        <option value="WV">WV</option>
                        <option value="WI">WI</option>
                        <option value="WY">WY</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="zip" class="col-lg-2 control-label">Zip Code</label>
                <div class="col-lg-10">
                    <input id="zip" name="zip" type="text" class="form-control" value="<?=$this->zip?>"/>
                </div>
            </div>
            
            <div class="form-group">
                <label for="zip" class="col-lg-2 control-label">Phone Number</label>
                <div class="col-lg-10">
                    <input id="phone" name="phone" type="text" class="form-control" value="<?=$this->phone?>"/>
                </div>
            </div>
        </div>
    </div>

    
    <div class="content-footer">
        <div class="pull-right">
            <img src="/images/loaders/large.gif" class="loading hide" alt="loading" />&nbsp;&nbsp;
            <input class="btn btn-lg btn-primary" type="submit" id="submit" data-loading-text="Saving" value="Finish" />    
        </div>
        <a href="/signup-end/billing" class="btn btn-default btn-lg" id="back">Back</a>
    </div>

</form>