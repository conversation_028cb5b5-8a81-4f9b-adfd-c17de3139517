<?php if ($this->error) : ?>
    <p class="ui message negative"><?=$this->error?></p>
<?php endif; ?>

<div class="content-row">
    <h2>Terms For <?=$this->account->getName() ?></h2>
        <p>Please review the following steps to ensure you understand how you will be charged each month. Then, check the boxes below to agree to these terms.</p>
    <br />
</div>


<div class="row">
    <div class="col-md-4">
        <div class="media">
                <span class="pull-left" href="#">
                    <img src="/images/moneybox.png" class="media-object" />
                </span>
            <div class="media-body">
                <h5 class="media-heading">How SpareFoot gets you tenants</h5>
                <p>When a new customer reserves one of your units, you and your facility manager will instantly get an email and phone call that contains the customer's contact information.</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="media">
                <span class="pull-left" href="#">
                    <img src="/images/statements.png" class="media-object" />
                </span>
            <div class="media-body">
                <h5 class="media-heading">Log in and view statements</h5>
                <p>You will receive an email on the first business day of each month to let you know your statement is ready to view and reconcile in MySpareFoot.</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="media">
                <span class="pull-left" href="#">
                    <img src="/images/time.png" class="media-object" />
                </span>
            <div class="media-body">
            <?php if ($this->bidType == Genesis_Entity_Account::BID_TYPE_RESIDUAL): ?>
                <h5 class="media-heading">Verify reservations and rent collected</h5>
                <p>You have ten days to log into MySpareFoot and confirm the dollar amount of rent collected for each SpareFoot customer last month.</p>
            <?php else : ?>
                <h5 class="media-heading">Report reservation outcome </h5>
                <p>You have ten days to log into MySpareFoot, tell us which customers didn't end up moving in.</p>
            <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <form id="terms-form" action="/signup-end/terms/" method="post">
            <input type="hidden" id="csrf_token" name="csrf_token" value="<?=$this->csrf_token?>">
            <?php if($this->bidType !== Genesis_Entity_Account::BID_TYPE_PERCENT): ?>
                <div class="form-group form-group-checkbox">
                    <label for="agree1" class="checkbox">
                        <input type="checkbox" name="agree1" id="agree1" <?=($this->agree1) ? 'checked="checked"' : '' ?>/>
                        I agree to the payment terms
                        <?php if($this->bidType == Genesis_Entity_Account::BID_TYPE_RESIDUAL): ?>
                            of a rent percent of <?=$this->account->getResidualPercent() * 100?>% per move-in.
                        <?php elseif($this->bidType == Genesis_Entity_Account::BID_TYPE_FLAT || $this->bidType == Genesis_Entity_Account::BID_TYPE_TIERED) :?>
                            of a middle-tier, one-time move in fee of $<?=$this->account->getMinBid()?>  per move-in.
                        <?php elseif($this->bidType == Genesis_Entity_Account::BID_TYPE_VISITOR) :?>
                           of $<?=$this->account->getMinBid()?> per visitor to my listing page
                        <?php else :?>
                            previously discussed with SpareFoot.
                        <?php endif; ?>
                    </label>
                </div>
            <?php endif ?>

            <div class="form-group form-group-checkbox">
                <label for="agree2" class="checkbox">
                    <input type="checkbox" name="agree2" id="agree2" <?=($this->agree2) ? 'checked="checked"' : '' ?>/>
                    I agree that I will be charged for any tenants who are not marked as "Did Not Move In" by the end of the 10 day reconciliation period.
                </label>
            </div>

            <div class="form-group form-group-checkbox">
                <label for="agree3" class="checkbox">
                    <input type="checkbox" name="agree3" id="agree3" <?=($this->agree3) ? 'checked="checked"' : '' ?>/>
                    I agree to accurately report all reservation statuses when I reconcile each month. I will be responsible for honestly reporting which customers from SpareFoot move in.
                </label>
            </div>

            <div class="form-group form-group-checkbox">
                <label for="agree4" class="checkbox">
                    <input type="checkbox" name="agree4" id="agree4" <?=($this->agree4) ? 'checked="checked"' : '' ?>/>
                    I agree to the <a href="http://www.sparefoot.com/legal/client/<?=$this->termsVersion?>.html" target="_blank">Terms of Use</a>.
                </label>
            </div>


            <div class="content-footer clearfix">
                <div class="pull-right">
                    <img src="/images/loaders/large.gif" class="loading hide" alt="loading" />&nbsp;&nbsp;
                    <input type="submit" id="submit" class="btn btn-lg btn-primary" value="Next" data-loading-text="Saving" />
                </div>
                <a href="<?=$this->backlink?>" class="btn btn-default btn-lg" id="back">Back</a>
            </div>
        </form>
    </div>
</div>
