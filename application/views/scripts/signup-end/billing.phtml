<?php if ($this->completed == "true") { ?>
<div class="content-row">
    <h2>Billing Information <img src="/images/lock.png"></h2>
    <p>We already have your payment information.  If you'd like to change it, you can do so at any time by visiting the Payment Setup tab in your MySpareFoot account.</p>
    <br/>
</div>

<div class="content-footer">
    <a href="<?=$this->backlink?>" class="btn btn-default btn-lg">Back</a>
    <a href="/" id="submit" class="btn btn-primary btn-lg pull-right">Finish</a>
    <div class="clear"></div>
</div>

<?php } else {?>

<script type="text/javascript">
var myfootlink = '<?=$this->myfootlink?>';
<?php if ($this->billableEntityId > 0) { ?>
ccType = 'existing';
<?php } else { ?>
var ccType = '';
<?php } ?>
var numFacilities = <?=count($this->user->getManageableFacilityIds())?>;
</script>

<div class="content-row">
    <h2>Billing Information <img src="/images/lock.png"></h2>
    <p>Please provide your credit card information on file using the form below. We keep your information safe using the latest technology in online encryption.</p>
    <p>Need to setup different payment methods for different facilities? You can add multiple payment methods after you've completed the signup.</p>
</div>

<form id="billing-form" action="payment/update-customer" method="post">

    <input type="hidden" name="billable_entity_id" id="billable-entity-id" value="<?=$this->billableEntityId?>" />
    <input type="hidden" name="account_id" id="account-id" value="<?=$this->accountId?>" />
    <input type="hidden" name="payment_type" id="payment-type" value="Credit Card" />
    <input type="hidden" name="csrf_token" value="<?=$this->csrf_token?>">

    <div class="input-row">
    
        <div class="form-horizontal">
            <div class="form-group">
                <label for="payment-type-nickname" class="col-lg-2 control-label">Payment Type Nickname</label>
                <div class="col-lg-10">
                    <input id="payment-type-nickname" name="payment_type_nickname" type="text" class="form-control" value="<?=$this->paymentTypeNickname?>" required pattern=".{4,}" title="4 characters minimum" />
                    <p class="help-block">What do you want to call this payment type? <br /> This nickname will appear as your company name on all invoices and receipts. Our clients typically use the name of their facility/facilities and the payment method. Examples: "ABC Storage - Business Amex" or "AA Storage Properties - Company Visa"</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="content-row">
        <h2>Credit Card Information</h2>
    </div>
    
    <div class="input-row string">
    
        <div class="form-horizontal">
            <div class="form-group">
                <label for="credit-card-number" class="col-lg-2 control-label">Credit Card Number</label>
                <div class="col-lg-10">
                    <input id="credit-card-number" name="credit_card_number" type="text" class="form-control" value="<?=$this->creditCardNumber?>" maxlength="16" required pattern=".{10,16}" title="please enter a valid credit card number"/>
                    <p class="help-block" id="credit-card-image"></p>
                    <p class="help-block">We accept Visa, Mastercard, Discover, and American Express and automatically detect your credit card type.</p>
                </div>
            </div>
        
            <div class="form-group">
                <label for="credit-card-name" class="col-lg-2 control-label">Name on Card</label>
                <div class="col-lg-10">
                    <input id="credit-card-name" name="credit_card_name" type="text" class="form-control" value="<?=$this->creditCardName?>" required pattern=".{4,}" title="4 characters minimum" />
                </div>
            </div>
        
        
            <div class="form-group">
                <label class="col-lg-2 control-label">Expiration Date</label>
                <div class="col-lg-5">
                
                    <?php
                        if($this->creditCardExpirationMonth){
                          $month = $this->creditCardExpirationMonth;
                        } else {
                            $month = date('m');
                        } 
                    ?>
                
                    <select id="credit-card-expiration-month" name="credit_card_expiration_month" class="form-control" required pattern=".{2,2}" title="2 digit month">
                        <option value="01"<?=($month=='01')?' selected="selected"':''?>>01</option>
                        <option value="02"<?=($month=='02')?' selected="selected"':''?>>02</option>
                        <option value="03"<?=($month=='03')?' selected="selected"':''?>>03</option>
                        <option value="04"<?=($month=='04')?' selected="selected"':''?>>04</option>
                        <option value="05"<?=($month=='05')?' selected="selected"':''?>>05</option>
                        <option value="06"<?=($month=='06')?' selected="selected"':''?>>06</option>
                        <option value="07"<?=($month=='07')?' selected="selected"':''?>>07</option>
                        <option value="08"<?=($month=='08')?' selected="selected"':''?>>08</option>
                        <option value="09"<?=($month=='09')?' selected="selected"':''?>>09</option>
                        <option value="10"<?=($month=='10')?' selected="selected"':''?>>10</option>
                        <option value="11"<?=($month=='11')?' selected="selected"':''?>>11</option>
                        <option value="12"<?=($month=='12')?' selected="selected"':''?>>12</option>
                    </select>
                    <br />
                </div>
                <div class="col-lg-5">
                    <select id="credit-card-expiration-year" name="credit_card_expiration_year" class="form-control" required pattern=".{4,4}" title="4 digit year">
                    
                        <?php for($i = date('Y'); $i < (date('Y')+10); $i++){ ?>
                            <option value="<?=$i?>"<?=($this->creditCardExpirationYear==$i)?' selected="selected"':''?>><?=$i?></option>
                        <?php } ?>
                        
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <div class="content-row">
        <h2>Billing Address</h2>
    </div>
    
    <div class="input-row">
        <div class="form-horizontal">

            <div class="checkbox">
                <label for="billing-address-same">
                    <input type="checkbox" name="billing_address_same" id="billing-address-same" />
                    My billing address is the same as my company address.
                </label>
            </div><br />
    
            <div class="form-group">
                <label for="address" class="col-lg-2 control-label">Address</label>
                <div class="col-lg-10">
                    <input id="address" name="address" type="text" class="form-control" value="<?=$this->address?>" required pattern=".{3,}" title="3 characters minimum"/>
                </div>
            </div>
        
            <div class="form-group">
                <label for="city" class="col-lg-2 control-label">City</label>
                <div class="col-lg-10">
                    <input id="city" name="city" type="text" class="form-control" value="<?=$this->city?>" required pattern=".{3,}" title="3 characters minimum"/>
                </div>
            </div>
        
            <div class="form-group">
                <label for="state" class="col-lg-2 control-label">State</label>
                <div class="col-lg-10">
                    <select id="state" name="state" class="form-control" required title="2 letter state code">
                        <option value=""></option>
    			        <option value="AL"<?=($this->state=='AL')?' selected="selected"':''?>>AL</option>
    			        <option value="AK"<?=($this->state=='AK')?' selected="selected"':''?>>AK</option>
    			        <option value="AZ"<?=($this->state=='AZ')?' selected="selected"':''?>>AZ</option>
    			        <option value="AR"<?=($this->state=='AR')?' selected="selected"':''?>>AR</option>
    			        <option value="CA"<?=($this->state=='CA')?' selected="selected"':''?>>CA</option>
    			        <option value="CO"<?=($this->state=='CO')?' selected="selected"':''?>>CO</option>
    			
    			        <option value="CT"<?=($this->state=='CT')?' selected="selected"':''?>>CT</option>
    			        <option value="DE"<?=($this->state=='DE')?' selected="selected"':''?>>DE</option>
    			        <option value="DC"<?=($this->state=='DC')?' selected="selected"':''?>>DC</option>
    			        <option value="FL"<?=($this->state=='FL')?' selected="selected"':''?>>FL</option>
    			        <option value="GA"<?=($this->state=='GA')?' selected="selected"':''?>>GA</option>
    			        <option value="HI"<?=($this->state=='HI')?' selected="selected"':''?>>HI</option>
    			
    			        <option value="ID"<?=($this->state=='ID')?' selected="selected"':''?>>ID</option>
    			        <option value="IL"<?=($this->state=='IL')?' selected="selected"':''?>>IL</option>
    			        <option value="IN"<?=($this->state=='IN')?' selected="selected"':''?>>IN</option>
    			        <option value="IA"<?=($this->state=='IA')?' selected="selected"':''?>>IA</option>
    			        <option value="KS"<?=($this->state=='KS')?' selected="selected"':''?>>KS</option>
    			        <option value="KY"<?=($this->state=='KY')?' selected="selected"':''?>>KY</option>
    			
    			        <option value="LA"<?=($this->state=='LA')?' selected="selected"':''?>>LA</option>
    			        <option value="ME"<?=($this->state=='ME')?' selected="selected"':''?>>ME</option>
    			        <option value="MD"<?=($this->state=='MD')?' selected="selected"':''?>>MD</option>
    			        <option value="MA"<?=($this->state=='MA')?' selected="selected"':''?>>MA</option>
    			        <option value="MI"<?=($this->state=='MI')?' selected="selected"':''?>>MI</option>
    			        <option value="MN"<?=($this->state=='MN')?' selected="selected"':''?>>MN</option>
    			
    			        <option value="MS"<?=($this->state=='MS')?' selected="selected"':''?>>MS</option>
    			        <option value="MO"<?=($this->state=='MO')?' selected="selected"':''?>>MO</option>
    			        <option value="MT"<?=($this->state=='MT')?' selected="selected"':''?>>MT</option>
    			        <option value="NE"<?=($this->state=='NE')?' selected="selected"':''?>>NE</option>
    			        <option value="NV"<?=($this->state=='NV')?' selected="selected"':''?>>NV</option>
    			        <option value="NH"<?=($this->state=='NH')?' selected="selected"':''?>>NH</option>
    			
    			        <option value="NJ"<?=($this->state=='NJ')?' selected="selected"':''?>>NJ</option>
    			        <option value="NM"<?=($this->state=='NM')?' selected="selected"':''?>>NM</option>
    			        <option value="NY"<?=($this->state=='NY')?' selected="selected"':''?>>NY</option>
    			        <option value="NC"<?=($this->state=='NC')?' selected="selected"':''?>>NC</option>
    			        <option value="ND"<?=($this->state=='ND')?' selected="selected"':''?>>ND</option>
    			        <option value="OH"<?=($this->state=='OH')?' selected="selected"':''?>>OH</option>
    			
    			        <option value="OK"<?=($this->state=='OK')?' selected="selected"':''?>>OK</option>
    			        <option value="OR"<?=($this->state=='OR')?' selected="selected"':''?>>OR</option>
    			        <option value="PA"<?=($this->state=='PA')?' selected="selected"':''?>>PA</option>
    			        <option value="RI"<?=($this->state=='RI')?' selected="selected"':''?>>RI</option>
    			        <option value="SC"<?=($this->state=='SC')?' selected="selected"':''?>>SC</option>
    			        <option value="SD"<?=($this->state=='SD')?' selected="selected"':''?>>SD</option>
    			
    			        <option value="TN"<?=($this->state=='TN')?' selected="selected"':''?>>TN</option>
    			        <option value="TX"<?=($this->state=='TX')?' selected="selected"':''?>>TX</option>
    			        <option value="UT"<?=($this->state=='UT')?' selected="selected"':''?>>UT</option>
    			        <option value="VT"<?=($this->state=='VT')?' selected="selected"':''?>>VT</option>
    			        <option value="VA"<?=($this->state=='VA')?' selected="selected"':''?>>VA</option>
    			        <option value="WA"<?=($this->state=='WA')?' selected="selected"':''?>>WA</option>
    			
    			        <option value="WV"<?=($this->state=='WV')?' selected="selected"':''?>>WV</option>
    			        <option value="WI"<?=($this->state=='WI')?' selected="selected"':''?>>WI</option>
    			        <option value="WY"<?=($this->state=='WY')?' selected="selected"':''?>>WY</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="zip" class="col-lg-2 control-label">Zip Code</label>
                <div class="col-lg-10">
                    <input id="zip" name="zip" type="text" class="form-control" value="<?=$this->zip?>" required pattern=".{5,10}" title="5 digit zip code minimum"/>
                </div>
            </div>
        </div>
    </div>
    
    <div class="input-row">
        <div class="form-horizontal">
            <div class="form-group">
                <label for="emails" class="col-lg-2 control-label">Email Address(es)</label>
                <div class="col-lg-10">
                    <input id="emails" name="emails" type="text" class="form-control" value="<?=$this->emails?>" required pattern=".{4,}" title="4 characters minimum"/>
                    <span class="help-inline">(comma separate for multiple)</span><br/>
                    <p class="help-block" id="cc_section">The email address/addresses entered here will receive an email copy of your receipt each time your credit card is charged</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="content-footer">
        <div class="pull-right">
            <img src="/images/loaders/large.gif" class="loading hide" alt="loading" />&nbsp;&nbsp;
            <a id="do_this_later" class="btn btn-default btn-lg" href="<?=$this->url(['action'=>'add-first'], 'features')?>">Do This Later</a>&nbsp;
            <input id="submit" type="submit" value="Next" class="btn btn-lg btn-primary" data-loading-text="Saving" />
            <p class="loading-text text-right hide"><br />We're securely saving your credit card.<br />This could take a minute.</p>
        </div>
        <a href="<?=$this->backlink?>" class="btn btn-default btn-lg" id="back">Back</a>
    </div>
</form>
<?php }?>