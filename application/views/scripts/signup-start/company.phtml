<form id="signup-company-form" action="/signup-start/add-company" method="post">
	<input type="hidden" id="login_csrf_token" name="login_csrf_token" value="<?=$this->login_csrf_token?>">
	<input type="hidden" name="csrf_token" value="<?=$this->csrf_token?>">
    <div class="content-row">
        <h2 id="companyinformation">Company Information</h2>
    </div>
    <div class="input-row string">
    	<div class="form-horizontal">
    		<fieldset>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="company-name">Company Name </label>
                    <div class="col-md-10">
                        <input type="text" name="company_name" class="form-control" required pattern=".{3,}" title="3 characters minimum" id="company-name" value="<?=$this->companyName?>"/>
                    </div>
    			</div>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="address">Business Address </label>
                    <div class="col-md-10">
    				    <input type="text" name="address" class="form-control" required pattern=".{4,}" title="4 characters minimum" id="address" value="<?=$this->address?>"/>
                    </div>
    			</div>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="city">City </label>
                    <div class="col-md-10">
    				    <input type="text" name="city" class="form-control" required pattern=".{3,}" title="3 characters minimum" id="city" value="<?=$this->city?>"/>
                    </div>
    			</div>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="state">State </label>
                    <div class="col-md-10">
        				<select name="state" id="state" class="form-control" required title="2 letter state code">
        				    <option value=""></option>
        			        <option value="AL"<?=($this->state=='AL')?' selected="selected"':''?>>AL</option>
        			        <option value="AK"<?=($this->state=='AK')?' selected="selected"':''?>>AK</option>
        			        <option value="AZ"<?=($this->state=='AZ')?' selected="selected"':''?>>AZ</option>
        			        <option value="AR"<?=($this->state=='AR')?' selected="selected"':''?>>AR</option>
        			        <option value="CA"<?=($this->state=='CA')?' selected="selected"':''?>>CA</option>
        			        <option value="CO"<?=($this->state=='CO')?' selected="selected"':''?>>CO</option>

        			        <option value="CT"<?=($this->state=='CT')?' selected="selected"':''?>>CT</option>
        			        <option value="DE"<?=($this->state=='DE')?' selected="selected"':''?>>DE</option>
        			        <option value="DC"<?=($this->state=='DC')?' selected="selected"':''?>>DC</option>
        			        <option value="FL"<?=($this->state=='FL')?' selected="selected"':''?>>FL</option>
        			        <option value="GA"<?=($this->state=='GA')?' selected="selected"':''?>>GA</option>
        			        <option value="HI"<?=($this->state=='HI')?' selected="selected"':''?>>HI</option>

        			        <option value="ID"<?=($this->state=='ID')?' selected="selected"':''?>>ID</option>
        			        <option value="IL"<?=($this->state=='IL')?' selected="selected"':''?>>IL</option>
        			        <option value="IN"<?=($this->state=='IN')?' selected="selected"':''?>>IN</option>
        			        <option value="IA"<?=($this->state=='IA')?' selected="selected"':''?>>IA</option>
        			        <option value="KS"<?=($this->state=='KS')?' selected="selected"':''?>>KS</option>
        			        <option value="KY"<?=($this->state=='KY')?' selected="selected"':''?>>KY</option>

        			        <option value="LA"<?=($this->state=='LA')?' selected="selected"':''?>>LA</option>
        			        <option value="ME"<?=($this->state=='ME')?' selected="selected"':''?>>ME</option>
        			        <option value="MD"<?=($this->state=='MD')?' selected="selected"':''?>>MD</option>
        			        <option value="MA"<?=($this->state=='MA')?' selected="selected"':''?>>MA</option>
        			        <option value="MI"<?=($this->state=='MI')?' selected="selected"':''?>>MI</option>
        			        <option value="MN"<?=($this->state=='MN')?' selected="selected"':''?>>MN</option>

        			        <option value="MS"<?=($this->state=='MS')?' selected="selected"':''?>>MS</option>
        			        <option value="MO"<?=($this->state=='MO')?' selected="selected"':''?>>MO</option>
        			        <option value="MT"<?=($this->state=='MT')?' selected="selected"':''?>>MT</option>
        			        <option value="NE"<?=($this->state=='NE')?' selected="selected"':''?>>NE</option>
        			        <option value="NV"<?=($this->state=='NV')?' selected="selected"':''?>>NV</option>
        			        <option value="NH"<?=($this->state=='NH')?' selected="selected"':''?>>NH</option>

        			        <option value="NJ"<?=($this->state=='NJ')?' selected="selected"':''?>>NJ</option>
        			        <option value="NM"<?=($this->state=='NM')?' selected="selected"':''?>>NM</option>
        			        <option value="NY"<?=($this->state=='NY')?' selected="selected"':''?>>NY</option>
        			        <option value="NC"<?=($this->state=='NC')?' selected="selected"':''?>>NC</option>
        			        <option value="ND"<?=($this->state=='ND')?' selected="selected"':''?>>ND</option>
        			        <option value="OH"<?=($this->state=='OH')?' selected="selected"':''?>>OH</option>

        			        <option value="OK"<?=($this->state=='OK')?' selected="selected"':''?>>OK</option>
        			        <option value="OR"<?=($this->state=='OR')?' selected="selected"':''?>>OR</option>
        			        <option value="PA"<?=($this->state=='PA')?' selected="selected"':''?>>PA</option>
        			        <option value="RI"<?=($this->state=='RI')?' selected="selected"':''?>>RI</option>
        			        <option value="SC"<?=($this->state=='SC')?' selected="selected"':''?>>SC</option>
        			        <option value="SD"<?=($this->state=='SD')?' selected="selected"':''?>>SD</option>

        			        <option value="TN"<?=($this->state=='TN')?' selected="selected"':''?>>TN</option>
        			        <option value="TX"<?=($this->state=='TX')?' selected="selected"':''?>>TX</option>
        			        <option value="UT"<?=($this->state=='UT')?' selected="selected"':''?>>UT</option>
        			        <option value="VT"<?=($this->state=='VT')?' selected="selected"':''?>>VT</option>
        			        <option value="VA"<?=($this->state=='VA')?' selected="selected"':''?>>VA</option>
        			        <option value="WA"<?=($this->state=='WA')?' selected="selected"':''?>>WA</option>

        			        <option value="WV"<?=($this->state=='WV')?' selected="selected"':''?>>WV</option>
        			        <option value="WI"<?=($this->state=='WI')?' selected="selected"':''?>>WI</option>
        			        <option value="WY"<?=($this->state=='WY')?' selected="selected"':''?>>WY</option>
                        </select>
                    </div>
    			</div>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="zip">Zip Code </label>
                    <div class="col-md-10">
    				    <input type="text" name="zip" class="form-control" required pattern=".{5,10}" title="5 characters minimum" id="zip" value="<?=$this->zip?>" />
                    </div>
    			</div>
    		</fieldset>
    	</div>
    </div>

    <div class="content-row">
        <h2>User Information</h2>
    </div>
    <div class="input-row string">
    	<div class="form-horizontal">
    		<fieldset>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="first">First Name </label>
                    <div class="col-md-10">
    				    <input type="text" name="first_name" class="form-control" required pattern=".{2,}" title="2 characters minimum" id="first-name" value="<?=$this->first?>"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="last">Last Name </label>
                    <div class="col-md-10">
    				    <input type="text" name="last_name" class="form-control" required pattern=".{2,}" title="2 characters minimum" id="last-name" value="<?=$this->last?>"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="phone">Phone </label>
                    <div class="col-md-10">
    				    <input type="tel" name="phone" class="form-control" required pattern=".{10,15}" title="10 characters minimum" id="phone" value="<?=$this->phone?>"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="email">Your Email </label>
                    <div class="col-md-10">
    				    <input type="email" name="email" class="form-control" required pattern=".{4,}" title="4 characters minimum" id="email" value="<?=$this->email?>"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="pass">Password </label>
                    <div class="col-md-10">
    				    <input type="password" name="password" class="form-control" required pattern=".{6,}" title="6 characters minimum" id="password" value=""/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="pass2">Re-Enter Password </label>
                    <div class="col-md-10">
    				    <input type="password" name="password_confirm" class="form-control" required pattern=".{6,}" title="6 characters minimum" id="password-confirm" value=""/>
                    </div>
                </div>
    		</fieldset>
    	</div>
    </div>
    <div class="content-footer">
        <div class="pull-right">
            <img src="/images/loaders/large.gif" class="loading hide" alt="loading" />&nbsp;&nbsp;
            <input id="submit" class="btn btn-primary btn-lg" name="commit" type="submit" value="Next" data-loading-text="Saving" />
        </div>
        <a href="<?=$this->backlink?>" class="btn btn-default btn-lg" id="back">Back</a>
    </div>
</form>