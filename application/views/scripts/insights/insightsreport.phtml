<script type="text/javascript">
var currentDateVal = '<?=date("Y-n")?>';
var report_count = <?=$this->reports_count?>;
</script>

<div class="page-header">
<h6>Report</h6>
<h2>Pricing Data for Your Competitive Trade Areas</h2>
</div>

<p>This report is generated on the 15th of each month.<br />
Each report is a snapshot of online prices from several online data sources.</p><br />
    <form class="form-horizontal" method="get" action="/insights">

        <div class="control-group">
            <label class="control-label" for="insight_date">Select Date</label>
            <div class="controls">
                <select id="insight_date" name="insight_date" class="form-control">
                    <?php foreach ($this->availableReports as $value => $dateStr) { ?>
                        <option value="<?=$value?>"><?=$dateStr?></option>
                    <?php } ?>
                </select>
                <p class="help-block"><?=$this->nextReport?> data will be available in <?=$this->nextReportDays?> <?=($this->nextReportDays > 1) ? 'days' : 'day'?>.</p>
            </div>
        </div>

        <div class="control-group" id="report_type_section">
            <label class="control-label">Report Type</label>
            <div class="controls">
                <div class="radio">
                    <label><input type="radio" name="report_type" value="<?=Genesis_Entity_Insights::REPORT_TYPE_LOCAL?>" checked="checked" />Dynamic Trade Area</label><p class="help-block">Reports survey the area around your facility for as far as is needed to reach a specific number of competitive facilities.</p><br />
                </div>
                <div class="radio">
                    <label><input type="radio" name="report_type" value="<?=Genesis_Entity_Insights::REPORT_TYPE_ORIGINAL?>" />Fixed Trade Area </label>
                </div>
                <p class="help-block">Reports survey the 15 mile area around your facility and include all facilities that fall into this area.</p>
            </div>
            </div>

        <div class="form-actions">
            <a href="#" class="btn btn-primary" id="insight_genreport">View Report</a>
            <a href="#" class="ui button" id="insight_download_btn">Download</a>
        </div>

    </form>

    <div id="report_return"></div>

