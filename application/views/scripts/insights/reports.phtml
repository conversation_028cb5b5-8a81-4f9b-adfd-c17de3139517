<?=$this->partial('insights/header.phtml', array('active' => 'other', 'reportCount'=> count($this->allReports)))?>

<?php $categories = array_keys($this->allReports);
    sort($categories);
    foreach ($categories as $category): ?>
        <h4><?=$category?></h4>
        <ul>
        <?php foreach ($this->allReports[$category] as $reportClassName => $reportName) { ?>
            <li><a href="/insights/custom/report/<?=$reportClassName?>"><?=$reportName?></a></li>
        <?php } ?>
        </ul>
    <?php endforeach; ?>
    
<span class="hidden" id="insightsOtherReportsPageTest"></span>