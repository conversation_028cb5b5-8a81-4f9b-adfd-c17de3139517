            <?=$this->data?>
            
            <h2><?=$this->facilityTitle?></h2>
            <p><?=$this->facilityAddress?></p>
            <p><?=$this->reportMonth?> <?=$this->reportYear?> &#183; Trade Area Radius: <?=$this->searchRadius?> miles &#183; Facilities Sampled: <?=$this->numSampledFacilities?></p>

            <div class="insight_reportdata">
                <table class="table1 insights_data">
                    <tr>
                        <th class="borderright_ccc">Unit Size</th>
                        <th class="borderright_ccc">Average Climate Price</th>
                        <th class="borderright_ccc">Average Non-Climate Price</th>
                        <th class="borderright_ccc">Your Average Climate Price</th>
                        <th>Your Average Non-Climate Price</th>
                    </tr>
                    
                    <?php
                    $unitTypes = array(
                        "5' x 5'" => 'cc25',
                        "5' x 10'" => 'cc50',
                        "5' x 15'" => 'cc75',
                        "10' x 10'" => 'cc100',
                        "10' x 15'" => 'cc150',
                        "10' x 20'" => 'cc200',
                    );
                    
                    foreach ($unitTypes as $size=>$type) {
                        $nontype = 'non'.$type;
                        $your_type = 'your_'.$type;
                        $your_nontype = 'your_'.$nontype;
                        ?>
                        <tr>
                            <th class="borderright_ccc"><?=$size?></th>
                            <td class="borderright_ccc bg_white"><?=(is_numeric($this->$type) ? "$".$this->$type : $this->$type)?></td>
                            <td class="borderright_ccc bg_white"><?=(is_numeric($this->$nontype) ? "$".$this->$nontype : $this->$nontype)?></td>
                            <td class="borderright_ccc bg_white <?php
                                if ($this->$type != "N/A" && $this->$your_type != "N/A") {
                                    if ($this->$type < $this->$your_type) {
                                        echo "price_bad";
                                    } elseif ($this->$type > $this->$your_type) {
                                        echo "price_good";
                                    } 
                                }
                                ?>"><?=(is_numeric($this->$your_type) ? "$".$this->$your_type : $this->$your_type)?> <a href="<?=$this->your_edit_link?>" class="small_link"> <i class="fa fa-pencil"></i></a></td>
                            <td class="bg_white <?php
                                if ($this->$nontype != "N/A" && $this->$your_nontype != "N/A") {
                                    if ($this->$nontype < $this->$your_nontype) {
                                        echo "price_bad ";
                                        echo $this->$nontype."  ".$this->$your_nontype;
                                    } elseif ($this->$nontype > $this->$your_nontype) {
                                        echo "price_good";
                                    } 
                                }
                                ?>"><?=(is_numeric($this->$your_nontype) ? "$".$this->$your_nontype : $this->$your_nontype)?> <a href="<?=$this->your_edit_link?>"> <i class="fa fa-pencil"></i></a></td>
                        </tr>                    
                        <?php
                    }
                    
                    ?>
                </table>
            </div>

            <div class="clear">&nbsp;</div>
<span class="hidden" id="insightsLandingPageTest"></span>