<?php if(!$this->account->getCpa()){ ?>
    <div class="setup-content-container">
        <div class="setup-content">  
            <h1>SpareFoot AdNetwork</h1>
            
            <div style="margin:0 1.667em;">
                <div class="form-section" style="float:right; width:15em; padding:1em; margin:0 1.667em;">
                    <h3>Questions?</h3>
                    <p>We can help you at any stage of the website setup process.</p>
                    <p><strong><?=Genesis_Config_Phone::ACCOUNT_MANAGEMENT?></strong></p>
                 </div>
                
                <h2>Join the largest self-storage marketplace</h2>
                <p>More than 6 million storage-seeking customers visit our network of websites every month, and we lead them to book free reservations at your facility. The SpareFoot network gets more traffic than all other independent storage directory and listing sites combined. More visibility means fewer empty units at your facility.</p>
                            
                <div style="padding:1em 0;">
                    <p style="margin-bottom:1em;">To get your AdNetwork account setup, call us at <strong><?=Genesis_Config_Phone::ACCOUNT_MANAGEMENT?></strong> or click the button below.</p><br />
                    <a href="/signup/facilities/which/<?= Genesis_Entity_UserAccess::PROD_CPA?>" class="input button" style="width:10em; margin:1em auto;">Add AdNetwork to my Account!</a>
                </div>
            </div>
            
            <div style="text-align:center;">
                <img src="http://www.cachews.com/images/sparefoot/facility-partners.jpg" width="348" height="276" alt="hosted website mockup" />
            </div>
        </div>
    </div>
<?php } else { ?>
You're already on the SpareFoot AdNetwork.
<?php } ?>