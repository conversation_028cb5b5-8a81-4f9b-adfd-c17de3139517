
<div class="page-header">
    <h1>Referral Program</h1>
</div>

<div>
    <div style="">
        <h2>$100 for you + a friend</h2><br />
        <strong>Refer a storage friend to SpareFoot!</strong><br />
        <strong>If they sign up, you’ll each get a $100 credit in SpareBucks.</strong><br /><br />
        
        Just complete the short form below, and we’ll get in touch with the friend you refer. We'll let you know if they sign up, then add the $100 credit to be applied to your next bill. It’s that easy to benefit from helping a storage friend get more new customers online!<br /><br />

        Let us know if you have any questions:<br />
        <EMAIL><br />
        855-427-8193 x2<br /><br />

        <strong>Simply complete this form:</strong>
    </div>

    <?php if($this->alert){ ?>
        <p class="alert<?=($this->alertClass?' '.$this->alertClass:'')?>">
            <?=$this->alert?>
        </p>
    <?php } ?>

    <div id="referral-form">
        <form action="/referral/home" method="post" class="form-horizontal">
            <div class="form-group<?=(in_array('referral_firstname', $this->erroredFields))?' error':''?>">
                <label class="col-lg-2 control-label" for="referral_firstname">THEIR first name</label>
                <div class="col-lg-10">
                    <input size="50" type="text" id="referral_firstname" name="referral_firstname" tabindex="1" class="form-control" value=""/>
                </div>
            </div>

            <div class="form-group<?=(in_array('referral_lastname', $this->erroredFields))?' error':''?>">
                <label class="col-lg-2 control-label" for="referral_lastname">THEIR last name</label>
                <div class="col-lg-10">
                    <input size="50" type="text" id="referral_lastname" name="referral_lastname" tabindex="2" class="form-control" value=""/>
                </div>
            </div>

            <div class="form-group<?=(in_array('referral_email', $this->erroredFields))?' error':''?>">
                <label class="col-lg-2 control-label" for="referral_email">THEIR email</label>
                <div class="col-lg-10">
                    <input size="50" type="text" id="referral_email" name="referral_email" tabindex="3" class="form-control" value=""/>
                </div>
            </div>

            <div class="form-group<?=(in_array('referral_phone', $this->erroredFields))?' error':''?>">
                <label class="col-lg-2 control-label" for="referral_phone">THEIR phone</label>
                <div class="col-lg-10">
                    <input size="50" type="text" id="referral_phone" name="referral_phone" tabindex="4" class="form-control" value=""/>
                </div>
            </div>

            <div class="form-group<?=(in_array('referral_businessname', $this->erroredFields))?' error':''?>">
                <label class="col-lg-2 control-label" for="referral_businessname">THEIR business name</label>
                <div class="col-lg-10">
                    <input size="50" type="text" id="referral_businessname" name="referral_businessname" tabindex="5" class="form-control" value=""/>
                </div>
            </div>

            <!-- 
                <div class="form-group<?=(in_array('referral_firstname', $this->erroredFields))?' error':''?>">
                <label class="col-lg-2 control-label" for="referral_firstname">THEIR admin</label>
                <div class="col-lg-10">
                    <input size="50" type="text" id="referral_firstname" name="referral_firstname" tabindex="6" class="form-control" value=""/>
                </div>
            </div>
            -->
            
            <input type="submit" class="btn btn-primary" value="Submit Referral" />
        </form>
    </div>
</div>