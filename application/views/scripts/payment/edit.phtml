<script type="text/javascript">
var accountId = <?=$this->accountId?>;
<?php if ($this->billableEntityId) { ?>
var billableEntityId = <?=$this->billableEntityId?>;
<?php } else { ?>
var billableEntityId = 0;
<?php } ?>
<?php if ($this->paymentType) { ?>
var paymentType = '<?=$this->paymentType?>';
var ccType = 'existing';
<?php } else { ?>
var paymentType = '';
var ccType = '';
<?php } ?>
var returnScreen = '<?=$this->returnscreen?>';
</script>

<form id="edit-payment-form" class="form-horizontal setup-content-container">
    <input type="hidden" id="csrf_token" name="csrf_token" value="<?=$this->csrf_token?>">

    <div class="setup-content">

        <div class="content-row">
            <h2>Advanced Billing Setup <i class="icon lock"></i></h2>
            <p>Need to setup different payment methods for different facilities? Add additional payment methods below and connect them to your facilities on the main Payment Setup screen when you're done.</p>

            <div id="type_buttons" class="ui secondary menu">
                <div class="ui buttons">
                    <a href="#" id="btn_cc" class="ui button secondary active">
                        <i class="visa icon"></i>
                        Credit Card
                    </a>
                    <a href="#" id="btn_ach" class="ui button secondary">
                        <i class="dollar icon"></i>
                        ACH
                    </a>
                    <?php if ($this->account->getAllowInvoiceBilling()) { ?>
                    <a href="#" id="btn_invoice" class="ui button secondary">
                        <i class="file icon"></i>
                        Invoice
                    </a>
                    <?php } ?>
                </div>
            </div>

        <div class="input-row" id="payment-type-nickname-div">
            <div class="form-group">
                <label for="payment-type-nickname" class="col-md-2 control-label">Payment Type Nickname</label>
                <div class="col-md-10">
                    <input id="payment-type-nickname" name="payment_type_nickname" type="text" class="form-control" value="<?=$this->paymentTypeNickname?>"/>
                    <p class="help-block">This nickname will appear as your company name on all invoices and receipts. Our clients typically use the name of their facility/facilities and the payment method.<br>
                    Examples: "ABC Storage - Business Amex" or "AA Storage Properties - Company Visa"</p>
                </div>
            </div>
        </div>
    <div id="cc_section">
        <div class="content-row">
            <h2>Credit Card Information</h2>
        </div>
        <div class="input-row string">

            <div>

                <div class="form-group">
                    <label for="credit-card-number" class="col-md-2 control-label">Credit Card Number</label>
                    <div class="col-md-10">
                        <input type="hidden" id="cc-change-listener" name="cc_change_listener" value="0" />
                        <input id="credit-card-number" name="credit_card_number" type="text" class="form-control" value="<?=$this->creditCardNumber?>" onChange="getCCType(this.value)"/>
                        <p class="help-block" id="credit-card-image">
                        <?php
                        switch ($this->ccType) {
                            case 'VISA':
                                ?><img src="/images/check.png" style="position:relative;left:40px;"/><img src="/images/visa.png" style=""/><?php
                                break;
                            case "Master Card":
                                ?><img src="/images/check.png" style="position:relative;left:40px;"/><img src="/images/mastercard.png" style=""/><?php
                                break;
                            case "American Express":
                                ?><img src="/images/check.png" style="position:relative;left:40px;"/><img src="/images/amex.png" style=""/><?php
                                break;
                            case "Discover":
                                ?><img src="/images/check.png" style="position:relative;left:40px;"/><img src="/images/discover.png" style=""/><?php
                                break;
                            default:
                                break;
                        }
                        ?></p>
                        <p class="help-block">We accept Visa, Mastercard, Discover, and American Express and automatically detect your credit card type.</p>
                    </div>
                </div>


                <div class="form-group">
                    <label for="credit-card-name" class="col-md-2 control-label">Name on Card</label>
                    <div class="col-md-10">
                        <input id="credit-card-name" name="credit_card_name" type="text" class="form-control" value="<?=$this->creditCardName?>"/>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">Expiration Date</label>
                    <p class="col-md-5">

                        <?php
                            if($this->creditCardExpirationMonth){
                                $month = $this->creditCardExpirationMonth;
                            } else {
                                $month = date('m');
                            }
                        ?>

                        <select id="credit-card-expiration-month" name="credit_card_expiration_month" class="form-control">
                            <option value="01"<?=($month=='01')?' selected="selected"':''?>>01</option>
                            <option value="02"<?=($month=='02')?' selected="selected"':''?>>02</option>
                            <option value="03"<?=($month=='03')?' selected="selected"':''?>>03</option>
                            <option value="04"<?=($month=='04')?' selected="selected"':''?>>04</option>
                            <option value="05"<?=($month=='05')?' selected="selected"':''?>>05</option>
                            <option value="06"<?=($month=='06')?' selected="selected"':''?>>06</option>
                            <option value="07"<?=($month=='07')?' selected="selected"':''?>>07</option>
                            <option value="08"<?=($month=='08')?' selected="selected"':''?>>08</option>
                            <option value="09"<?=($month=='09')?' selected="selected"':''?>>09</option>
                            <option value="10"<?=($month=='10')?' selected="selected"':''?>>10</option>
                            <option value="11"<?=($month=='11')?' selected="selected"':''?>>11</option>
                            <option value="12"<?=($month=='12')?' selected="selected"':''?>>12</option>
                        </select>
                    </p>
                    <p class="col-md-5">
                        <select id="credit-card-expiration-year" name="credit_card_expiration_year" class="form-control">

                            <?php for($i = date('Y'); $i < (date('Y')+10); $i++){ ?>
                                <option value="<?=$i?>"<?=($this->creditCardExpirationYear==$i)?' selected="selected"':''?>><?=$i?></option>
                            <?php } ?>

                        </select>
                    </p>
                </div>
            </div>
        </div>
    </div>
	<input type="hidden" name="is_new" value="<?=$this->isNew?>" />
    <div id="ach_section" style="display:none">
        <?php if ($this->isNew) { ?>
        <div class="content-row">
            <p>Download the <a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">ACH form</a> and email it back to us.  Once we have verified your billing information, we will add it to your account.<p/>
            <p style="text-align: center;"><img src="/images/small_pdf_icon.gif"/><a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">ACH Bank Draft Form</a></p>
            <p>Tel: (*************<br/>Email: <EMAIL></p>
        </div>
        <?php } else { ?>
        <div class="content-row">
            <p>To change your ACH information, download the <a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">ACH form</a> and email it back to us.  Once we have verified your billing information, we will update your account.<p/>
            <p style="text-align: center;"><img src="/images/small_pdf_icon.gif"/><a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">ACH Bank Draft Form</a></p>
            <p>Tel: (*************<br/>Email: <EMAIL></p>
        </div>
        <!--Cannot collect ACH online yet-->
        <!--<div class="content-row">
                <h2>ACH Information</h2>
        </div>
        <div class="input-row string">
            <label for="bank_name">Bank Name</label>
            <input id="bank_name" name="bank_name" type="text" class="form-control" value="<?=$this->achName?>"/>
            <div style="clear:both"></div>
        </div>
        <div class="input-row string">
            <label for="bank_account_number">Bank Account Number</label>
            <input id="bank_account_number" name="bank_account_number" type="text" class="form-control" value="<?=$this->achAccount?>"/>
            <div style="clear:both"></div>
        </div>
        <div class="input-row string">
            <label for="bank_routing_number">Bank Routing Number</label>
            <input id="bank_routing_number" name="bank_routing_number" type="text" class="form-control" value="<?=$this->achRouting?>"/>
            <div style="clear:both"></div>
        </div>-->
        <?php } ?>
    </div>

    <div id="address_section">
        <div class="content-row">
            <h2>Billing Address</h2>
        </div>

        <div class="input-row">

            <div>

                <div class="form-group">
                    <div class="controls">
                        <div class="ui checkbox bootstrap-aligned">
                            <input type="checkbox" name="billing_address_same" id="billing-address-same" />
                            <label>My billing address is the same as my company address.</label>

                        </div>
                        <?php if (!$this->return) { ?>
                        <p class="help-block"><a href="/settings/myaccount">Edit my company address</a></p>
                        <?php } ?>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address" class="col-md-2 control-label">Address</label>
                    <div class="col-md-10">
                        <input id="address" name="address" type="text" class="form-control" value="<?=$this->address?>"/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="city" class="col-md-2 control-label">City</label>
                    <div class="col-md-10">
                        <input id="city" name="city" type="text" class="form-control" value="<?=$this->city?>"/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="state" class="col-md-2 control-label">State</label>
                    <div class="col-md-10">
                        <select id="state" name="state" class="form-control">
                            <option value=""></option>
        			        <option value="AL"<?=($this->state=='AL')?' selected="selected"':''?>>AL</option>
        			        <option value="AK"<?=($this->state=='AK')?' selected="selected"':''?>>AK</option>
        			        <option value="AZ"<?=($this->state=='AZ')?' selected="selected"':''?>>AZ</option>
        			        <option value="AR"<?=($this->state=='AR')?' selected="selected"':''?>>AR</option>
        			        <option value="CA"<?=($this->state=='CA')?' selected="selected"':''?>>CA</option>
        			        <option value="CO"<?=($this->state=='CO')?' selected="selected"':''?>>CO</option>

        			        <option value="CT"<?=($this->state=='CT')?' selected="selected"':''?>>CT</option>
        			        <option value="DE"<?=($this->state=='DE')?' selected="selected"':''?>>DE</option>
        			        <option value="DC"<?=($this->state=='DC')?' selected="selected"':''?>>DC</option>
        			        <option value="FL"<?=($this->state=='FL')?' selected="selected"':''?>>FL</option>
        			        <option value="GA"<?=($this->state=='GA')?' selected="selected"':''?>>GA</option>
        			        <option value="HI"<?=($this->state=='HI')?' selected="selected"':''?>>HI</option>

        			        <option value="ID"<?=($this->state=='ID')?' selected="selected"':''?>>ID</option>
        			        <option value="IL"<?=($this->state=='IL')?' selected="selected"':''?>>IL</option>
        			        <option value="IN"<?=($this->state=='IN')?' selected="selected"':''?>>IN</option>
        			        <option value="IA"<?=($this->state=='IA')?' selected="selected"':''?>>IA</option>
        			        <option value="KS"<?=($this->state=='KS')?' selected="selected"':''?>>KS</option>
        			        <option value="KY"<?=($this->state=='KY')?' selected="selected"':''?>>KY</option>

        			        <option value="LA"<?=($this->state=='LA')?' selected="selected"':''?>>LA</option>
        			        <option value="ME"<?=($this->state=='ME')?' selected="selected"':''?>>ME</option>
        			        <option value="MD"<?=($this->state=='MD')?' selected="selected"':''?>>MD</option>
        			        <option value="MA"<?=($this->state=='MA')?' selected="selected"':''?>>MA</option>
        			        <option value="MI"<?=($this->state=='MI')?' selected="selected"':''?>>MI</option>
        			        <option value="MN"<?=($this->state=='MN')?' selected="selected"':''?>>MN</option>

        			        <option value="MS"<?=($this->state=='MS')?' selected="selected"':''?>>MS</option>
        			        <option value="MO"<?=($this->state=='MO')?' selected="selected"':''?>>MO</option>
        			        <option value="MT"<?=($this->state=='MT')?' selected="selected"':''?>>MT</option>
        			        <option value="NE"<?=($this->state=='NE')?' selected="selected"':''?>>NE</option>
        			        <option value="NV"<?=($this->state=='NV')?' selected="selected"':''?>>NV</option>
        			        <option value="NH"<?=($this->state=='NH')?' selected="selected"':''?>>NH</option>

        			        <option value="NJ"<?=($this->state=='NJ')?' selected="selected"':''?>>NJ</option>
        			        <option value="NM"<?=($this->state=='NM')?' selected="selected"':''?>>NM</option>
        			        <option value="NY"<?=($this->state=='NY')?' selected="selected"':''?>>NY</option>
        			        <option value="NC"<?=($this->state=='NC')?' selected="selected"':''?>>NC</option>
        			        <option value="ND"<?=($this->state=='ND')?' selected="selected"':''?>>ND</option>
        			        <option value="OH"<?=($this->state=='OH')?' selected="selected"':''?>>OH</option>

        			        <option value="OK"<?=($this->state=='OK')?' selected="selected"':''?>>OK</option>
        			        <option value="OR"<?=($this->state=='OR')?' selected="selected"':''?>>OR</option>
        			        <option value="PA"<?=($this->state=='PA')?' selected="selected"':''?>>PA</option>
        			        <option value="RI"<?=($this->state=='RI')?' selected="selected"':''?>>RI</option>
        			        <option value="SC"<?=($this->state=='SC')?' selected="selected"':''?>>SC</option>
        			        <option value="SD"<?=($this->state=='SD')?' selected="selected"':''?>>SD</option>

        			        <option value="TN"<?=($this->state=='TN')?' selected="selected"':''?>>TN</option>
        			        <option value="TX"<?=($this->state=='TX')?' selected="selected"':''?>>TX</option>
        			        <option value="UT"<?=($this->state=='UT')?' selected="selected"':''?>>UT</option>
        			        <option value="VT"<?=($this->state=='VT')?' selected="selected"':''?>>VT</option>
        			        <option value="VA"<?=($this->state=='VA')?' selected="selected"':''?>>VA</option>
        			        <option value="WA"<?=($this->state=='WA')?' selected="selected"':''?>>WA</option>

        			        <option value="WV"<?=($this->state=='WV')?' selected="selected"':''?>>WV</option>
        			        <option value="WI"<?=($this->state=='WI')?' selected="selected"':''?>>WI</option>
        			        <option value="WY"<?=($this->state=='WY')?' selected="selected"':''?>>WY</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="zip" class="col-md-2 control-label">Zip Code</label>
                    <div class="col-md-10">
                        <input id="zip" name="zip" type="text" class="form-control" value="<?=$this->zip?>"/>
                    </div>
                </div>

            </div>

        </div>
    </div>
    <div id="invoice_section" style="display:none">
        <div class="content-row">
            <h2>Email Invoice Delivery</h2>
            <p>Our invoices are sent by email only.  Please include the email address of whomever should receive a copy of this invoice.</p>
        </div>
    </div>
        <div class="input-row string" id="emails-div">
            <div class="form-horizontal">
                <div class="form-group">
                    <label for="emails" class="col-md-2 control-label">Email Address(es)</label>
                    <div class="col-md-10">
                        <input id="emails" name="emails" type="text" class="form-control" value="<?=$this->emails?>"/>
                        <p class="help-block" id="cc_section">(comma separate for multiple)<br />The email address/addresses entered here will receive an email copy of your receipt each time your payment method is charged</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-footer">
            <div class="pull-right">
                <img src="/images/loaders/large.gif" class="loading hide" />&nbsp;&nbsp;
                <input id="submit" class="ui primary large button" name="commit" type="submit" data-loading-text="Saving" value="<?php if ($this->isNew) {?>Add<?php } else {?>Update<?php }?>" />
            </div>
            <a class="ui basic large button" href="<?=$this->returnscreen?>">Back</a>
        </div>

    </div>

</form>
