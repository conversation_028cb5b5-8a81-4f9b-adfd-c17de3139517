<?=$this->partial('settings/header.phtml', array('loggedUser' => $this->loggedUser, 'active' => 'payment'))?>

<script type="text/javascript">
//write billing data to json
var pms = <?=json_encode($this->payment_methods)?>;
</script>

<h3>Your Payment Methods</h3>
<?php if (count($this->payment_methods) > 0) : ?>
<p>These are the payment methods set up for your account</p>
<?php else: ?>
<a class="ui primary button" href="/payment/add"><i class="plus icon"></i>Add Payment Method</a>
<?php endif; ?>

<form action="/payment/edit" method="post">
    <table class="table table-striped">
		<tr>
			<th>Actions</th>
			<th>Nickname</th>
			<th>Invoice / Receipt Contacts</th>
		</tr>
        <?php foreach($this->payment_methods as $be) { ?>
            <tr>
                <td>
                    <a href="#" class="ui secondary button" onclick="editFacPayment(<?=$be['id']?>);">
                        <i class="pencil icon"></i> Edit / Replace
                    </a>
                </td>
                <td>
                    <?=$be['displayStr']?>
                </td>
				<td>
					<?=$be['emails']?>
				</td>
            </tr>
        <?php } ?>
    </table>
</form>

<div class="spacer"></div>

<h3>Payment Method by Facility</h3>
<p>Use the drop-down menus to assign a payment method to each facility or to create a more advanced account setup, contact <EMAIL>.</p>
<form action="/payment/edit" method="post" id="edit_pymt_form"></form>

<table class="ui sortable striped cell-headers table">
    <thead>
        <tr>
            <th>Facility</th>
            <th>Payment Method</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
<?php foreach($this->facilities as $fac) { ?>
    <tr id="<?=$fac->getId()?>" <?php if (!$fac->getBillableEntityId()) {?>class="red"<?php }?>>
        <td><?=$fac->getTitleWithCompanyCode()?></td>
        <td>
                <select id="fac_<?=$fac->getId()?>" name="beId_<?=$fac->getId()?>" class="form-control fac_beId" onChange="changeFacBe(<?=$fac->getId()?>, this.value);">
                <?php if (!$fac->getBillableEntityId()) {?><option value="choose" selected="selected">Select a payment method</option><?php }?>
                <?php foreach($this->payment_methods as $be) { ?>
                    <option value="<?=$be['id']?>" <?php if ($fac->getBillableEntityId() == $be['id']) {?>selected="selected"<?php }?>><?=$be['displayStr']?></option>
                <?php } ?>
                </select>
        </td>
        <td id="<?=$fac->getId()?>_notlive">
            <?php if (!$fac->getBillableEntity()) {?>NOT LIVE<?php } ?>
        </td>
    </tr>
<?php } ?>
    </tbody>

</table>

<span class="hidden" id="paymentOverviewPageTest"></span>
