<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'inventory', 'loggedUser' => $this->loggedUser, 'facility' => $this->facility))?>

<div id="units-actions-alert" style="margin-top:5px;display:none;">
    <div class="ui warning message">
        <p><strong>Select the checkbox next to each unit</strong> to add a discount, promo, or free item.</p>
    </div>
</div>

<div class="ui secondary menu">
    <div class="form-inline" id="selected-unit-nav">
        <div class="ui buttons pull-right">
            <a class="ui button disabled previous-unit"><i class="fa fa-chevron-left"></i></a>
            <a class="ui button disabled next-unit"><i class="fa fa-chevron-right"></i></a>
        </div>
    </div>
<div id="units-actions" class="form-inline <?=($this->inventory ? '' : 'hide')?>">

<div class="ui buttons">

<!--
$('#unit-actions .ui.dropdown').dropdown({action:'hide'});
-->
<?php if ($this->sourceType == Genesis_Entity_Source::ID_MANUAL) { ?>
    <div class="ui secondary button" id="add-unit"><i class="add icon"></i> Add Unit</div>
<?php } ?>
    <div class="ui secondary dropdown button toggled-actions disabled">
        <i class="tag icon"></i>
        Discounts
        <i class="dropdown icon"></i>
        <div class="menu">
        <?php foreach ($this->defaultSpecials as $special):
            if (! $special->isDiscount()) { continue; } ?>
            <div class="item special-menu-option"><a id="special-<?=$special->getId()?>"><?=$special->getString()?></a></div>
        <?php endforeach; ?>
        <?php if ($this->customDiscounts && sizeof($this->customDiscounts) > 0): ?>
            <div class="divider js-custom-discounts-start"></div>
            <?php foreach ($this->customDiscounts as $special): ?>
                <div class="item special-menu-option"><a id="special-<?=$special['id']?>"><?=$special['name']?></a></div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="divider js-custom-discounts-start" style="display:none;"></div>
        <?php endif; ?>
            <div class="divider js-custom-discounts-end"></div>
            <div class="item new-custom-discount"><a href="#"><i class="fa fa-plus"></i> New Custom Discount</a></div>
            <div class="divider"></div>
            <div class="item special-delete" rootType="<?=Genesis_Entity_Special::TYPE_DISCOUNT_DOLLAR?>"><a href="#"><i class="fa fa-times"></i> Remove Discount</a></div>
        </div>
    </div>
    <div class="ui secondary dropdown button toggled-actions disabled">
        <i class="star icon"></i>
        Promos
        <i class="dropdown icon"></i>
        <div class="menu">
        <?php foreach ($this->defaultSpecials as $special):
            if (! $special->isPromo()) continue; ?>
            <div class="item special-menu-option"><a id="special-<?=$special->getId()?>"><?=$special->getString()?></a></div>
        <?php endforeach;?>
        <?php if ($this->customPromos && sizeof($this->customPromos) > 0): ?>
            <div class="divider js-custom-promos-start"></div>
            <?php foreach ($this->customPromos as $special): ?>
                <div class="item special-menu-option"><a id="special-<?=$special['id']?>"><?=$special['name']?></a></div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="divider js-custom-promos-start" style="display:none;"></div>
        <?php endif; ?>
            <div class="divider js-custom-promos-end"></div>
            <div class="item new-custom-promo"><a href="#"><i class="fa fa-plus"></i> New Custom Promo</a></div>
            <div class="divider"></div>
            <div class="item special-delete" rootType="<?=Genesis_Entity_Special::TYPE_PROMO_DOLLAR?>"><a href="#"><i class="fa fa-times"></i> Remove Promo</a></div>
        </div>
    </div>
    <div class="ui secondary dropdown button toggled-actions disabled">
        <i class="gift icon"></i>
        Free Items
        <i class="dropdown icon"></i>
        <div class="menu">
            <?php $freeItemTypes = array(Genesis_Entity_Special::TYPE_FREE_ITEM);
            foreach ($this->defaultSpecials as $special):
                if (! in_array($special->getType(), $freeItemTypes)) { continue; } ?>
                <div class="item special-menu-option"><a id="special-<?=$special->getId()?>"><?=$special->getString()?></a></div>
            <?php endforeach;
            if ($this->specials && sizeof($this->specials) > 0): ?>
                <div class="divider"></div>
                <?php foreach ($this->specials as $special):
                    if (! in_array($special->getType(), $freeItemTypes)) { continue; } ?>
                    <div class="item special-menu-option"><a id="special-<?=$special->getId()?>"><?=$special->getString()?></a></div>
                <?php endforeach ?>
            <?php endif; ?>

            <div class="divider"></div>
            <div class="item special-delete" rootType="<?=Genesis_Entity_Special::TYPE_FREE_ITEM?>"><a href="#"><i class="fa fa-times"></i> Remove Free Item</a></div>
        </div>
    </div>
</div>

    </div>
    <div id="unit-actions" class="form-inline hide">
        <div class="btn-group">
            <a class="ui button"
                href="<?=$this->url(['action'=>'units'], 'features')?>?fid=<?=$this->facilityId?>">
                <i class="fa fa-arrow-left"></i> </a>
        </div>
    </div>
</div>

<div id="units-table-container" style="display:none;">
<!--     <div class="form-search">
        <span class="fa fa-search"></span>
        <input class="form-control" type="search" id="search" placeholder="Search units" />
    </div> -->
    <?php if ($this->inventory) : ?>
    <form>
        <div class="table-responsive">
            <table id="units-table" class="ui table sortable cell-headers">
                <thead>
                    <tr>
                        <th class="center on-off-checkbox check-col no-sort">
                            <div class="ui checkbox">
                                <input type="checkbox" name="all" id="check-all" value="all" />
                            </div>
                        </th>
                        <th class="center availability-checkbox no-sort">Available</th>
                        <th>Unit</th>
                        <?=($this->showGrouped) ? '<th># Rentable</th>' : null ?>
                        <th>Price</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($this->inventory as $key => $unit):
                        if($this->facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_MANUAL && !$unit['published'] || count($unit['unitIds']) == 0){ continue;} ?>
                        <tr id="<?=$key?>" data-unitindex="<?=$key?>" class="<?=($unit['active'] == 0) ? 'disabled-row' : 'enabled-row' ?>">
                            <td class="center on-off-checkbox js-unit-checkbox" id="<?=$unit['unitIds'][0]?>">
                                <!-- <label for="selectbox-<?=$key?>" class="on-off-checkbox-label"> -->
                                <div class="ui checkbox">
                                    <input type="checkbox" id="selectbox-<?=$key?>" class="js-unit-selector" name="on-off" />
                                </div>
                                <!-- </label> -->
                            </td>
                            <td class="center availability-checkbox">
                                <div class="ui checkbox">
                                    <input type="checkbox" name="listing" id="toggle-<?=$key?>"
                                    value="<?=$key?>" <?=($unit['active']?' checked="checked"':'')?> />
                                </div>
                            </td>
                            <td data-sort-value="<?= $unit['width'] * $unit['length'] ?>">
                                <div class="js-unit-loading unit-loading loading pull-right" style="display:none;"></div>
                                <strong><span><?=str_replace(' ','&nbsp;',$unit['dimensions'])?> <?=$unit['type']?></span></strong><br />
                                <span class="minor"><?=$unit['amenities']?></span>
                                <span class="js-special-string minor units-special"><?=(! empty($unit['specialString'])) ? $unit['specialString'] : null ?></span>
                            </td>
                            <?=($this->showGrouped) ? '<td>'.$unit['numRentable'].'</td>' : null ?>
                            <td data-sort-value="<?= ($unit['sparefootPrice'] ? $unit['sparefootPrice'] : $unit['standardRate']) ?>">
                                <?php if($unit['sparefootPrice'] && $unit['standardRate'] > $unit['sparefootPrice']) { ?>
                                    <span class="js-reg-price strikethrough">$<?=number_format($unit['standardRate'], 2)?></span><br/>
                                    <span class="js-sale-price">$<?=number_format($unit['sparefootPrice'], 2)?></span>
                                <?php } else { ?>
                                    <span class="js-reg-price">$<?=number_format($unit['standardRate'], 2)?></span><br/>
                                    <span class="js-sale-price"></span>
                                <?php } ?>
                                <span class="js-discount-string minor units-special"><?=(! empty($unit['discountString'])) ? $unit['discountString'] : null ?></span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?=$this->partial('facility/add-more-units.phtml', ['units'=> count($this->inventory), 'facility'=>$this->facility])?>
        </div>
    </form>
    <?php elseif ($this->unpublishedUnits) : ?>
        <br /><p>You currently have no vacant units in inventory. If you believe this is a mistake, please contact <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    <?php else : ?>
        <br />
        <div class="jumbo">
            <h2>Next up: List your inventory.</h2>
            <p>Let's add your first unit.</p>
            <a id="add-first-unit" class="huge ui blue button">Add Unit</a>
        </div>
    <?php endif; ?>
</div>

<div id="units-loading">
    <img src="/images/loaders/large.gif" class="loading" />
</div>

<div id="edit-unit-content" style="display:none;">
    <?=$this->partial('inventory/unit-edit.phtml')?>
</div>

<?=$this->partial('facility/hide-facility-reason-modal.phtml')?>
<?=$this->partial('inventory/units_modals.phtml', array('loggedUser' => $this->loggedUser))?>
<?=$this->partial('inventory/units_json.phtml', array('facility' => $this->facility, 'inventory' => $this->inventory, 'sourceType' => $this->sourceType))?>