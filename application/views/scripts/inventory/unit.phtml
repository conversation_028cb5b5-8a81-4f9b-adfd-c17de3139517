<?php
$unitIds = [];
foreach($this->units as $singleUnit) {
    foreach($singleUnit['unitIds'] as $unitId) {
        $unitIds[] = $unitId;
    }
}
$partialName = '';
$viewPayload = array('units' => $this->units, 'facility' => $this->facility, 'unitIds' => $unitIds);
$sourceId = $this->facility->getCorporation()->getSourceId();
//echo '<h3>units in group '.$this->unitGroupCount.'/source ID '.$sourceId.'</h3>';
if ($this->unitGroupCount > 1) {
    switch ($sourceId) {
        case Genesis_Entity_Source::ID_MANUAL:
            $partialName = 'inventory/unit/manual_multi.phtml';
            break;
        case Genesis_Entity_Source::ID_SITELINK:
            $partialName = 'inventory/unit/group3_multi.phtml';
            break;
        case Genesis_Entity_Source::ID_CENTERSHIFT:
        case Genesis_Entity_Source::ID_DOMICO:
            $partialName = 'inventory/unit/group2_multi.phtml';
            break;
        default:
            echo '<h2>Error.</h2><p>You cannot edit more than one unit at a time.</p>';
    }
} else {
    switch ($sourceId) {
        case Genesis_Entity_Source::ID_MANUAL:
            $partialName = 'inventory/unit/manual_single.phtml';
            break;
        case Genesis_Entity_Source::ID_SITELINK:
            $partialName = 'inventory/unit/group3_single.phtml';
            break;
        case Genesis_Entity_Source::ID_CENTERSHIFT:
        case Genesis_Entity_Source::ID_DOMICO:
            $partialName = 'inventory/unit/group2_single.phtml';
            break;
        default:
            $partialName = 'inventory/unit/group1_single.phtml';
    }
}

// Render the Template
if ($partialName) {
    echo $this->partial($partialName, $viewPayload);
}?>
<script type="text/javascript">
    var controller = '/features/';
</script>
