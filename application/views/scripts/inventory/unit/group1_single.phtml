<?php
$unitIds = $this->unitIds;
$unit = $this->units[0];
echo $this->partial(
    'inventory/unit/header-partial.phtml', array('unit' => $unit, 'units' => $this->units)
)?>
<form class="form-horizontal group1_single" id="unit-form">
    <p class="alert alert-danger hide"></p>

    <input type="hidden" name="change_unit_type" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label" for="unit-type">Space Type</label>
        <div class="col-md-10 faux-control-label">
            <?php switch($unit['typeNum']) {
                case Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                    echo 'Parking Space';
                    break;
                case Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                    echo 'Office Space / Warehouse';
                    break;
                case Genesis_Entity_StorageSpace::TYPE_WINE:
                    echo 'Wine Storage';
                    break;
                case Genesis_Entity_StorageSpace::TYPE_LOCKER:
                    echo 'Locker';
                    break;
                case Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                    echo 'Outdoor Space';
                    break;
                default:
                    echo 'Self-Storage Unit';
            }?>
        </div>
    </div>

    <input type="hidden" name="change_unit_dimensions" value="1" />
    <div class="form-group">
        <label class="col-md-2 control-label">Dimensions</label>
        <div class="col-md-10">
            <div class="row">
                <div class="col-sm-4">
                    <div class="faux-control-label"><?=$unit['width']?> ft wide</div>
                </div>
                <div class="col-sm-4">
                    <div class="faux-control-label"><?=$unit['length']?> ft long</div>
                </div>
                <div class="col-sm-4">
                    <div class="faux-control-label"><?=$unit['height']?> ft long</div>
                </div>
            </div>
        </div>
    </div>


    <input type="hidden" name="change_standard_rate" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label" for="standard-rate">Standard Rate</label>
        <div class="col-md-10 faux-control-label">
            $<?=$unit['standardRate']?>
        </div>
    </div>

    <?php if ($unit['hasDiscountSpecial']): ?>
        <div class="form-group">
            <label class="col-md-2 control-label">Customer Rate</label>
            <div class="col-md-10">
                $<?=$unit['sparefootPrice']?>
            </div>
        </div>
    <?php endif; ?>

    <hr />

    <input type="hidden" name="change_amenities" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label">Amenities</label>
        <div class="col-md-10">
            <label for="power" class="checkbox"><input type="checkbox" name="power" id="power" <?=($unit['power'])?'checked="checked" ':''?>disabled="disabled" />Power Outlet</label>
            <label for="alarm" class="checkbox"><input type="checkbox" name="alarm" id="alarm" <?=($unit['alarm'])?'checked="checked" ':''?>disabled="disabled" />Alarm</label>
            <label for="unit-lights" class="checkbox"><input type="checkbox" name="unit_lights" id="unit-lights" <?=($unit['unitLights'])?'checked="checked" ':''?>disabled="disabled" />Light in Unit</label>
            <label for="shelves-in-unit" class="checkbox"><input type="checkbox" name="shelves_in_unit" id="shelves-in-unit" <?=($unit['shelvesInUnit'])?'checked="checked" ':''?>disabled="disabled" />Shelves in Unit</label>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_heating_cooling" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label">Heating &amp; Cooling</label>
        <div class="col-md-10">
            <label for="climate-controlled" class="checkbox"><input type="checkbox" name="climate_controlled" id="climate-controlled" <?=($unit['climateControlled'])?'checked="checked" ':''?>disabled="disabled" />Climate Controlled</label>
            <label for="humidity-controlled" class="checkbox"><input type="checkbox" name="humidity_controlled" id="humidity-controlled" <?=($unit['humidityControlled'])?'checked="checked" ':''?>disabled="disabled" />Humidity Controlled</label>
            <label for="air-cooled" class="checkbox"><input type="checkbox" name="air_cooled" id="air-cooled" <?=($unit['airCooled'])?'checked="checked" ':''?>disabled="disabled" />Air Cooled</label>
            <label for="heated" class="checkbox"><input type="checkbox" name="heated" id="heated" <?=($unit['heated'])?'checked="checked" ':''?>disabled="disabled" />Heated</label>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_access" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label">Access</label>
        <div class="col-md-10">

            <?php if($this->facility->getTwentyFourHourAccess() && in_array(Genesis_Entity_FacilityData::SPECIFIC_UNITS_24_HOUR_ACCESS, $this->facility->getTwentyFourHourAccessSupplemental())){ ?>
                <label for="twenty-four-hour-access" class="checkbox"><input type="checkbox" name="twenty_four_hour_access" id="twenty-four-hour-access" <?=($unit['twentyFourHourAccess'])?'checked="checked" ':''?>disabled="disabled" />24 Hour Access</label>
            <?php } ?>

            <label for="stacked" class="checkbox" id="stacked-span"><input type="checkbox" name="stacked" id="stacked" <?=($unit['stacked'])?'checked="checked" ':''?>disabled="disabled" />Stacked Space</label>

            <label for="basement" class="checkbox"><input type="checkbox" name="basement" id="basement" <?=($unit['basement'])?'checked="checked" ':''?>disabled="disabled" />Underground Level</label>
            <label for="parking-warehouse" class="checkbox"><input type="checkbox" name="parking_warehouse" id="parking-warehouse" <?=($unit['parkingWarehouse'])?'checked="checked" ':''?>disabled="disabled" />Parking Warehouse</label>
            <label for="pull-thru" class="checkbox"><input type="checkbox" name="pull_thru" id="pull-thru" <?=($unit['pullThru'])?'checked="checked" ':''?>disabled="disabled" />Pull-Thru</label>
        </div>
    </div>

    <?php echo $this->partial('inventory/unit/fields/drive_up.phtml', array('unit'=>$unit, 'disabled'=>false)); ?>

    <div class="form-group">
        <div class="col-md-10 col-md-offset-2">
            How do tenants get to this unit?<br />
                <label for="inside" class="radio"><input type="radio" name="outdoor_access" id="inside" value="0" <?=($unit['outdoorAccess']!==null && !$unit['outdoorAccess'])?'checked="checked" ':''?>/>&nbsp;From an indoor hallway</label>
                <label for="outside" class="radio"><input type="radio" name="outdoor_access" id="outside" value="1" <?=($unit['outdoorAccess'])?'checked="checked" ':''?>/>&nbsp;From outside </label>
        </div>
    </div>

    <div class="form-group">
        <div class="col-md-10 col-md-offset-2">
            Available for vehicle storage?<br />
            <label for="vehicle-yes" class="radio"><input type="radio" name="vehicle" id="vehicle-yes" value="yes" <?=($unit['vehicle']=='yes')?'checked="checked" ':''?>disabled="disabled" />Yes, for storage or vehicles</label>
            <label for="vehicle-only" class="radio"><input type="radio" name="vehicle" id="vehicle-only" value="only" <?=($unit['vehicle']=='only')?'checked="checked" ':''?>disabled="disabled" />Yes, for vehicles only</label>
            <label for="vehicle-no" class="radio"><input type="radio" name="vehicle" id="vehicle-no" value="0" <?=($unit['vehicle']==false)?'checked="checked" ':''?>disabled="disabled" />No</label>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_door_type" value="0" />
    <div class="form-group" id="trdoor">
        <label class="col-md-2 control-label">Door Type</label>
        <div class="col-md-10">
            <label for="door-rollup" class="radio"><input type="radio" name="door_type" id="door-rollup" value="ROLL_UP" <?=($unit['doorType']=='roll_up')?'checked="checked" ':''?>disabled="disabled" />&nbsp;Roll-up Door</label>
            <label for="door-swing" class="radio"><input type="radio" name="door_type" id="door-swing" value="SWING" <?=($unit['doorType']=='swing')?'checked="checked" ':''?>disabled="disabled" />&nbsp;Swing Door</label>
            <label for="door-none" class="radio"><input type="radio" name="door_type" id="door-none" value="NONE" <?=($unit['doorType']=='none')?'checked="checked" ':''?>/disabled="disabled" >&nbsp;None</label>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_floor" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label" for="floor">Floor</label>
        <div class="col-md-10 faux-control-label">
            #<?=$unit['floor']?>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_covered" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label">Location</label>
        <div class="col-md-10">
            <label for="location-covered" class="radio-inline"><input type="radio" name="covered" id="location-covered" value="1" <?=($unit['covered'])?'checked="checked" ':''?>disabled="disabled" />Covered</label>
            <label for="location-uncovered" class="radio-inline"><input type="radio" name="covered" id="location-uncovered" value="0" <?=($unit['covered']!==null && !$unit['covered'])?'checked="checked" ':''?>disabled="disabled" />Uncovered</label>

            <label for="premium" class="checkbox"><input type="checkbox" name="premium" id="premium" <?=($unit['premium'])?'checked="checked" ':''?>disabled="disabled" />Premium Location</label>
            <label for="ada" class="checkbox"><input type="checkbox" name="ada" id="ada" <?=($unit['ada'])?'checked="checked" ':''?>disabled="disabled" /><abbr title="Americans with Disabilities Act">ADA</abbr> Accessible</label>
        </div>
    </div>

    <div class="form-actions">
        <?php foreach($unitIds as $unitId){ ?>
            <input type="hidden" name="unit_ids[]" value="<?=$unitId?>" />
        <?php } ?>
        <div class="col-md-offset-2">
            <a href="/inventory/units" class="ui basic button" id="unit-cancel">Cancel</a>&nbsp;
            <input type="submit" class="ui primary button" data-loading-text="Saving" data-complete-text="Saved" value="Save" id="unit-save" />
            <img src="/images/loaders/small.gif" class="loading hide" />
        </div>
    </div>
</form>
