<?php if (count($unitIds) == 0): ?>
    <h3 id="unit-header">Add Unit</h3>
    <p>Set the appropriate attributes and remember to save your changes.</p>
<?php elseif ($this->facility->canGroupUnits()): ?>
    <h3 id="unit-header">Edit Units</h3>
    <p>Set the appropriate attributes and remember to save your changes.</p>
    <h4><?=$this->unit['classType']?></h4>

    <p>
        <?php for ($i=0; $i < count($this->units); $i++): ?>
            <a class="edit-single-unit" href="#<?=$this->units[$i]['unitIds'][0]?>"><?=$this->units[$i]['unitName']?></a><?=($i<count($this->units)-1 && $i > 0)?',':''?>
        <?php endfor; ?>
    </p>
<?php else: ?>
    <h3>Edit Unit</h3>
    <p>Set the appropriate attributes and remember to save your changes.</p>
<?php endif; ?>
<br />