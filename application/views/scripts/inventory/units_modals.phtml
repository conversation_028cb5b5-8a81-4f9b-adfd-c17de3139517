<?=$this->partial('inventory/single-unit-confirm-modal.phtml')?>
<?=$this->partial('inventory/all-unit-confirm-modal.phtml')?>
<?=$this->partial('inventory/reactivate-facility-modal.phtml')?>

<div id="delete-special-confirm-modal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure?</h4>
            </div>
            <div class="modal-body">
                <?php if($this->loggedUser->canAccessAllFacilities()){ ?>
                   <p>Deleting this special will remove it from all units it's been applied to.</p>
                   <p>To discuss promotion strategies, give us a call at 855-427-8193.</p>
                   <p>Do you still want to delete this special?</p>
               <?php } else { ?>
                   <p>Clearing this special will remove it from all units it's been applied to.</p>
                   <p>To discuss promotion strategies, give us a call at 855-427-8193.</p>
                   <p>Do you want to clear this special from all your units?</p>
               <?php } ?>
            </div>
            <div class="modal-footer">
                <a class="ui basic button" data-dismiss="modal">No</a>
                <a id="delete-special-confirm-yes" class="ui primary button">Yes</a>
            </div>
        </div>
    </div>
</div>

<form id="new-custom-promo-modal" method="post" action="/inventory/custom-promotion" class="modal js-special-form">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <a class="close" data-dismiss="modal">×</a>
                <h4 class="modal-title">New Custom Promo</h4>
            </div>
            <div class="modal-body">
                <p>Set the appropriate attributes and remember to save your changes.</p><br />

                <div class="form-horizontal">
                    <p class="alert alert-danger hide"></p>

                    <div id="special-promotion-fields">
                        <div class="col-sm-offset-1">
                            <div class="option-container">
                                <div class="radio">
                                    <label><input type="radio" name="promotion_type" value="percent_off" />% Off <span class="minor">&mdash; i.e. 25% off</span></label>
                                </div>
                                <div class="special-fields-more js-special-fields-more well"><br />
                                    <span class="input-group"><input type="text" class="form-control" name="promotion_percent" /><span class="input-group-addon">%</span></span><br />
                                    <label>Months</label>
                                    <div id="promotion-percent-months">
                                        <div class="row">
                                            <div class="col-sm-8">
                                                <label class="promotion-month checkbox" for="promotion-percent-month-0">
                                                    <input type="checkbox" name="promotion_months[]" value="0" id="promotion-percent-month-0" />
                                                    Remaining days this month
                                                </label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-4">
                                            <?php
                                            for($i=1; $i<=12; $i++) {
                                                $buttonName = Genesis_Util_Formatter::numToOrdinal($i);
                                                ?>
                                                <label class="promotion-month checkbox" for="promotion-percent-month-<?=$i?>">
                                                    <input type="checkbox" name="promotion_months[]" value="<?=$i?>" id="promotion-percent-month-<?=$i?>" />
                                                    <?=$buttonName?>
                                                </label>
                                                <?php
                                                if ($i % 4 == 0) {
                                                    ?></div><div class="col-sm-4"><?php
                                                }
                                            }
                                            ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="option-container">
                                <div class="radio">
                                    <label><input type="radio" name="promotion_type" value="dollar_off" />$ Off <span class="minor">&mdash; i.e. $10 off</span></label>
                                </div>
                                <div class="special-fields-more js-special-fields-more well"><br />
                                    <span class="input-group"><span class="input-group-addon">$</span><input type="text" class="form-control" name="promotion_dollar" /></span><br />
                                    <label>Months</label>
                                    <div id="promotion-dollar-months">
                                        <div class="row">
                                            <div class="col-sm-8">
                                                <label class="promotion-month checkbox" for="promotion-dollar-month-0">
                                                    <input type="checkbox" name="promotion_months[]" value="0" id="promotion-dollar-month-0" />
                                                    Remaining days this month
                                                </label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-4">
                                            <?php
                                            for($i=1; $i<=12; $i++) {
                                                $buttonName = Genesis_Util_Formatter::numToOrdinal($i);
                                                ?>
                                                <label class="promotion-month checkbox" for="promotion-dollar-month-<?=$i?>">
                                                    <input type="checkbox" name="promotion_months[]" value="<?=$i?>" id="promotion-dollar-month-<?=$i?>" />
                                                    <?=$buttonName?>
                                                </label>
                                                <?php
                                                if ($i % 4 == 0) {
                                                    ?></div><div class="col-sm-4"><?php
                                                }
                                            }
                                            ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="option-container">
                                <div class="radio">
                                    <label><input type="radio" name="promotion_type" value="dollar_override" />$ Override <span class="minor">&mdash; i.e. $1 move-in special</span></label>
                                </div>
                                <div class="special-fields-more js-special-fields-more well"><br />
                                    <span class="input-group"><span class="input-group-addon">$</span><input type="text" class="form-control" name="promotion_override" /></span><br />
                                    <label>Months</label>
                                    <div id="promotion-override-months">
                                        <div class="row">
                                            <div class="col-sm-8">
                                                <label class="promotion-month checkbox" for="promotion-override-month-0">
                                                    <input type="checkbox" name="promotion_months[]" value="0" id="promotion-override-month-0" />
                                                    Remaining days this month
                                                </label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-4">
                                            <?php
                                            for($i=1; $i<=12; $i++) {
                                                $buttonName = Genesis_Util_Formatter::numToOrdinal($i);
                                                ?>
                                                <label class="promotion-month checkbox" for="promotion-override-month-<?=$i?>">
                                                    <input type="checkbox" name="promotion_months[]" value="<?=$i?>" id="promotion-override-month-<?=$i?>" />
                                                    <?=$buttonName?>
                                                </label>
                                                <?php
                                                if ($i % 4 == 0) {
                                                    ?></div><div class="col-sm-4"><?php
                                                }
                                            }
                                            ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <br />
                    <label>Requires Minimum Lease Length</label>
                    <select name="promotion_restrictions_minimum_lease_length" class="form-control">
                        <option value="0">None</option>
                        <option value="1">1 month</option><option value="2">2 months</option><option value="3">3 months</option>
                        <option value="4">4 months</option><option value="5">5 months</option><option value="6">6 months</option>
                        <option value="7">7 months</option><option value="8">8 months</option><option value="9">9 months</option>
                        <option value="10">10 months</option><option value="11">11 months</option><option value="12">12 months</option>
                    </select>
                    <br/>
                    <label>Requires Pre-Payment</label>
                    <select name="promotion_restrictions_prepaid_months" class="form-control">
                        <option value="0">None</option>
                        <option value="1">1 month</option><option value="2">2 months</option><option value="3">3 months</option>
                        <option value="4">4 months</option><option value="5">5 months</option><option value="6">6 months</option>
                        <option value="7">7 months</option><option value="8">8 months</option><option value="9">9 months</option>
                        <option value="10">10 months</option><option value="11">11 months</option><option value="12">12 months</option>
                    </select>

                    <br />
                    <div id="verbiator-preview" class="well">
                        <h6>Preview</h6>
                        <h4></h4>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <img src="/images/loaders/small.gif" class="loading hide" />&nbsp;
                <a href="/inventory/units" class="ui button" data-dismiss="modal">Cancel</a>
                <input type="submit" class="ui primary button" data-loading-text="Saving" data-complete-text="Saved" value="Save" id="custom-promo-save"/>
            </div>
        </div>
    </div>
</form>

<form id="new-custom-discount-modal" method="post" action="/inventory/custom-discount" class="js-special-form modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <a class="close" data-dismiss="modal">×</a>
                <h4 class="modal-title">New Custom Discount</h4>
            </div>
            <div class="modal-body">
                A customer must be able to rent a unit at the discounted rate for at least six months.
                <div id="special-discount-fields">
                    <div class="col-sm-offset-1">
                        <div class="option-container">
                            <div class="radio">
                                <label><input type="radio" name="discount_type" value="percent_off" />% Off</label>
                            </div>
                            <div class="special-fields-more js-special-fields-more">
                                <br />
                                <span class="input-group">
                                    <input type="text" class="form-control" name="discount_percent" value="" /><span class="input-group-addon">%</span>
                                </span><br /><br />
                            </div>
                        </div>
                        <div class="option-container">
                            <div class="radio">
                                <label><input type="radio" name="discount_type" value="dollar_off" />$ Off</label>
                            </div>
                            <div class="special-fields-more js-special-fields-more">
                                <br />
                                <span class="input-group">
                                    <span class="input-group-addon">$</span><input type="text" class="form-control" name="discount_dollar" value="" />
                                </span><br /><br />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <img src="/images/loaders/small.gif" class="loading hide" />&nbsp;
                <a href="/inventory/units" class="ui basic button" data-dismiss="modal">Cancel</a>
                <input type="submit" class="ui button primary" data-loading-text="Saving" data-complete-text="Saved" value="Save" id="custom-discount-save"/>
            </div>
        </div>
    </div>
</form>

