<script type="text/javascript">
    var sourceType = '<?=$this->sourceType;?>';
    var facilityId = '<?=$this->facility->getId()?>';

<?php if($this->inventory){

    $unitInfo = array();

    foreach($this->inventory as $key => $unit) {

        $unitInfo[$key] = array(
            "unitIds"           => $unit['unitIds'],
            "active"            => $unit['active']
        );
    }
    ?>

    var units = <?=json_encode($unitInfo)?>;

<?php } else { ?>
    var units = [];
<?php } ?>
</script>
