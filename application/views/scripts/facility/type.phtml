<script>
    var issnFeature = <?= Genesis_Service_Feature::isActive('myFootIssnIntegrationJSFrontend',array('account_id'=>$this->accountId)) ? "true" : "false" ?>;
</script>
<div class="setup-content-container">

    <div class="setup-content">

        <div class="content-row">
            <h2>Add a facility from an existing integration</h2>
            <p>Listed below are connections we have with your current facility software integrations. You can add a facility from an existing integration by selecting the facility software you would like to re-sync and clicking "next".</p>
        </div>

            <div class="input-row">
                <?php //for each existing integration reveal an option
                $showEss = true;
                $showManual = true;

                foreach ($this->corporations as $corp) {
                    switch($corp->getSourceId()) {
                        case Genesis_Entity_Source::ID_SITELINK:
                            ?>
                                <div class="control-group">
                                    <div class="radio">
                                        <label for="<?=$corp->getId()?>-option">
                                            <input type="radio" id="<?=$corp->getId()?>-option" name="option" class="input radio" onClick="$('#corpId').val('<?=$corp->getId()?>');$('#integrationType').val('');" /><img src="/images/sitelink.png" id="<?=$corp->getId()?>" /> (corp code: <?=$corp->getSitelinkCorpCode()?>)
                                        </label>
                                    </div>
                                    <br />
                                </div>
                            <?php
                            break;
                        case Genesis_Entity_Source::ID_CENTERSHIFT4:
                            ?>
                                <div class="control-group">
                                    <div class="radio">
                                        <label for="<?=$corp->getId()?>-option">
                                            <input type="radio" id="<?=$corp->getId()?>-option" name="option" class="input radio" onClick="$('#corpId').val('<?=$corp->getId()?>');$('#integrationType').val('');"/><img src="/images/centershift.png" id="<?=$corp->getId()?>" /> 4.0 (username: <?=$corp->getCsUsername()?>)
                                            </label>
                                    </div>
                                    <br />
                                </div>
                            <?php
                            break;
                        case Genesis_Entity_Source::ID_QUIKSTOR:
                            ?>
                                <div class="control-group">
                                    <div class="radio">
                                        <label for="<?=$corp->getId()?>-option">
                                            <input type="radio" id="<?=$corp->getId()?>-option" name="option" class="input radio" onClick="$('#integrationType').val('qs');$('#corpId').val('');"/><img src="/images/quikstor.png" id="<?=$corp->getId()?>" />
                                            </label>
                                    </div>
                                    <br />
                                </div>
                            <?php
                            break;
                        case Genesis_Entity_Source::ID_SELFSTORAGEMANAGER:
                            ?>
                                <div class="control-group">
                                    <div class="radio">
                                        <label for="<?=$corp->getId()?>-option">
                                            <input type="radio" id="<?=$corp->getId()?>-option" name="option" class="input radio" onClick="$('#integrationType').val('<?=Genesis_Entity_Source::ID_SELFSTORAGEMANAGER?>');$('#corpId').val('');"/><img src="/images/selfstoragemanager.png" id="<?=$corp->getId()?>" />
                                            </label>
                                    </div>
                                    <br />
                                </div>
                            <?php
                            break;
                        case Genesis_Entity_Source::ID_STOREDGE:
                            ?>
                                <div class="control-group">
                                    <div class="radio">
                                        <label for="<?=$corp->getId()?>-option">
                                            <input type="radio" id="<?=$corp->getId()?>-option" name="option" class="input radio" onClick="$('#corpId').val('<?=$corp->getId()?>');$('#integrationType').val('');"/><img src="/images/se-logo.svg" style="width: 120px;" id="<?=$corp->getId()?>" />
                                        </label>
                                    </div>
                                    <br />
                                </div>
                            <?php
                            break;
                        //Manual option only needs to show once because an existing corp id is irrelevant for this option
                        case Genesis_Entity_Source::ID_MANUAL:
                            if($showManual == true) { ?>
                                <div class="control-group">
                                    <div class="radio">
                                        <label for="<?=$corp->getId()?>-option">
                                            <input type="radio" id="<?=$corp->getId()?>-option" name="option" class="input radio" onClick="$('#integrationType').val('man_exist');$('#corpId').val('');"/>I will enter it on my own
                                            </label>
                                    </div>
                                    <br />
                                </div>

                                <?php
                                $showManual = false;
                            }
                            break;
                        //We will have a one to one relationship for corp -> facility
                        //Only want to display ESS option once without the corp id
                        case Genesis_Entity_Source::ID_ESS:
                            if($showEss == true) { ?>
                                <div class="control-group">
                                    <div class="radio">
                                        <label>
                                            <input id="ess" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('39');$('#integrationType').change();$('#corpId').val('');">
                                            <img src="/images/ess-logo.png" style="width: 120px;" alt="Onboard">
                                        </label>
                                    </div>
                                    <br/>
                                </div>

                                <?php
                                $showEss = false;
                            }
                            break;
                        default:
                            ?>
                                <div class="control-group">
                                    <div class="radio">
                                        <label for="<?=$corp->getId()?>-option">
                                            <input type="radio" id="<?=$corp->getId()?>-option" name="option" class="input radio" onClick="$('#integrationType').val('other');$('#corpId').val('');"/><?=$corp->getSource()->getName()?> (#<?=$corp->getId()?>)
                                        </label>
                                    </div>
                                    <br/>
                                </div>
                            <?php
                            break;
                    } ?>
                <?php } ?>
            </div>

            <div class="content-row">
                <h2>Create a new integration</h2>
                <p>To add a facility from a new software product, please select that product below and continue.</p>
            </div>

            <div class="input-row">
                <div class="control-group">
                    <div class="radio">
                        <label>
                            <input id="centershift4" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('20');$('#integrationType').change();$('#corpId').val('');">
                            <img src="/images/centershift-4.png" alt="Centershift 4">
                        </label>
                    </div>
                    <br/>
                </div>
                <div class="control-group">
                    <div class="radio">
                        <label>
                            <input id="quickstor" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('10');$('#integrationType').change();$('#corpId').val('');">
                            <img src="/images/quikstor.png" alt="QuikStor">
                        </label>
                    </div>
                    <br/>
                </div>
                <div class="control-group">
                    <div class="radio">
                        <label>
                            <input id="selfstoragemanager" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('19');$('#integrationType').change();$('#corpId').val('');">
                            <img src="/images/selfstoragemanager.png" alt="SelfStorageManager">
                        </label>
                    </div>
                    <br/>
                </div>
                <div class="control-group">
                    <div class="radio">
                        <label>
                            <input id="sitelink" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('7');$('#integrationType').change();$('#corpId').val('');">
                            <img src="/images/sitelink-web-edition.png" alt="SiteLink">
                        </label>
                    </div>
                    <br/>
                </div>
                <div class="control-group">
                    <div class="radio">
                        <label>
                            <input id="storedge" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('36');$('#integrationType').change();$('#corpId').val('');">
                            <img src="/images/se-logo.svg" style="width: 120px;" alt="Onboard">
                        </label>
                    </div>
                    <br/>
                </div>

                <div class="control-group">
                    <div class="radio">
                        <label>
                            <input id="ess" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('39');$('#integrationType').change();$('#corpId').val('');">
                            <img src="/images/ess-logo.png" style="width: 120px;" alt="Onboard">
                        </label>
                    </div>
                    <br/>
                </div>

                <div class="control-group">
                    <div class="radio">
                        <label>
                            <input id="other" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('27');$('#integrationType').change();$('#corpId').val('');">
                            Other software
                        </label>
                    </div>
                    <br/>
                </div>
                <div class="control-group">
                    <div class="radio" >
                        <label>
                            <input id="manual" type="radio" name="option" class="input radio" onclick="$('#integrationType').val('30');$('#integrationType').change();$('#corpId').val('');">
                            I don't use software
                        </label>
                    </div>
                    <br/>
                </div>

            </div>
            <div class="issn_usage" style="display:none">
                <div class="content-row">
                    <h2>Do you currently use ISSN software?</h2>
                </div>
                <div class="input-row">
                    <div class="control-group">
                        <div class="radio">
                            <label>
                                <input type="radio" name="option" class="input radio" onclick="$('#usesIssn').val('1');">
                                Yes
                            </label>
                        </div>
                        <br/>
                    </div>
                    <div class="control-group">
                        <div class="radio">
                            <label>
                                <input type="radio" name="option" class="input radio" onclick="$('#usesIssn').val('0');">
                                No
                            </label>
                        </div>
                        <br/>
                    </div>
                    <div class="control-group">
                        <div class="radio">
                            <label>
                                <input type="radio" name="option" class="input radio" onclick="$('#usesIssn').val('0');">
                                I don't know
                            </label>
                        </div>
                        <br/>
                    </div>
                </div>
            </div>
            <div class="content-footer">
                <form id="target" class="pull-right" action="<?=$this->url(['action'=>'selectionhandler'], 'features')?>" method="post">
                    <input type="hidden" name="integrationType" id="integrationType" value=""/>
                    <input type="hidden" name="corpId" id="corpId" value=""/>
                    <input type="hidden" name="usesIssn" id="usesIssn" value=""/>

                    <div class="pull-right">
                    <img src="/images/loaders/large.gif" class="loading hide" />&nbsp;&nbsp;
                    <input id="next" class="large ui blue button" type="submit" value="Next" data-loading-text="Syncing" />
                    </div>
                    <p class="loading hide">We're connecting to your software's servers now.<br />This could take a minute.</p>
                </form>
                <a class="large ui basic button" href="<?=$this->url(['action'=>'units'], 'features')?>?fid=<?=$this->facilityId?>">Back</a>

            </div>

    </div>

</div>
