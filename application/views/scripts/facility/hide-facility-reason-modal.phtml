<div id="hide-single-facility-confirm-modal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal" id="hide-facility-modal-close">×</button>
                <h4 class="modal-title" id="hide-modal-title">Why do you want to turn this facility off?</h4>
            </div>
            <div class="modal-body">
                <!--  sf-9642 Manual facilities COVID message -->
                <?php if(AccountMgmt_Service_User::isFeatureActive('myfoot.covid_closure_modal')): ?>
                    <section class="covid-container">
                        <div>
                            <img src="/images/ic_alert.svg" class="covid-image">
                        </div>
                        <div>
                            <div class="covid-description">
                                We recommend creating a custom closure instead of hiding any of your inventory in the event of any COVID-19 pandemic response.
                            </div>
                            <div class="covid-link-container">
                                <a class="covid-link" href="https://support.sparefoot.com/hc/en-us/articles/*********" target="_blank">
                                    Learn how to set custom closures.
                                </a>
                                ➤
                            </div>
                        </div>
                    </section>
                <?php endif ?>
                <form id="hide_reasons" method="POST">
                    <div id="hide-facility-reason-div">
                        <p>Please note that as long as your facility is hidden you will not receive reservations from SpareFoot.</p>
                        <label><input type="radio" name="hide_facility_reason" value="This facility is 100% full" id="hide-reason-full"> This facility is 100% full</label><br/>
                        <label><input type="radio" name="hide_facility_reason" value="This facility is temporarily closed" id="hide-reason-closed"> This facility is temporarily closed</label><br/>
                        <label><input type="radio" name="hide_facility_reason" value="I'm holding these units for non-SpareFoot customers" id="hide-reason-non-sparefoot"> I'm holding these units for non-SpareFoot customers</label><br/>
                        <label><input type="radio" name="hide_facility_reason" value="I'm unhappy with SpareFoot" id="hide-reason-unhappy"> I'm unhappy with SpareFoot</label><br/>
                        <label><input type="radio" name="hide_facility_reason" value="This facility is no longer owned/managed by my company." id="hide-reason-sold-facility"> This facility is no longer owned/managed by my company</label><br/>
                    </div>
                    <div id="hide-facility-how-full-div" style="display:none">
                        <label><input type="radio" name="hide_facility_how_full" value="95-100%"> 95-100%</label><br/>
                        <label><input type="radio" name="hide_facility_how_full" value="90-95%"> 90-95%</label><br/>
                        <label><input type="radio" name="hide_facility_how_full" value="85-90%"> 85-90%</label><br/>
                        <label><input type="radio" name="hide_facility_how_full" value="Less than 85%"> Less than 85%</label>
                    </div>
                    <div id="hide-facility-when-can-reactivate-div" style="display:none">
                        <label><input type="radio" name="reactivation_select" id="set_reactivation_date" value="yes"> Set a date for SpareFoot to turn your facility back on (and we'll send you a reminder).</label>
                        <br>
                        <div class="customer-reason-specifics">
                            <input type="text" id="reactivation_date" name="reactivation_date" readonly="readonly" class="form-control datepicker-field" />
                        </div>
                        <br>
                        <label><input type="radio" name="reactivation_select" id="reactivation_decide_later" checked="checked" value="no"> I'll decide later</label>
                    </div>
                    <div id="why-unhappy-div" style="display:none">
                        <p>We appreciate your comments. We're listening, and will use your feedback to improve SpareFoot.</p>
                        <textarea id="why-unhappy-textarea" name="hide_facility_unhappy_because" class="form-control" rows="10" ></textarea><br/>
                       <p>Please note that hiding your facility is not the same as canceling your account. If you indicate here that you'd like to cancel, we'll follow up with you.</p>
                    </div>
                    <div id="new-facility-contact-div" style="display:none">
                        <textarea id="new-facility-contact-textarea" name="hide_facility_new_owner" class="form-control" rows="5" ></textarea>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <a class="ui button primary pull-right" id="hide-facility-next-step" style="display:none">Next</a>
                <a class="ui button pull-left"  id="hide-facility-prev-step" style="display:none">Back</a>
                <!-- Each submit button has different functionality in the javascript code -->
                <a class="ui button primary pull-right" id="submit-hide-facility" style="display:none">Submit</a>
                <a class="ui button primary pull-right" id="submit-hide-all-facilities" style="display:none" >Submit</a>
                <a class="ui button primary pull-right" id="submit-hide-toggle-last-unit" style="display:none" >Submit</a>
                <a class="ui button primary pull-right" id="submit-hide-toggle-all-units" style="display:none" >Submit</a>
            </div>

        </div>
    </div>
</div>
