<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'hours', 'loggedUser' => $this->loggedUser, 'facility' => $this->facility))?>
<?php
    $appointmentOnlyOfficeHours = $this->facility->getAppointmentOnlyOfficeHours();
    $daysOfTheWeek = Genesis_Entity_FacilityHours::$DAYS_OF_THE_WEEK;

    function isAndInArray($needle, $array) {
        if (is_array($array)
			&& in_array($needle, $array)
		) {
        	return 'checked="checked"';
		}
		return '';
	}
?>

<form id="hours-form" class="ui form form-horizontal" method="post" action="<?=$this->url(['action'=>'hours'], 'features')?>?fid=<?=$this->facilityId?>">
    <?php if($this->alert): ?>
        <p class="alert<?=($this->alertClass?' '.$this->alertClass:'')?>">
            <?=$this->alert?>
        </p>
    <?php endif ?>

    <img id="loading-spinner" src="/images/loading.gif" width="15" height="15" style="display:none" />

    <h4>Observed Holidays</h4>
    <div class="ui grid holidays-section">
        <div class="two wide column">
        </div>
        <div class="four wide column">
            <div class="ui checkbox">
                <input class="holiday" id="newYearsDay" name="observed[newYearsDay]" type="checkbox"<?=(in_array('newYearsDay', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>New Year's Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="mlkDay" name="observed[mlkDay]" type="checkbox"<?=(in_array('mlkDay', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>Martin Luther King, Jr. Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="presidentsDay" name="observed[presidentsDay]" type="checkbox"<?=(in_array('presidentsDay', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>Presidents' Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="memorialDay" name="observed[memorialDay]" type="checkbox"<?=(in_array('memorialDay', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>Memorial Day</label>
            </div>
        </div>
        <div class="four wide column">
            <div class="ui checkbox">
                <input class="holiday" id="independenceDay" name="observed[independenceDay]" type="checkbox"<?=(in_array('independenceDay', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>Independence Day</label>
            </div>
            <div class="ui checkbox bootstrap-aligned">
                <input class="holiday" id="laborDay" name="observed[laborDay]" type="checkbox"<?=(in_array('laborDay', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>Labor Day</label>
            </div>
            <div class="ui checkbox bootstrap-aligned">
                <input class="holiday" id="columbusDay" name="observed[columbusDay]" type="checkbox"<?=(in_array('columbusDay', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>Columbus Day</label>
            </div>
            <div class="ui checkbox bootstrap-aligned">
                <input class="holiday" id="easterDay" name="observed[easter]" type="checkbox"<?=(in_array('easter', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>Easter</label>
            </div>
        </div>
        <div class="four wide column">
            <div class="ui checkbox">
                <input class="holiday" id="veteransDay" name="observed[veteransDay]" type="checkbox"<?=(in_array('veteransDay', $this->observedHolidays) ? 'checked="checked"' : '')?>/>
                <label>Veterans Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="thanksgivingDay" name="observed[thanksgivingDay]" type="checkbox"<?=(in_array('thanksgivingDay', $this->observedHolidays) ? 'checked="checked"' : '')?>>
                <label>Thanksgiving Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="christmasDay" name="observed[christmasDay]" type="checkbox"<?=(in_array('christmasDay', $this->observedHolidays) ? 'checked="checked"' : '')?>>
                <label>Christmas Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="christmasEve" name="observed[christmasEve]" type="checkbox"<?=(in_array('christmasEve', $this->observedHolidays) ? 'checked="checked"' : '')?>>
                <label>Christmas Eve</label>
            </div>
        </div>
        <div class="two wide column">
            <a class="ui secondary button holidays-select-all" onclick="$('.holiday').attr('checked',true);">Select All</a>
            <a class="ui secondary button holidays-unselect-all" onclick="$('.holiday').attr('checked', false);">Unselect All</a>
        </div>
    </div>

    <hr />

    <h4>Custom Closures</h4>
    <div class="custom-closures-section">
        <div class="form-group">
            <label class="col-md-2 control-label">Closed On</label>
            <p class="col-md-3">
            <span class="input-group">
              <input class="form-control" type="text" value="" name="custom_closure_start" placeholder="Starting">
              <span class="input-group-addon">
                <i class="fa fa-calendar"></i>
              </span>
            </span>
            </p>
            <p class="col-md-3">
            <span class="input-group">
              <input class="form-control" type="text" value="" name="custom_closure_end" placeholder="Ending">
              <span class="input-group-addon">
                <i class="fa fa-calendar"></i>
              </span>
            </span>
            </p>
            <div class="col-md-2">
            <p><button type="button" class="ui secondary button" data-js="add-closure" disabled="">Add</button></p>
            </div>
            <input type="hidden" id="custom_closure" name="custom_closure" value="">
        </div>

        <div class="form-group">
            <label class="col-md-2 control-label">Upcoming Closures</label>
            <div class="col-md-4">
            <ol data-js="upcoming-closures" class="upcoming-closures list-unstyled">
                <li data-js="closure-template" class="hidden">
                    <a class="pull-right" data-id="">
                        <i class="fa fa-times-circle"></i>
                    </a>
                </li>
                <?php foreach($this->upcomingClosures as $closure): ?>
                <li>
                    <a class="pull-right" data-id="<?=$closure['closureId'];?>">
                        <i class="fa fa-times-circle"></i>
                    </a>
                    <?=$closure['dateString'];?>
                </li>
                <?php endforeach; ?>
            </ol>
            <p data-js="no-closures" class="help-block<?php if(count($this->upcomingClosures) > 0):?> hidden<?php endif; ?>">No upcoming closures.</p>
            </div>
        </div>
    </div>

    <hr />

    <h4>Office Hours</h4>
    <div class="ui grid office-hours-section">
        <div class="fourteen wide column">
            <?php foreach ($daysOfTheWeek as $i=>$dayOfTheWeek): ?>
                <?php
                    $shortName = substr($dayOfTheWeek, 0, 3);
                    $lowerName = strtolower($shortName);
                    $startGetter = 'get'.$shortName.'Start';
                    $endGetter = 'get'.$shortName.'End';
                    $hideOfficeHours = (!$this->officeHours->$startGetter() || !$this->officeHours->$endGetter());
                ?>
                <div class="form-group">
                    <label class="col-md-2 control-label"><?=$dayOfTheWeek?></label>
                    <div class="col-md-2">
                        <input id="hrs_o_<?=$lowerName?>_s" name="hrs_o_<?=$lowerName?>_s" class="form-control <?php if ($hideOfficeHours) {?> gray<?php } ?>" type="text" value="<?=$this->officeHours->humanReadable($this->officeHours->$startGetter())?>" <?php if ($hideOfficeHours) {?>disabled<?php } ?>/>
                    </div>
                    <div class="col-md-2">
                        <input id="hrs_o_<?=$lowerName?>_e" name="hrs_o_<?=$lowerName?>_e" class="form-control <?php if ($hideOfficeHours) {?> gray<?php } ?>" type="text" value="<?=$this->officeHours->humanReadable($this->officeHours->$endGetter())?>" <?php if ($hideOfficeHours) {?>disabled<?php } ?>/>
                    </div>
                    <div class="col-md-1">
                        <div id="check_o_<?=$lowerName?>_s" class="ui checkbox bootstrap-aligned">
                            <input name="closed_<?=$lowerName?>" type="checkbox" <?php if ($hideOfficeHours) {?>checked="checked"<?php } ?> />
                            <label>Closed</label>
                        </div>
                    </div>
                    <div class="col-md-2 <?=Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_APPOINTMENT_ONLY) ? '' : 'hide'?>">
                        <div class="ui checkbox appointment-only-<?=$lowerName?>-checkbox">
                            <input id="appointment_only_<?=$lowerName?>" type="checkbox" name="appointment_only_office_hours_<?=$lowerName?>" <?php if ($appointmentOnlyOfficeHours->$lowerName) echo 'checked="checked"'; ?> value="1" onclick ="toggleAppointmentOnlyHours(this,new Array('hrs_o_<?=$lowerName?>_s', 'hrs_o_<?=$lowerName?>_e'),'check_o_<?=$lowerName?>_s')"/>
                            <label for="appointment_only_<?=$lowerName?>">Appointment Only</label>
                        </div>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
        <div class="two wide column">
            <a class="ui secondary button apply-to-all-office-button" onclick="applyToAll('o')">Apply to All</a>
        </div>
    </div>

    <hr />

    <h4>Access Hours</h4>
    <div class="ui grid access-hours-section">
        <div class="row">
            <div class="sixteen wide column">
                <div class="form-group<?=(in_array('twentyfour_hr_access', $this->erroredFields))?' has-error':''?>">
                    <label class="col-md-2 control-label">24-hour access</label>
                    <div class="col-md-10">
                        <div class="radio">
                            <label>
                                <input type="radio" name="twentyfour_hr_access" value="1" id="twentyfour_hr_access_yes" <?php if ($this->facility->getTwentyFourHourAccess() == '1' && $this->facility->getTwentyFourHourAccessRestricted() == '0') { echo 'checked="checked"'; } ?> />Yes, for all units and customers
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" name="twentyfour_hr_access" value="2" id="twentyfour_hr_access_yes_with_restrictions" <?php if ($this->facility->getTwentyFourHourAccess() == '1' && $this->facility->getTwentyFourHourAccessRestricted() == '1') { echo 'checked="checked"'; } ?> />Yes, but with restrictions
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" name="twentyfour_hr_access" value="0" id="twentyfour_hr_access_no" <?php if ($this->facility->getTwentyFourHourAccess() == '0') { echo 'checked="checked"'; } ?> />No
                            </label>
                        </div>
                    </div>
                </div>

                <div class="ui segment" id="show-24-hour-access-type" style="display:<?= $this->facility->getTwentyFourHourAccessRestricted() ? 'block' : 'none'?>;">
                    <div class="form-group">
                        <label class="col-md-2 col-md-offset-1 control-label">Limitations to 24-hour access</label>
                        <div class="col-md-9">
                        <div class="ui form">
                        <div class="grouped fields">
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
										   value="<?=Genesis_Entity_FacilityData::SPECIFIC_UNITS_24_HOUR_ACCESS?>"
                                        <?=isAndInArray(
                                            Genesis_Entity_FacilityData::SPECIFIC_UNITS_24_HOUR_ACCESS,
                                        	$this->facility->getTwentyFourHourAccessSupplemental()
										)?>
									/>
									<label>Specific units (must be applied to individual units in the <a href="<?=$this->url(['action'=>'inventory'], 'features')?>?fid=<?=$this->facilityId?>">Units section</a>)
                                    </label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
										   value="<?=Genesis_Entity_FacilityData::BACKGROUND_CHECK_24_HOUR_ACCESS?>"
										<?=isAndInArray(
											Genesis_Entity_FacilityData::BACKGROUND_CHECK_24_HOUR_ACCESS,
                                            $this->facility->getTwentyFourHourAccessSupplemental()
										)?>
									/>
									<label>Background check</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
										   value="<?=Genesis_Entity_FacilityData::BUSINESS_ACCESS_24_HOUR_ACCESS?>"
                                        <?=isAndInArray(
                                            Genesis_Entity_FacilityData::BUSINESS_ACCESS_24_HOUR_ACCESS,
                                            $this->facility->getTwentyFourHourAccessSupplemental()
                                        )?>
									/>
									<label>Business accounts</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
									<input type="checkbox" name="twentyfour_hr_access_type[]"
										   value="<?=Genesis_Entity_FacilityData::MANAGER_APPROVAL_24_HOUR_ACCESS?>"
										<?=isAndInArray(
											Genesis_Entity_FacilityData::MANAGER_APPROVAL_24_HOUR_ACCESS,
                                            $this->facility->getTwentyFourHourAccessSupplemental()
										)?>
									/>
									<label>Manager approval</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
										id="twentyfour_hour_access_fee_option"
										value="<?=Genesis_Entity_FacilityData::ADDITIONAL_FEES_24_HOUR_ACCESS?>"
                                        <?=isAndInArray(
                                            Genesis_Entity_FacilityData::ADDITIONAL_FEES_24_HOUR_ACCESS,
                                            $this->facility->getTwentyFourHourAccessSupplemental()
                                        )?>
									/>
									<label>Additional fees</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned" id="24hr-fee-input"
									<?=(isAndInArray(
											Genesis_Entity_FacilityData::ADDITIONAL_FEES_24_HOUR_ACCESS,
											$this->facility->getTwentyFourHourAccessSupplemental()
										)
											? ''
											: 'style="display: none;"'
									)?>
								>
                                    <div class="form-group">
                                        <label class="col-md-2 control-label">Additional fee amount</label>
                                        <div class="col-md-10">
                                            <div class="input-group">
                                                <span class="input-group-addon">$</span>
                                                <input type="text" name="twentyfour_hr_access_fee" id="twentyfour_hr_access_fee" class="form-control" value="<?=($this->facility->getTwentyFourHourAccessFee() ? $this->facility->getTwentyFourHourAccessFee() : '')?>" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
									   	value="<?=Genesis_Entity_FacilityData::APPOINTMENT_ONLY_24_HOUR_ACCESS?>"
										<?=isAndInArray(
											Genesis_Entity_FacilityData::APPOINTMENT_ONLY_24_HOUR_ACCESS,
                                            $this->facility->getTwentyFourHourAccessSupplemental()
										)?>/>
                                    <label>By appointment only</label>
                                </div>
                            </div>
                        </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="fourteen wide column">
                <?php foreach ($daysOfTheWeek as $i=>$dayOfTheWeek): ?>
                    <?php
                        $shortName = substr($dayOfTheWeek, 0, 3);
                        $lowerName = strtolower($shortName);
                        $startGetter = 'get'.$shortName.'Start';
                        $endGetter = 'get'.$shortName.'End';
                        $isClosed = (!$this->accessHours->$startGetter() || !$this->accessHours->$endGetter());

                        $hideAccessHours = $this->facility->getTwentyFourHourAccess() == '1' && $this->facility->getTwentyFourHourAccessRestricted() != '1';
                    ?>
                    <div class="form-group js-access-hours">
                        <label class="col-md-2 control-label"><?=$dayOfTheWeek?></label>
                        <div class="col-md-2">
                            <input id="hrs_a_<?=$lowerName?>_s" name="hrs_a_<?=$lowerName?>_s" class="form-control js-access-hours-start <?php if ($hideAccessHours || $isClosed) {?> gray<?php } ?>" type="text" value="<?=$this->accessHours->humanReadable($this->accessHours->$startGetter())?>" <?php if ($hideAccessHours || $isClosed) {?>disabled<?php } ?>/>
                        </div>
                        <div class="col-md-2">
                            <input id="hrs_a_<?=$lowerName?>_e" name="hrs_a_<?=$lowerName?>_e" class="form-control js-access-hours-end <?php if ($hideAccessHours || $isClosed) {?> gray<?php } ?>" type="text" value="<?=$this->accessHours->humanReadable($this->accessHours->$endGetter())?>" <?php if ($hideAccessHours || $isClosed) {?>disabled<?php } ?>/>
                        </div>
                        <div class="col-md-1">
                            <div id="check_a_<?=$lowerName?>_s" class="ui checkbox bootstrap-aligned">
                                <input  type="checkbox" class="js-access-closed" <?php if ($isClosed) {?>checked="checked"<?php } ?> />
                                <label>Closed</label>
                            </div>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
            <div class="two wide column">
                <a class="ui secondary button apply-to-all-access-button" onclick="applyToAll('a')">Apply to All</a>
            </div>
        </div>
    </div>

    <input type="hidden" id="facility_id" name="facility_id" value="<?=$this->facility->getId()?>" />
    <div class="form-actions">
        <div class="right">
            <input class="ui primary button" name="commit" type="submit" value="Save Changes" />
        </div>
    </div>
</form>
