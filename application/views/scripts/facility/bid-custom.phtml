<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'details', 'loggedUser' => $this->loggedUser, 'facility'=>$this->facility))?>

<?php
$isBidTypePercent = ($this->facility->getAccount()->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT);
?>
<script type="text/javascript">
var smallUnitsBidPercent = <?=Genesis_Entity_Transaction::BID_SMALL_PERCENT?>;
var largeUnitsBidPercent = <?=Genesis_Entity_Transaction::BID_LARGE_PERCENT?>;
var updateBidRequest = new XMLHttpRequest();
var updateBidTimeout;
var facilityMinBid = "<?=number_format($this->facility->getMinBid(), 2, '.', '');?>";
var facilityMaxBid = "<?=number_format($this->facility->getMaxBid(), 2, '.', '');?>";
var unitAvgs = <?=json_encode($this->facility->getAvgUnitPrices())?>;
var currentFacilityId = <?=$this->facility->getId()?>;
var bidShiftAmount = <?=$isBidTypePercent ? 0.05: 5?>;
var isBidTypePercent = <?=var_export($isBidTypePercent, true)?>;
</script>

<div class="row header-row">
    <div class="col-md-12">
        <h2>Update Your Bid Modifier</h2>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div id="bid_amount">
            <input type="hidden" id="facility_id" name="facility_id" value="<?=$this->facility->getId()?>" />

            <label for="bid_amount"><strong>Update My Bid</strong></label>
            <div style="z-index:10;">
                <?php if ($this->canChangeBid): ?>
                    <div class="bid-tool">
                        <input id="bid_amount_value" type="number" min="<?=$this->facility->getMinBid()?>" max="<?=$this->facility->getMaxBid()?>" value="<?=$this->facility->getEffectiveBidAmount()?>" />
                        <div id="bid-toggle"><span id="bid-up"></span><span id="bid-down"></span></div>
                    </div>
                <?php else: ?>
                    <div id="bid-amount-display"><?=$this->facility->getEffectiveBidAmount()?></div>
                <?php endif ?>
            </div>
            <div class="clear"></div>
            <?php if ($isBidTypePercent): ?>
                <small>For example the amount on a $50 <br>
                    unit will be <?=number_format($this->facility->getEffectiveBidAmount(),2)?> x $50 = $<?=number_format($this->facility->getEffectiveBidAmount()*50,2)?></small>
            <?php else: ?><!-- TODO REMOVE AFTER CPA TIER IS GONE, AND VARS -->
                <p><strong>Small Units</strong> <small class="minor">&lt; 50 sqft</small><br />
                    <span id="smallUnitsBid">$<?=number_format($this->facility->getBidFlat() * Genesis_Entity_Transaction::BID_SMALL_PERCENT, 2)?></span></p>
                <p><strong>Standard</strong><br />
                    <span id="standardUnitsBid">$<?=number_format($this->facility->getBidFlat(),2)?></span></p>
                <p><strong>Large Units</strong> <small class="minor">&gt;= 100 sqft</small><br />
                    <span id="largeUnitsBid">$<?=number_format($this->facility->getBidFlat() * Genesis_Entity_Transaction::BID_LARGE_PERCENT, 2)?></span></p>
            <?php endif ?>
        </div>

        <div id="submit-button-container">
            <?php if ($this->canChangeBid) { ?>
                <input id="submit_button" class="ui primary button update-bid-btn" name="commit" type="submit" value="Update My Bid" onclick="return saveBid();" /><img src="/images/loading.gif" class="hide" />
            <?php } else { ?>
                Only system administrators are able to change your bid and improve your search ranking. Please contact <?=$this->adminEmails?> to make this change. Thanks!
            <?php } ?>
        </div>
    </div>
    <div class="col-md-7">
        <div class="panel panel-default">
            <div class="panel-body">
                <table id="rankingsTable" class="table bid-opps-table">
                   <tbody class="table-body">
                       <tr class="header-row">
                           <th></th>
                           <th>Search Term</th>
                           <th>Facility Rank</th>
                       </tr>
                       <tr>
                           <th>City</th>
                           <td><?=$this->facility->getLocation()->getCity()?>, <?=$this->facility->getLocation()->getState()?></td>
                           <td class="ranking" data-opptype="city">-</td>
                       </tr>
                       <tr>
                           <th>Zip Code</th>
                           <td><?=$this->facility->getLocation()->getZip()?></td>
                           <td class="ranking" data-opptype="zip">-</td>
                       </tr>
                       <tr>
                           <th>Custom Zip Code</th>
                           <td>
                               <div class="ui action input">
                                   <input type="text" name="custom_zip" id="custom_zip" size="10" maxlength="5" />
                                   <button type="button" id="custom_zip_button" class="ui secondary button" onclick="refreshBid();return false;">View</button>
                               </div>
                           </td><td class="ranking" data-opptype="customZip">-</td>
                       </tr>
                       <tr>
                           <th>
                               Custom City
                               <br />
                               <i><span class="subtext">example: Austin, TX</span></i>
                           </th>
                           <td>
                               <div class="ui action input">
                                   <input type="text" name="custom_city" id="custom_city" size="10" />
                                   <button type="button" id="custom_city_button" class="ui secondary button" onclick="refreshBid();return false;">View</button>
                               </div>
                           </td><td class="ranking" data-opptype="customCity">-</td>
                       </tr>
                   </tbody>
                </table>
            </div>
        </div>

        <?php if($this->algorithms) { ?>
            <div>
                <br/>
                <select id="algorithm" name="algorithm">
                    <?php foreach ($this->algorithms as $class => $name) { ?>
                        <option value="<?=$class?>"<?=($this->algorithm == $class || (!$this->algorithm && $class == Genesis_Entity_Search_Container::DEFAULT_STORAGE_SEARCH)) ? ' selected="selected"' : ''?>><?=($class == Genesis_Entity_Search_Container::DEFAULT_STORAGE_SEARCH ? '[DEFAULT] ' : '').$name?></option>
                    <?php } ?>
                </select>
            </div>
        <?php } ?>
    </div>
</div>

<div id="why-change-bid" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
            <button class="close" data-dismiss="modal">×</button>
                <h4 class="modal-title">Why change your bid?</h4>
            </div>
            <div class="modal-body">
                <img src="/images/bookings_by_rank_chart.gif" width="361" height="289" style="float:right; margin-left:1.667em;" />
                <p>The number of reservations SpareFoot can generate for your facility strongly depends on your rank in our search results. For example, a facility that ranks first will typically get more than four times as many bookings than a facility ranked seventh.</p>
                <p>SpareFoot uses a complex algorithm to determine rank, based on relevance factors such as distance from where the user is searching (closer is better) and unit prices (lower is better). We also consider quality factors such as how often consumers actually reserve a unit after viewing your listing, and the rate at which they move in.</p>
                <p>The amount you are willing to pay for tenants is also a factor— the higher you bid, the better your facility will rank in search results. But note that when your facility’s distance or quality factors are lower or less relevant than local competition, it will generally be more expensive to bid to a higher rank.</p>
            </div>
        </div>
    </div>
</div>
