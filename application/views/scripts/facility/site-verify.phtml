<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'site-verify', 'loggedUser' => $this->loggedUser, 'facility' => $this->facility))?>

<h2>Boost your SpareFoot search ranking for FREE and get MORE reservations!</h2>
<p>Want to increase your ranking in the SpareFoot marketplace and get in front of more customers? All you need is a valid website with a link to your SpareFoot facility details page OR, simply implement our <a href="/widget">booking widget</a>.<br /><br />It's easy. Simply copy the link below and place it on your website. Then, come back here and enter the URL (web address) of the page you placed the link on in the box below and click "check". This lets us verify the link. Once we confirm the link, your placement in SpareFoot search results will be automatically boosted.</p>
<p class="form-section">
    <b>STEP 1: </b> Place the following link to your SpareFoot facility details page on your own website OR, simply implement our <a href="/widget">booking widget</a>.<br/><br/>
    <span class="code">&lt;a href="<?=$this->facility->getFacilityCityPageLink()?>"&gt;<?= $this->facility->getSiteVerifyText() ?>&lt;/a&gt;</span>
</p>

<p class="form-section">
    <b>STEP 2: </b> Publish the change to your website so the link is live.<br/>
</p>

<form name="bid_edit" id="bid_edit" method="post" action="<?=$this->url(['action'=>'checksiteverify'], 'features')?>?fid=<?=$this->facility->getId()?>">
    <div class="form-section">
        <b>STEP 3: </b> Enter the URL (web address) of the page on your website where you published the booking widget or link to your SpareFoot facility details page (<i>ex: http://www.myfacility.com</i>)<br /><br />
        <div class="form-inline">
            <span id="dynamic_notifications"><img src="/images/<?php if ($this->facility->getUrlVerified()) {?>icon_check<?php } else { ?>icon_warn<?php } ?>.png"></span>
            <input size="80" type="text" value="<?=$this->facility->getUrl()?>" id="facility_url" name="facility_url" />
            <a class="ui button" id="check_url">Check</a>
        </div>
        <br/><br/>Note: We confirm this daily.
    </div>
    <input type="hidden" id="facility_id" name="facility_id" value="<?=$this->facility->getId()?>" />
</form>

<p id="verified_msg" class="messages-success" style="margin:1em 1.5em;<?php if (!$this->facility->getUrlVerified()) {?>display: none;<?php }?>">
    Link Verified! Your SpareFoot results placement is currently being boosted.
</p>

<p id="unverified_msg" class="messages-error" style="margin:1em 1.5em;<?php if ($this->facility->getUrlVerified()) {?>display: none;<?php }?>">
    Link NOT PUBLISHED. You are not currently receiving your free SpareFoot placement boost.
</p>