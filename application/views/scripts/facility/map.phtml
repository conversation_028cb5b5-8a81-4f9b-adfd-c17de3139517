<style type="text/css">
#map_canvas, #panorama img {
    border: none !important;
    max-width: none !important;
}
</style>

<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'map', 'loggedUser' => $this->loggedUser, 'facility' => $this->facility))?>

<script type="text/javascript">
var latitude = <?=$this->latitude ?>;
var longitude = <?=$this->longitude ?>;

<?php if ($this->pov) { ?>
var myPov = { heading: <?=$this->pov->getYaw(); ?>, pitch: <?=$this->pov->getPitch(); ?>, zoom:<?=$this->pov->getZoom(); ?> };
<?php } else { ?>
var myPov = {heading:0, pitch:0, zoom: 1.5};
<?php } ?>

</script>

<div class="ui segment basic">
    <h2>Fine tune your street view image</h2>
    <p>We use your facility address to show a street view.  Sometimes it's not perfect so we built a tool that allows you to tune in your street view and orient the camera so that it points at your facility.  Just follow the steps below and your street view picture will be updated on SpareFoot automatically.</p>

    <?php if($this->alert){ ?>
        <div class="ui message <?=($this->alertClass?' '.$this->alertClass : '')?>">
            <?=$this->alert?>
        </div>
    <?php } ?>

    <h6>Step 1</h6>
    <h4>Click your exact location on the map</h4><br />
    <div id="map_canvas" style="width:99.9%; height:300px; border:1px solid #ccc;"></div><br /><br />

    <h6>Step 2</h6>
    <h4>Orient the view to point at your facility</h4><br />
    <div id="panorama" style="width:99.9%; height:300px; border:1px solid #ccc; margin-bottom: 1.5em;"></div>

    <form class="ui right form" method="post" action="<?=$this->url(['action'=>'map'], 'features')?>?fid=<?=$this->facilityId?>">
        <input id="lat" name="lat" type="hidden" />
        <input id="lng" name="lng" type="hidden" />
        <input id="yaw" name="yaw" type="hidden" />
        <input id="pitch" name="pitch" type="hidden" />
        <input id="zoom" name="zoom" type="hidden" />
        <input name="facility_id" type="hidden" value="<?=$this->facility->getId();?>" />
        <button onclick="return updateStreetView();" class="ui primary button">Save Changes</button>
    </form>

</div>

<script type="text/javascript" src="https://maps.google.com/maps/api/js?sensor=false"></script>
