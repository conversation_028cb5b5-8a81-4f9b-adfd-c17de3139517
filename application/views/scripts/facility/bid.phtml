<?php
$maxOpportunities = AccountMgmt_Clients_ClientApiClient::MAX_BID_OPPORTUNITIES_SHOWN;
$isBidTypePercent = ($this->facility->getAccount()->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT);
?>

<script type="text/javascript">
    var smallUnitsBidPercent = <?=Genesis_Entity_Transaction::BID_SMALL_PERCENT?>;
    var largeUnitsBidPercent = <?=Genesis_Entity_Transaction::BID_LARGE_PERCENT?>;
    var updateBidRequest = new XMLHttpRequest();
    var updateBidTimeout;
    var facilityMinBid = "<?=number_format($this->facility->getMinBid(), 2, '.', '');?>";
    var facilityMaxBid = "<?=number_format($this->facility->getMaxBid(), 2, '.', '');?>";
    var unitAvgs = <?=json_encode($this->facility->getAvgUnitPrices())?>;
    var currentFacilityId = <?=$this->facility->getId()?>;
    var bidShiftAmount = <?=$isBidTypePercent ? 0.05 : 5?>;
    var isBidTypePercent = <?=var_export($isBidTypePercent, true)?>;
    var currentTempBid = <?= $this->currentBid ?>;
    var currentSavedBid = <?= $this->currentBid ?>;
    var initialEffectiveBid = <?= $this->currentBid ?>;
    var myfootRole = "<?= $this->loggedUser->getMyfootRole() ?>";
    var canChangeBid = <?= var_export($this->canChangeBid, true) ?>;
    var isBidOptimizerActive = <?= var_export($this->isBidOptimizerActive, true) ?>;
</script>

<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'details', 'loggedUser' => $this->loggedUser, 'facility'=>$this->facility))?>

<div class="bidding-page">
        <?php if($this->alert): ?>
            <p class="ui success message<?=($this->alertClass?' '.$this->alertClass:'')?>"><?=$this->alert?></p>
        <?php endif; ?>
        <div class="bid-menu full-width-row">
            <div class="row">
                <div class="col-md-12">
                    <h2 class="page-header adjust-bid-modifier">
                        Adjust Your Bid Modifier to Get More Leads
                        <a href="<?=Genesis_Util_Url::facilityUrl($this->facility)?>" target="_blank" class="pull-right ui basic secondary button" data-segment-category="adjust bid modifier" data-segment-label="view on sparefoot">View on SpareFoot.com <i class="external icon"></i></a>
                    </h2>
                </div>
            </div>

            <p>Your bid modifier determines what you pay for each SpareFoot customer who moves in. The price per move-in for your facility is your facility’s bid modifier times the list price of the unit reserved, if your account is manual. For accounts integrated with auto-reconciliation, this is the price of the unit when the tenant moves in. For example, if your bid modifier is 2.2 and the unit price is $50, you’d pay SpareFoot a one-time fee of $110 (2.2 x 50).<br /><br />
                Additionally, your bid factors into where your facility ranks on our site. The number of leads SpareFoot can generate for a facility strongly depends on where it ranks in our search results. We know on average facilities that rank first and second get 45% of all clicks! Adjust your bid modifier below to achieve your desired rank by zip code or city in our search results to reach your occupancy goals. <a id="show-why-change-bid" href="https://support.sparefoot.com/hc/en-us/articles/************-All-About-Bidding" data-segment-category="adjust bid modifier" data-segment-label="learn more">Learn more &raquo;</a></p>
             <!-- Added bidoptimizer active check -->        
            <?php if(!$this->isBidOptimizerActive) :?>  
            <h2 class="page-header">Your Current Rankings</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="panel">
                        <div class="panel-body">
                            <?php if (!$this->facility->getActive()): ?>
                                <p>You currently do not appear in the search results for your city or zip because this facility is not accepting reservations.
                                    You can make this facility available for bookings on the SpareFoot network from the
                                    <a href="<?=$this->url(['action'=>'details'], 'features')?>?fid=<?=$this->facility->getId()?>" data-segment-category="current rankings" data-segment-label="view features page">Features page.</a></p>
                            <?php else: ?>
                                <table class="current-rankings-table table">
                                    <tr>
                                        <td>
                                            <b>by Zip Code:</b> <?=$this->facility->getLocation()->getZip()?>
                                        </td>
                                        <td class="ranking">
                                            <?php if (!$this->zipBidOppsErrorMessage): ?>
                                                <?=$this->facilityZipBidOpps['rank']?> of <?=$this->facilityZipBidOpps['num_results']?>
                                            <?php else: ?>
                                                <p><?= $this->zipBidOppsErrorMessage ?></p>
                                            <?php endif ?>
                                        </td>
                                        <td>
                                            <a href="<?=$this->sparefootSearchZipUrl?>" target="_blank" class="analytics-enabled" data-segment-category="current rankings" data-segment-label="current search results - zip">
                                                View Current Search Results &raquo;
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <b>by City:</b> <?=$this->facility->getLocation()->getCity()?>, <?=$this->facility->getLocation()->getState()?>
                                        </td>
                                        <td class="ranking">
                                            <?php if (!$this->cityBidOppsErrorMessage): ?>
                                                <?=$this->facilityCityBidOpps['rank']?> of <?=$this->facilityCityBidOpps['num_results']?>
                                            <?php else: ?>
                                                <p><?= $this->cityBidOppsErrorMessage ?></p>
                                            <?php endif ?>
                                        </td>
                                        <td>
                                            <a href="<?=$this->sparefootSearchCityUrl?>" target="_blank" data-segment-category="current rankings" data-segment-label="current search results - city">View Current Search Results &raquo;</a>
                                        </td>
                                    </tr>
                                </table>
                            <?php endif ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="panel">
                        <div class="panel-body">
                            <table class="current-bid-table table">
                                <tr>
                                    <td>
                                        <b>Your Current Bid:</b> <span class="current-bid"><?=$this->currentBid?></span>
                                        <span class="bid-delta">
                                        <?php if($this->bidDelta != 0): ?>
                                            (+<?=number_format($this->bidDelta, 2, '.', '')?>)
                                        <?php endif; ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Minimum Bid:</b> <?=$this->minBid?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <p class="ui success message alert hide">Changes saved. Please allow 15 minutes for the changes to take effect.</p>
        </div>

        <div class="full-width-row">
            <div class="row header-row">
                <div class="col-md-12">
                    <h2>Update Your Bid Modifier</h2>
                    <?php
                    if ($this->facility) : ?>
                        <p> Have multiple facilities? Access <a href="<?=$this->url(['action'=>'bulkbid'], 'features')?>?fid=<?=$this->facilityId?>">Bulk Bidding > </a></p>
                    <?php endif; ?>
                    <p> Use the arrows to update your bid modifier by increments of .05 or enter in the bid amount directly into the box.
                        Keep in mind you can only select one bid which will affect both zip code and city rankings.</p>
                    <p class="subtext">Your Current Bid: <span class="current-bid"><?=$this->currentBid?></span></p>
                </div>
            </div>
             
            <div class="row" style="margin-bottom:20px !important;"></div>         
            <div class="row">
                <div class="col-md-3">
                    <form action="javascript:;" onsubmit="return saveBid(this)" data-segment-category="update bid modifier" data-segment-label="save bid form">
                        <div id="bid_amount">
                            <input type="hidden" id="facility_id" name="facility_id" value="<?=$this->facility->getId()?>" />
                            <input type="hidden" name="update_bid_csrf_token" value="<?=$this->update_bid_csrf_token?>">

                            <label for="bid_amount"><strong>Update My Bid</strong></label>
                            <div style="z-index:10;">
                                <?php if ($this->canChangeBid): ?>
                                    <div class="bid-tool">
                                        <input id="bid_amount_value" name="current_temp_bid" type="number" step=".01" min="<?=$this->facility->getMinBid()?>" max="<?=$this->facility->getMaxBid()?>" value="<?=$this->currentBid?>" data-segment-category="update bid modifier" data-segment-label="bid value input" />
                                        <div id="bid-toggle">
                                            <span id="bid-up" class="analytics-enabled" data-segment-category="update bid modifier" data-segment-label="bid up"></span>
                                            <span id="bid-down" class="analytics-enabled" data-segment-category="update bid modifier" data-segment-label="bid down"></span>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div id="bid-amount-display"><?=$this->facility->getEffectiveBidAmount()?></div>
                                <?php endif ?>
                            </div>
                        </div>

                        <div id="submit-button-container">
                            <?php if ($this->canChangeBid) { ?>
                                <button id="submit_button" class="ui primary button update-bid-btn" type="submit" data-segment-category="update bid modifier" data-segment-label="update my bid">Update My Bid</button>
                                <img src="/images/loading.gif" class="hide" />
                            <?php } else { ?>
                                <hr />
                                Only system administrators are able to change your bid and improve your search ranking. Please contact <?=$this->adminEmails?> to make this change. Thanks!
                            <?php } ?>
                        </div>
                    </form>
                </div>
                <div class="col-md-7">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <form action="javascript:;" onsubmit="return refreshBid(this)" id="update-temp-bid-form" data-segment-category="update bid modifier" data-segment-label="refresh temp bid form">
                            <input type="hidden" name="rank_bid_csrf_token" value="<?=$this->rank_bid_csrf_token?>">
                                <table id="rankingsTable" class="table bid-opps-table">
                                    <tbody class="table-body">
                                        <tr class="header-row">
                                            <th></th>
                                            <th>Search Term</th>
                                            <th>Facility Rank</th>
                                        </tr>
                                        <tr>
                                            <th>Zip Code</th>
                                            <td><?=$this->facility->getLocation()->getZip()?></td>
                                            <td class="ranking" data-opptype="zip">-</td>
                                        </tr>
                                        <tr>
                                            <th>City</th>
                                            <td><?=$this->facility->getLocation()->getCity()?>, <?=$this->facility->getLocation()->getState()?></td>
                                            <td class="ranking" data-opptype="city">-</td>
                                        </tr>
                                        <tr>
                                            <th>Custom Zip Code</th>
                                            <td>
                                                <div class="ui action input">
                                                    <input type="text" name="custom_zip" id="custom_zip" size="10" maxlength="5" />
                                                    <button type="submit" id="custom_zip_button" class="ui secondary button" data-segment-category="update bid modifier" data-segment-label="view - custom zip">View</button>
                                                </div>
                                            </td>
                                            <td class="ranking" data-opptype="customZip">-</td>
                                        </tr>
                                        <tr>
                                            <th>
                                                Custom City
                                                <br />
                                                <span class="subtext"><i>example: Austin, TX</i></span>
                                            </th>
                                            <td>
                                                <div class="ui action input">
                                                    <input type="text" name="custom_city" id="custom_city" size="10" />
                                                    <button type="submit" id="custom_city_button" class="ui secondary button" data-segment-category="update bid modifier" data-segment-label="view - custom city">View</button>
                                                </div>
                                            </td>
                                            <td class="ranking" data-opptype="customCity">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </form>
                        </div>
                    </div>

                    <?php if($this->algorithms) { ?>
                        <div>
                            <br/>
                            <select id="algorithm" name="algorithm">
                                <?php foreach ($this->algorithms as $class => $name) { ?>
                                    <option value="<?=$class?>"<?=($this->algorithm == $class || (!$this->algorithm && $class == Genesis_Entity_Search_Container::DEFAULT_STORAGE_SEARCH)) ? ' selected="selected"' : ''?>><?=($class == Genesis_Entity_Search_Container::DEFAULT_STORAGE_SEARCH ? '[DEFAULT] ' : '').$name?></option>
                                <?php } ?>
                            </select>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
                           
        <div class="bidding-opportunities full-width-row">
            <div class="row">
                <div class="col-md-12" id="bid-by-zip">
                    <h2 class="page-header bidding-header">Bidding Opportunities</h2>
                    <p> This chart shows your bid modifier options and the rank that those bids would yield in searches by zip code or city.
                        A dash on the chart indicates that there is no bidding opportunity for that ranking position.</p>
                    <?php if (!$this->facility->getActive()): ?>
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <p>You currently do not appear in the search results for your city or zip because this facility is not accepting reservations.
                                    You can make this facility available for bookings on the SpareFoot network from the
                                    <a href="<?=$this->url(['action'=>'details'], 'features')?>?fid=<?=$this->facility->getId()?>">Features page.</a></p>
                            </div>
                        </div>
                    <?php endif ?>
                    <table class="table bidding-table">
                        <tr>
                            <th class="bidding-cell" width="15%">Search Rank</th>
                            <?php for ($i = 1; $i < $maxOpportunities + 1; $i++): ?>
                                <th colspan="3" class="bidding-cell"><?= $i ?></th>
                            <?php endfor; ?>
                        </tr>

                        <tr>
                            <th class="bidding-cell" width="15%">Bid By Zip Code</th>
                            <?php for ($i = 1; $i < $maxOpportunities + 1; $i++): ?>
                                <?php
                                    $hasBidOpp = !$this->zipBidOppsErrorMessage && $this->facilityZipBidOpps['bid_opportunities'][$i] && (float)$this->facilityZipBidOpps['bid_opportunities'][$i] <= 10 && round((float)$this->facilityZipBidOpps['bid_opportunities'][$i], 2) > 0;
                                ?>
                                <td colspan="3" class="bidding-cell"><?= $hasBidOpp ? number_format($this->facilityZipBidOpps['bid_opportunities'][$i], 2) : '-'?></td>
                            <?php endfor; ?>
                        </tr>

                        <tr>
                            <th class="bidding-cell" width="15%">Bid By City</th>
                            <?php for ($i = 1; $i < $maxOpportunities + 1; $i++): ?>
                                <?php
                                    $hasBidOpp = !$this->cityBidOppsErrorMessage && $this->facilityCityBidOpps['bid_opportunities'][$i] && (float)$this->facilityCityBidOpps['bid_opportunities'][$i] <= 10 && round((float)$this->facilityCityBidOpps['bid_opportunities'][$i], 2) > 0;
                                ?>
                                <td colspan="3" class="bidding-cell"><?= $hasBidOpp ? number_format($this->facilityCityBidOpps['bid_opportunities'][$i], 2) : '-'?></td>
                            <?php endfor; ?>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="full-width-row">
            <div class="row header-row">
                <div class="col-md-12">
                    <h2>Export Data</h2>
                    <p>Download detailed bidding opportunities for your facilities by zip code or city.
                        Get access to even more data about your listing performance, average market prices on SpareFoot and how your facility compares to help you make informed bidding decisions.</p>
                </div>
            </div>

            <div class="export-row">
                <div class="export-zip-opps">
                    <div class="export-button-wrapper">
                        <button id="download-zip-opp-btn" class="ui secondary button download-opp-btn" onclick="return downloadBidOpportunities('zip');" data-segment-category="export data" data-segment-label="export by zip">Export by Zip</button>
                    </div>
                    <div class="messaging">
                        <div class="progress zip">
                            <div id="zip_progress" class="progress-bar progress-bar-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span class="download-link"></span>
                        <span class="error-message"></span>
                    </div>
                    <div class="spacer"></div>
                </div>
            </div>
            <div class="export-row">
                <div class="export-city-opps">
                    <div class="export-button-wrapper">
                        <button id="download-city-opp-btn" class="ui secondary button download-opp-btn" onclick="return downloadBidOpportunities('city');" data-segment-category="export data" data-segment-label="export by zip">Export by City</button>
                    </div>
                    <div class="messaging">
                        <div class="progress city">
                            <div id="city_progress" class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span class="download-link"></span>
                        <span class="error-message"></span>
                    </div>
                    <div class="spacer"></div>
                </div>
            </div>
        </div>
        <?php endif ?> 
</div>
