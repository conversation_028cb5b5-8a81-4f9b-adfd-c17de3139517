<form id="add-facility-form" method="post" action="<?=$this->url(['action'=> 'add_facility'], 'features')?>">
    <div class="setup-content-container">
        <div class="setup-content">
            <div class="content-row">
                <h1>Add a Facility</h1>
                <p>Add a new facility here, then add units to the facility. Those units will appear on SpareFoot.com. You can always hide your facility or its units later on. (Note: If you use SiteLink, Centershift, or QuikStor, please click back and choose the appropriate option on the prior page.)</p>
            </div>
            <div class="content-row">
                <div class="form-horizontal">

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="name">Facility Name</label>
                        <div class="col-md-10">
                            <input size="50" type="text" id="name" name="name" tabindex="1" class="form-control" value="<?=$this->facName?>"/>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="phone">Phone</label>
                        <div class="col-md-10">
                            <input size="12" type="tel" value="<?=$this->facPhone?>" id="phone" name="phone" tabindex="5" class="form-control" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="address">Address</label>
                        <div class="col-md-10">
                            <input size="30" type="text" value="<?=$this->facAddress?>" id="address" name="address" tabindex="6" class="form-control" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="city">City</label>
                        <div class="col-md-10">
                            <input size="15" type="text" value="<?=$this->facCity?>" id="city" name="city" tabindex="7" class="form-control" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="state">State</label>
                        <div class="col-md-10">

                            <select name="state" id="state" tabindex="8" class="form-control">
                                <option value=""></option>
                                <option value="AK"<?=$this->facState=='AK'?' selected="selected"':''?>>AK</option>
                                <option value="AL"<?=$this->facState=='AL'?' selected="selected"':''?>>AL</option>
                                <option value="AR"<?=$this->facState=='AR'?' selected="selected"':''?>>AR</option>
                                <option value="AZ"<?=$this->facState=='AZ'?' selected="selected"':''?>>AZ</option>
                                <option value="CA"<?=$this->facState=='CA'?' selected="selected"':''?>>CA</option>
                                <option value="CO"<?=$this->facState=='CO'?' selected="selected"':''?>>CO</option>
                                <option value="CT"<?=$this->facState=='CT'?' selected="selected"':''?>>CT</option>
                                <option value="DC"<?=$this->facState=='DC'?' selected="selected"':''?>>DC</option>
                                <option value="DE"<?=$this->facState=='DE'?' selected="selected"':''?>>DE</option>
                                <option value="FL"<?=$this->facState=='FL'?' selected="selected"':''?>>FL</option>
                                <option value="GA"<?=$this->facState=='GA'?' selected="selected"':''?>>GA</option>
                                <option value="HI"<?=$this->facState=='HI'?' selected="selected"':''?>>HI</option>
                                <option value="IA"<?=$this->facState=='IA'?' selected="selected"':''?>>IA</option>
                                <option value="ID"<?=$this->facState=='ID'?' selected="selected"':''?>>ID</option>
                                <option value="IL"<?=$this->facState=='IL'?' selected="selected"':''?>>IL</option>
                                <option value="IN"<?=$this->facState=='IN'?' selected="selected"':''?>>IN</option>
                                <option value="KS"<?=$this->facState=='KS'?' selected="selected"':''?>>KS</option>
                                <option value="KY"<?=$this->facState=='KY'?' selected="selected"':''?>>KY</option>
                                <option value="LA"<?=$this->facState=='LA'?' selected="selected"':''?>>LA</option>
                                <option value="MA"<?=$this->facState=='MA'?' selected="selected"':''?>>MA</option>
                                <option value="MD"<?=$this->facState=='MD'?' selected="selected"':''?>>MD</option>
                                <option value="ME"<?=$this->facState=='ME'?' selected="selected"':''?>>ME</option>
                                <option value="MI"<?=$this->facState=='MI'?' selected="selected"':''?>>MI</option>
                                <option value="MN"<?=$this->facState=='MN'?' selected="selected"':''?>>MN</option>
                                <option value="MO"<?=$this->facState=='MO'?' selected="selected"':''?>>MO</option>
                                <option value="MS"<?=$this->facState=='MS'?' selected="selected"':''?>>MS</option>
                                <option value="MT"<?=$this->facState=='MT'?' selected="selected"':''?>>MT</option>
                                <option value="NC"<?=$this->facState=='NC'?' selected="selected"':''?>>NC</option>
                                <option value="ND"<?=$this->facState=='ND'?' selected="selected"':''?>>ND</option>
                                <option value="NE"<?=$this->facState=='NE'?' selected="selected"':''?>>NE</option>
                                <option value="NH"<?=$this->facState=='NH'?' selected="selected"':''?>>NH</option>
                                <option value="NJ"<?=$this->facState=='NJ'?' selected="selected"':''?>>NJ</option>
                                <option value="NM"<?=$this->facState=='NM'?' selected="selected"':''?>>NM</option>
                                <option value="NV"<?=$this->facState=='NV'?' selected="selected"':''?>>NV</option>
                                <option value="NY"<?=$this->facState=='NY'?' selected="selected"':''?>>NY</option>
                                <option value="OH"<?=$this->facState=='OH'?' selected="selected"':''?>>OH</option>
                                <option value="OK"<?=$this->facState=='OK'?' selected="selected"':''?>>OK</option>
                                <option value="OR"<?=$this->facState=='OR'?' selected="selected"':''?>>OR</option>
                                <option value="PA"<?=$this->facState=='PA'?' selected="selected"':''?>>PA</option>
                                <option value="RI"<?=$this->facState=='RI'?' selected="selected"':''?>>RI</option>
                                <option value="SC"<?=$this->facState=='SC'?' selected="selected"':''?>>SC</option>
                                <option value="SD"<?=$this->facState=='SD'?' selected="selected"':''?>>SD</option>
                                <option value="TN"<?=$this->facState=='TN'?' selected="selected"':''?>>TN</option>
                                <option value="TX"<?=$this->facState=='TX'?' selected="selected"':''?>>TX</option>
                                <option value="UT"<?=$this->facState=='UT'?' selected="selected"':''?>>UT</option>
                                <option value="VA"<?=$this->facState=='VA'?' selected="selected"':''?>>VA</option>
                                <option value="VT"<?=$this->facState=='VT'?' selected="selected"':''?>>VT</option>
                                <option value="WA"<?=$this->facState=='WA'?' selected="selected"':''?>>WA</option>
                                <option value="WI"<?=$this->facState=='WI'?' selected="selected"':''?>>WI</option>
                                <option value="WV"<?=$this->facState=='WV'?' selected="selected"':''?>>WV</option>
                                <option value="WY"<?=$this->facState=='WY'?' selected="selected"':''?>>WY</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="zip">Zip</label>
                        <div class="col-md-10">
                            <input size="15" type="text" value="<?=$this->facZip?>" id="zip" name="zip" tabindex="9" class="form-control" />
                        </div>
                    </div>

                    <hr />

                    <h3>Facility Contacts</h3>
                    <p>You can edit your users list later.</p>
                    <p><strong>Who can access this facility in MySpareFoot?</strong><br/><?=$this->accessemails?></p>

                    <?php if (!$this->update) { ?>
                    <div class="form-group">
                        <label class="col-md-2 control-label" for="reservation-emails">Reservation Email(s)</label>
                        <div class="col-md-10">
                            <input type="text" name="reservation_emails" id="reservation-emails" value="" class="form-control" />
                            <p class="help-block">Where to send reservation confirmation for new tenants. (Comma separate for multiple)<br/>
                            These people already receive emails for all facilities: <?=$this->reservationemails?></p>
                        </div>
                    </div>
                    <?php } ?>

                    <div class="form-actions">
                        <div class="right">
                            <input type="hidden" name="csrf_token" value="<?=$this->csrf_token?>">
                            <input type="hidden" name="update" id="update" value="<?=$this->update?>" />
                            <a id="cancel" href="<?=$this->url(['action'=>'type'], 'features')?>" class="ui basic large button" />Cancel</a>&nbsp;&nbsp;<img src="/images/loaders/small.gif" class="hide" />
                            <input id="add-facility-button" class="ui primary large button" name="commit" type="submit" value="<?=($this->update)?'Update Facility':'Add Facility'?>" data-loading-text="Saving" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
