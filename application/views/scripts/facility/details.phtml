<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'details', 'loggedUser' => $this->loggedUser, 'facility'=>$this->facility))?>

<script>
    try {
        var facilityId = '<?=$this->facility->getId()?>';
        var FACILITY = <?=json_encode($this->facility->toArray())?>;
        if(FACILITY) FACILITY.reservationWindow = <?=json_encode($this->facilityResRules)?>;
    } catch(err) { if(CONFIG.env != 'live') console.error('failed to parse facility data', err); }

</script>


<br>
<form id="facility-details-form" method="post" action="<?=$this->url(['action'=>'details'], 'features')?>?fid=<?=$this->facilityId?>" class="form-horizontal">

    <?php if($this->alert){ ?>
        <p class="alert<?=($this->alertClass?' '.$this->alertClass:'')?>">
            <?=$this->alert?>
        </p>
    <?php } ?>
    <script>
        var integratedFields = <?php echo json_encode($this->integratedFields) ?>
    </script>

    <div class="form-group<?=(in_array('facility_name', $this->erroredFields))?' has-error':''?>">
        <label class="col-lg-2 control-label" for="facility_name">Facility Name</label>
        <div class="col-lg-10">
            <input size="50" type="text" id="facility_name" name="facility_name" tabindex="<?=$i = 1;?>" class="form-control" value="<?=$this->facility->getTitle()?>"/>
        </div>
    </div>

    <div class="form-group<?=(in_array('facility_code', $this->erroredFields))?' has-error':''?>">
        <label class="col-lg-2 control-label" for="facility_code">Company Code</label>
        <div class="col-lg-10">
            <input size="12" type="text" id="facility_code" name="facility_code" tabindex="<?=++$i?>" class="form-control" value="<?=$this->facility->getCompanyCode()?>"/>
            <p class="help-block">If you use a code to reference this facility, you can enter it here and we will surface it on your statements to help you with accounting.</p>
        </div>
    </div>

    <div class="form-group<?=(in_array('facility_url', $this->erroredFields))?' has-error':''?>">
        <label class="col-lg-2 control-label" for="facility_url">Facility URL</label>
        <div class="col-lg-10">
            <input size="50" type="text" value="<?=$this->facility->getUrl()?>" id="facility_url" name="facility_url" tabindex="<?=++$i?>" class="form-control"/>
        </div>
    </div>

    <div class="form-group<?=(in_array('facility_admin_fee', $this->erroredFields))?' has-error':''?>">
        <label class="col-lg-2 control-label" for="facility_admin_fee">Admin Fee</label>
        <div class="col-lg-7">
            <div class="input-group">
                <span class="input-group-addon">$</span><input type="text" value="<?=$this->facility->getAdminFee()?>" id="facility_admin_fee" name="facility_admin_fee" tabindex="<?=++$i?>" class="form-control"/>
            </div>
            <p class="help-block">How much you charge a customer on their initial move-in (not including rent fees).</p>
        </div>
    </div>

    <?php if($this->facility->canEditReservationWindow()): ?>
        <div class="form-group res-window-group">
        <div class="col-lg-2 form-label">
            <label class="control-label">Reservation Window</label>
            <a class="res-window-more-info btn btn-xs btn-link" href="#">Learn More</a>
        </div>
        <div class="col-lg-10 <?= $this->sources === false ? 'integrated' : 'manual' ?>">
            <div class="facility-res-win-widget"></div>

            <?php // unit level exceptions ?>
            <?php
                $units = [];
                /**
                 * @var $unit Genesis_Entity_StorageSpace
                 */
                foreach ($this->units as $unit) {
                    $resDays = count($unit->getReservationWindowRules()) > 0 ? $unit->getReservationDays() : null;
                    $units[] = [
                        'id' => $unit->getId(),
                        'name'=> str_replace('&nbsp;', ' ', $unit->stringDimensions(false)).' '.$unit->stringType(),
                        'amenities'=> $unit->stringAmenities(),
                        'numAvail' => $unit->getGroupedNumAvailable(),
                        'qty' => $unit->getQuantity(),
                        'resDays' => $resDays,
                        'unitGroups' => $unit->getGroupedUnitNames(),
                        'length'=>$unit->getLength(),
                        'width'=>$unit->getWidth()
                    ];
                }
            ?>
            <script>
                var canGroupUnits = <?= $this->facility->canGroupUnits() ? 'true' : 'false' ?>;
                var units = <?= count($this->units) ? json_encode($units) : '[]' ?>;
            </script>
        </div>
    </div>
    <?php else: ?>
        <?php $resWindow = $this->facility->getSuppAttrHash()['integratedReservationWindow']; ?>
        <div class="form-group">
            <label class="col-lg-2 control-label">Reservation Window</label>
            <div class="col-lg-10">
                <input size="15" type="text" value="<?=$resWindow[1] . ' Days'?>" readonly="readonly" class="form-control disabled" />
                <p class="help-block-disabled">Number of days you will hold a unit for a reservation. To update this field, please edit within your management software.</p>
            </div>
        </div>
    <?php endif ?>

    <?php if(Genesis_Service_Feature::isActive('tenant_connect_sms')): ?>

        <div class="form-group<?=(in_array('facility_phone', $this->erroredFields))?' has-error':''?>">
            <label class="col-lg-2 control-label" for="facility_phone">Phone Number</label>
            <div class="col-lg-10">
                <input size="12" type="text" value="<?=($this->facility->getPhone())?$this->facility->stringPhone():''?>" id="facility_phone" name="facility_phone" tabindex="<?=++$i?>" class="form-control" />
            </div>
        </div>

        <?php /*
        <div class="form-group<?=(in_array('facility_tenant_connect_override_phone', $this->erroredFields))?' has-error':''?>">
            <label class="col-lg-2 control-label" for="facility_tenant_connect_override_phone">Direct Phone Number</label>
            <div class="col-lg-10">
                <input size="12" type="text" value="<?=($this->facility->getTenantConnectOverridePhone())?$this->facility->stringTenantConnectOverridePhone():''?>" id="facility_tenant_connect_override_phone" name="facility_tenant_connect_override_phone" tabindex="<?=++$i?>" class="form-control" />
            </div>
        </div>
         */ ?>

        <div class="form-group<?=(in_array('facility_tenant_connect_sms_number', $this->erroredFields))?' has-error':''?>">
            <label class="col-lg-2 control-label" for="facility_tenant_connect_sms_number">Cell Phone Number</label>
            <div class="col-lg-10">
                <input size="12" type="text" value="<?=($this->facility->getTenantConnectSMSNumber())?$this->facility->stringTenantConnectSMSNumber():''?>" id="facility_tenant_connect_sms_number" name="facility_tenant_connect_sms_number" tabindex="<?=++$i?>" class="form-control" />
                <p class="help-block">We'll send a text message when you get a new reservation! This message will include the customer's phone #, so you can follow up with them immediately. </p>
            </div>
        </div>
        <?php if(Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::TENANT_CONNECT_TOGGLE)): ?>
        <div class="form-group">
            <div class="col-lg-10 col-lg-offset-2">
                <label>Enable Tenant Connect</label>
                <p class="help-block">We will give you a phone call when a booking occurs and you will have the option to immediately be connected with them via phone.</p>

                <div>
                    <label class="control-label radio-inline" style="vertical-align: top;">
                        <input type="radio" id="facility-tenant-connect-yes" name="facility_tenant_connect" value="1"  <?=($this->facility->getTenantConnect() != '0')?'checked="checked"':null?> />Yes
                    </label>
                    <label class="control-label radio-inline" style="vertical-align: top;">
                        <input type="radio" id="facility-tenant-connect-no" name="facility_tenant_connect" value="0" <?=($this->facility->getTenantConnect() == '0')?'checked="checked"':null?> />No
                    </label>
                </div>
            </div>
        </div>
            <?php endif; ?>
    <?php else: ?>
        <div class="form-group<?=(in_array('facility_phone', $this->erroredFields))?' has-error':''?>">
            <label class="col-lg-2 control-label" for="facility_phone">Phone</label>
            <div class="col-lg-10">
                <input size="12" type="text" value="<?=($this->facility->getPhone())?$this->facility->stringPhone():''?>" id="facility_phone" name="facility_phone" tabindex="<?=++$i?>" class="form-control" />
            </div>
        </div>
    <?php endif ?>

    <div class="form-group<?=(in_array('facility_address1', $this->erroredFields))?' has-error':''?>">
        <label class="col-lg-2 control-label" for="facility_address1">Address</label>
        <div class="col-lg-10">
            <input size="30" type="text" value="<?=$this->facility->getLocation()->getAddress1()?>" id="facility_address1" name="facility_address1" tabindex="<?=++$i?>" class="form-control" />
        </div>
    </div>

    <div class="form-group<?=(in_array('facility_city', $this->erroredFields))?' has-error':''?>">
        <label class="col-lg-2 control-label" for="facility_city">City</label>
        <div class="col-lg-10">
            <input size="15" type="text" value="<?=$this->facility->getLocation()->getCity()?>" id="facility_city" name="facility_city" tabindex="<?=++$i?>" class="form-control" />
        </div>
    </div>

    <div class="form-group<?=(in_array('facility_state', $this->erroredFields))?' has-error':''?>">
        <label class="col-lg-2 control-label" for="facility_state">State</label>
        <div class="col-lg-10">

            <select name="facility_state" id="facility_state" tabindex="<?=++$i?>" class="form-control">
                <option value=""></option>
                <option value="AK"<?=$this->facility->getLocation()->getState()=='AK'?' selected="selected"':''?>>AK</option>
                <option value="AL"<?=$this->facility->getLocation()->getState()=='AL'?' selected="selected"':''?>>AL</option>
                <option value="AR"<?=$this->facility->getLocation()->getState()=='AR'?' selected="selected"':''?>>AR</option>
                <option value="AZ"<?=$this->facility->getLocation()->getState()=='AZ'?' selected="selected"':''?>>AZ</option>
                <option value="CA"<?=$this->facility->getLocation()->getState()=='CA'?' selected="selected"':''?>>CA</option>
                <option value="CO"<?=$this->facility->getLocation()->getState()=='CO'?' selected="selected"':''?>>CO</option>
                <option value="CT"<?=$this->facility->getLocation()->getState()=='CT'?' selected="selected"':''?>>CT</option>
                <option value="DC"<?=$this->facility->getLocation()->getState()=='DC'?' selected="selected"':''?>>DC</option>
                <option value="DE"<?=$this->facility->getLocation()->getState()=='DE'?' selected="selected"':''?>>DE</option>
                <option value="FL"<?=$this->facility->getLocation()->getState()=='FL'?' selected="selected"':''?>>FL</option>
                <option value="GA"<?=$this->facility->getLocation()->getState()=='GA'?' selected="selected"':''?>>GA</option>
                <option value="HI"<?=$this->facility->getLocation()->getState()=='HI'?' selected="selected"':''?>>HI</option>
                <option value="IA"<?=$this->facility->getLocation()->getState()=='IA'?' selected="selected"':''?>>IA</option>
                <option value="ID"<?=$this->facility->getLocation()->getState()=='ID'?' selected="selected"':''?>>ID</option>
                <option value="IL"<?=$this->facility->getLocation()->getState()=='IL'?' selected="selected"':''?>>IL</option>
                <option value="IN"<?=$this->facility->getLocation()->getState()=='IN'?' selected="selected"':''?>>IN</option>
                <option value="KS"<?=$this->facility->getLocation()->getState()=='KS'?' selected="selected"':''?>>KS</option>
                <option value="KY"<?=$this->facility->getLocation()->getState()=='KY'?' selected="selected"':''?>>KY</option>
                <option value="LA"<?=$this->facility->getLocation()->getState()=='LA'?' selected="selected"':''?>>LA</option>
                <option value="MA"<?=$this->facility->getLocation()->getState()=='MA'?' selected="selected"':''?>>MA</option>
                <option value="MD"<?=$this->facility->getLocation()->getState()=='MD'?' selected="selected"':''?>>MD</option>
                <option value="ME"<?=$this->facility->getLocation()->getState()=='ME'?' selected="selected"':''?>>ME</option>
                <option value="MI"<?=$this->facility->getLocation()->getState()=='MI'?' selected="selected"':''?>>MI</option>
                <option value="MN"<?=$this->facility->getLocation()->getState()=='MN'?' selected="selected"':''?>>MN</option>
                <option value="MO"<?=$this->facility->getLocation()->getState()=='MO'?' selected="selected"':''?>>MO</option>
                <option value="MS"<?=$this->facility->getLocation()->getState()=='MS'?' selected="selected"':''?>>MS</option>
                <option value="MT"<?=$this->facility->getLocation()->getState()=='MT'?' selected="selected"':''?>>MT</option>
                <option value="NC"<?=$this->facility->getLocation()->getState()=='NC'?' selected="selected"':''?>>NC</option>
                <option value="ND"<?=$this->facility->getLocation()->getState()=='ND'?' selected="selected"':''?>>ND</option>
                <option value="NE"<?=$this->facility->getLocation()->getState()=='NE'?' selected="selected"':''?>>NE</option>
                <option value="NH"<?=$this->facility->getLocation()->getState()=='NH'?' selected="selected"':''?>>NH</option>
                <option value="NJ"<?=$this->facility->getLocation()->getState()=='NJ'?' selected="selected"':''?>>NJ</option>
                <option value="NM"<?=$this->facility->getLocation()->getState()=='NM'?' selected="selected"':''?>>NM</option>
                <option value="NV"<?=$this->facility->getLocation()->getState()=='NV'?' selected="selected"':''?>>NV</option>
                <option value="NY"<?=$this->facility->getLocation()->getState()=='NY'?' selected="selected"':''?>>NY</option>
                <option value="OH"<?=$this->facility->getLocation()->getState()=='OH'?' selected="selected"':''?>>OH</option>
                <option value="OK"<?=$this->facility->getLocation()->getState()=='OK'?' selected="selected"':''?>>OK</option>
                <option value="OR"<?=$this->facility->getLocation()->getState()=='OR'?' selected="selected"':''?>>OR</option>
                <option value="PA"<?=$this->facility->getLocation()->getState()=='PA'?' selected="selected"':''?>>PA</option>
                <option value="RI"<?=$this->facility->getLocation()->getState()=='RI'?' selected="selected"':''?>>RI</option>
                <option value="SC"<?=$this->facility->getLocation()->getState()=='SC'?' selected="selected"':''?>>SC</option>
                <option value="SD"<?=$this->facility->getLocation()->getState()=='SD'?' selected="selected"':''?>>SD</option>
                <option value="TN"<?=$this->facility->getLocation()->getState()=='TN'?' selected="selected"':''?>>TN</option>
                <option value="TX"<?=$this->facility->getLocation()->getState()=='TX'?' selected="selected"':''?>>TX</option>
                <option value="UT"<?=$this->facility->getLocation()->getState()=='UT'?' selected="selected"':''?>>UT</option>
                <option value="VA"<?=$this->facility->getLocation()->getState()=='VA'?' selected="selected"':''?>>VA</option>
                <option value="VT"<?=$this->facility->getLocation()->getState()=='VT'?' selected="selected"':''?>>VT</option>
                <option value="WA"<?=$this->facility->getLocation()->getState()=='WA'?' selected="selected"':''?>>WA</option>
                <option value="WI"<?=$this->facility->getLocation()->getState()=='WI'?' selected="selected"':''?>>WI</option>
                <option value="WV"<?=$this->facility->getLocation()->getState()=='WV'?' selected="selected"':''?>>WV</option>
                <option value="WY"<?=$this->facility->getLocation()->getState()=='WY'?' selected="selected"':''?>>WY</option>
            </select>
        </div>
    </div>

    <div class="form-group<?=(in_array('facility_zip', $this->erroredFields))?' has-error':''?>">
        <label class="col-lg-2 control-label" for="facility_zip">Zip</label>
        <div class="col-lg-10">
            <input size="15" type="text" value="<?=$this->facility->getLocation()->getZip()?>" id="facility_zip" name="facility_zip" tabindex="<?=++$i?>" class="form-control" />
        </div>
    </div>

    <div class="form-group<?=(in_array('has_alt_address', $this->erroredFields))?' has-error':''?>">
        <div class="col-lg-10 col-lg-offset-2">
            <label>SpareFoot will occasionally send things to you in the mail. Can you receive mail at the above address?</label>
            <div>
              <label class="control-label radio-inline">
                  <input type="radio" id="has-alt-address-yes" name="has_alt_address" value="0" tabindex="<?=++$i?>" <?php if ($this->facility->getAltAddress() == '0') { echo 'checked="checked"'; } ?> />Yes
              </label>
              <label class="control-label radio-inline">
                  <input type="radio" id="has-alt-address-no" name="has_alt_address" value="1" tabindex="<?=$i?>"<?php if ($this->facility->getAltAddress() && $this->facility->getAltAddress() != '0') { echo 'checked="checked"'; } ?> />No
              </label>
            </div>
        </div>
    </div>

    <div class="ui segment col-lg-offset-2" id="has-alt-address-options" style="display:<?= ($this->facility->getAltAddress() && $this->facility->getAltAddress() != '0') ? 'block' : 'none'?>;">

        <?php $altAddress = $this->facility->getAltAddress(); ?>

        <p>Alternate address for receiving packages</p>

        <div class="form-group<?=(in_array('alt_facility_address', $this->erroredFields))?' has-error':''?>">
            <label class="col-lg-2 control-label" for="alt_facility_address1">Address</label>
            <div class="col-lg-10">
                <input size="30" type="text" value="<?=(isset($altAddress->address)?$altAddress->address:'')?>" id="facility_address1" name="alt_facility_address1" class="form-control" />
            </div>
        </div>

        <div class="form-group<?=(in_array('alt_facility_city', $this->erroredFields))?' has-error':''?>">
            <label class="col-lg-2 control-label" for="alt_facility_city">City</label>
            <div class="col-lg-10">
                <input size="15" type="text" value="<?=(isset($altAddress->city)?$altAddress->city:'')?>" id="facility_city" name="alt_facility_city" class="form-control" />
            </div>
        </div>

        <div class="form-group<?=(in_array('alt_facility_state', $this->erroredFields))?' has-error':''?>">
            <label class="col-lg-2 control-label" for="alt_facility_state">State</label>
            <div class="col-lg-10">

                <select name="alt_facility_state" id="facility_state" class="form-control">
                    <option value=""></option>
                    <option value="AK"<?=$altAddress->state=='AK'?' selected="selected"':''?>>AK</option>
                    <option value="AL"<?=$altAddress->state=='AL'?' selected="selected"':''?>>AL</option>
                    <option value="AR"<?=$altAddress->state=='AR'?' selected="selected"':''?>>AR</option>
                    <option value="AZ"<?=$altAddress->state=='AZ'?' selected="selected"':''?>>AZ</option>
                    <option value="CA"<?=$altAddress->state=='CA'?' selected="selected"':''?>>CA</option>
                    <option value="CO"<?=$altAddress->state=='CO'?' selected="selected"':''?>>CO</option>
                    <option value="CT"<?=$altAddress->state=='CT'?' selected="selected"':''?>>CT</option>
                    <option value="DC"<?=$altAddress->state=='DC'?' selected="selected"':''?>>DC</option>
                    <option value="DE"<?=$altAddress->state=='DE'?' selected="selected"':''?>>DE</option>
                    <option value="FL"<?=$altAddress->state=='FL'?' selected="selected"':''?>>FL</option>
                    <option value="GA"<?=$altAddress->state=='GA'?' selected="selected"':''?>>GA</option>
                    <option value="HI"<?=$altAddress->state=='HI'?' selected="selected"':''?>>HI</option>
                    <option value="IA"<?=$altAddress->state=='IA'?' selected="selected"':''?>>IA</option>
                    <option value="ID"<?=$altAddress->state=='ID'?' selected="selected"':''?>>ID</option>
                    <option value="IL"<?=$altAddress->state=='IL'?' selected="selected"':''?>>IL</option>
                    <option value="IN"<?=$altAddress->state=='IN'?' selected="selected"':''?>>IN</option>
                    <option value="KS"<?=$altAddress->state=='KS'?' selected="selected"':''?>>KS</option>
                    <option value="KY"<?=$altAddress->state=='KY'?' selected="selected"':''?>>KY</option>
                    <option value="LA"<?=$altAddress->state=='LA'?' selected="selected"':''?>>LA</option>
                    <option value="MA"<?=$altAddress->state=='MA'?' selected="selected"':''?>>MA</option>
                    <option value="MD"<?=$altAddress->state=='MD'?' selected="selected"':''?>>MD</option>
                    <option value="ME"<?=$altAddress->state=='ME'?' selected="selected"':''?>>ME</option>
                    <option value="MI"<?=$altAddress->state=='MI'?' selected="selected"':''?>>MI</option>
                    <option value="MN"<?=$altAddress->state=='MN'?' selected="selected"':''?>>MN</option>
                    <option value="MO"<?=$altAddress->state=='MO'?' selected="selected"':''?>>MO</option>
                    <option value="MS"<?=$altAddress->state=='MS'?' selected="selected"':''?>>MS</option>
                    <option value="MT"<?=$altAddress->state=='MT'?' selected="selected"':''?>>MT</option>
                    <option value="NC"<?=$altAddress->state=='NC'?' selected="selected"':''?>>NC</option>
                    <option value="ND"<?=$altAddress->state=='ND'?' selected="selected"':''?>>ND</option>
                    <option value="NE"<?=$altAddress->state=='NE'?' selected="selected"':''?>>NE</option>
                    <option value="NH"<?=$altAddress->state=='NH'?' selected="selected"':''?>>NH</option>
                    <option value="NJ"<?=$altAddress->state=='NJ'?' selected="selected"':''?>>NJ</option>
                    <option value="NM"<?=$altAddress->state=='NM'?' selected="selected"':''?>>NM</option>
                    <option value="NV"<?=$altAddress->state=='NV'?' selected="selected"':''?>>NV</option>
                    <option value="NY"<?=$altAddress->state=='NY'?' selected="selected"':''?>>NY</option>
                    <option value="OH"<?=$altAddress->state=='OH'?' selected="selected"':''?>>OH</option>
                    <option value="OK"<?=$altAddress->state=='OK'?' selected="selected"':''?>>OK</option>
                    <option value="OR"<?=$altAddress->state=='OR'?' selected="selected"':''?>>OR</option>
                    <option value="PA"<?=$altAddress->state=='PA'?' selected="selected"':''?>>PA</option>
                    <option value="RI"<?=$altAddress->state=='RI'?' selected="selected"':''?>>RI</option>
                    <option value="SC"<?=$altAddress->state=='SC'?' selected="selected"':''?>>SC</option>
                    <option value="SD"<?=$altAddress->state=='SD'?' selected="selected"':''?>>SD</option>
                    <option value="TN"<?=$altAddress->state=='TN'?' selected="selected"':''?>>TN</option>
                    <option value="TX"<?=$altAddress->state=='TX'?' selected="selected"':''?>>TX</option>
                    <option value="UT"<?=$altAddress->state=='UT'?' selected="selected"':''?>>UT</option>
                    <option value="VA"<?=$altAddress->state=='VA'?' selected="selected"':''?>>VA</option>
                    <option value="VT"<?=$altAddress->state=='VT'?' selected="selected"':''?>>VT</option>
                    <option value="WA"<?=$altAddress->state=='WA'?' selected="selected"':''?>>WA</option>
                    <option value="WI"<?=$altAddress->state=='WI'?' selected="selected"':''?>>WI</option>
                    <option value="WV"<?=$altAddress->state=='WV'?' selected="selected"':''?>>WV</option>
                    <option value="WY"<?=$altAddress->state=='WY'?' selected="selected"':''?>>WY</option>
                </select>
            </div>
        </div>

        <div class="form-group<?=(in_array('alt_facility_zip', $this->erroredFields))?' has-error':''?>">
            <label class="col-lg-2 control-label" for="alt_facility_zip">Zip</label>
            <div class="col-lg-10">
                <input size="15" type="text" value="<?=(isset($altAddress->zip)?$altAddress->zip:'')?>" id="facility_zip" name="alt_facility_zip" class="form-control" />
            </div>
        </div>

    </div>

    <div class="form-group<?=(in_array('onsite_office_at_facility', $this->erroredFields))?' has-error':''?>">
        <div class="col-lg-10 col-lg-offset-2">
            <label>Is there an on-site office at the above address?</label>
            <div>
                <label class="control-label radio-inline">
                    <input type="radio" id="onsite-office-at-facility-yes" name="onsite_office_at_facility" value="1" tabindex="<?=++$i?>" <?=($this->facility->getOnsiteOfficeAtFacility() == '1')?'checked="checked"':null?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="onsite-office-at-facility-no" name="onsite_office_at_facility" value="0" tabindex="<?=$i?>" <?=($this->facility->getOnsiteOfficeAtFacility() == '0') ? 'checked="checked"':null?> />No
                </label>
            </div>
        </div>
    </div>

    <?php if ($this->source->getId() != Genesis_Entity_Source::ID_MANUAL) : ?>
    <div class="form-group<?=(in_array('facility_promotions', $this->erroredFields))?' has-error':''?>">

        <label class="col-lg-2 control-label" for="facility_promotions">Special Offer</label>
        <div class="col-lg-10">

            <?php if ($this->corporation->getPullPromos()): ?>
                <input type="text" value="<?=$this->facility->getSpecials()?>" disabled="disabled" size="50" maxlength="100" class="form-control" />
                <input type="hidden" name="facility_promotions" value="<?=$this->facility->getSpecials()?>"/>
            <?php else: ?>
                <input type="text" name="facility_promotions" id="facility_promotions" value="<?=$this->facility->getSpecials()?>" tabindex="<?=++$i?>" size="50" maxlength="100" class="form-control" />
                <span class="help-inline">(<span class="countdown"></span>max. 100 characters)</span>
                <p class="help-block">Example: "First Month Free!". The facility-wide promotion will show up by default, but will be overwritten for any units with unit-level promotions.</p>
            <?php endif; ?>

        </div>
    </div>
    <?php endif; ?>

    <div class="form-group<?=(in_array('facility_description', $this->erroredFields))?' has-error':''?>">

        <label class="col-lg-2 control-label" for="facility_description">Facility Description</label>
        <div class="col-lg-10">
            <textarea cols="57" rows="5" name="facility_description" id="facility_description" tabindex="<?=++$i?>"  class="form-control"><?=$this->facility->getDescription()?></textarea>
        </div>
    </div>

    <div class="form-group<?=(in_array('facility_software', $this->erroredFields))?' has-error':''?>">
        <a name="facility_software"></a>
        <label class="col-lg-2 control-label" for="facility_software"><?=($this->sources === false)?'This Facility Uses':'What software do you use to manage your facility?'?></label>
        <div class="col-lg-10">
            <?php if ($this->sources === false): //just show the integration name, no options ?>
                <label class="control-label"><strong><?
                    if($this->source->getId() == Genesis_Entity_Source::ID_OPENTECH) {
                        echo $this->facility->getOpentechFacility()->getPropertyManagementSoftware() . " " . "(Opentech ISSN)";
                    } else {
                        echo $this->source->getSource();
                    } ?></strong></label>
                <input type="hidden" name="facility_software" value="<?=$this->source->getId(); ?>" />
            <?php else: ?>
            <select name="facility_software" id="facility_software" tabindex="<?=++$i?>" class="form-control">
                <option value=""></option>
                <?php foreach ($this->sources as $sourceEntity): ?>
                    <option value="<?=$sourceEntity->getId()?>"<?=($sourceEntity->getId() == $this->facility->getSelfReportedSourceId() ? 'selected="selected"' : '')?>><?=$sourceEntity->getSource()?></option>
                <?php endforeach; ?>
            </select>
            <?php endif; ?>
        </div>
    </div>

    <?php if($this->facility->getSourceId() != Genesis_Entity_Source::ID_EXTRA_SPACE) { ?>
        <div class="form-group<?=(in_array('facility_active', $this->erroredFields))?' has-error':''?>">
            <div class="col-lg-10 col-lg-offset-2">
                <label>Make this facility available for bookings on the SpareFoot network?</label>
                <div>
                    <label class="control-label radio-inline" style="vertical-align: top;">
                        <input type="radio" id="facility-active-yes" name="facility_active" value="1" tabindex="<?=$i?>" <?=($this->facility->getActive() != '0')?'checked="checked"':null?> />Yes
                    </label>
                    <label class="control-label radio-inline" style="vertical-align: top;">
                        <input type="radio" id="facility-active-no" name="facility_active" value="0" tabindex="<?=++$i?>" <?=($this->facility->getActive() == '0')?'checked="checked"':null?> />No
                    </label>
                    <?php if (strtotime($this->facility->getAutomaticReactivationDate()) > strtotime("today")) {
                        $reactivationDate = strtotime($this->facility->getAutomaticReactivationDate());
                        $hasReactivationDate = true;
                    } else {
                        $reactivationDate = strtotime("+1 month");
                        $hasReactivationDate = false;
                    }

                    ?>
                    <script>
                        var nativeReactivationDate = "<?=$this->facility->getAutomaticReactivationDate()?>";
                        var hasReactivationDate = <?=$hasReactivationDate ? 'true' : 'false'?>;
                        var defaultReactivationDate =       new Date(<?=$reactivationDate*1000 ?>);
                        var firstAllowedReactivationDate =  new Date(<?=strtotime("tomorrow")*1000?>);
                        var lastAllowedReactivationDate =   new Date(<?=strtotime("+1 year")*1000?>);
                    </script>
                    <label id="reactivation-message" for="automatic_reactivation_date" <?=($this->facility->getActive() == '1' || ! $hasReactivationDate) ? 'style="display:none"':null?> class="control-label radio-inline<?=(in_array('automatic_reactivation_date', $this->erroredFields))?' has-error':''?>">
                        This facility is scheduled to reactivate on
                        <span id="automatic-reactivation-date-input" style="display:none">
                            <input id="automatic-reactivation-date-datepicker" type="text" name="automatic_reactivation_date" value="<?=$hasReactivationDate ? date('M j, Y', $reactivationDate) : null ?>" class="form-control datepicker-field"/>
                        </span>
                        <span id="automatic-reactivation-date-label" style="font-weight: bold">
                            <?=date('M j, Y', $reactivationDate)?>
                        </span>
                        <a id="change-automatic-reactivation-date">
                        Change
                        </a>
                    </label>
                    <label id="option-automatic-reactivation-date-label" for="option-automatic-reactivation-date" <?=($this->facility->getActive() == '1' || $hasReactivationDate) ? 'style="display:none"':null?> class="control-label radio-inline<?=(in_array('automatic_reactivation_date', $this->erroredFields))?' has-error':''?>">
                        <a id="option-automatic-reactivation-date">
                            Set an automatic reactivation date for this facility?
                        </a>
                    </label>
                </div>
            </div>
        </div>
    <?php } ?>

    <div class="form-actions">
        <div class="right">
            <input type="hidden" id="facility_id" name="facility_id" value="<?=$this->facility->getId()?>" />
            <input type="hidden" id="facility_address_change" name="facility_address_change" value="0" />
            <input id="facility-details-save" class="ui primary button" name="commit" type="submit" value="Save Changes" tabindex="<?=++$i?>"/>
        </div>
    </div>
</form>

<?=$this->partial('facility/hide-facility-reason-modal.phtml')?>
