<?php
$action = Zend_Controller_Front::getInstance()->getRequest()->getActionName();

if (! $this->facility || ! $this->facility instanceof Genesis_Entity_Facility) {
    throw new Exception('facility object must be passed to partial view' . '<br/>' . $action .' '. Zend_Controller_Front::getInstance()->getRequest()->getControllerName());
}
$this->facilityId = $this->facility->getId();
$isFSS = $this->facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
?>
<div class="subnav">
    <?php if ($this->facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_MANUAL || $isFSS): ?>
        <?php if ($this->facility) : ?>
            <?php if ($this->facility->getAccount()->getBidType() !== Genesis_Entity_Account::BID_TYPE_RESIDUAL) : ?>
                <?php if ($action === 'bid' || $action === 'bulkbid'):?>
                    <ul class="nav nav-tabs">
                        <li<?=in_array($action, ['bid','bidsetup','bid-custom']) ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'bid'], 'features')?>?fid=<?=$this->facilityId?>" id="facility-bid" data-segment-category="facility subnav" data-segment-label="bidding">Facility Bidding</a></li>
                        <li<?= $action === 'bulkbid' ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'bulkbid'], 'features')?>?fid=<?=$this->facilityId?>" id="bulk-bidding" data-segment-category="facility subnav" data-segment-label="bulk bidding">Bulk Bidding</a></li>
                    </ul>
                <?php endif; ?>
            <?php endif; ?>
        <?php elseif ('listings' !== $action): ?>
            <a class="ui button" href="<?=$this->url(['action'=>'units'], 'features')?>?fid=<?=$this->facilityId?>" data-segment-category="facility subnav" data-segment-label="back button"><i class="arrow left icon"></i> Back</a>
        <?php endif; ?>
    <?php else: ?>
        <ul class="nav nav-tabs">
            <li<?= in_array($action, ['inventory','units', 'groupedinventory']) ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'units'], 'features')?>?fid=<?=$this->facilityId?>" id="facility-units">Units</a></li>
            <li<?= 'details'   === $action  ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'details'], 'features')?>?fid=<?=$this->facilityId?>" id="facility-details" data-segment-category="facility subnav" data-segment-label="details">Details</a></li>
            <li<?= 'amenities' === $action  ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'amenities'], 'features')?>?fid=<?=$this->facilityId?>" id="facility-amenities" data-segment-category="facility subnav" data-segment-label="amenities">Amenities</a></li>
            <li<?= 'hours'     === $action  ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'hours'], 'features')?>?fid=<?=$this->facilityId?>" id="facility-hours" data-segment-category="facility subnav" data-segment-label="hours">Hours</a></li>
            <li<?= 'photos'    === $action  ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'photos'], 'features')?>?fid=<?=$this->facilityId?>" id="facility-media" data-segment-category="facility subnav" data-segment-label="photos">Photos</a></li>
            <?php if ($this->facility && $this->facility->getAccount()->getBidType() !== Genesis_Entity_Account::BID_TYPE_RESIDUAL): ?>
                <?php if($this->isBidOptimizerActive): ?>
                    <li><a href="<?=$this->url(['action'=>'demandoptimizer'], 'features')?>?fid=<?=$this->facilityId?>" id="bid-optimizer" data-segment-category="facility subnav" data-segment-label="bidding">Bid Optimizer</a></li>
                <?php endif; ?>
                <li<?=in_array($action, ['bid','bidsetup','bid-custom']) ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'bid'], 'features')?>?fid=<?=$this->facilityId?>" id="facility-bid" data-segment-category="facility subnav" data-segment-label="bidding">Facility Bidding</a></li>
                <li<?= 'bulkbid' === $action  ? ' class="active"' : '' ?>><a href="<?=$this->url(['action'=>'bulkbid'], 'features')?>?fid=<?=$this->facilityId?>" id="bulk-bidding" data-segment-category="facility subnav" data-segment-label="bulk bidding"> Bulk Bidding</a></li>
             <?php endif; ?>
            <?php if ($action === 'list'): //hidden option for gods ?>
                <li class="active"><a href="<?=$this->url(['action'=>'list'], 'features')?>">List</a></li>
            <?php endif; ?>
        </ul>
    <?php endif; ?>

    <div class="clear"></div>
</div>
