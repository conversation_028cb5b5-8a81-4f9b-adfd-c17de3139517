<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'photos', 'loggedUser' => $this->loggedUser, 'facility' => $this->facility))?>

<h3>Upload Photos</h3>
<p>(Maximum size: 5 MB)</p>

<form id="photos-form" method="post" enctype="multipart/form-data"
    action="<?=$this->url(['action'=>'photos'], 'features')?>?fid=<?=$this->facilityId?>"
    class="ui form segment">

    <p>Choose images:</p>
    <div id="image-uploaders">
        <input type="file" name="image[]" />
    </div><br />
    <p><a id="add-image" href="#">Add another image</a></p>
    <div class="right">
        <button type="submit" id="photos-form-submit" class="ui primary button" data-loading-text="Uploading">Upload</button>&nbsp;&nbsp;<img src="/images/loaders/large.gif" class="is-hidden" id="photos-form-submit-loading" />
    </div>
    <input type="hidden" id="facility_id" name="facility_id" value="<?=$this->facility->getId()?>" />
</form>

<div class="row">
    <div class="col-md-9">
        <div class="col-md-5">
        <?php
        /**
         * @var $image Genesis_Entity_FacilityImage
         */
        foreach ($this->facility->getImages() as $key => $image): ?>
        <?php if ($image->getPictureNum() == 1): ?>

            <div class="thumbnail">
                <img src="<?=$this->facility->getFirstImage()->getCdnMedium()?>" /><br />
                <div class="caption">
                    <a href="javascript:confirmRedirect('Are you sure you want to delete this image?','<?=$this->url(['action'=>'deleteimage'], 'features')?>?fid=<?=$this->facilityId?>&number=<?=$this->facility->getFirstImage()->getPictureNum()?>');" class="ui button icon">
                        <i class="trash outline icon"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-7">

        <?php else: ?>
            <div style="margin-bottom:1em; margin-right:1em; float:left;" class="thumbnail">
                <img src="<?=$image->getCdnSmall()?>" />
                <br />
                <div class="caption">
                    <a href="javascript:confirmRedirect('Are you sure you want to delete this image?','<?=$this->url(['action'=>'deleteimage'], 'features')?>?fid=<?=$this->facilityId?>&number=<?=$image->getPictureNum()?>');" class="ui icon button">
                        <i class="trash outline icon"></i>
                    </a>

                    <br />
                    <br />
                    <a href="<?=$this->url(['action'=>'defaultimage'], 'features')?>?fid=<?=$this->facilityId?>&number=<?=$image->getPictureNum()?>" class="ui secondary button">Set as Default</a>

                </div>
            </div>
        <?php endif; ?>
        <?php endforeach; ?>
        <div class="clear"></div>
        </div>
    </div>
    <div class="col-md-3" >
        <p>NOTE: SpareFoot prohibits photos that display your phone number or other contact information, and reserves the right to edit or remove such photos. If you would like us to edit a photo for you, email <a href="mailto:<EMAIL>"><EMAIL></a> with your facility name, address, and the photo attached.</p>
    </div>
</div>
