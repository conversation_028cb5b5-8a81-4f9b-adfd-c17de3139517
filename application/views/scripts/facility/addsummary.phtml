<div class="setup-content-container">

    <div class="setup-content">

        <div class="content-row">
            <?php if($this->facility) {?>
                <h2>New facility added</h2>
            <?php } else {?>
                <h2>New facilities synced</h2>
            <?php } ?>
            <p>For each new facility, please select a payment method. You can use one of your existing payment methods by selecting it from the drop down box, or enter a new payment method (new credit card, ACH, etc.) by selecting the "Add a New Payment Type" option.</p>
        </div>

        <div class="content-row smaller">

            <div class="alert alert-danger hide">Please choose a payment type for each new facility.</div>

            <table class="table">
                <tr class="header">
                    <th>Facility</th>
                    <th>Payment Method</th>

                </tr>

            <?php foreach($this->facilities as $fac) { ?>
                <tr id="<?=$fac->getId()?>">
                    <td><?=$fac->getTitle()?></td>
                    <td>
                        <select id="fac_<?=$fac->getId()?>" name="beId" class="form-control" onChange="changeFacBe(<?=$fac->getId()?>, this.value)">
                        <?php if (!$fac->getBillableEntityId()) {?><option value="choose" selected="selected">Choose a payment type</option><?php }?>
                        <?php foreach($this->payment_methods as $be) { ?>
                            <option value="<?=$be['id']?>" <?php if ($fac->getBillableEntityId() == $be['id']) {?>selected="selected"<?php }?>><?=$be['displayStr']?></option>
                        <?php } ?>
                            <option value="new">Add a new payment type</option>
                        </select>
                    </td>
                </tr>
            <?php } ?>

            </table>

        </div>

        <div class="content-footer">
            <form id="target" method="post" action="<?=$this->url(['action'=>'addsummary'], 'features')?>" class="pull-right">
                <img src="/images/loaders/small.gif" class="hide" />&nbsp;&nbsp;
                <input type="hidden" id="syncd_fac_ids" name="syncd_fac_ids" value="<?=implode(",",$this->facIds)?>"/>
                <input class="ui primary large button" type="submit" data-loading-text="Saving" value="Finish"/>
            </form>
            <a href="<?=$this->url(['action'=>'type'], 'features')?>" class="ui basic large button">Back</a>
        </div>

    </div>

</div>
