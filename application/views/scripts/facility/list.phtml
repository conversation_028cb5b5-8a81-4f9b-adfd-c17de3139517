<?php
$facility = Genesis_Service_Facility::loadById($this->facilityId);
?>

<div class="toolbar">
    <?=$this->partial('daterange.phtml',
        array(
            'action' => $this->url([], 'account-overview'),
            'trueDateRange' => $this->trueDateRange,
            'trueBeginDate' => $this->trueBeginDate,
            'trueEndDate'   => $this->trueEndDate,
            'limit'         => $this->limit,
            'page'          => $this->page,
            'showExport'    => false,
        )
    )?>

    <a href="<?=$this->url(['action'=>'export'], 'features')?>?true_date_range=<?=urlencode($this->trueDateRange)?>" class="ui secondary button pull-right" style="margin-right:0.5em;"><i class="file icon"></i> Export Spreadsheet</a>

    <?php
    //now they can add a facility even if they don't have a manual integration yet (one will be created)
    if ( $this->loggedUser->isMyfootGod() || $this->loggedUser->isMyfootAdmin()) { ?>
        <a id="add_facility" class="ui primary button" href="<?=$this->url(['action'=>'type'], 'features')?>"><i class="fa fa-plus"></i> Add Facility</a>
    <?php } ?>
</div>
<br class="clear" />
<form class="ui form form-search" action="<?=$this->url([], 'account-overview')?>" method="get">
    <!-- <span class="fa fa-search"></span> -->

    <div class="ui fluid action input">
      <input type="search" name="search_term" id="search_term" placeholder="Search Facilities" value="<?=$this->searchTerm?>" />
      <button class="ui icon button">
        <i class="search icon"></i>
      </button>
    </div>


    <?php if($this->searchTerm): ?>
    <span class="clear-search">
        <a href="<?=$this->url([], 'account-overview')?>?clear_search_term=1">
            <img src="/images/search-clear.gif" />
        </a>
    </span>
    <?php endif; ?>
</form>

<?php if (count($this->facilities) == 0): ?>
<h5>No facilities found. <a href="<?=$this->url([], 'account-overview')?>?clear_search_term=1">Clear Search.</a></h5>
<?php else: ?>

<table id="facilities" class="ui table table-striped data-grid">
    <thead>
        <tr>
            <th>Facility</th>
            <th><a data-rel="tooltip" title="The method of which this facility data was input into SpareFoot.">Source</a></th>
            <?php if ($this->loggedUser->getAccount()->getBidType() !== Genesis_Entity_Account::BID_TYPE_RESIDUAL) {?>
                <th><a data-rel="tooltip" title="Your bid affects where you rank in AdNetwork search results. Increasing your bid raises your ranking, which generally results in more reservations.">Bid</a></th>
            <?php } ?>
            <th>Reservations</th>
            <?php if ($this->loggedUser->getAccount()->getBidType() !== Genesis_Entity_Account::BID_TYPE_RESIDUAL) {?>
                <th><a data-rel="tooltip" title="Average cost per reservation is the total cost of all reservations at a facility divided by the number of reservations at that facility. If you received reservations at different bid amounts, the average cost per reservation is the average of those bid amounts.">Avg. Cost Per Reservation</a></th>
            <?php } ?>
            <th>Inquiries</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($this->facilities as $facility): ?>
            <tr id="<?=$facility['id']?>" class="<?=$facility['hidden'] ? 'disabled-row' : 'enabled-row' ?>">
                <td>
                    <strong><a href="<?=$this->url(['action' => 'units'], 'features')?>?fid=<?=$facility['id']?>" id="facility-page-<?=$facility['id']?>"><?=$facility['title'];?></a></strong>
                    <?php if ($facility['hidden']): ?>
                    <span class="ui label">Inactive</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?=$facility['source_name']?>
                    <?php
                    if ($facility['source_id'] == Genesis_Entity_Source::ID_MANUAL && ! $facility['self_reported_source_id']) {
                        ?>(<a href="<?=$this->url(['action'=>'details'], 'features')?>?fid=<?=$facility['id']?>#facility_software">Edit</a>)<?php
                    }
                    ?>
                </td>
                <?php if ($this->loggedUser->getAccount()->getBidType() !== Genesis_Entity_Account::BID_TYPE_RESIDUAL): ?>
                <td><?=$facility['bid_string']?></td>
                <?php endif; ?>
                <td><?=$facility['num_reservations']?></td>
                <?php if ($this->loggedUser->getAccount()->getBidType() !== Genesis_Entity_Account::BID_TYPE_RESIDUAL): ?>
                <td>$<?=number_format($facility['cost_per_reservation'], 2)?></td>
                <?php endif; ?>
                <td><?=$facility['consumer_contacts']?></td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>

<?php
/*
Quick Paginator adapted from:
http://code.tutsplus.com/tutorials/how-to-paginate-data-with-php--net-2928
*/
function createLinks( $links, $list_class, $page, $limit, $total) {
    if ( $limit == 'all' ) {
        return '';
    }

    $last       = ceil( $total / $limit );
    $start      = ( ( $page - $links ) > 0 ) ? $page - $links : 1;
    $end        = ( ( $page + $links ) < $last ) ? $page + $links : $last;

    $html       = '<div class="' . $list_class . '">';
    $class      = ( $page == 1 ) ? "disabled" : "";
    $url        = ( $page == 1 ) ? '' : ('?limit=' . $limit . '&page=' . ( $page - 1 ));
    $html       .= '<a class="'.$class.' item icon" href="' . $url . '"><i class="icon arrow left"></i></a>';

    if ( $start > 1 ) {
        $html   .= '<a class="item" href="?limit=' . $limit . '&page=1">1</a>';
        $html   .= '<div class="disabled item"><span>...</span></div>';
    }

    for ( $i = $start ; $i <= $end; $i++ ) {
        $class  = ( $page == $i ) ? "active" : "";
        $html   .= '<a class="' . $class . ' item" href="?limit=' . $limit . '&page=' . $i . '">' . $i . '</a>';
    }

    if ( $end < $last ) {
        $html   .= '<div class="disabled item"><span>...</span></div>';
        $html   .= '<a class="item" href="?limit=' . $limit . '&page=' . $last . '">' . $last . '</a>';
    }

    $class      = ( $page == $last ) ? "disabled" : "";
    $url        = ( $page == $last ) ? '' : '?limit=' . $limit . '&page=' . ( $page + 1 );
    $html       .= '<a class="' . $class . ' item icon" href="'.$url.'"><i class="icon arrow right"></i></a>';
    $html       .= '</div>';

    return $html;
}
?>

<?php
if (!$this->searchTerm && $this->totalItems > $this->limit) {
    echo createLinks(3, 'ui pagination menu', $this->page, $this->limit, $this->totalItems);
}
?>

<?php endif; ?>
