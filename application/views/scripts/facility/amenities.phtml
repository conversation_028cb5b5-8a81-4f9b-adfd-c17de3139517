<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'amenities', 'loggedUser' => $this->loggedUser, 'facility' => $this->facility))?>

<h3>Amenities</h3>

<form method="post" action="<?=$this->url(['action'=>'amenities'], 'features')?>?fid=<?=$this->facilityId?>" class="form-horizontal">

    <?php if($this->alert){ ?>
        <p class="alert<?=($this->alertClass?' '.$this->alertClass:'')?>">
            <?=$this->alert?>
        </p>
    <?php } ?>
    <script>
        var integratedFields = <?php echo isset($depositIsDisabled) && $depositIsDisabled ? "['security_deposit_required']" : "[]" ?>
    </script>
    <h4>Moving Options</h4>

    <div class="form-group<?=(in_array('truck_rental', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Truck rental</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="truck-rental-yes" name="truck_rental" value="1"<?=($this->facility->getTruckRental() == '1') ? ' checked="checked"':''?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="truck-rental-no" name="truck_rental" value="0"<?=($this->facility->getTruckRental() == '0') ? ' checked="checked"':''?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('free_truck_rental', $this->erroredFields))?' has-error':''?>" id="free-truck">
        <label class="col-md-2 control-label">Free use of truck</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="free-truck-yes" name="free_truck_rental" value="1" onclick="$('#free-truck-stipulations').show();" <?=($this->facility->getFreeTruckRental() == '1') ? 'checked="checked"':''?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="free-truck-no" name="free_truck_rental" value="0" onclick="$('#free-truck-stipulations').hide();" <?=($this->facility->getFreeTruckRental() == '0') ? 'checked="checked"':''?> />No
            </label>
        </div>
    </div>

    <div class="ui segment" id="free-truck-stipulations" style="display:<?=$this->facility->getFreeTruckRental() ? 'block' : 'none'?>">

        <div class="form-group<?=(in_array('truck_distance_limit', $this->erroredFields))?' has-error':''?>" id="show-distance-limit">
            <label class="col-md-2 col-md-offset-1 control-label">Truck distance limit</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="free-truck-distance-limit-yes" name="truck_distance_limit" onclick="$('#show-max-mileage').show();" value="1" <?php if ($this->facility->getTruckDistanceLimit() || in_array('truck_max_mileage', $this->erroredFields)) { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="free-truck-distance-limit-no" name="truck_distance_limit" onclick="$('#show-max-mileage').hide();$('#free-truck-max-mileage').val('');" value="0" <?php if ($this->facility->getTruckDistanceLimit() == '0' && !in_array('truck_max_mileage', $this->erroredFields)) { echo 'checked="checked"'; } ?> />No</span></td>
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('truck_max_mileage', $this->erroredFields))?' has-error':''?>" id="show-max-mileage" style="display:<?=$this->facility->getTruckDistanceLimit() > 0 || in_array('truck_max_mileage', $this->erroredFields) ? 'block' : 'none'?>;">
            <label class="col-md-2 col-md-offset-2 control-label">Max mileage</label>
            <div class="col-md-8">
                <div class="input-group">
                    <input class="form-control" type="text" id="free-truck-max-mileage" name="truck_max_mileage" value="<?=$this->facility->getTruckDistanceLimit()?>">
                    <span class="input-group-addon">mi</span>
                </div>
            </div>
        </div>

        <div class="form-group<?=(in_array('truck_insurance_required', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 col-md-offset-1 control-label">Truck insurance required</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="truck-insurance-required-yes" name="truck_insurance_required" onclick="$('#show-free-truck-insurance-amount').show();" value="1" <?php if ($this->facility->getTruckInsurance() || in_array('truck_insurance_amount', $this->erroredFields)) { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="truck-insurance-required-no" name="truck_insurance_required" onclick="$('#show-free-truck-insurance-amount').hide();$('#free-truck-insurance-amount').val('');" value="0" <?php if ($this->facility->getTruckInsurance() == '0' && !in_array('truck_insurance_amount', $this->erroredFields)) { echo 'checked="checked"'; } ?> />No</span></td>
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('truck_insurance_amount', $this->erroredFields))?' has-error':''?>" id="show-free-truck-insurance-amount" style="display:<?= $this->facility->getTruckInsurance() > 0 || in_array('truck_insurance_amount', $this->erroredFields) ? 'block' : 'none'?>;">
            <label class="col-md-2 col-md-offset-2 control-label">Truck insurance amount</label>
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-addon">$</span>
                    <input class="form-control" type="text" id="free-truck-insurance-amount" name="truck_insurance_amount" value="<?=$this->facility->getTruckInsurance()?>">
                </div>
            </div>
        </div>

        <div class="form-group<?=(in_array('truck_fuel_refill', $this->erroredFields))?' has-error':''?>" id="show_distance_limit">
            <label class="col-md-2 col-md-offset-1 control-label">Tenant refuels</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="free-truck-fuel-refill-yes" name="truck_fuel_refill" value="1" <?php if ($this->facility->getTruckRefuelPolicy() == '1') { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="free-truck-fuel-refill-no" name="truck_fuel_refill" value="0" <?php if ($this->facility->getTruckRefuelPolicy() == '0') { echo 'checked="checked"'; } ?> />No</span></td>
                </label>
            </div>
        </div>

    </div>


    <div class="form-group<?=(in_array('truck_access', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Loading dock</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="truck-access-yes" name="truck_access" value="1" onclick="document.getElementById('show-truck-access-size').style.display='block';" <?php if ($this->facility->getTruckAccess() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="truck-access-no" name="truck_access" value="0" onclick="document.getElementById('show-truck-access-size').style.display='none';$('#truck-access-size').val('');" <?php if ($this->facility->getTruckAccess() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="ui segment" id="show-truck-access-size" style="display:<?= $this->facility->getTruckAccess() ? 'block' : 'none'?>;">
        <div class="form-group<?=(in_array('truck_access_size', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 col-md-offset-1 control-label">What size truck can fit?</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input class="form-control" type="text" id="truck-access-size" name="truck_access_size" value="<?=$this->facility->getTruckAccessSize()?>">
                    <span class="input-group-addon">ft</span>
                </div>
            </div>
        </div>
    </div>

    <?php if (Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_ALLOW_18WHEELER_DROPOFF)): ?>
        <div class="form-group<?=(in_array('allow_18wheeler_dropoff', $this->erroredFields))?' has-error': null ?>">
            <label class="col-md-2 control-label">Is your facility parking lot or driveway large enough for an 18-wheel truck?</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="allow-18wheeler-dropoff-yes" name="allow_18wheeler_dropoff" value="1" <?=($this->facility->getAllow18WheelerDropoff() == '1') ? 'checked="checked"': null ?> onclick="$('#has-18wheeler-alleys').removeClass('hidden'); " />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="allow-18wheeler-dropoff-no" name="allow_18wheeler_dropoff" value="0" <?=($this->facility->getAllow18WheelerDropoff() == '0') ? 'checked="checked"' : null ?> onclick="$('#has-18wheeler-alleys').addClass('hidden'); $('#has-18wheeler-alleys-false').prop('checked', true );" />No
                </label>
            </div>
        </div>

        <div id="has-18wheeler-alleys" class="ui segment <?=$this->facility->getAllow18WheelerDropoff() == '1' ? null : 'hidden' ?>">
            <div class="form-group<?=(in_array('has_18wheeler_alleys', $this->erroredFields))?' has-error': null ?>">
                <label class="col-md-2 col-md-offset-1 control-label">Are your unit drive aisles wide enough for an 18-wheel truck?</label>

                <div class="col-md-9">
                    <label class="control-label radio-inline">
                        <input type="radio" id="has-18wheeler-alleys-true" name="has_18wheeler_alleys" value="1" <?=($this->facility->getHas18WheelerAlleys() == '1') ? 'checked="checked"': null ?> />Yes
                    </label>
                    <label class="control-label radio-inline">
                        <input type="radio" id="has-18wheeler-alleys-false" name="has_18wheeler_alleys" value="0" <?=($this->facility->getHas18WheelerAlleys() == '0') ? 'checked="checked"': null ?> />No
                    </label>
                </div>
            </div>
        </div>
    <?php endif; ?>


    <div class="form-group<?=(in_array('handcarts', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Handcarts or dollies</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="handcarts-yes" name="handcarts" value="1" <?php if ($this->facility->getHandcarts() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="handcarts-no" name="handcarts" value="0" <?php if ($this->facility->getHandcarts() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('elevator', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Elevator</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="elevator-yes" name="elevator" value="1" <?php if ($this->facility->getElevator() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="elevator-yes" name="elevator" value="0" <?php if ($this->facility->getElevator() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('sell_moving_supplies', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Moving supplies for sale</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="sell-moving-supplies-yes" name="sell_moving_supplies" value="1" <?php if ($this->facility->getSellsMovingSupplies() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="sell-moving-supplies-no" name="sell_moving_supplies" value="0" <?php if ($this->facility->getSellsMovingSupplies() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <hr />

    <h4>Security Options</h4>

    <div class="form-group<?=(in_array('surveillance', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Video cameras onsite</label>
        <div class="col-md-10">
            <label class="radio-inline">
                <input type="radio" id="surveillance-yes" name="surveillance" value="1" <?php if ($this->facility->getSurveillance() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label control-label radio-inline">
                <input type="radio" id="surveillance-no" name="surveillance" value="0" <?php if ($this->facility->getSurveillance() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('egate_access', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Electronic gate access</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="egate-access-yes" name="egate_access" value="1" <?php if ($this->facility->getEGateAccess() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="egate-access-no" name="egate_access" value="0" <?php if ($this->facility->getEGateAccess() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('fenced_lighted', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Fenced &#43; lighted</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="fenced-lighted-yes" name="fenced_lighted" value="1" <?php if ($this->facility->getFencedLighted() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="fenced-lighted-no" name="fenced_lighted" value="0" <?php if ($this->facility->getFencedLighted() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('resident_manager', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Resident manager</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="resident-manager-yes" name="resident_manager" value="1" <?php if ($this->facility->getResidentManager() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="resident-manager-no" name="resident_manager" value="0" <?php if ($this->facility->getResidentManager() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <hr />

    <h4>Access</h4>

    <div class="form-group<?=(in_array('kiosk', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">24-hour kiosk</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="kiosk-yes" name="kiosk" value="1" <?php if ($this->facility->getKiosk() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="kiosk-no" name="kiosk" value="0" <?php if ($this->facility->getKiosk() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('bilingual_manager_available', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Bilingual manager</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="bilingual-manager-available-yes" name="bilingual_manager_available" value="1" onclick="document.getElementById('show_bilingual_language').style.display='block';" <?php if ($this->facility->getBilingualManager()) { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="bilingual-manager-available-no" name="bilingual_manager_available" value="0" onclick="document.getElementById('show_bilingual_language').style.display='none';$('#show_bilingual_language').val('');" <?php if ($this->facility->getBilingualManager() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="show_bilingual_language" style="display:<?= $this->facility->getBilingualManager() ? 'block' : 'none'?>;">
        <div class="form-group">
            <label class="col-md-2 col-md-offset-1 control-label">Bilingual language</label>
            <div class="col-md-9">
                <input type="text" id="bilingual-language" name="bilingual_language" class="form-control" value="<?=($this->facility->getBilingualManager() ? $this->facility->getBilingualManager() : '') ?>" />
            </div>
        </div>
    </div>

    <div class="form-group<?=(in_array('accept_tenant_mail', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Mail or packages accepted</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="accept-tenant-mail-yes" name="accept_tenant_mail" value="1" <?php if ($this->facility->getAcceptTenantMail() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="accept-tenant-mail-no" name="accept_tenant_mail" value="0" <?php if ($this->facility->getAcceptTenantMail() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <hr />

    <h4>Payment Options</h4>

    <div class="form-group<?=(in_array('payment_accept_cash', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Cash</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="payment-accept-cash-yes" name="payment_accept_cash" value="1" <?php if ($this->paymentAcceptCash == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="payment-accept-cash-no" name="payment_accept_cash" value="0" <?php if ($this->paymentAcceptCash == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('payment_accept_check', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Check</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="payment-accept-check-yes" name="payment_accept_check" value="1" <?php if ($this->paymentAcceptCheck == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="payment-accept-check-no" name="payment_accept_check" value="0" <?php if ($this->paymentAcceptCheck == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('payment_accept_credit', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Credit cards</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input onclick="$('#accepted_cards').show();" type="radio" id="payment-accept-credit-yes" name="payment_accept_credit" value="1" <?php if ($this->paymentAcceptCredit == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input onclick="$('#accepted_cards').hide(); $('[name=\'payment_accepted_cards[]\']').prop('checked', false);" id="payment-accept-credit-no" type="radio" name="payment_accept_credit" value="0" <?php if ($this->paymentAcceptCredit == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="accepted_cards" style="display:<?=($this->paymentAcceptCredit ? 'block' : 'none')?>">

        <div class="form-group<?=(in_array('payment_accepted_cards', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-3 col-md-offset-1 control-label">Cards accepted</label>
            <div class="col-md-8">
                <label class="control-label checkbox-inline">
                    <input type="checkbox" id="payment-visa" name="payment_accepted_cards[]" value="Visa"
                        <?php if (is_array($this->paymentAcceptedCards)
                            && in_array('Visa', $this->paymentAcceptedCards)
                        ) {
                            echo 'checked="checked"';
                        }
                        ?>
                    />Visa
                </label>
                <label class="control-label checkbox-inline">
                    <input type="checkbox" id="payment-mastercard" name="payment_accepted_cards[]" value="Mastercard"
                        <?php if (is_array($this->paymentAcceptedCards)
                            && in_array('Mastercard', $this->paymentAcceptedCards)
                        ) {
                            echo 'checked="checked"';
                        }
                        ?>
                    />Mastercard
                </label>
                <label class="control-label checkbox-inline">
                    <input type="checkbox" id="payment-amex" name="payment_accepted_cards[]" value="AMEX"
                        <?php if (is_array($this->paymentAcceptedCards)
                            && in_array('AMEX', $this->paymentAcceptedCards)
                        ) {
                            echo 'checked="checked"';
                        }
                        ?>
                    />AMEX
                </label>
                <label class="control-label checkbox-inline">
                    <input type="checkbox" id="payment-discover" name="payment_accepted_cards[]" value="Discover"
                        <?php if (is_array($this->paymentAcceptedCards)
                            && in_array('Discover', $this->paymentAcceptedCards)
                        ) {
                            echo 'checked="checked"';
                        }
                        ?>
                    />Discover
                </label>
            </div>
        </div>

    </div>

    <hr />

    <h4>Billing</h4>

    <div class="form-group<?=(in_array('email_invoicing', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Email invoicing available</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="email-invoicing-yes" name="email_invoicing" value="1" <?php if ($this->facility->getEmailInvoicingAvailable() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="email-invoicing-no" name="email_invoicing" value="0" <?php if ($this->facility->getEmailInvoicingAvailable() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('auto_payments', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Automatic payments available</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="auto-payments-yes" name="auto_payments" value="1" <?php if ($this->facility->getAutoPayAvailable() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="auto-payments-no" name="auto_payments" value="0" <?php if ($this->facility->getAutoPayAvailable() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('charge_date', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Charge date</label>
            <div class="col-md-10">
                <select name="charge_date" id="charge-date" class="form-control">
                    <option value=""></option>
                    <option value="<?=Genesis_Entity_FacilityData::CHARGE_ON_FIRST_OF_MONTH?>" <?=($this->facility->getChargeDate() == Genesis_Entity_FacilityData::CHARGE_ON_FIRST_OF_MONTH ? 'selected="selected"' : '')?>>First of month</option>
                    <option value="<?=Genesis_Entity_FacilityData::CHARGE_ON_MOVE_IN_ANNIVERSARY?>" <?=($this->facility->getChargeDate() == Genesis_Entity_FacilityData::CHARGE_ON_MOVE_IN_ANNIVERSARY ? 'selected="selected"' : '')?>>Monthly anniversary date</option>
                    <option value="<?=Genesis_Entity_FacilityData::CHARGE_OTHER_TIME?>" <?=($this->facility->getChargeDate() == Genesis_Entity_FacilityData::CHARGE_OTHER_TIME ? 'selected="selected"' : '')?>>Other</option>
                </select>
            </div>
    </div>

    <div class="form-group<?=(in_array('security_deposit_required', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Security deposit required</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="security-deposit-required-yes" name="security_deposit_required" value="1" onclick="$('#security-deposit-options').show();" <?php if ($this->facility->getSecurityDeposit()) { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="security-deposit-required-no" name="security_deposit_required" value="0" onclick="$('#security-deposit-options').hide();$('#deposit_price').val('');" <?php if ($this->facility->getSecurityDeposit() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="security-deposit-options" style="display:<?= $this->facility->getSecurityDeposit() ? 'block' : 'none'?>;">
        <div class="form-group<?=(in_array('security_deposit_refundable', $this->erroredFields))?' has-error':''?>" >
            <label class="col-md-2 col-md-offset-1 control-label">Security deposit refundable</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="security-deposit-refundable-yes" name="security_deposit_refundable" value="1" <?php if ($this->facility->getSecurityDepositRefundable() == '1') { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="security-deposit-refundable-no" name="security_deposit_refundable" value="0" <?php if ($this->facility->getSecurityDepositRefundable() == '0') { echo 'checked="checked"'; } ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('deposit_price', $this->erroredFields))?' has-error':''?>" id="show_security_deposit_options">
           <label class="col-md-2 col-md-offset-1 control-label">Deposit amount</label>

            <div class="col-md-9 <?=(in_array('security_deposit_type', $this->erroredFields))?'has-error':''?>">
                <div class="btn-group btn-group-justified">
                    <a role="button" id="security-deposit-percent-button" class="ui basic teal button" data-deposit-type="percent">% of first month's rent</a>
                    <a role="button" id="security-deposit-dollar-button" class="ui basic teal button" data-deposit-type="dollar">Flat dollar amount</a>
                </div>
                <input type="hidden" name="security_deposit_type" value="<?=$this->facility->getSecurityDepositType();?>" />
            </div>
        </div>

        <div class="form-group<?=(in_array('deposit_price', $this->erroredFields))?' has-error':''?>">

            <div class="col-md-9 col-md-offset-3 security-deposit-types security-deposit-percent">
                <div class="input-group <?=(in_array('security_deposit_percent', $this->erroredFields))?'has-error':''?>">
                    <input type="text" id="security-deposit-percent" name="security_deposit_percent" class="form-control" value="<?=($this->facility->getSecurityDepositPercent() ? $this->facility->getSecurityDepositPercent() : '') ?>" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
            <div class="col-md-9 col-md-offset-3 security-deposit-types security-deposit-dollar">
                <div class="input-group <?=(in_array('security_deposit_dollar', $this->erroredFields))?'has-error':''?>">
                    <span class="input-group-addon">$</span>
                    <input type="text" id="security-deposit-dollar" name="security_deposit_dollar" class="form-control" value="<?=($this->facility->getSecurityDepositDollar() ? $this->facility->getSecurityDepositDollar() : '') ?>" />
                </div>
            </div>

        </div>

    </div>

    <hr />

    <h4>Discounts</h4>

    <div class="form-group<?=(in_array('military_discount_available', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Military discount</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="military-discount-available-yes" name="military_discount_available" value="1" onclick="document.getElementById('show-military-discount').style.display='block';" <?php if ($this->facility->getMilitaryDiscount()) { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="military-discount-available-no" name="military_discount_available" value="0" onclick="document.getElementById('show-military-discount').style.display='none';$('#military_discount_amount').val('');" <?php if ($this->facility->getMilitaryDiscount() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="show-military-discount" style="display:<?= $this->facility->getMilitaryDiscount() ? 'block' : 'none'?>;">
        <div class="form-group<?=(in_array('military_discount_amount', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 col-md-offset-1 control-label">Military discount amount</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="text" name="military_discount_amount" class="form-control" value="<?=($this->facility->getMilitaryDiscount() ? $this->facility->getMilitaryDiscount() : $this->facility->getDefaultMilitaryDiscount()) ?>" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
        </div>
        <div class="form-group<?=(in_array('military_discount_reserves', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 col-md-offset-1 control-label">Applies to reserves</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="military-discount-reserves-yes" name="military_discount_reserves" value="1" <?php if ($this->facility->getMilitaryDiscountAppliesToReserves()) { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="military-discount-reserves-no" name="military_discount_reserves" value="0" <?php if ($this->facility->getMilitaryDiscountAppliesToReserves() == '0') { echo 'checked="checked"'; } ?> />No
                </label>
            </div>
        </div>
        <div class="form-group<?=(in_array('military_discount_veterans', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 col-md-offset-1 control-label">Applies to veterans</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="military-discount-veterans-yes" name="military_discount_veterans" value="1" <?php if ($this->facility->getMilitaryDiscountAppliesToVeterans()) { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="military-discount-veterans-no" name="military_discount_veterans" value="0" <?php if ($this->facility->getMilitaryDiscountAppliesToVeterans() == '0') { echo 'checked="checked"'; } ?> />No
                </label>
            </div>
        </div>
    </div>

    <!--
    <div class="ui segment " id="show-military-discount" style="display:<?//= $this->facility->getMilitaryDiscount() ? 'block' : 'none'?>;">
        <div class="form-group<?//=(in_array('military_discount_amount', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 col-md-offset-1 control-label">Military discount amount</label>
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" name="military_discount_percent_amount" class="form-control" value="<?//=($this->facility->getMilitaryDiscount() ? $this->facility->getMilitaryDiscount() : '') ?>" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
            <div class="col-md-1">
                OR
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-addon">$</span>
                    <input type="text" name="military_discount_dollar_amount" class="form-control" value="<?//=($this->facility->getMilitaryDiscount() ? $this->facility->getMilitaryDiscount() : '') ?>" />
                </div>
            </div>
        </div>
    </div>
    -->

    <div class="form-group<?=(in_array('senior_discount_available', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Senior discount</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="senior-discount-available-yes" name="senior_discount_available" value="1" onclick="document.getElementById('show-senior-discount').style.display='block';" <?php if ($this->facility->getSeniorDiscount()) { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="senior-discount-available-no" name="senior_discount_available" value="0" onclick="document.getElementById('show-senior-discount').style.display='none';$('#senior_discount_amount').val('');" <?php if ($this->facility->getSeniorDiscount() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="show-senior-discount" style="display:<?= $this->facility->getSeniorDiscount() ? 'block' : 'none'?>;">
        <div class="form-group<?=(in_array('senior_discount_amount', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 col-md-offset-1 control-label">Senior discount amount</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="text" name="senior_discount_amount" class="form-control" value="<?=($this->facility->getSeniorDiscount() ? $this->facility->getSeniorDiscount() : $this->facility->getDefaultSeniorDiscount()) ?>" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group<?=(in_array('student_discount_available', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Student discount</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="student-discount-available-yes" name="student_discount_available" value="1" onclick="document.getElementById('show-student-discount').style.display='block';" <?php if ($this->facility->getStudentDiscount()) { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="student-discount-available-no" name="student_discount_available" value="0" onclick="document.getElementById('show-student-discount').style.display='none';$('#student_discount_amount').val('');" <?php if ($this->facility->getStudentDiscount() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="show-student-discount" style="display:<?= $this->facility->getStudentDiscount() ? 'block' : 'none'?>;">
        <div class="form-group<?=(in_array('student_discount_amount', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 col-md-offset-1 control-label">Student discount amount</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="text" name="student_discount_amount" class="form-control" value="<?=($this->facility->getStudentDiscount() ? $this->facility->getStudentDiscount() : $this->facility->getDefaultStudentDiscount()) ?>" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
        </div>
    </div>

    <hr />

    <h4>Insurance</h4>

    <div class="form-group<?=(in_array('insurance_required', $this->erroredFields))?' has-error':''?>" id="show_insurance_options">
        <label class="col-md-2 control-label">Insurance required</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="insurance-options-yes" name="insurance_required" value="1" <?php if ($this->facility->getInsuranceRequired() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="insurance-options-no" name="insurance_required" value="0" <?php if ($this->facility->getInsuranceRequired() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

	<div class="form-group<?=(in_array('insurance_available', $this->erroredFields))?' has-error':''?>" id="show-available-insurance-options">
		<label class="col-md-2 control-label">Insurance available</label>
		<div class="col-md-9">
			<label class="control-label radio-inline">
				<input type="radio" id="insurance-available-yes" name="insurance_available" value="1" <?php if ($this->facility->getInsuranceAvailable() == 1) { echo 'checked="checked"'; } ?> />Yes
			</label>
			<label class="control-label radio-inline">
				<input type="radio" id="insurance-available-no" name="insurance_available" value="0" <?php if (!is_null($this->facility->getInsuranceAvailable()) && $this->facility->getInsuranceAvailable() == 0) { echo 'checked="checked"'; } ?> />No
			</label>
		</div>
	</div>

	<?php if (AccountMgmt_Service_User::isFeatureActive('myfoot.protection_plan_facility_amenities')) { ?>

		<div class="form-group<?=(in_array('protection_plan_required', $this->erroredFields))?' has-error':''?>">
			<label class="col-md-2 control-label">Protection Plan required</label>
			<div class="col-md-10">
				<label class="control-label radio-inline">
					<input type="radio" id="protection-plan-required-yes" name="protection_plan_required" value="1" <?php if ($this->facility->getProtectionPlanRequired() == '1') { echo 'checked="checked"'; } ?> />Yes
				</label>
				<label class="control-label radio-inline">
					<input type="radio" id="protection-plan-required-no" name="protection_plan_required" value="0" <?php if ($this->facility->getProtectionPlanRequired() == '0') { echo 'checked="checked"'; } ?> />No
				</label>
			</div>
		</div>

		<div class="form-group<?=(in_array('protection_plan_available', $this->erroredFields))?' has-error':''?>">
			<label class="col-md-2 control-label">Protection Plan available</label>
			<div class="col-md-9">
				<label class="control-label radio-inline">
					<input type="radio" id="protection-plan-available-yes" name="protection_plan_available" value="1" <?php if ($this->facility->getProtectionPlanAvailable() == 1) { echo 'checked="checked"'; } ?> />Yes
				</label>
				<label class="control-label radio-inline">
					<input type="radio" id="protection-plan-available-no" name="protection_plan_available" value="0" <?php if (!is_null($this->facility->getProtectionPlanAvailable()) && $this->facility->getProtectionPlanAvailable() == 0) { echo 'checked="checked"'; } ?> />No
				</label>
			</div>
		</div>

	<?php } ?>

	<div class="form-group<?=(in_array('homeowners_insurance_accepted', $this->erroredFields))?' has-error':''?>">
		<label class="col-md-2 control-label">Homeowners or renters insurance accepted</label>
		<div class="col-md-9">
			<label class="control-label radio-inline">
				<input type="radio" id="homeowners-insurance-accepted-yes" name="homeowners_insurance_accepted" value="1" <?php if ($this->facility->getHomeownersInsuranceAccepted() == '1') { echo 'checked="checked"'; } ?> />Yes
			</label>
			<label class="control-label radio-inline">
				<input type="radio" id="homeowners-insurance-accepted-no" name="homeowners_insurance_accepted" value="0" <?php if ($this->facility->getHomeownersInsuranceAccepted() == '0') { echo 'checked="checked"'; } ?> />No
			</label>
		</div>
	</div>

	<hr>
    <h4>Other Amenities</h4>

    <div class="form-group<?=(in_array('band_practice_allowed', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Band practice allowed</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="band-practice-allowed-yes" name="band_practice_allowed" value="1" <?php if ($this->facility->getBandPracticeAllowed() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="band-practice-allowed-no" name="band_practice_allowed" value="0" <?php if ($this->facility->getBandPracticeAllowed() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <div class="form-group<?=(in_array('remote_paperwork', $this->erroredFields))?' has-error':''?>">
        <label class="col-md-2 control-label">Paperwork can be done remotely</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="remote-paperwork-yes" name="remote_paperwork" value="1" <?php if ($this->facility->getRemotePaperwork() == '1') { echo 'checked="checked"'; } ?> />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="remote-paperwork-no" name="remote_paperwork" value="0" <?php if ($this->facility->getRemotePaperwork() == '0') { echo 'checked="checked"'; } ?> />No
            </label>
        </div>
    </div>

    <hr>

    <h4>Vehicle Storage</h4>
    <script>
        /**
         * Semantic ui css is present, but not JS to make its accordian available.
         * @param state
         * @param event
         */
        function toggleVehicle(event) {
            event.preventDefault();
            if (event.currentTarget.text === 'Hide vehicle options') {
                event.currentTarget.text = 'Show vehicle options';
                document.getElementById('vehicle-amenities').style.display='none';
            } else {
                event.currentTarget.text = 'Hide vehicle options';
                document.getElementById('vehicle-amenities').style.display='block';
            }
        }
    </script>
    <div class="col-md-10">
        <a id="vehicle-hide" onclick="toggleVehicle(event);">Hide vehicle options</a>
    </div>

    <hr>
    <hr>

    <div id="vehicle-amenities" class="ui segment">

        <div class="form-group<?=(in_array('vehicle_require_title', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Requires title</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-requires-title-yes" name="vehicle_require_title" value="1"<?php if($this->vehicleRequireTitle == '1'){?> checked<?php }?>>Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-requires-title-no" name="vehicle_require_title" value="0"<?php if($this->vehicleRequireTitle != '1'){?> checked<?php }?>>No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('vehicle_require_registration', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Requires registration</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-registration-yes" name="vehicle_require_registration" value="1"<?php if($this->vehicleRequireRegistration == '1'){?> checked<?php }?>>Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-registration-no" name="vehicle_require_registration" value="0"<?php if($this->vehicleRequireRegistration!= '1'){?> checked<?php }?>>No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('vehicle_require_insurance', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Requires insurance</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-insurance-yes" name="vehicle_require_insurance" value="1"<?php if($this->vehicleRequireInsurance == '1'){?> checked<?php }?>>Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-insurance-no" name="vehicle_require_insurance" value="0"<?php if($this->vehicleRequireInsurance!= '1'){?> checked<?php }?>>No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('vehicle_require_running', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Must be drivable</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-running-yes" name="vehicle_require_running" value="1"<?php if($this->vehicleRequireRunning == '1'){?> checked<?php }?>>Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-running-no" name="vehicle_require_running" value="0"<?php if($this->vehicleRequireRunning!= '1'){?> checked<?php }?>>No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('maintenance_allowed', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Maintenance allowed</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="maintenance-allowed-yes" name="maintenance_allowed" value="1" <?php if ($this->facility->getMaintenanceAllowed() == '1') { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="maintenance-allowed-no" name="maintenance_allowed" value="0" <?php if ($this->facility->getMaintenanceAllowed()!= '1') { echo 'checked="checked"'; } ?> />No
                </label>
            </div>
        </div>

        <?php if (AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::VEHICLE_MINIMUM_STAY)) { ?>
        <div class="form-group<?=(in_array('minimum_stay', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Minimum stay required</label>
            <div class="col-md-10">
                <select name="minimum_stay" id="minimum-stay" class="form-control">
                    <option value="0">No minimum stay required</option>
                    <option value="1">1 month</option>
                    <?php
                        $minStay = $this->facility->getMinimumStayRequired();
                        for($x = 2; $x < 13; $x++) {
                        ?>
                            <option value="<?=($x)?>" <?=($minStay == $x ? 'selected="selected"' : '')?>>
                                <?=($x)?> months
                            </option>
                        <?php
                    }?>
                </select>
            </div>
        </div>
        <?php } ?>

        <h5>Premium Services</h5>
        <h6>*Additional fees may apply</h6>

        <div class="form-group<?=(in_array('wash_station', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Wash Station available</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="wash-station-yes" name="wash_station" value="1" <?php if ($this->facility->getWashStationAvailable() == '1') { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="wash-station-no" name="wash_station" value="0" <?php if ($this->facility->getWashStationAvailable()!= '1') { echo 'checked="checked"'; } ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('dump_station', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Dump Station available</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="dump-station-yes" name="dump_station" value="1" <?php if ($this->facility->getDumpStationAvailable() == '1') { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="dump-station-no" name="dump_station" value="0" <?php if ($this->facility->getDumpStationAvailable()!= '1') { echo 'checked="checked"'; } ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('general_maintenance', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">General Maintenance</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="general-maintenance-yes" name="general_maintenance" value="1" <?php if ($this->facility->getGeneralMaintenance() == '1') { echo 'checked="checked"'; } ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="general-maintenance-no" name="general_maintenance" value="0" <?php if ($this->facility->getGeneralMaintenance()!= '1') { echo 'checked="checked"'; } ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('propane', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Propane</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="propane-yes" name="propane" value="1" <?= ($this->facility->getPropane() == '1') ? 'checked="checked"' : '' ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="propane-no" name="propane" value="0" <?= ($this->facility->getPropane()!= '1') ? 'checked="checked"' : '' ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('diesel_and_gas', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Diesel & Gas</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="diesel-and-gas-yes" name="diesel_and_gas" value="1" <?= ($this->facility->getFuel() == '1') ? 'checked="checked"' : '' ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="diesel-and-gas-no" name="diesel_and_gas" value="0" <?= ($this->facility->getFuel()!= '1') ? 'checked="checked"' : '' ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('does_state_inspections', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Does State Inspections</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="does-state-inspections-yes" name="does_state_inspections" value="1" <?= ($this->facility->getDoesStateInspections() == '1') ? 'checked="checked"' : '' ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="does-state-inspections-no" name="does_state_inspections" value="0" <?= ($this->facility->getDoesStateInspections()!= '1') ? 'checked="checked"' : '' ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('auto_cleaning_and_detailing', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Auto Cleaning and/or Detailing</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="auto-cleaning-and-detailing-yes" name="auto_cleaning_and_detailing" value="1" <?= ($this->facility->getDetailing() == '1') ? 'checked="checked"' : '' ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="auto-cleaning-and-detailing-no" name="auto_cleaning_and_detailing" value="0" <?= ($this->facility->getDetailing()!= '1') ? 'checked="checked"' : '' ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('air_pump', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Has Air Pump</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="air-pump-yes" name="air_pump" value="1" <?= ($this->facility->getAirPump() == '1') ? 'checked="checked"' : '' ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="air-pump-no" name="air_pump" value="0" <?= ($this->facility->getAirPump()!= '1') ? 'checked="checked"' : '' ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('vacuum_station', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Has Vacuum Station</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vacuum-station-yes" name="vacuum_station" value="1" <?= ($this->facility->getVacuum() == '1') ? 'checked="checked"' : '' ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vacuum-station-no" name="vacuum_station" value="0" <?= ($this->facility->getVacuum()!= '1') ? 'checked="checked"' : '' ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('ice_machine', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Has Ice Machine</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="ice-machine-yes" name="ice_machine" value="1" <?= ($this->facility->getIce() == '1') ? 'checked="checked"' : '' ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="ice-machine-no" name="ice_machine" value="0" <?= ($this->facility->getIce()!= '1') ? 'checked="checked"' : '' ?> />No
                </label>
            </div>
        </div>

        <div class="form-group<?=(in_array('water_hose_spigot', $this->erroredFields))?' has-error':''?>">
            <label class="col-md-2 control-label">Water (Hose and/or Spigot)</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="water-hose-spigot-yes" name="water_hose_spigot" value="1" <?= ($this->facility->getWater() == '1') ? 'checked="checked"' : '' ?> />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="water-hose-spigot-no" name="water_hose_spigot" value="0" <?= ($this->facility->getWater()!= '1') ? 'checked="checked"' : '' ?> />No
                </label>
            </div>
        </div>
    </div>

    <hr>

    <input type="hidden" id="facility_id" name="facility_id" value="<?=$this->facility->getId()?>" />
    <div class="form-actions">
        <div class="right">
            <input class="ui primary button" name="commit" type="submit" value="Save Changes" />
            <input type="hidden" name="csrf_token" value="<?=$this->csrf_token?>">
        </div>
    </div>
</form>