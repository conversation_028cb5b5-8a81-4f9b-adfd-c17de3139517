<script type="text/javascript">
    var sourceType = '<?=$this->sourceType;?>';
    var facilityId = <?=$this->facility->getId()?>;
    var promoSync = <?=$this->promoSync?>;
<?php if ($this->inventory):

    $unitInfo = array();
    foreach($this->inventory as $unit) {
        $unitInfo[$unit['id']] = array(
            "facilityId"    => $this->facility->getId(),
            "type"          => $unit['type_num'],
            "uw"            => $unit['unit_w'],
            "ul"            => $unit['unit_l'],
            "uh"            => $unit['unit_h'],
            "dw"            => $unit['door_w'],
            "dh"            => $unit['door_h'],
            "climate"       => $unit['climate'],
            "humidity"      => $unit['humidity'],
            "alarm"         => $unit['alarm'],
            "power"         => $unit['power'],
            "outdoorAccess" => $unit['outdoorAccess'],
            "driveUp"       => $unit['driveUp'],
            "stacked"       => $unit['stacked'],
            "premium"       => $unit['premium'],
            "heated"        => $unit['heated'],
            "aircooled"     => $unit['aircooled'],
            "ada"           => $unit['ada'],
            "unitlights"    => $unit['unitlights'],
            "twentyfourhouraccess" => $unit['twentyfourhouraccess'],
            "shelvesinunit" => $unit['shelvesinunit'],
            "basement"      => $unit['basement'],
            "parkingwarehouse" => $unit['parkingwarehouse'],
            "pullthru"      => $unit['pullthru'],
            "vehicle"       => $unit['vehicle'],
            "doorType"      => $unit['doorType'],
            "floor"         => $unit['rawFloor'],
            "covered"       => $unit['covered'],
            "desc"          => $unit['desc'],
            "special"       => $unit['special'],
            "qty"           => $unit['qty'],
            "deposit"       => $unit['deposit'],
            "regPrice"      => $unit['list_price'],
            "sfPrice"       => $unit['sparefoot_price']
        );
    }
    ?>

    var integratedFields = <?=json_encode($this->integratedFields)?>;
    //print jsons of unit data, used to populate unit edit dialog
    var unit = <?=json_encode($unitInfo)?>;

<?php else: ?>
    var unit = [];
<?php endif; ?>
</script>

<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'inventory', 'loggedUser' => $this->loggedUser, 'facility' => $this->facility, 'isBidOptimizerActive'=>$this->isBidOptimizerActive))?>

<div class="ui menu secondary">
<?php if ($this->sourceType == Genesis_Entity_Source::ID_MANUAL) { ?>
    <a id="add_unit_modal_dialog" href="#" class="ui secondary button" onclick="unit_modal(<?=$this->facility->getId()?>);">
        <i class="plus icon"></i> Add Unit
    </a>
<?php } ?>
    <a href="<?=$this->url(['action'=>'unitexport'], 'features')?>?fid=<?=$this->facilityId?>" class="ui secondary button">
        <i class="icon file"></i> Export Spreadsheet
    </a>
</div>

<?php if ($this->inventory) { ?>
    <form>
        <div class="table-responsive">
            <table id="units-table" class="ui table striped sortable cell-headers">
                <thead>
                    <tr>
                        <th class="center availability-checkbox no-sort">
                            Available
                        </th>
                        <th class="no-sort"></th>
                        <th>Size</th>
                        <th>Type</th>
                        <?php if ($this->sourceType == Genesis_Entity_Source::ID_EXTRA_SPACE): ?>
                             <th>ExtraSpace Unit Type</th>
                        <?php endif; ?>

                        <th>Amenities</th>
                        <th>Floor</th>
                        <th><a href="#" rel="tooltip" title="Price you will allow SpareFoot to sell your unit for.">SpareFoot Price</a></th>
                        <th>List Price</th>
                        <th>Unit Promo</th>
                        <?php if ($this->sourceType != Genesis_Entity_Source::ID_MANUAL): ?>
                            <th>Quantity</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($this->inventory as $unit): ?>
                        <?php //echo print_r($unit, true); ?>
                        <tr id="<?=$unit['id']?>" data-unitindex="<?=$unit['id']?>" class="<?=($unit['hidden'] == true) ? 'disabled-row' : 'enabled-row' ?>">
                            <td class="center availability-checkbox">
                                <?php if ($this->facility->getApproved() && ! $unit['approved']) : ?>
                                    <p></p>
                                <?php else: ?>
                                    <div class="ui checkbox">
                                        <input type="checkbox" name="listing" id="tg_<?=$unit['id']?>"
                                            value="<?=$unit['id']?>" <?=($unit['hidden'] == true ? '': 'checked="checked"')?> />
                                    </div>
                                <?php endif; ?>
                             </td>
                            <td class="center">
                                <?php if ($this->facility->getApproved() && ! $unit['approved'] ||
                                        ($this->facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_QUIKSTOR
                                        || $this->facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_EXTRA_SPACE)
                                        ) : ?>
                                    <p>&nbsp;</p>
                                <?php else: ?>
                                    <a onclick="unit_modal(<?=$this->facility->getId()?>,<?=$unit['id']?>);">Edit</a>
                                <?php endif; ?>
                            </td>
                            <td data-sort-value="<?= $unit['unit_w'] * $unit['unit_l'] ?>">
                                <?=str_replace(' ','&nbsp;',$unit['dimensions'])?>
                            </td>
                            <td><?=$unit['type']?></td>
                            <?php if ($this->sourceType == Genesis_Entity_Source::ID_EXTRA_SPACE): ?>
                            <td><?=$unit['unitType']?></td>
                            <?php endif; ?>
                            <td><?=$unit['amenities']?></td>
                            <td><?=$unit['floor']?></td>
                            <td><div id="<?=$unit['id']?>" name="sparefootprice"><?=(isset($unit['sparefoot_price']) && $unit['sparefoot_price'] > 0 ? '$'.number_format($unit['sparefoot_price'], 2) : '')?></div></td>
                            <td>$<?=number_format($unit['list_price'], 2)?></td>
                            <td><?=$unit['special'] ? $unit['special'] : ''?></td>
                            <?php if ($this->sourceType != Genesis_Entity_Source::ID_MANUAL): ?>
                            <td><?=$unit['quantity']?></td>
                            <?php endif; ?>
                        </tr>
                    <?php endforeach; ?>
                </tbody>

            </table>
            <?=$this->partial('facility/add-more-units.phtml', ['units'=> count($this->inventory), 'facility'=>$this->facility])?>
        </div>
    </form>
<?php } elseif ($this->unpublishedUnits) { ?>
          <p>You currently have no vacant units in inventory. If you believe this is a mistake, please contact <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
<?php } else {
      if ($this->sourceType == Genesis_Entity_Source::ID_CENTERSHIFT) {
                ?><br /><div id="loading" align="center"><img src="/images/loading.gif" /><p>We are currently syncing your units for this facility with Centershift.<br/>Check back shortly.</p><?php
      } elseif ($this->sourceType == Genesis_Entity_Source::ID_SITELINK) {
                ?><br /><div id="loading" align="center"><img src="/images/loading.gif" /><p>We are currently syncing your units for this facility with SiteLink.<br/>Check back shortly.</p><?php
      } elseif ($this->sourceType == Genesis_Entity_Source::ID_MANUAL) { ?>
                <br />
                <div class="jumbo">
                    <h2>Next up: List your inventory.</h2>
                    <p>Let's add your first unit.</p>
                    <a id="add-first-unit" onclick="unit_modal(<?=$this->facility->getId()?>);" class="huge ui blue button">Add Unit</a>
                </div>
<?php } else { ?>
            <br />
            <div class="ui warning message">
                <p>No units found</p>
            </div>
      <?php } ?>
<?php } ?>

<?=$this->partial('facility/hide-facility-reason-modal.phtml')?>
<?=$this->partial('facility/inventory-modals.phtml', array('facility'=>$this->facility, 'covidModal' => isset($this->covidModal) ? $this->covidModal : false) )?>
