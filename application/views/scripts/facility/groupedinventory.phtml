<?php
$unitInfo = array();

if ($this->inventory) {
    foreach ($this->inventory as $unit) {
        $unitInfo[$unit['id']] = array(
            "facilityId" => $this->facility->getId(),
            "type"          => $unit['type_num'],
            "uw"            => $unit['unit_w'],
            "ul"            => $unit['unit_l'],
            "uh"            => $unit['unit_h'],
            "dw"            => $unit['door_w'],
            "dh"            => $unit['door_h'],
            "climate"       => $unit['climate'],
            "humidity"      => $unit['humidity'],
            "alarm"         => $unit['alarm'],
            "power"         => $unit['power'],
            "outdoorAccess" => $unit['outdoorAccess'],
            "driveUp"       => $unit['driveUp'],
            "stacked"       => $unit['stacked'],
            "premium"       => $unit['premium'],
            "heated"        => $unit['heated'],
            "aircooled"     => $unit['aircooled'],
            "ada"           => $unit['ada'],
            "unitlights"    => $unit['unitlights'],
            "twentyfourhouraccess" => $unit['twentyfourhouraccess'],
            "shelvesinunit" => $unit['shelvesinunit'],
            "basement"      => $unit['basement'],
            "parkingwarehouse" => $unit['parkingwarehouse'],
            "pullthru"      => $unit['pullthru'],
            "vehicle"       => $unit['vehicle'],
            "doorType"      => $unit['doorType'],
            "floor"         => $unit['rawFloor'],
            "covered"       => $unit['covered'],
            "desc"          => $unit['desc'],
            "special"       => $unit['special'],
            "qty"           => $unit['qty'],
            "deposit"       => $unit['deposit'],
            "regPrice"      => $unit['list_price'],
            "sfPrice"       => $unit['sparefoot_price']
        );
    }
}

if ($this->groupedUnits) {
    foreach ($this->groupedUnits as $gUnit) {
        $unitInfo[$gUnit['groupIdsStr']] = array(
            "facilityId" => @$this->facility->getId(),
            "type"          => @$gUnit['type_num'],
            "uw"            => @$gUnit['unit_w'],
            "ul"            => @$gUnit['unit_l'],
            "uh"            => @$gUnit['unit_h'],
            "dw"            => @$gUnit['door_w'],
            "dh"            => @$gUnit['door_h'],
            "climate"       => @$gUnit['climate'],
            "humidity"      => @$gUnit['humidity'],
            "alarm"         => @$gUnit['alarm'],
            "power"         => @$gUnit['power'],
            "outdoorAccess" => @$gUnit['outdoorAccess'],
            "driveUp"       => @$gUnit['driveUp'],
            "stacked"       => @$gUnit['stacked'],
            "premium"       => @$gUnit['premium'],
            "heated"        => @$gUnit['heated'],
            "aircooled"     => @$gUnit['aircooled'],
            "ada"           => @$gUnit['ada'],
            "unitlights"    => @$gUnit['unitlights'],
            "twentyfourhouraccess" => @$gUnit['twentyfourhouraccess'],
            "shelvesinunit" => @$gUnit['shelvesinunit'],
            "basement"      => @$gUnit['basement'],
            "parkingwarehouse" => @$gUnit['parkingwarehouse'],
            "pullthru"      => @$gUnit['pullthru'],
            "vehicle"       => @$gUnit['vehicle'],
            "doorType"      => @$gUnit['doorType'],
            "floor"         => @$gUnit['rawFloor'],
            "covered"       => @$gUnit['covered'],
            "desc"          => @utf8_decode($gUnit['desc']),
            "special"       => @utf8_decode($gUnit['special']),
            "qty"           => @$gUnit['qty'],
            "deposit"       => @$gUnit['deposit'],
            "regPrice"      => @$gUnit['list_price'],
            "sfPrice"       => @$gUnit['sparefoot_price'],
            "slyn" => @$gUnit['sitelinkunit']
        );
    }
}
?>
<script type="text/javascript">
let sourceType = <?=$this->sourceType?>;
let promoSync = <?=$this->promoSync?>;
let facilityId = <?=$this->facility->getId()?>;
let unit = <?=json_encode($unitInfo)?>;
let integratedFields = <?=json_encode($this->integratedFields)?>;
</script>

<?=$this->partial('facility/header.phtml', array('facility' => $this->facility))?>
<?=$this->partial('facility/subnav.phtml', array('selected' => 'inventory', 'loggedUser' => $this->loggedUser, 'facility' => $this->facility,'isBidOptimizerActive'=>$this->isBidOptimizerActive))?>

<?php if(!$this->facility->getApproved()) { ?>
<div id="unapproved-units-msg" class="ui warning message">
    This facility currently has unapproved units. These units are pending approval by a SpareFoot account manager.
    If the units remain unapproved for over 72 hours, please e-mail
    <a href="mailto:<EMAIL>"><EMAIL></a> or call ************.
</div>
<?php } ?>

<div class="ui secondary menu">
    <?php if ($this->inventory) { ?>
<!-- TODO: Remove these buttons
        <div class="btn-group pull-right">
            <a class="ui secondary button" id="group_edit" onclick="$('div#i_units').hide();$('#g_units').show();$('a#group_edit').addClass('active');$('#individual_edit').removeClass('active');$('input[type=checkbox]').attr('checked', false);">All Units</a>
            <a class="ui secondary button active" id="individual_edit" onclick="$('div#i_units').show();$('#g_units').hide();$('#individual_edit').addClass('active');$('#group_edit').removeClass('active');$('input[type=checkbox]').attr('checked', false);">Units in Group</a>
        </div> -->
    <?php } ?>
    <div class="ui buttons">
        <a class="ui secondary button" onclick="multi_modal(<?=$this->facility->getId()?>);">
            <i class="pencil icon"></i> Edit Selections
        </a>
        <a class="ui secondary button" href="<?=$this->url(['action'=>'unitexport'], 'features')?>?fid=<?=$this->facilityId?>">
            <i class="file icon"></i> Export Spreadsheet
        </a>
    </div>
</div>
    <?php if ($this->inventory) { ?>
        <div id="i_units">
            <div class="table-responsive">
                <table id="units-table" class="ui sortable cell-headers striped table">
                    <thead>
                        <tr>
                            <th class="no-sort">
                                <div class="ui checkbox">
                                    <input type="checkbox" id="checkall_i"/>
                                </div>
                            </th>
                            <th class="no-sort">Available</th>
                            <th></th>
                            <th><a href="#" rel="tooltip" title="Unit name from your management software.">Name</a></th>
                            <th>Size</th>
                            <th><a href="#" rel="tooltip" title="How sparefoot has classified this unit.">Type</a></th>
                            <?php if($this->sourceType == Genesis_Entity_Source::ID_SITELINK) { ?>
								<th>
									<a href="#" rel="tooltip"
									   title="The unit type classification from your management software."
									>SiteLink Type</a>
								</th>
							<?php } elseif($this->sourceType == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) { ?>
								<th>
									<a href="#" rel="tooltip"
									   title="The unit type classification from your management software."
									>Unit Type Code</a>
								</th>
                            <?php }?>
                            <th>Amenities</th>
                            <th>Promo</th>
                            <th>List Price</th>
                            <th>
								<a href="#" rel="tooltip"
								   title="Price you will allow SpareFoot to sell your unit for."
								>SpareFoot Price</a>
							</th>
                            <th>
								<a href="#" rel="tooltip"
								   title="Is this unit available to rent? (unoccupied, not reserved, etc)"
								>Rentable?</a>
							</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if ($this->inventory) {
                            foreach ($this->inventory as $unit) { ?>
                            <tr id="<?=$unit['id']?>" data-unitindex="<?=$unit['id']?>" class="<?=($unit['hidden'] == true) ? "disabled-row" : "enabled-row" ?>">
                                <td class="center on-off-checkbox">
                                    <div class="ui checkbox">
                                        <input type="checkbox" id="<?=$unit['id']?>" name="unit"/>
                                    </div>
                                </td>
                                <td class="center availability-checkbox">
                                    <?php if($this->facility->getApproved() && !$unit['approved']) { ?>
                                        <p></p>
                                    <?php } else { ?>
                                        <div class="ui checkbox">
                                            <input type="checkbox" name="listing" id="tg_<?=$unit['id']?>"
                                                value="<?=$unit['id']?>" <?=($unit['hidden']?'':' checked="checked"')?> />
                                        </div>
                                    <?php } ?>
                                </td>
                                <td>
                                    <?php if($this->facility->getApproved() && !$unit['approved']) { ?>
                                        <p></p>
                                    <?php } else { ?>
                                        <a onclick="unit_modal(<?=$this->facility->getId()?>,<?=$unit['id']?>);">Edit</a>
                                    <?php } ?>
                                </td>
                                <!--<td><?=$unit['approved'] ? '<span style="color:green;"><b>&#x2713; Yes</b></span>' : ''?></td>-->
                                <td><?=$unit['unitName']?></td>
                                <td data-sort-value="<?= $unit['unit_w'] * $unit['unit_l'] ?>"><?=str_replace(' ','&nbsp;',$unit['dimensions'])?></td>
                                <td><?=$unit['type']?></td>
                                <?php
								if($this->sourceType == Genesis_Entity_Source::ID_SITELINK
									|| $this->sourceType == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER
								) { ?>
									<td><?=$unit['classType']?></td>
								<?php }?>
                                <td><?=$unit['amenities']?></td>
                                <td><?=$unit['special']?></td>
                                <td>$<?=number_format($unit['list_price'], 2)?></td>
                                <td><div id="<?=$unit['id']?>" name="sparefootprice">
                                <?php if ($unit['sparefoot_price'] != "") {?>
                                $<?=number_format($unit['sparefoot_price'], 2)?>
                                <?php }?>
                                </div></td>
                                <td><?=$unit['published'] ? '<span style="color:green;"><b>&#x2713; Yes</b></span>' : ''?></td>
                            </tr>
                            <?php }
                        } ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php }

    if ($this->groupedUnits) {?>
		<div id="g_units" <?php if ($this->inventory) { echo 'class="hidden"'; }?>>
            <div class="table-responsive">
                <table id="units-table" class="ui sortable cell-headers striped table">
                    <thead>
                        <tr>
                            <th class="no-sort">
                                <div class="ui checkbox">
                                    <input type="checkbox" id="checkall_g"/>
                                </div>
                            </th>
                            <th class="no-sort"></th>
                            <th class="no-sort" style="width:170px;"></th>
                            <th class="no-sort">Available</th>
                            <th width="300px"><a href="#" rel="tooltip" title="Unit names from your management software.">Units in Group</a></th>
                            <th>Size</th>
                            <?php if($this->sourceType == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER ) { ?><th>Unit Type Code</th><?php }?>
                            <th><a href="#" rel="tooltip" title="How SpareFoot has classified this unit.">Type</a></th>
                            <?php if($this->sourceType == Genesis_Entity_Source::ID_SITELINK) { ?><th><a href="#" rel="tooltip" title="Unit types as designated by your management software.">SiteLink Type</a></th><?php }?>
                            <th>Amenities</th>
                            <th><a href="#" rel="tooltip" title="Price you will allow SpareFoot to sell your unit for.">SpareFoot Price</a></th>
                            <th>List Price</th>
                            <th>Promo</th>
                            <?php if (Genesis_Service_Feature::isActive('myfoot.unit_list_show_reservation_window',['account_id'=>$this->facility->getAccountId(),'listing_avail_id'=>$this->facility->getId()])) { ?>
                                <th>Reservation Window</th>
                            <?php } ?>
                            <th>Quantity</th>
                            <th style="width: 95px;" <?=($this->sourceType == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) ? ' class="hide"' : '' ?>><a href="#" rel="tooltip" title="The number of units in this group that are available for rent."># Rentable</a></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($this->groupedUnits as $gUnit) {
                            $selectedRow = false;
                            ?>
                            <tr id="<?=$gUnit['groupIdsStr']?>"
                                data-unitindex="<?=$gUnit['groupIdsStr']?>"
                                class="<?=($gUnit['hidden'] == true) ? "disabled-row" : 'enabled-row'?>">

                                <td class="center on-off-checkbox">
                                    <div class="ui checkbox">
                                        <input type="checkbox" id="<?=$gUnit['groupIdsStr']?>" name="unit"/>
                                    </div>
                                </td>
                                <td>
                                    <a onclick="unit_modal(<?=$this->facility->getId()?>,'<?=$gUnit['groupIdsStr']?>');">Edit</a>
                                </td>
                                <td>
                                    <?php if ($selectedRow) {?>
                                        Selected
                                    <?php } else {?>
                                        <a href="<?=$this->url(['unitIds' => $gUnit['groupIdsStr']], 'features-uid', true) ?>">Detailed View</a>
                                    <?php } ?>
                                </td>
                                <td class="center availability-checkbox">
                                    <div class="ui checkbox">
                                        <input type="checkbox" name="listing"
                                            id="tg_<?=$gUnit['groupIdsStr']?>"
                                            value="<?=$gUnit['groupIdsStr']?>" <?=($gUnit['hidden']?'':' checked="checked"')?> />
                                    </div>
                                </td>
                                <td>
                                    <?php
									$unitNames = [];
                                    /** @var Genesis_Entity_StorageSpace $groupUnit */
                                    foreach ($gUnit['groupUnits'] as $groupUnit) {
                                    	$unitName = "<span ";
										if ($gUnit['quantity']) {
											$unitName .= ' class="'
												. ($groupUnit->getPublish() ? 'unit-rentable' : 'unit-unrentable')
												. '"'
											;
										}
										$unitName .= ">";
										$unitName .= $groupUnit->getUnitName();
										$unitName .= "</span>";
										$unitNames[] = $unitName;
                                    }
                                    ?>
									<?=implode(", ", $unitNames)?>
								</td>
                                <td data-sort-value="<?= $gUnit['unit_w'] * $gUnit['unit_l'] ?>"><?=str_replace(' ','&nbsp;',$gUnit['dimensions'])?></td>
                                <?php if($this->sourceType == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) { ?><td><?=$gUnit['classType']?></td><?php }?>
                                <td><?=$gUnit['type']?></td>
                                <?php if($this->sourceType == Genesis_Entity_Source::ID_SITELINK) { ?><td><?=$gUnit['classType']?></td><?php }?>
                                <td><?=$gUnit['amenities']?></td>
                                <td><div id="<?=$gUnit['groupIdsStr']?>" name="sparefootprice">
                                <?php if ($gUnit['sparefoot_price'] != "") {?>
                                $<?=number_format($gUnit['sparefoot_price'], 2)?>
                                <?php }?>
                                </div></td>
                                <td>$<?=number_format($gUnit['list_price'], 2)?></td>
                                <td><?=$gUnit['special']?></td>
                                <?php if (Genesis_Service_Feature::isActive('myfoot.unit_list_show_reservation_window',['account_id'=>$this->facility->getAccountId(),'listing_avail_id'=>$this->facility->getId()])) { ?>
                                    <td><?=$gUnit['reservation_days']?> days</td>
                                <?php } ?>
                                <td><?=$gUnit['quantity']?></td>
                                <td<?=($gUnit['source_id']==Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) ? ' class="hide"' : '' ?>><?=$gUnit['numRentable']?></td>
                            </tr>
                        <?php } ?>

                    </tbody>
                </table>
            </div>
        </div>

    <?php } ?>

    <?php if(!$this->inventory && !$this->groupedUnits && $this->unpublishedUnits) { ?>
        <br /><p>You currently have no vacant units in inventory. If you believe this is a mistake, please contact <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    <?php } else if(!$this->inventory && !$this->groupedUnits){ ?>
        <br /><div id="loading" align="center"><img src="/images/loading.gif" /></div><br/><p>We are currently syncing your units for this facility with your management software.<br/>Check back shortly.</p>
    <?php } ?>
<?=$this->partial('facility/hide-facility-reason-modal.phtml')?>
<?=$this->partial('facility/inventory-modals.phtml', array('facility'=>$this->facility, 'covidModal' => isset($this->covidModal) ? $this->covidModal : false))?>
