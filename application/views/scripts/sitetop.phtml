<?php
header('P3P: CP=\"NOI ADM DEV PSAi COM NAV OUR OTRo STP IND DEM\"');
header("Cache-Control: no-cache, must-revalidate");

if ($this->nocache) {
    //Set no caching
    header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
    header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
    header("Cache-Control: no-store, no-cache, must-revalidate");
    header("Cache-Control: post-check=0, pre-check=0", false);
    header("Pragma: no-cache");
}

function CDN($path) {
    return Genesis_Util_Versioner::version($path);
};
?>
<!doctype html>
<html lang="en">
<!-- <?=gethostname()?> -->
<head>
    <meta charset="utf-8" />
    <title><?=$this->title ? $this->title.' | ' : null ?>MySpareFoot</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>

    <script>
        var rollbarPayload = {
            cookies: document.cookie.split(';').reduce(function (o, v) {
                var parts = v.split('=');
                o[parts[0]] = parts[1];
                return o;
            }, {}),
            environment: "<?=getenv('SF_ENV')?>"
        };
        <?php if ($loggedUser = AccountMgmt_Service_User::getLoggedUser()): ?>
            rollbarPayload.person = {
                account_id: <?=$loggedUser->getAccount()->getSfAccountId()?>,
                email: "<?=$loggedUser->getEmail()?>",
                id: <?=$loggedUser->getId()?>,
                role: "<?=$loggedUser->getMyfootRole()?>"
            };
        <?php endif ?>
        var _rollbarConfig = {
            accessToken: "2dc3c792d2a54c90a0d53244a2d663b0",
            captureUncaught: true,
            captureUnhandledRejections: true,
            payload: rollbarPayload
        };
        // Rollbar Snippet
        !function(r){function e(t){if(o[t])return o[t].exports;var n=o[t]={exports:{},id:t,loaded:!1};return r[t].call(n.exports,n,n.exports,e),n.loaded=!0,n.exports}var o={};return e.m=r,e.c=o,e.p="",e(0)}([function(r,e,o){"use strict";var t=o(1).Rollbar,n=o(2);_rollbarConfig.rollbarJsUrl=_rollbarConfig.rollbarJsUrl||"https://d37gvrvc0wt4s1.cloudfront.net/js/v1.9/rollbar.min.js";var a=t.init(window,_rollbarConfig),i=n(a,_rollbarConfig);a.loadFull(window,document,!_rollbarConfig.async,_rollbarConfig,i)},function(r,e){"use strict";function o(r){return function(){try{return r.apply(this,arguments)}catch(e){try{console.error("[Rollbar]: Internal error",e)}catch(o){}}}}function t(r,e,o){window._rollbarWrappedError&&(o[4]||(o[4]=window._rollbarWrappedError),o[5]||(o[5]=window._rollbarWrappedError._rollbarContext),window._rollbarWrappedError=null),r.uncaughtError.apply(r,o),e&&e.apply(window,o)}function n(r){var e=function(){var e=Array.prototype.slice.call(arguments,0);t(r,r._rollbarOldOnError,e)};return e.belongsToShim=!0,e}function a(r){this.shimId=++c,this.notifier=null,this.parentShim=r,this._rollbarOldOnError=null}function i(r){var e=a;return o(function(){if(this.notifier)return this.notifier[r].apply(this.notifier,arguments);var o=this,t="scope"===r;t&&(o=new e(this));var n=Array.prototype.slice.call(arguments,0),a={shim:o,method:r,args:n,ts:new Date};return window._rollbarShimQueue.push(a),t?o:void 0})}function l(r,e){if(e.hasOwnProperty&&e.hasOwnProperty("addEventListener")){var o=e.addEventListener;e.addEventListener=function(e,t,n){o.call(this,e,r.wrap(t),n)};var t=e.removeEventListener;e.removeEventListener=function(r,e,o){t.call(this,r,e&&e._wrapped?e._wrapped:e,o)}}}var c=0;a.init=function(r,e){var t=e.globalAlias||"Rollbar";if("object"==typeof r[t])return r[t];r._rollbarShimQueue=[],r._rollbarWrappedError=null,e=e||{};var i=new a;return o(function(){if(i.configure(e),e.captureUncaught){i._rollbarOldOnError=r.onerror,r.onerror=n(i);var o,a,c="EventTarget,Window,Node,ApplicationCache,AudioTrackList,ChannelMergerNode,CryptoOperation,EventSource,FileReader,HTMLUnknownElement,IDBDatabase,IDBRequest,IDBTransaction,KeyOperation,MediaController,MessagePort,ModalWindow,Notification,SVGElementInstance,Screen,TextTrack,TextTrackCue,TextTrackList,WebSocket,WebSocketWorker,Worker,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload".split(",");for(o=0;o<c.length;++o)a=c[o],r[a]&&r[a].prototype&&l(i,r[a].prototype)}return e.captureUnhandledRejections&&(i._unhandledRejectionHandler=function(r){var e=r.reason,o=r.promise,t=r.detail;!e&&t&&(e=t.reason,o=t.promise),i.unhandledRejection(e,o)},r.addEventListener("unhandledrejection",i._unhandledRejectionHandler)),r[t]=i,i})()},a.prototype.loadFull=function(r,e,t,n,a){var i=function(){var e;if(void 0===r._rollbarPayloadQueue){var o,t,n,i;for(e=new Error("rollbar.js did not load");o=r._rollbarShimQueue.shift();)for(n=o.args,i=0;i<n.length;++i)if(t=n[i],"function"==typeof t){t(e);break}}"function"==typeof a&&a(e)},l=!1,c=e.createElement("script"),d=e.getElementsByTagName("script")[0],p=d.parentNode;c.crossOrigin="",c.src=n.rollbarJsUrl,c.async=!t,c.onload=c.onreadystatechange=o(function(){if(!(l||this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState)){c.onload=c.onreadystatechange=null;try{p.removeChild(c)}catch(r){}l=!0,i()}}),p.insertBefore(c,d)},a.prototype.wrap=function(r,e){try{var o;if(o="function"==typeof e?e:function(){return e||{}},"function"!=typeof r)return r;if(r._isWrap)return r;if(!r._wrapped){r._wrapped=function(){try{return r.apply(this,arguments)}catch(e){throw e._rollbarContext=o()||{},e._rollbarContext._wrappedSource=r.toString(),window._rollbarWrappedError=e,e}},r._wrapped._isWrap=!0;for(var t in r)r.hasOwnProperty(t)&&(r._wrapped[t]=r[t])}return r._wrapped}catch(n){return r}};for(var d="log,debug,info,warn,warning,error,critical,global,configure,scope,uncaughtError,unhandledRejection".split(","),p=0;p<d.length;++p)a.prototype[d[p]]=i(d[p]);r.exports={Rollbar:a,_rollbarWindowOnError:t}},function(r,e){"use strict";r.exports=function(r,e){return function(o){if(!o&&!window._rollbarInitialized){var t=window.RollbarNotifier,n=e||{},a=n.globalAlias||"Rollbar",i=window.Rollbar.init(n,r);i._processShimQueue(window._rollbarShimQueue||[]),window[a]=i,window._rollbarInitialized=!0,t.processPayloads()}}}}]);
        // End Rollbar Snippet
    </script>

    <?php
    // Segment code only if key is set
    $segment_key = getenv('SEGMENT_KEY');
    if($segment_key) { ?>

        <script>
            !function(){var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on"];analytics.factory=function(t){return function(){var e=Array.prototype.slice.call(arguments);e.unshift(t);analytics.push(e);return analytics}};for(var t=0;t<analytics.methods.length;t++){var e=analytics.methods[t];analytics[e]=analytics.factory(e)}analytics.load=function(t,e){var n=document.createElement("script");n.type="text/javascript";n.async=!0;n.src=("https:"===document.location.protocol?"https://":"http://")+"cdn.segment.com/analytics.js/v1/"+t+"/analytics.min.js";var o=document.getElementsByTagName("script")[0];o.parentNode.insertBefore(n,o);analytics._loadOptions=e};analytics.SNIPPET_VERSION="4.1.0";
                analytics.load("<?= $segment_key; ?>");
            }}();
            <?php if ($loggedUser) { ?>
            analytics.identify('<?= $loggedUser->getId()?>', {
                name: <?= json_encode($loggedUser->getFirstName().' '.$loggedUser->getLastName())?>,
                email: '<?= $loggedUser->getEmail()?>',
                sf_account_id: '<?= $loggedUser->getAccount()->getSfAccountId()?>',
                account_name: <?= json_encode($loggedUser->getAccount()->getName())?>,
                bid_type: '<?= $loggedUser->getAccount()->getBidType()?>',
                <?php
                $facility = Genesis_Service_Facility::loadById(AccountMgmt_Service_UserCookie::get(AccountMgmt_Service_UserCookie::ACTIVE_FACILITY_ID));
                if($facility) { ?>
                integration_type: <?= json_encode($facility->getCorporation()->getSource()->getSource())?>
                <?php } ?>
            });
            <?php } ?>
            analytics.page();
        </script>
    <?php } ?>

    <link href="<?=CDN('/dist/app.css')?>" rel="stylesheet"/>
    <link href="<?=CDN('/vendors/semantic-ui/dist/semantic.css')?>" rel="stylesheet"/>
    <link href="<?=CDN('/new-ui/dist/myfoot.css')?>" rel="stylesheet"/>

    <!--[if lt IE 9]>
    <script src="//html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->

    <!--[if lte IE 9]>
    <link href="<?=CDN('/dist/app-ie.css')?>" rel="stylesheet"/>
    <link href="<?=CDN('/vendors/semantic-ui/dist/semantic-ie.css')?>" rel="stylesheet"/>
    <link href="<?=CDN('/new-ui/dist/ie-fixes.css')?>" rel="stylesheet"/>
    <![endif]-->

    <link rel="shortcut icon" href="<?=CDN('/images/ico/favicon.ico')?>"/>
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="<?=CDN('/images/ico/apple-toch-icon-114-precomposed.png')?>"/>
    <link rel="apple-touch-icon-precomposed" sizes="72x72" href="<?=CDN('/images/ico/apple-touch-icon-72-precomposed.png')?>"/>
    <link rel="apple-touch-icon-precomposed" href="<?=CDN('/images/ico/apple-touch-icon-57-precomposed.png')?>"/>
    <script>
        // Global MyFoot App
        App = {};
    </script>

    <?=isset($this->head)?$this->head:null?>
</head>
