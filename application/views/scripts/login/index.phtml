<div id="login-container" class="ui grid">
    <div id="login-box" class="ui column <?=Genesis_Config_Server::getEnvironment()?>">
        <div class="ui raised segment left aligned">
            <?php if ( ! Genesis_Config_Server::isProduction()):
            switch (strtolower(Genesis_Config_Server::getEnvironmentAsString())) {
                case 'staging':
                    $color = 'orange';
                    break;
                case 'dev':
                    $color = 'purple';
                    break;
                case 'local':
                    $color = 'teal';
                    break;
                default:
            } ?>
            <a class="ui <?=$color?> ribbon label"><?=Genesis_Config_Server::getEnvironmentAsString()?></a>
            <?php endif ?>

            <form class="ui form" method="post" action="<?=$this->url(['action' => 'process-login'], 'login')?>">
                <img src="/images/mysparefootx2.png" alt="MySpareFoot" class="mysparefootx2" />
                <?php if($this->error): ?>
                <p class="ui message negative"><?=$this->error?></p>
                <?php endif ?>
                <div class="field">
                    <input name="email" type="text" placeholder="Email" value="<?=$this->email?>"/>
                </div>
                <div class="field">
                    <input name="password" type="password" placeholder="Password"/>
                </div>
                <div class="two fields">
                    <div class="field remember-me-field">
                        <input name="remember" type="hidden" />
                        <div class="ui checkbox">
                            <input name="remember" type="checkbox" <?=$this->remember ? 'checked="checked"' : null ?>/>
                            <label>Stay signed in</label>
                        </div>
                    </div>
                    <div class="field">
                        <a id="forgot-password" href="#password-reset-modal">Forgot your password?</a>
                    </div>
                </div>
                <input name="login_buttond" type="hidden" value="Log In"/>
                <button id="login-button" class="ui submit button fluid primary large login-button">Sign In</button>
                <input type="hidden" name="csrf_token" value="<?=$this->csrf_token?>">
            </form>
            <div class="ui divider"></div>
            <p class="centered column signup-row">
                New to SpareFoot? <a id="signup-button" href="<?=$this->url([], 'signup-start')?>">Sign Up</a>
            </p>
        </div>
    </div>
</div>

<form id="password-reset-modal" class="ui modal small">
    <i class="close icon" id="password-reset-modal-close"></i>
    <div class="header">Forgot Your Password?</div>
    <div class="content">
        <div class="ui form">
            <p>Enter your email address and we'll send you directions to reset your password.</p>
            <div class="field fluid">
                <input type="text" id="reset-email" name="email" value="<?=$this->email?>" placeholder="Email address" />
            </div>
            <div class="ui message"></div>
        </div>
    </div>
    <div class="actions">
        <button class="ui button default close">Cancel</button>
        <button class="ui button primary" type="submit">Submit</button>
    </div>
</form>
