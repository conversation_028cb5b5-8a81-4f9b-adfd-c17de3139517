<?=$this->partial('sitetop.phtml')?>

<body data-path="<?= $_SERVER['REQUEST_URI'] ?>">




<div id="page-content-wrapper">
    <div id="minimal-header" class="ui fixed main menu header-bar">
        <a class="logo" href="/"><h1>MySpareFoot</h1></a>
    </div>
    <div class="master-layout">
        <?php echo $this->layout()->content ?>
        <div id="featuresApp"></div>

        <footer id="page-footer">
            <div class="ui center aligned basic segment">
            <p>&copy; <?=date('Y');?> SpareFoot.  Got questions? Check out our <a href="https://support.sparefoot.com/hc/en-us" target="_blank">Help Center</a>.</p>
            </div>
        </footer>
    </div>
</div><!--/#page-content-wrapper-->


<script type="text/javascript">
    //Global variables used within the App
    App.authBearerToken = '<?= $this->authBearerToken ?>';
    App.servicesBaseUrl = '<?= $this->servicesBaseUrl ?>' + (location.port ? ':'+location.port : '');
</script>

<script src="/dist/init.js"></script>

<script type="text/javascript">
    var CONFIG = { featureFlags: {} };
    // Feature Flags
    CONFIG.featureFlags['<?=AccountMgmt_Models_Features::UNIT_DELETE?>'] = <?=(AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::UNIT_DELETE) ? 'true' : 'false')?>;

    SF.tools.setReadOnly(CONFIG, {
        appUrl: '//<?=$_SERVER['HTTP_HOST']?>',
        cdnUrl: '//<?=$_SERVER['HTTP_HOST']?>',
        env: location.hostname.match('sparefoot.com') ? 'live' : location.hostname.match('localhost') ? 'dev' : 'staging'
    });

    <?php $fields = ['email', 'firstName', 'id', 'lastName', 'phone', 'pictureExt', 'username'];
    $user = $this->loggedUser->toArray($fields) ?>
    var USER = {};
    SF.tools.setReadOnly(USER, <?=json_encode($user)?>);

    <?php $fields = ['accountId', 'bidType', 'cpa', 'infoString', 'integrationsString', 'locationId', 'name', 'numFacilities', 'phone', 'sfAccountId', 'status'];
    $account = $this->loggedUser->getAccount()->toArray($fields) ?>
    var ACCOUNT = {};
    SF.tools.setReadOnly(ACCOUNT, <?=json_encode($account)?>);
    App.ACCOUNT = ACCOUNT;
    App.FACILITY = <?= AccountMgmt_Service_Facility::toJson($this->facility) ?>;

    <?php if ($this->authBookingToken): ?>
    App.AUTH_BOOKING_TOKEN = <?= $this->authBookingToken ?>;
    <?php endif; ?>
</script>

<script type="text/javascript">
        $(document).ready(() => {
            $('#contact-us-button').click(() => {
                $('#contact-us-modal').modal('show')
            })
        })
    </script>

<?=$this->partial('sitebottom.phtml', [
    'userId'=>$this->loggedUser->getId(),
    'loggedUser' => $this->loggedUser,
    'scripts'=>$this->scripts
   ]);
?>
