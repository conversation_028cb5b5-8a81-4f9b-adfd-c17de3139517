<h1>Looks like an error has occurred.</h1>
<h2>For help, <NAME_EMAIL>.</h2>
<h2><?=$this->message?></h2>

<?php if (! Genesis_Config_Server::isProduction()): ?>
    <h3>Exception information:</h3>
    <p><b>Message:</b> <?=$this->exception->getMessage()?></p>

    <h3>Stack trace:</h3>
    <pre><?=$this->exception->getTraceAsString()?></pre>

    <h3>Request Parameters:</h3>
    <pre><?=var_export($this->requestParams, true)?></pre>

<?php else: ?>
    <!--<p>Errors disabled for environment production.</p>-->
<?php endif; ?>
