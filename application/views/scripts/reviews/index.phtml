<?php if ($this->reviews->current() == NULL && !$this->q): ?>
    <div class="ui grid">
        <div class="sixteen wide tablet ten wide computer column"/>
            <div class="ui icon warning message">
                <i class="star icon"></i>
                <div class="header">Looks like you don't have any reviews yet</div>
                <p>
                    Reviews help you stand out and earn a potential renter's trust.<br/>
                    Customers are 50% <em>more likely</em> to reserve at facilities with at least one review.
                </p>
            </div>

            <a href="<?=$this->url(['action'=>'request'], 'reviews')?>?fid=<?=$this->facility->getId()?>" class="ui blue button right floated">Request Reviews</a>
        </div>
    </div>

<?php else: ?>
    <form action="<?=$this->url([], 'reviews')?>?fid=<?=($this->facility)?$this->facility->getId():''?>" class="ui form form-search">
        <div class="inline field">
            <a href="<?=$this->url(['action'=>'request'], 'reviews')?>?fid=<?=($this->facility)?$this->facility->getId():''?>" class="ui blue button">Request Reviews</a>
        </div>
        <div class="field">
            <div class="ui icon input">
                <input type="text" class="form-control search-query" name="q" value="<?=$this->q?>" placeholder="Search Reviews">
                <input type="hidden" name="fid" value="<?=($this->facility)?$this->facility->getId():''?>"/>
                <i class="search icon"></i>

                <?php if($this->q){ ?>
                    <span class="clear-search">
                        <a href="<?=$this->url([], 'reviews')?>?fid=<?=($this->facility)?$this->facility->getId():''?>">
                        <img src="/images/search-clear.gif" />
                        </a>
                    </span>
                <?php } ?>
            </div>
        </div>
    </form>

    <br/>

    <?php if ($this->reviews->current() == NULL && $this->q) { ?>
    <div class="ui warning message">
        <p>No results found.</p>
    </div>
    <?php } ?>

    <!-- TODO: Old Theme below -->
    <div class="list-group reviews-list">
        <?php foreach($this->reviews as $review){ ?>

            <a href="<?=$this->url(['action'=>'detail', 'rid' => $review->getId()], 'reviews')?>" class="list-group-item">
                <span class="pull-right text-right" style="color:#0088cc;"><?=date("m/j/y", strtotime($review->getTimestamp()))?></span>
                <?=($review->getChildReview() || $review->hasPendingResponse())?'<i class="fa fa-reply" style="color:#999;"></i> ':''?><strong><?=$review->getNickname()?></strong>

                <div class="rating-stars">
                    <?php for($i=0; $i<5; $i++){

                        if($i < floor($review->getRating())){
                            echo '<i class="fa fa-star"></i>';
                        } elseif($i < floor($rating*2)/2) {
                            echo '<i class="fa fa-star-half-o"></i>';
                        } else {
                            echo '<i class="fa fa-star-o"></i>';
                        }
                    } ?>
                </div>

                <span class="review-message"><?=$review->getMessage()?></span>

                <?php if($this->numManageableFacilities > 1){ ?>
                    <span class="minor"><?=$review->getFacility()->getTitleWithCompanyCode().$code?></span>
                <?php } ?>
            </a>

        <?php } ?>
    </div>
<?php endif ?>
