<a class="ui button" href="javascript:history.back();"><i class="arrow left icon"></i> Back</a>
<br/><br/>
<?php if($this->alertMessage){ ?>
    <div class="alert alert-<?=$this->alertType?>">
        <?=$this->alertMessage?>
    </div>
<?php } ?>
<div class="ui grid">
    <div class="sixteen wide tablet ten wide computer column">
    <?php if (count($this->emailFailures) > 0){  ?>
        <div class="ui message warning">
            <div class="header">
                <strong>Heads up!</strong> We didn't send emails to the following addresses:
            </div>
            <ul class="list">
                <?php foreach($this->emailFailures as $failure) { ?>
                <li><strong><?=$failure['email']?></strong> because <?= $failure['error'] ?></li>
                <?php }?>
            </ul>
        </div>
    <?php } ?>
    <h3>Request reviews</h3>

    <p>You can request reviews from any SpareFoot tenants by using our Review Request tool below.</p>

    <p>
        Please enter the tenant's email address below. To send to multiple people, separate each email address with a comma, space, or new line.
        <!-- To send to multiple people, separate each email address with a comma. -->
        <i class="teal info circle icon"
           data-position="bottom right"
           data-html="<h4>We will not send this email to:</h4>
            <ul>
                <li>Customers who already reviewed your facility</li>
                <li>SpareFoot customers scheduled to move in to your facility in the last 30 days</li>
                <li>Any tenant who has already received a request from you</li>
                <li>Facility clients like yourself</li>
                <li>Invalid email addresses</li>
                <li>Customers who have unsubscribed from SpareFoot emails</li>
            </ul>"
           data-variation="large"></i>



    </p>

    <form class="ui form" method="post" action="<?=$this->url(['action'=>'request'], 'reviews')?><?=($this->facility)?'?fid='.$this->facility->getId():''?>" id="request-reviews-form">

        <?php if($this->alert){ ?>
            <p class="alert<?=($this->alertClass?' '.$this->alertClass:'')?>">
                <?=$this->alert?>
            </p>
        <?php } ?>

        <?php if($this->facility && ! empty($this->facility->getId())) { ?>
            <input type="hidden" name="facility_id" value="<?=$this->facility->getId()?>"/>
        <?php } elseif($this->numManageableFacilities > 1){ ?>
            <div class="field">
                <label for="facility-list" class="control-label">Facility</label>

                    <select id="facility-list" name="facility_id" class="form-control">
                        <option value=""></option>
                    <?php foreach ($this->facilities as $manageableFacility) { ?>
                        <option value="<?=$manageableFacility->getId()?>"<?=($this->facility && $this->facility->getId() == $manageableFacility->getId())?' selected="selected"':''?>><?=$manageableFacility->getTitleWithCompanyCode()?></option>
                    <?php } ?>
                    </select>

            </div>
        <?php } else { ?>
            <input type="hidden" name="facility_id" value="<?=$this->facilities->uniqueResult()->getId()?>" />
        <?php } ?>

        <div class="field">
            <textarea name="emails" id="emails" rows="5" cols="60" class="form-control" maxlength="2500"><?=$this->emails?></textarea>
            <small id="email-character-count" style="color:black;">0 / 2500</small>
            <small id="email-character-count-error" hidden style="color:red;">Must not exceed 2500 characters</small>
        </div>

        <div class="ui segment basic">
            <a href="javascript:void(0)" id="preview-email-button">Preview Your Email</a>
            <input type="submit" id="request-reviews-form-submit" value="Send Requests" data-loading-text="Sending" class="ui button primary right floated" />
            &nbsp;&nbsp;<span id="request-reviews-form-loading" class="hide"><img src="/images/loaders/large.gif" /></span>
        </div>


</form>


    </div>
</div> <!-- End grid -->

<div class="ui segment basic">
    <div class="panel panel-default email-preview hidden"><div class="panel-body"><?=$this->emailTemplate?></div></div>
</div>

