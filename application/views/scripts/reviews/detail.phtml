<a id="back-button" class="ui button"><i class="icon arrow left"></i> Back</a>

<br />

<h3>Review</h3>

<p>
    <strong>Name:</strong> <?=$this->review->getNickname()?><br />
    <strong>Date:</strong> <?=date("M. j, Y", strtotime($this->review->getTimestamp()))?>
</p>

<?php if($this->numManageableFacilities > 1){ ?>
    <p class="minor">
        <?=$this->review->getFacility()->getTitleWithCompanyCode()?><br />
        <?=$this->review->getFacility()->getLocation()->getAddress1()?><br />
        <?=$this->review->getFacility()->getLocation()->getCity()?>,
        <?=$this->review->getFacility()->getLocation()->getState()?>
        <?=$this->review->getFacility()->getLocation()->getZip()?>
    </p>
<?php } ?>

<div class="ui segment">
    <h3><?=$this->review->getTitle()?></h3>
    <div class="rating-stars">
        <?php for($i=0; $i<5; $i++){

            if($i < floor($this->review->getRating())){
                echo '<i class="fa fa-star"></i>';
            } elseif($i < floor($rating*2)/2) {
                echo '<i class="fa fa-star-half-o"></i>';
            } else {
                echo '<i class="fa fa-star-o"></i>';
            }
        } ?>
    </div>
    <p><?=$this->review->getMessage()?></p><br />
</div>

<?php if ($this->review->getRatingPrice() !== null) { ?>
<?php
    $revPrice = $this->review->getRatingPrice();
    $revLocation = $this->review->getRatingLocation();
    $revPaperwork = $this->review->getRatingPaperwork();
    $revSecurity = $this->review->getRatingSecurity();
    $revService = $this->review->getRatingService();
    $revClean = $this->review->getRatingCleanliness();
?>
<?php if ($revPrice) { ?>
    <strong>Price</strong> - <?=$revPrice?>/5 (<?=$this->review->getPriceValue($revPrice)?>)
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="<?=$revPrice?>" aria-valuemin="0" aria-valuemax="5" style="width: <?=($revPrice/5)*100?>%">
      </div>
    </div>
<?php } ?>
<?php if ($revLocation) { ?>
    <strong>Location</strong> - <?=$revLocation?>/5 (<?=$this->review->getLocationValue($revLocation)?>)
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="<?=$revLocation?>" aria-valuemin="0" aria-valuemax="5" style="width: <?=($revLocation/5)*100?>%">
      </div>
    </div>
<?php } ?>
<?php if ($revPaperwork) { ?>
    <strong>Time on paperwork</strong> - <?=$revPaperwork?>/5 (<?=$this->review->getPaperworkValue($revPaperwork)?>)
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="<?=$revPaperwork?>" aria-valuemin="0" aria-valuemax="5" style="width: <?=($revPaperwork/5)*100?>%">
      </div>
    </div>
<?php } ?>
<?php if ($revSecurity) { ?>
    <strong>Security</strong> - <?=$revSecurity?>/5 (<?=$this->review->getSecurityValue($revSecurity)?>)
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="<?=$revSecurity?>" aria-valuemin="0" aria-valuemax="5" style="width: <?=($revSecurity/5)*100?>%">
      </div>
    </div>
<?php } ?>
<?php if ($revService) { ?>
    <strong>Service</strong> - <?=$revService?>/5 (<?=$this->review->getServiceValue($revService)?>)
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="<?=$revService?>" aria-valuemin="0" aria-valuemax="5" style="width: <?=($revService/5)*100?>%">
      </div>
    </div>
<?php } ?>
<?php if ($revClean) { ?>
    <strong>Cleanliness</strong> - <?=$revClean?>/5 (<?=$this->review->getCleanlinessValue($revClean)?>)
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="<?=$revClean?>" aria-valuemin="0" aria-valuemax="5" style="width: <?=($revClean/5)*100?>%">
      </div>
    </div>
<?php } ?>
<p class="clear"><strong>Recommended:</strong> <?=$this->review->getRatingRecommended() == 1 ? 'Yes' : 'No'?></p>
<?php } ?>
<br />
<?php if($this->review->getChildReview()){ ?>
    <h4>Manager's Response</h4>
    <p><?=$this->review->getChildReview()->getMessage()?></p>
<?php } else if($this->review->hasPendingResponse()) { ?>
    <h4>Manager's Response</h4>
    <p>Pending review</p>
<?php } else { ?>
    <form id="review-response-form" method="post" action="<?=$this->url(['action'=>'response'], 'reviews')?>">
        <br />
        <p style="font-size:11px;color:#777;">Please use your discretion when posting your response to a review. This is your chance to publicly address any negative experiences associated with your facility. Remember, everyone who visits your facility page will be able to see this. Check out our quick list of <a target="_blank" href="http://blog.sparefoot.com/reviews/">review response best practices</a> before submitting yours. To stay within SpareFoot <a target="_blank" href="http://www.sparefoot.com/reviews.html">terms and conditions</a>, please do not include phone numbers, email addresses or websites in your response.</p>
        <input type="hidden" name="parent_id" value="<?=$this->review->getId()?>" />
        <div class="form-group">
            <textarea name="response" class="form-control" rows="5"></textarea>
        </div>
        <div class="form-actions">
            <input type="submit" id="review-response-form-submit" value="Leave Response" data-loading-text="Saving" class="ui blue button" />
            &nbsp;&nbsp;<span id="review-response-form-loading" class="hide"><img src="/images/loaders/large.gif" /></span>
        </div>
    </form>
<?php } ?>