<div class="page-header">
    <h6>Report</h6>
    <h1>Booking Widget Reservations</h1>
</div>

<div class="toolbar">
    <div>
        <div class="pull-right">
            <?=$this->partial('date.phtml', array('action' => '/widget/reservations', 'selected' => $this->dateRange))?>
        </div>

        <form method="post" action="/widget/reservations" id="facility_choose_form" name="facility_choose_form">
            <select onchange="javascript:$('#facility_choose_form').submit();" id="fid" name="fid">
                <option value="all">All Facilities</option>
                <?php foreach ($this->facilities as $fac) { ?>
                <option <?=($fac->getId() == $this->facility_id ? ' SELECTED ' : '')?> value="<?=$fac->getId()?>"><?=$fac->getTitle()?></option>
                <?php }?>
            </select>
        </form>
    </div>
</div>

<table class="data-grid">
    <thead>
        <tr>
            <th>Facility</th>
            <th>Date Reserved</th>
            <th>Last Name</th>
            <th>First Name</th>
            <th>Email</th>
            <th>Phone</th>
            <th>Unit</th>
            <th>Monthly Rent</th>
            <th>Move-In Date</th>
            <th>Unit Size</th>
            <th>Traffic Source</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach($this->reservations as $reservation) { ?>
        <tr>
            <td><?=$reservation['title']?></td>
            <td><?=date("m/d/Y", strtotime($reservation['timestamp']))?></td>
            <td><?=$reservation['last_name']?></td>
            <td><?=$reservation['first_name']?></td>
            <td><?=($reservation['email'] == '<EMAIL>' || empty($reservation['email']))? 'No e-mail address provided' : $reservation['email']?></td>
            <td><?=$reservation['phone']?></td>
            <td><?=$reservation['unit_number']?></td>
            <td>$<?=number_format($reservation['monthly_rent'], 2)?></td>
            <td><?=date("m/d/Y", strtotime($reservation['move_in']))?></td>
            <td><?=$reservation['size_w']?> x <?=$reservation['size_d']?></td>
            <td><?=$reservation['traffic_source']?></td>
        </tr>
        <?php } ?>
    </tbody>
</table>

<span class="hidden" id="bookingWidgetReservationsPageTest"></span>