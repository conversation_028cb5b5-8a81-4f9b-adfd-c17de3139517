<h1 class="page-header">Booking Widget Setup</h1>

<p style="margin:1em 2em;">Follow the steps below to install the Booking Widget and Analytics tools on your website and start accepting online reservations for free! We don't charge any transaction fees for customers sourced through your own website, and we don't charge any monthly fees to use the widget...it is 100% free. Our only requirement is that you install the link that is appended to the booking widget code on your site. You don't have to do any additional work, just copy and paste the full code we give you onto the site and you will be all set.</p>
<p style="margin:1em 2em;">Alternatively, you can <a href="/widget/downloadsetup">download the code and instructions <img src="/images/xls_icon.gif" style="vertical-align:middle;"></a></p>
<h2 style="margin:1em 2em;">SpareFoot Analytics (include on all your site pages)</h2>
<p class="form-section">
    <b>STEP 1: </b> Place the following div tag on every page of your site in the body.  We use this as a handle for the tracking informaiton.  It must come before any of the code in the other steps.<br/><br/>
    <span class="code">
        &lt;div id="__sf"&gt;&lt;/div&gt;
    </span>
</p>

<p class="form-section">
    <b>STEP 2: </b> Place the following code on your pages after the code from step 1.  This includes our analytics library so we can tell you what your customers are doing!<br/><br/>
    <span class="code">
        &lt;script type="text/javascript" src="https://service.sparefoot.com/js/sfanalytics.js">&lt;/script&gt;<br/>
        &lt;script type="text/javascript"&gt;<br/>
            &nbsp;&nbsp;var __sf = new SFAnalytics(<?=$this->account->getId()?>, document.getElementById('__sf'));<br/>
            &nbsp;&nbsp;__sf.renderVisit();<br/>
        &lt;/script&gt;
    </span>
</p>

<h2 style="margin:1em 2em;">SpareFoot Booking Widget (include where you want reservation/hold buttons to appear)</h2>
<div class="form-section">
    <b>STEP 3: </b> Select your favorite button style.<br/><br/>
    <table>
        <tr align="center">
            <td><div class="client-hold-button" style="background-color: #088A09;" id="resaunit">Reserve Unit</div><br/><input type="radio" name="bookButtons" checked="checked" onclick="javascript:swapColor('088A09');"/></td>
            <td><div class="client-hold-button" style="background-color: #B80000;" id="resaunit">Reserve Unit</div><br/><input type="radio" name="bookButtons" onclick="javascript:swapColor('B80000');"/></td>
            <td><div class="client-hold-button" style="background-color: #084A8C;" id="resaunit">Reserve Unit</div><br/><input type="radio" name="bookButtons" onclick="javascript:swapColor('084A8C');"/></td>
            <td><div class="client-hold-button" style="background-color: #E77A0D;" id="resaunit">Reserve Unit</div><br/><input type="radio" name="bookButtons" onclick="javascript:swapColor('E77A0D');"/></td>
        </tr>
    </table>
</div>

<p class="form-section">
    <b>STEP 4: </b> Place the following code on your page one time.  These lines are required only once regardless of how many facility reservation buttons you put on a page.<br/><br/>
    <span class="code">
        &lt;link href="https://service.sparefoot.com/css/booking-widget.css" rel="stylesheet" type="text/css"/&gt;<br/>
        &lt;script type="text/javascript" src="https://service.sparefoot.com/js/booking-widget-package.js"&gt;&lt;/script&gt;
    </span>
</p>

<p class="form-section">
    <b>STEP 5: </b> Place the following code on your website for each facility where you want the button selected in step 3 to appear.  You can replace the text "Reserve Unit" with whatever you want the button to say.  If you have more than 1 facility, you can place multiple buttons on the same page for each facility.<br/><br/>
    <?php foreach ($this->facilities as $facility) {?>
        <?=$facility->getTitle()?> [<a href='#' onClick="toggleVisibility($('#code_<?=$facility->getId()?>'));">show/hide code</a>]<br/>
        <span id="code_<?=$facility->getId()?>" class="code"><br/>
            &lt;script type="text/javascript" src="https://service.sparefoot.com/syndication/booking/proxy?fid=<?=$facility->getId()?>"&gt;&lt;/script&gt;<br/>
            &lt;a class="client-hold-button" style="background-color: #<span id="bkgdColor">088A09</span>;" id="sparefootBooking_<?=$facility->getId()?>"&gt;<b>Reserve Unit</b>&lt;/a&gt;
            &lt;p class="sparefoot-hold-link"&gt;&lt;a href="<?=$facility->getFacilityCityPageLink()?>"&gt;<?=$facility->getSiteVerifyText()?>&lt;/a&gt; Powered by &lt;a href="http://www.sparefoot.com"&gt;SpareFoot&lt;/a&gt;&lt;/p&gt;
        <br/><br/></span>
    <?php } ?>


</p>

<p class="form-section">
    <b>STEP 6: </b> Publish the changes to your website so the code is live.<br/>
</p>

<span class="hidden" id="bookingWidgetSetupPageTest"></span>