<div class="page-header">
    <h6>Report</h6>
    <h1>GeoPages Calls</h1>
</div>

<div class="toolbar">
    <?=$this->partial('daterange.phtml',
        array(
            'action' => '/sites/calls',
            'trueDateRange' => $this->trueDateRange,
            'trueBeginDate' => $this->trueBeginDate,
            'trueEndDate'   => $this->trueEndDate,
            'showExport'    => true,
        )
    )?> 

    <form method="POST" action="/sites/calls" id="facility_choose_form" name="facility_choose_form">
        <select onchange="javascript:$('#facility_choose_form').submit();" id="fid" name="fid">
            <option value="all">All Facilities</option>
            <?php foreach ($this->facilities as $fac) { ?>
            <option <?=($fac->getId() == $this->facility_id ? ' SELECTED ' : '')?> value="<?=$fac->getId()?>"><?=$fac->getTitle()?></option>
            <?php }?>
        </select>
    </form>
</div>

<div class="ui-layout-content">
    <table class="data-grid">
        <thead>
            <tr>
                <th>Time (CST)</th>
                <th>Duration</th>
                <th>Facility</th>
                <th>Status</th>
                <th>Caller ID</th>
                <th>Recording</th>
            </tr>
        </thead>
        <tbody>
            <?php if($this->calls){ ?>
        
                <?php foreach($this->calls as $call) { ?>
                <tr>
                    <td><?=date('M d, Y g:i A',strtotime($call['start_time']))?></td>
                    <td><?=floor($call['duration']/60)?>:<?=str_pad(($call['duration']%60),2,'0',STR_PAD_LEFT)?></td>
                    <td><?=$call['facility_title']?></td>
                    <td><?=$call['dial_status']?></td>
                    <td><?=$call['caller_name']?></td>
                    <td><a href="<?=$call['recording_url']?>" target="_blank">Listen</a></td>
                </tr>
                <?php } ?>
            
            <?php } ?>
        </tbody>
    </table>
</div>

<span class="hidden" id="geoPagesCallsPageTest"></span>