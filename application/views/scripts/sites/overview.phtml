<div class="page-header">
    <h6>Report</h6>
    <h1>GeoPages Overview</h1>
</div>

<?php if(!$this->facilities &&
        $this->loggedUser->getMyFootRole() != Genesis_Entity_UserAccess::ROLE_ADMIN &&
        $this->loggedUser->getMyFootRole() != Genesis_Entity_UserAccess::ROLE_GOD){ ?>
    <div class="setup-content-container">
        <div class="setup-content">
            You don't have access to any facilities with GeoPages.
        </div>
    </div>
<?php } elseif(!$this->facilities){ ?>

    <h1>GeoPages</h1>
    <p>You do not have any facilities using SpareFoot GeoPages.</p>

<?php }else{ ?>

    <div class="toolbar">
        <?=$this->partial('daterange.phtml',
            array(
                'action' => '/sites',
                'trueDateRange' => $this->trueDateRange,
                'trueBeginDate' => $this->trueBeginDate,
                'trueEndDate'   => $this->trueEndDate,
                'showExport'    => true,
            )
        )?>
        
        <form method="post" action="/sites/calls" id="facility_choose_calls_form" name="facility_choose_calls_form">
            <input type="hidden" id="calls_fid" name="fid" value="" />
        </form>
        
        <form method="post" action="/sites/reservations" id="facility_choose_reservations_form" name="facility_choose_reservations_form">
            <input type="hidden" id="reservations_fid" name="fid" value="" />
        </form>
    </div>

    <div class="ui-layout-content">
    <table class="data-grid">
        <thead>
            <tr>
                <th>Facility</th>
                <th>Visits</th>
                <th>Reservations</th>
                <th>Calls</th>
                <th>URL</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach($this->facilities as $facility) { ?>
            <tr>
                <td><?=$facility['entity']->getTitle()?></td>
                <td><?=$facility['num_visits']?></td>
                <td><a onclick="javascript:$('#reservations_fid').val('<?=$facility['entity']->getId()?>');$('#facility_choose_reservations_form').submit();" style="text-decoration:underline; cursor:pointer;"><?=$facility['num_reservations']?></a></td>
                <td><a onclick="javascript:$('#calls_fid').val('<?=$facility['entity']->getId()?>');$('#facility_choose_calls_form').submit();" style="text-decoration:underline; cursor:pointer;"><?=$facility['num_calls']?></a></td>
                <td><?php if ($this->showUrls) { ?><a href="<?=Genesis_Util_Url::hostedsiteUrl($facility['entity'])?>"><?=Genesis_Util_Url::hostedsiteUrl($facility['entity'])?></a><?php } else { ?><font color="red">NOT ACTIVATED:</font> Your GeoPage URL will be generated when payment information is received (<a href="/payment">click here</a>).<?php } ?></td>
            </tr>
            <?php } ?>
        </tbody>
    </table>
</div>

<?php } ?>

<span class="hidden" id="geoPagesOverviewPageTest"></span>
