<div class="page-header">
    <h6>Report</h6>
    <h2>Tenant Connect Calls</h2>
</div>

<div class="ui secondary menu">
    <?=$this->partial('daterange.phtml',
        array(
            'action' => '/reports/tenant-connect/',
            'trueDateRange' => $this->trueDateRange,
            'trueBeginDate' => $this->trueBeginDate,
            'trueEndDate'   => $this->trueEndDate,
            'showExport'    => false,
        )
    )?>

    <a href="/reports/tenant-connect?export=true&true_date_range=<?=urlencode($this->trueDateRange)?>" class="ui button secondary pull-right" style="margin-right:0.5em;">
        <i class="file icon"></i> Export Spreadsheet</a>
</div>
<br />
<table class="ui table striped sortable cell-headers">
    <thead>
        <tr>
            <th></th>
            <th>Call Attempts</th>
            <th>Facility Answered</th>
            <th>Facility Responded to Call Prompts</th>
            <th>Connected to Tenant</th>
        </tr>
    </thead>
    <tbody>
    <?php foreach($this->callData as $facilityId => $facData){
        $responseRate = round(($facData['facilityRespondedCalls']/$facData['totalCalls']),2)*100;
    ?>
        <tr id="<?=$facilityId?>">
            <td data-sort-value="<?=$responseRate?>">
                <a class="report-tenant-connect-link" href="/reports/tenant-connect-detail/fid/<?=$facilityId?>/true_date_range/<?=$this->trueDateRange?>">
                    <strong><?=$facData['facilityTitle']?></strong>
                </a><br />
                <span class="minor"><?=$responseRate?>% Response Rate</span>
            </td>
            <td>
                <?=$facData['totalCalls']?>
            </td>
            <td>
                <?=$facData['answeredCalls']?>
            </td>
            <td>
                <?=$facData['facilityRespondedCalls']?>
            </td>
            <td>
                <?=$facData['connectedCalls']?>
            </td>
        </tr>
    <?php } ?>
    </tbody>
</table>