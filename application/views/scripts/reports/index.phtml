<div class="page-header">
    <h1>Reports</h1>
</div>

<h2>Performance</h2>
<ul>
    <li><a href="<?=$this->url(['action'=> 'reservations'], 'widget')?>">Booking Widget Reservations</a></li>
    <li><a href="<?=$this->url(['action'=> 'analytics'], 'widget')?>">Booking Widget Analytics</a></li>

<?php if ($this->loggedUser->getAccount() && $this->loggedUser->getAccount()->getHostedSite()) { ?>
    <li><a href="<?=$this->url(['action'=> 'reservations'], 'sites')?>">GeoPages Reservations</a></li>
    <li><a href="<?=$this->url(['action'=> 'calls'], 'sites')?>">GeoPages Calls</a></li>
<?php } ?>

<?php if($this->loggedUser->hasTenantConnect()){ ?>
    <li><a href="<?=$this->url(['action'=> 'tenant-connect'], 'reports')?>">Tenant Connect Calls</a></li>
<?php } ?>
</ul>


<?php if (!empty($this->otherReports)): ?>
    <?php 
    $categories = array_keys($this->otherReports);
    sort($categories);
    foreach ($categories as $category): ?>
        <h2><?=$category?></h2>
        <ul>
            <?php foreach ($this->otherReports[$category] as $reportClassName => $reportName): ?>
                <li><a href="<?=$this->url(['action'=>'custom'], 'insights')?>?report=<?=$reportClassName?>"><?=$reportName?></a></li>
            <?php endforeach; ?>
        </ul>
    <?php endforeach; ?>
<?php else: ?>
    <div class="ui error message">
        <p>No more reports available at the moment. Please check back later!</p>
    </div>
<?php endif; ?>
