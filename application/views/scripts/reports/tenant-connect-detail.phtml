<h6>Tenant Connect Calls</h6>
<h3><?=$this->facility->getTitle()?></h3><br />

<div class="toolbar">
    <?=$this->partial('daterange.phtml',
        array(
            'action' => '/reports/tenant-connect-detail/fid/'.$this->facility->getId(),
            'trueDateRange' => $this->trueDateRange,
            'trueBeginDate' => $this->trueBeginDate,
            'trueEndDate'   => $this->trueEndDate,
            'showExport'    => false,
        )
    )?>
    <a href="/reports/tenant-connect-detail/fid/<?=$this->facility->getId()?>?export=true&true_date_range=<?=urlencode($this->trueDateRange)?>" class="btn btn-default pull-right" style="margin-right:0.5em;"><i class="fa fa-file"></i> Export Spreadsheet</a>
</div>
<br />
<table class="table table-striped data-grid">
    <thead>
        <tr>
            <th>Call</th>
            <th>Customer</th>
            <th>Call Status</th>
            <th>Listen to Call</th>
        </tr>
    </thead>
    <tbody>
    <?php foreach($this->callData as $tcCall){ ?>
        <?php $i++; ?>
     	<tr id="<?=$tcCall->getId()?>">
     		<td>
                <span class="minor"><?=$tcCall->stringDate()?></span><br />
                <strong><?=$tcCall->getBooking()->stringPhone()?></strong>
     		</td>
     		<td>
                 <?=trim(ucwords($tcCall->getBooking()->getFirstName()))?> <?=trim(ucwords($tcCall->getBooking()->getLastName()))?><br />
     		     <?php if($tcCall->getBooking()->getUser()->getEmail()=='<EMAIL>'){ ?>
     		         No email
     		     <?php } else { ?>
         		     <a href="mailto:<?=$tcCall->getBooking()->getUser()->getEmail()?>"><?=$tcCall->getBooking()->getUser()->getEmail()?></a>
     		     <?php } ?>
     		</td>
     		<td><?=$tcCall->stringStatus()?></td>
     		<td>
     			<?php if($tcCall->getRecordingUrl()) { ?>
     				<a class="ui button" href="<?=$tcCall->getRecordingUrl()?>" target="_blank"><i class="fa fa-play"></i></a>
     			<?php } else { ?>
     				No audio
     			<?php }?>
     		</td>
    	</tr>
    <?php } ?>
    </tbody>
</table>

<?php if($i == 0){ ?>
    <p>No call data available for the time period.</p>
<?php } ?>