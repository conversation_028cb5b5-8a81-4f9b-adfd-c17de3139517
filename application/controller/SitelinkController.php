<?
class SitelinkController extends AccountMgmt_Controller_Restricted {
    const OID = '00DC0000000PXv8';

    // anytime anyone clicks a sitelink ad it gets redirected here to submit to salesforce and write to the actions table
    public function salesLeadAction() {
        header('Content-Type: application/json');

        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if($this->getRequest()->isPost()) {
            $data = $_POST;

            $data['oid'] = self::OID;
            $data['retURL'] = 'http://sparefoot.force.com/thankyou';
            $data['lead_source'] = 'SpareFoot';
            $data['promotion__c'] = "MyFoot-Jan15";
            $data['00NC0000005eubi'] = "MyFoot-Jan15";
            // $data['debug'] = 1;
            // $data['debugEmail'] = '<EMAIL>';

            // curl submit the salesforce form
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://www.salesforce.com/servlet/servlet.WebToLead?encoding=UTF-8');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);

            $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            http_response_code($statusCode);

            // saves to the actions table
            $logger = new Genesis_Util_ActionLogger();
            $logger->logAction('sitelink_lead', 0, 0, $data['user_id']);
						
            if($statusCode != 200) {
                $errText = 'Sitelink lead submit to Salesforce failed.';
                echo json_encode([ 'error'=>$errText ]);
            } else {
                echo json_encode([ 'success'=>true ]);
            }

            curl_close($ch);
        } else {
            http_response_code(405);
            echo json_encode([ 'error'=>'Method not allowed.' ]);
        }
    }
}
