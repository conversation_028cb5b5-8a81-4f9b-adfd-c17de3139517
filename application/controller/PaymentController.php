<?php
/*
 * Payment Controller
 *
 * @copyright SpareFoot
 * <AUTHOR>
 */

use Sparefoot\SalesForce\Client;
use Sparefoot\SalesForce\SalesForceClientException;

class PaymentController extends AccountMgmt_Controller_Restricted
{
    public const PAYMENT_CSRF_TOKEN = 'payment_csrf_token';

    public function indexAction()
    {
        //direct back to signup process if not yet completed
        try {
            $user = $this->getLoggedUser()->getUserAccess();
        } catch (Exception $e) {
            Rollbar::report_message($e);
        }

        //SIGNUP PROCESS RE-ENTRY
        if ($user->getAccount() && $user->getAccount()->getNumBillableEntities() == 0) {
            $this->forward('complete-signup');
        } else {
            $this->forward('summary');
        }
    }

    public function completeSignupAction()
    {
    }

    /**
     * This page is displayed when the user hits the payments tab in myfoot.
     *
     * @throws Exception
     */
    public function summaryAction()
    {
        $account = $this->getLoggedUser()->getAccount();

        //make sure user is allowed to modify
        if (!Genesis_Service_UserAccess::isAllowed($this->getLoggedUser()->getUserAccess(), $account)) {
            throw new Exception('User '.$this->getLoggedUser()->getId().' is not authorized for an action on the requested account '.$account->getId());
        }

        $this->view->facilities = Genesis_Service_Facility::loadByAccountId($account->getId(),
            Genesis_Db_Restriction::equal('published', 1)
        );

        $billableEntities = $account->getPublishedBillableEntities()->toArray();

        $output = [];

        //get each payment details
        /**
         * @var $be Genesis_Entity_BillableEntity
         */
        foreach ($billableEntities as $be) {
            $be->popNetsuiteFields();

            $output[$be->getId()]['id'] = $be->getId();
            $output[$be->getId()]['nsCustId'] = $be->getNsCustId();
            $output[$be->getId()]['nsParentId'] = $be->getNsParentId();
            $output[$be->getId()]['paymentTypeNickname'] = $be->getNsName();
            $output[$be->getId()]['paymentType'] = $be->getTerms();
            $output[$be->getId()]['emails'] = str_replace(' ', ', ', $be->getBillingEmails());

            $output[$be->getId()]['address'] = '';
            $output[$be->getId()]['city'] = '';
            $output[$be->getId()]['state'] = '';
            $output[$be->getId()]['zip'] = '';

            $address = $be->getAddress();

            if ($address instanceof Genesis_Entity_Netsuite_Address) {
                $output[$be->getId()]['address'] = $address->getAddress1();
                $output[$be->getId()]['city'] = $address->getCity();
                $output[$be->getId()]['state'] = $address->getState();
                $output[$be->getId()]['zip'] = $address->getZip();
            }

            switch ($be->getTerms()) {
                case Genesis_Entity_BillableEntity::TYPE_CREDIT_CARD:
                    $cc = $be->getCreditCard();

                    if ($cc instanceof Genesis_Entity_Netsuite_CreditCard) {
                        $output[$be->getId()]['creditCard']['number'] = $cc->getCcNumber();
                        $output[$be->getId()]['creditCard']['nsId'] = $cc->getNsInternalId();
                        $output[$be->getId()]['creditCard']['type'] = $cc->getCcType();
                        $output[$be->getId()]['creditCard']['name'] = $cc->getCcName();
                        $output[$be->getId()]['creditCard']['expirationMonth'] = $cc->getCcExpireMonth();
                        $output[$be->getId()]['creditCard']['expirationYear'] = $cc->getCcExpireYear();

                        $output[$be->getId()]['displayStr'] = Genesis_Entity_BillableEntity::TYPE_CREDIT_CARD.' - '.$cc->getCcNumber().', '.$be->getNsName();
                    } else {
                        $output[$be->getId()]['displayStr'] = Genesis_Entity_BillableEntity::TYPE_CREDIT_CARD.' - No Card On File'.', '.$be->getNsName();
                    }
                    break;
                case Genesis_Entity_BillableEntity::TYPE_ACH:
                    $ach = $be->getACH();
                    if ($ach instanceof Genesis_Entity_Netsuite_ACH) {
                        $output[$be->getId()]['ach']['name'] = $ach->getBankName();
                        $output[$be->getId()]['ach']['account'] = $ach->getAccountNum();
                        $output[$be->getId()]['ach']['routing'] = $ach->getRoutingNum();

                        $output[$be->getId()]['displayStr'] = Genesis_Entity_BillableEntity::TYPE_ACH.' - '.$be->getNsName();
                    } else {
                        $output[$be->getId()]['displayStr'] = Genesis_Entity_BillableEntity::TYPE_ACH.' - '.$be->getNsName();
                    }

                    break;
                case Genesis_Entity_BillableEntity::TYPE_NET_30:
                    $output[$be->getId()]['displayStr'] = Genesis_Entity_BillableEntity::TYPE_NET_30.' - '.$be->getNsName();
                    break;
                case Genesis_Entity_BillableEntity::TYPE_NET_10:
                    $output[$be->getId()]['displayStr'] = Genesis_Entity_BillableEntity::TYPE_NET_10.' - '.$be->getNsName();
                    break;
                default:
                    $output[$be->getId()]['displayStr'] = 'n/a';
                    break;
            }
        }

        $this->view->payment_methods = $output;

        $this->view->scripts = ['payment/summary'];
        $this->view->title = 'Payment - Settings';
    }

    public function addAction()
    {
        $this->forward('edit');
    }

    public function editAction()
    {
        $accountId = $this->getLoggedUser()->getAccountId();
        $this->view->accountId = $accountId;
        $this->view->account = $account = Genesis_Service_Account::loadById($accountId);
        $this->view->csrf_token = CsrfUtil::getToken(self::PAYMENT_CSRF_TOKEN);

        //make sure user is allowed to modify
        if (!Genesis_Service_UserAccess::isAllowed($this->getLoggedUser()->getUserAccess(), $account)) {
            throw new Exception('User '.$this->getLoggedUser()->getId().' is not authorized for an action on the requested account '.$account->getId());
        }
        $this->view->isNew = 0;
        $this->view->return = $this->getParam('return');

        //see if we should return to the facility and process after saving
        $this->view->returnscreen = '/payment';
        if ($this->getParam('return')) {
            $this->view->returnscreen = $this->view->url(['action' => 'addsummary'], 'features');
        } else {
            $this->view->returnscreen = '/payment';
        }

        //TODO: make this pull the NetSuite data, not the summary page.  Summary page may have to sacrifice last for of CC or store locally
        $billableEntityId = $this->getParam('billable_entity_id');

        //if be exists, then we know we are editing
        if ($billableEntityId) {
            $be = Genesis_Service_BillableEntity::loadById($billableEntityId);
            if (empty($be)) {
                throw new Exception('Billable Entity not found. <NAME_EMAIL> if you need help.');
            }
        } else {
            return $this->redirect('payment');
        }

        //NOTE: we are doing this param passing between views to minimize netsuite api calls
        $this->view->billableEntityId = $billableEntityId;
        $this->view->paymentType = $this->getParam('payment_type');
        $this->view->emails = $this->getParam('emails');
        $this->view->paymentTypeNickname = $this->getParam('payment_type_nickname');
        $this->view->address = $this->getParam('address');
        $this->view->city = $this->getParam('city');
        $this->view->state = $this->getParam('state');
        $this->view->zip = $this->getParam('zip');

        $this->view->creditCardNumber = $this->getParam('credit_card_number');
        $this->view->ccType = $this->getParam('ccType'); //VISA, etc
        $this->view->ccNsId = $this->getParam('ccNsId');
        $this->view->creditCardName = $this->getParam('credit_card_name');
        $this->view->creditCardExpirationMonth = $this->getParam('credit_card_expiration_month');
        $this->view->creditCardExpirationYear = $this->getParam('credit_card_expiration_year');

        $this->view->achName = $this->getParam('achName');
        $this->view->achAccount = $this->getParam('achAccount');
        $this->view->achRouting = $this->getParam('achRouting');

        $this->view->scripts = ['payment/edit'];
    }

    public function tempAction()
    {
        $account = $this->getLoggedUser()->getAccount();

        $this->view->account = $account;
    }

    /**
     * gets the address to put in the address field when user checks
     * "billing address is same as company address".
     *
     * @throws Exception
     */
    public function sameCoAddressAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            $accountId = $this->getLoggedUser()->getAccountId();
            if ($accountId === null) {
                throw new Exception('account_id is required');
            }
            $account = Genesis_Service_Account::loadById($accountId);
            if ($account === null) {
                throw new Exception('No such account.');
            }
            //make sure user is allowed to modify
            if (!Genesis_Service_UserAccess::isAllowed($this->getLoggedUser()->getUserAccess(), $account)) {
                throw new Exception('User '.$this->getLoggedUser()->getId().' is not authorized for an action on the requested account '.$account->getId());
            }
            if (!$account->getLocation()) {
                throw new Exception('No corporate address on file.');
            }
            echo json_encode([
                'success' => true,
                'address' => $account->getLocation()->getAddress1(),
                'city' => $account->getLocation()->getCity(),
                'state' => $account->getLocation()->getState(),
                'zip' => $account->getLocation()->getZip(),
            ]);
        } catch (Exception $e) {
            Genesis_Util_ErrorLogger::exceptionToHipChat($e);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * updates the billable entity a facility is assigned to.
     *
     * @throws Exception
     */
    public function changeFacilityBillableEntityAction()
    {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            $facilityId = $this->getParam('facility_id');
            $billableEntityId = $this->getParam('billable_entity_id');
            $facility = Genesis_Service_Facility::loadById($facilityId);

            //make sure user is allowed to modify
            if (!$this->getLoggedUser()->getUserAccess()->canAccessFacility($facility)) {
                throw new Exception('User '.$this->getLoggedUser()->getId().' is not allowed to change facility '.$facility->getId());
            }

            $facility->setBillableEntityId($billableEntityId);
            Genesis_Service_Facility::save($facility);
        } catch (Exception $e) {
            Genesis_Util_ErrorLogger::exceptionToHipChat($e);
            echo 'Error: ',  $e->getMessage(), "\n";
        }
    }

    /**
     * update/adds the netsuite customer.
     */
    public function updateCustomerAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        header('Content-type: application/json');
        $csrfToken = $this->getSession()->csrf_token = $this->getParam('csrf_token');

        if (CsrfUtil::validateToken(SignupEndController::SIGNUP_END_CSRF_TOKEN, $csrfToken) || CsrfUtil::validateToken(self::PAYMENT_CSRF_TOKEN, $csrfToken)) {
            try {
                $account = Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
                if (!$account) {
                    throw new Exception('No account with that ID.');
                }
                //make sure user is allowed to modify
                if (!Genesis_Service_UserAccess::isAllowed($this->getLoggedUser()->getUserAccess(), $account)) {
                    throw new Exception('User '.$this->getLoggedUser()->getId().' is not authorized for an action on the requested account '.$account->getId());
                }

                if ($this->getParam('billable_entity_id') > 0) {
                    $billableEntity = Genesis_Service_BillableEntity::loadById($this->getParam('billable_entity_id'));
                    if (!$billableEntity) {
                        throw new Exception('No billable entity with that ID.');
                    }
                } else {
                    $billableEntity = new Genesis_Entity_BillableEntity();
                    $billableEntity->setAccountId($account->getId());
                }
                if ($billableEntity->getAccountId() !== $account->getAccountId()) {
                    throw new Exception('not authorized to modify billable entity '.$billableEntity->getId().' for account '.$account->getId());
                }
                $billableEntity->setNsName($this->getParam('payment_type_nickname', $account->getName()));
                $billableEntity->setPaymentType($this->getParam('payment_type'));
                /**
                 * do not move. must happen here. we set the real fields, the rest are temporary on entity.
                 */
                $billableEntity = Genesis_Service_BillableEntity::save($billableEntity);

                //validate email
                $emailFound = false;
                $rawEmailsTest = $this->getParam('emails');
                $emails = array_unique(explode(',', $rawEmailsTest));
                foreach ($emails as $email) {
                    $email = trim($email);
                    if (!strlen($email)) {
                        continue;
                    }

                    $sanitizedEmail = filter_var($email, FILTER_SANITIZE_EMAIL);
                    if ($email != $sanitizedEmail || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception($email.' is an invalid email address');
                    }

                    if (strlen($email)) {
                        $emailFound = true;
                    }
                }
                if (!$emailFound) {
                    throw new Exception('A valid billing email address was not found');
                }
                //format check emails
                try {
                    $billableEntity->setBillingEmails($rawEmailsTest);
                } catch (InvalidArgumentException $e) {
                    throw new Exception('Please provide billing emails as a comma separated list.');
                }
                //address
                $address = new Genesis_Entity_Netsuite_Address();
                $address->setAddress1($this->getParam('address'));
                $address->setCity($this->getParam('city'));
                $address->setState($this->getParam('state'));
                //validate zip if supplied
                if (strlen($this->getParam('zip')) > 0 && !preg_match("/^\d{5}$|^\d{5}-\d{4}$/", $this->getParam('zip'))) {
                    throw new Exception('Please enter a valid zip code.');
                }
                $address->setZip($this->getParam('zip'));
                $billableEntity->setAddress($address);
                $billableEntity->setNsPhone($account->getPhone());
                $creditCard = null; //override into a credit card
                switch ($this->getParam('payment_type')) {
                    case Genesis_Entity_BillableEntity::TYPE_CREDIT_CARD:
                        foreach ([
                                     'credit_card_number' => 'Credit Card number required.',
                                     'credit_card_name' => 'Credit Card name required.',
                                     'credit_card_expiration_month' => 'Credit Card expiration month required.',
                                     'credit_card_expiration_year' => 'Credit Card expiration year required.',
                                     'ccType' => 'Credit Card type required.',
                                 ] as $inputName => $errorMsg) {
                            if ($this->getParam($inputName) === null) {
                                throw new Exception($errorMsg);
                            }
                        }
                        if ($this->getParam('ccType') === 'existing') { //didn't change, so keep the CC null on update
                            break;
                        }

                        $creditCard = new Genesis_Entity_Netsuite_CreditCard();
                        $creditCard->setCcNumber($this->getParam('credit_card_number'));
                        $creditCard->setCcName($this->getParam('credit_card_name'));
                        $creditCard->setCcExpireMonth($this->getParam('credit_card_expiration_month'));
                        $creditCard->setCcExpireYear($this->getParam('credit_card_expiration_year'));
                        $creditCard->setCcType($this->getParam('ccType'));

                        break;
                    case Genesis_Entity_BillableEntity::TYPE_ACH:
    //                    foreach (array(
    //                                 'bankName'                 => 'Bank Name is required.',
    //                                 'bankAcctNum'              => 'Account Number is required.',
    //                                 'bankRoutingNum'           => 'Routing Number is required.',
    //                             ) as $inputName => $errorMsg) {
    //                        if ($this->getParam($inputName) === null) {
    //                            throw new Exception($errorMsg);
    //                        }
    //                    }
    //                    $ach = new Genesis_Entity_Netsuite_ACH();
    //                    $ach->setBankName($this->getParam('bankName'));
    //                    $ach->setAccountNum($this->getParam('bankAcctNum'));
    //                    $ach->setRoutingNum($this->getParam('bankRoutingNum'));

                        break;
                    case Genesis_Entity_BillableEntity::TYPE_NET_10:
                    case Genesis_Entity_BillableEntity::TYPE_NET_30:
                        break;
                    default:
                        throw new Exception('Invalid payment type: '.$this->getParam('payment_type'));
                }

                //make the api call
                try {
                    $nsId = Genesis_Service_BillableEntity::saveNetsuiteCustomer(
                        $billableEntity, $creditCard, $this->getLoggedUser()
                    );
                } catch (Exception $e) {
                    throw new Exception('Unable to save credit card entry. '.$e->getMessage());
                }

                $this->sendPaymentMethodUpdateTicket($billableEntity);

                if ($this->getParam('cc_change_listener') == 1) {
                    $msg = new Genesis_Entity_EmailMessage();
                    $msg->setSubject($account->getName().' has changed billing information');

                    $cpa = $account->getCpa() ? 'yes' : 'no';

                    $payment_type_str = ($billableEntity->getPaymentType() === 'Credit Card') ? $billableEntity->getPaymentType().' (new last 4 digits: '.substr($this->getParam('credit_card_number'), -4, 4).')' : $billableEntity->getPaymentType();

                    $user_account_name = ($this->getLoggedUser()->getAccount()) ? $this->getLoggedUser()->getAccount()->getName() : '';

                    $msg->setBody('Account: '.$account->getName().chr(10).chr(13).
                        'SPAREFOOT ID: '.$account->getSfAccountId().''.chr(10).chr(13).
                        'PITA Created Date: '.$account->getTimeCreated().''.chr(10).chr(13).chr(10).chr(13).
                        'CPA Enabled?: '.$cpa.' '.chr(10).chr(13).
                        '# of Billable Entities: '.$account->getNumBillableEntities().' '.chr(10).chr(13).
                        'Customer/Billable Entity Name: '.$billableEntity->getNsName().' '.chr(10).chr(13).
                        'Payment Type: '.$payment_type_str.' '.chr(10).chr(13).
                        'Action: Updated '.chr(10).chr(13).chr(10).chr(13).
                        'User That Updated This: '.chr(10).chr(13).
                        'Account: '.$user_account_name.chr(10).chr(13).
                        'Name: '.$this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName().' '.chr(10).chr(13).
                        'Email: '.$this->getLoggedUser()->getEmail().' '.chr(10).chr(13).
                        'NetsuiteId: '.$nsId.' ');

                    if (!$this->getLoggedUser()->isMyFootGod()) {
                        Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $msg, [], $this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName(), $this->getLoggedUser()->getEmail());
                    }
                }

                echo json_encode(['success' => true]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'There was an error during  the submission. Please refresh and try again.']);
        }
    }

    /**
     * @throws SalesForceClientException
     */
    private function sendPaymentMethodUpdateTicket(
        Genesis_Entity_BillableEntity $billableEntity
    ) {
        if ($billableEntity) {
            $user = $this->getLoggedUser();

            // For list of keys: https://developer.salesforce.com/docs/api-explorer/sobject/Case
            $ticketOptions = [
                // Owner ID set in SalesForce Client
                'Type' => 'Question',
                'Origin' => 'Internal',
                'Status' => 'New',
                'Subject' => $billableEntity->getAccount()->getName().' - Billing Information Update',
                'Priority' => 'Low',

                'SuppliedName' => $user->getFirstName().' '.$user->getLastName(),
                'SuppliedEmail' => $user->getEmail(),
                'SuppliedPhone' => $user->getPhone(),

                'Description' => 'Payment Nickname: '.$billableEntity->getNsName().PHP_EOL
                    .'Payment Type: '.$billableEntity->getPaymentType().PHP_EOL
                    .'Billable Entity ID:'.$billableEntity->getId().PHP_EOL
                    .'Billable Entity Email:'.$billableEntity->getBillingEmails().PHP_EOL
                    .PHP_EOL
                    .'Email:'.$user->getEmail().PHP_EOL
                    .PHP_EOL
                    .'mysparefoot_payment-update_q6xJ34',
            ];

            // Not frequently used (hopefully), so create a client per update
            $salesForceClient = new Client();
            $salesForceClient->createTicket($ticketOptions);
        }
    }
}
