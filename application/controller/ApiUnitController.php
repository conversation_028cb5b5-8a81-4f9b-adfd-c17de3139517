<?php
use AccountMgmt_Models_ApiException as ApiException;
class ApiUnitController extends AccountMgmt_Controller_ApiBaseController
{
    public function init() {
        parent::init();
    }

    public function indexAction()
    {
        $unitId = $this->_getParam('unit_id');
        $facilityId = $this->getParam('facility_id');
        $json_body = $this->getRequest()->getRawBody();

        if ($this->_request->isGet()) {
            $hasMultipleUnitIds = (strpos($unitId, ',') !== false) ||(strpos($unitId, '%2C') !== false);
            //this combo, throw an exception when commas and no fid
            if ($hasMultipleUnitIds && ! $facilityId) {
                throw new ApiException(ApiException::BAD_REQUEST, 'bad request. missing facility id');
            }
            //?facilityId route
            if ($facilityId) {
                try {
                    $facility = AccountMgmt_Service_Facility::validateFacilityId($facilityId);
                } catch (Exception $e) {
                    throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
                }
                try {
                    AccountMgmt_Service_User::validateFacilityAccess($facility);
                } catch (Exception $e) {
                    throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
                }
                $unitId = str_replace('%2C', ',', $unitId); //change unicode commas to normal

                // TODO - we should add pagination and filtering here
                //
                $response = [];

                if ($hasMultipleUnitIds) {
                    $units = Genesis_Service_StorageSpace::load(
                        Genesis_Db_Restriction::and_(
                            Genesis_Db_Restriction::in('id', explode(',', $unitId)),
                            Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                            Genesis_Db_Restriction::equal('publish', 1)
                        )
                    );
                    if (! count($units)) { // if this record doesn't exist
                        throw new ApiException(ApiException::NOT_FOUND, 'no units found');
                    }
                } else {
                    //this grouped call is always right
                    $units =  $facility->getGroupedUnits();
                }
                /**
                 * @var $unit Genesis_Entity_StorageSpace
                 */
                foreach ($units as $unit) {
                    $response[] = AccountMgmt_Service_Unit::toArray($unit);
                }
                $this->_helper->json(['data' => $response]);
            }
            //single unit path
            if ($unitId) {
                try {
                    $unit = AccountMgmt_Service_Unit::validateUnitId($unitId);
                } catch (Exception $e) {
                    throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
                }
                try {
                    AccountMgmt_Service_User::validateUnitAccess($unit);
                } catch (Exception $e) {
                    throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
                }
                $this->_helper->json(['data'=>AccountMgmt_Service_Unit::toArray($unit)]);
            }
        }



        /**
         * Endpoint: [GET] /api/units?facility_id=:facility_id
         *     Grabs all units belonging to a facility.
         * Parameters:
         *     facility_id - (required) facility id you want
         * Response:
         *     200 - (array) array of units
         *     400 - bad request. missing {parameter}
         *     500 - server error
         */
        /**
         * Endpoint: [GET] /api/units/:id,:id,:id,...?facility_id=:facility_id
         *     Grabs specific units with :id belonging to a facility.
         * Parameters:
         *     :id - unit id
         *     facility_id - (required) facility id you want
         * Response:
         *     200 - (object) one unit
         *           (array) array of units
         *     400 - bad request. missing {parameter}
         *     404 - not found
         *     500 - server error
         *
         *  4xx = your damn fault
         *  5xx = our damn fault
         */



        /**
         * Endpoint: [PUT] /api/units/:id
         *     Updates a specific unit.
         */
        if ($this->_request->isPut()) {
            $decoded_json = json_decode($json_body, true);
            unset($decoded_json['facility_id']);
            try {
                $unit = AccountMgmt_Service_Unit::validateUnitId($unitId);
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            try {
                $checkWriteAccess = true;
                AccountMgmt_Service_User::validateUnitAccess($unit, $checkWriteAccess);
            } catch (Exception $e) {
                throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
            }

            try {
                $this->_helper->json(['data' => AccountMgmt_Service_Unit::upsertFromJson($unit, json_encode($decoded_json))]);
            } catch (Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }
        }

        /**
         * Endpoint: [POST] /api/units/
         *     Creates a new unit.
         */
        if ($this->_request->isPost()) {
            $decoded_json = json_decode($json_body);
            if (! $decoded_json) {
                throw new ApiException(ApiException::BAD_REQUEST, 'json decode failed');
            }
            try {
                AccountMgmt_Service_Unit::validateInsert($decoded_json);
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            $unit = AccountMgmt_Service_Unit::getNewUnitFromType($decoded_json->unit_type);
            $unit->setFacilityId($decoded_json->facility_id);

            try {
                $checkWriteAccess = true;
                AccountMgmt_Service_User::validateUnitAccess($unit, $checkWriteAccess);
            } catch (Exception $e) {
                throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
            }

            $unit->setFacilityId($decoded_json->facility_id);
            $this->_helper->json(['data' => AccountMgmt_Service_Unit::upsertFromJson($unit, $json_body)]);
        }

        if ($this->_request->isDelete()) {
            $reason = null;
            $decoded_json = json_decode($json_body, true);
            if ($decoded_json && array_key_exists('delete_reason', $decoded_json)) {
                $reason = $decoded_json['delete_reason'];
            }

            // Check valid unit
            try {
                $unit = AccountMgmt_Service_Unit::validateUnitId($unitId);
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            // Check Access
            try {
                $checkWriteAccess = true;
                AccountMgmt_Service_User::validateUnitAccess($unit, $checkWriteAccess);
            } catch (Exception $e) {
                throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
            }

            // Delete (Unpublish, etc) Unit
            try {
                AccountMgmt_Service_Unit::delete($unit, $reason);
            } catch (Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }

            self::sendOKEmptyResponse();
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function specialsAction()
    {
        $unitId = $this->_getParam('unit_id'); //required
        $specialId = $this->getParam('special_id'); //maybe
        $json_body = $this->getRequest()->getRawBody(); //maybe

        try {
            $unit = AccountMgmt_Service_Unit::validateUnitId($unitId);
        } catch (Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }

        // Check Access to Read / Write
        try {
            $checkWriteAccess = $this->_request->isGet() ? false : true;
            AccountMgmt_Service_User::validateUnitAccess($unit, $checkWriteAccess);
        } catch (Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
        }

        if ($this->_request->isGet()) {
            $this->_helper->json(['data' => AccountMgmt_Service_Unit::toSpecialsArray($unit)]);
        }

        if ($this->_request->isPost()) {
            try {
                AccountMgmt_Service_Unit::createSpecialFromJson($unit, $json_body);
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            $this->_helper->json(['data' => AccountMgmt_Service_Unit::toSpecialsArray($unit)]);
        }

        if ($this->_request->isDelete()) {
            try {
                $unit = AccountMgmt_Service_Unit::validateUnitId($unitId);
                $special = AccountMgmt_Service_Unit::validateSpecialId($specialId);
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            try {
                Genesis_Service_UnitSpecial::deleteByRootType($unit->getId(), $special->getType());
            } catch (Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }
            //reload
            $unit = Genesis_Service_StorageSpace::loadById($unitId);
            $this->_helper->json(['data' => AccountMgmt_Service_Unit::toSpecialsArray($unit)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }
}
