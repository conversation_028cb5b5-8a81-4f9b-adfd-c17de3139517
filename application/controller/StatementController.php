<?php
/*
 * Statement Controller
 *
 * @copyright 2013 SpareFoot Inc
 */

use AccountMgmt_Models_BidIncreaseBannerValidation;

class StatementController extends AccountMgmt_Controller_Restricted
{
    private $mirfUtil;
    protected function _init()
    {
        $this->mirfUtil = new \Genesis_Util_NewMirfCalculation();
        if (!count($this->getLoggedUser()->getManagableFacilities())) {
            $this->redirect($this->view->url(['action' => 'add-first'], 'features'));
        }
        if (!$this->getLoggedUser()->canUseBilling()) {
            $this->redirect('/');
        }

        $this->view->banner = [
            'showNotificationBanner' => AccountMgmt_Models_BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount())
        ];
    }

    public function indexAction()
    {
        // if a statement is open, forward to it
        $statements = Genesis_Service_Statement::loadByAccount($this->getLoggedUser()->getAccount());
        foreach ($statements as $statement) {
            if ($statement->getStatementBatch()->getStatus() === Genesis_Entity_StatementBatch::STATUS_OPEN) {
                $this->redirect('statement/view/id/' . $statement->getId());

                return;
            }
        }

        // otherwise, forward to list of statements
        $this->redirect('statement/list');
    }

    // For the given Account: retrieves and sorts this year's statements; chooses View to display statements
    private function _getMirfFacilities(iterable $facilities)
    {
        $mirfFacilities = [];
        foreach ($facilities as $facility) {
            if ($this->mirfUtil->isMIRFEligible($facility, $facility->getAccount())) {
                $mirfFacilities[] = $facility->getId();
            }
        }

        return $mirfFacilities;
    }

    public function getMirfAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if (!$this->getParam('facility_id')) {
            throw new Exception("a facility ID is required");
        }

        echo json_encode($this->mirfUtil->getMoveInRateFloors(
            null,
            [$this->getParam('facility_id')],
            true
        ), JSON_PRETTY_PRINT);
    }

    public function listAction()
    {
        // Do not render default action phtml
        $this->_helper->viewRenderer->setNoRender(true);
        $userAccess = $this->getLoggedUser();

        $view = new Zend_View();
        $view->facilities = $userAccess->getManagableFacilities(Genesis_Db_Order::ASC('title'));
        $view->selectedFacilityId = $this->getSession()->facilityId;
        $view->loggedUser = $userAccess;
        $view->reconcileStatementAction = Genesis_Service_feature::isActive(
            AccountMgmt_Models_Features::NEW_STATEMENTS_PAGE,
            ['account_id' => $userAccess->getAccount()->getAccountId()]
        ) ? 'dispute' : 'view';
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/statement/');

        if ($userAccess->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD) {
            $view->userId = null;
        } else {
            $view->userId = $userAccess->getId();
        }

        # Fetches the first open statement batch
        $openStatementBatch = Genesis_Service_StatementBatch::loadByStatus();
        $view->openStatements = array();


        if (!empty($openStatementBatch) && $openStatementBatch) {
            # it does fetch only one record
            $statement = Genesis_Service_Statement::loadByAccountIdAndStatementBatchId(
                $userAccess->getAccount()->getAccountId(),
                $openStatementBatch->getId()
            );

            if ($statement) {
                $clientStatement = Genesis_Entity_Statement_Client::buildClientStatement($userAccess, $statement);
                $view->openStatements[] = $clientStatement;
            }
        }

        $this->view->title = 'Statements';
        $this->view->scripts = [
            '../new-ui/js/statement/statement',
            '../new-ui/js/statement/billingHistory'
        ];

        // Go to the view for the account type
        switch ($this->getLoggedUser()->getAccount()->getBidType()) {
            case Genesis_Entity_Account::BID_TYPE_RESIDUAL:
                echo $view->render('list-residual.phtml');
                break;
            case Genesis_Entity_Account::BID_TYPE_TIERED:
            default:
                # MIRF flags are used only here
                if (Genesis_Service_Feature::isActive('billing.mirf_enable')) {
                    $view->mirfElegibleFacilityIds = $this->_getMirfFacilities($view->facilities);
                    $view->isMIRFElegible = count($view->mirfElegibleFacilityIds) > 0;

                    if ($view->isMIRFElegible) {
                        $mirfData = $this->mirfUtil->getMoveInRateFloors(null, $view->mirfElegibleFacilityIds, true);

                        if (empty($mirfData['facilities'])) {
                            //No facilities were returned which means no bookings were made at mirf eligible facilities
                            // Disable isMIRFElegible to prevent statements summary page from showing MIRF total of $0
                            // MIRF total of $0 will still show for facilities with bookings > 50% MIR
                            $view->isMIRFElegible = false;
                        }

                        $view->totalMIRF = "$" . number_format(
                            $mirfData['total_mirf'],
                            2
                        );
                    } else {
                        $view->totalMIRF = "$0.00";
                    }
                    $view->mirfPercentage = $this->mirfUtil->getAppliedMirf() * 100;
                } else {
                    $view->mirfElegibleFacilityIds = [];
                    $view->isMIRFElegible = false;
                }

                $view->account = $userAccess->getAccount();
                echo $view->render('list-cpa.phtml');
                break;
        }
    }

    private function _getStatement()
    {
        $statement = Genesis_Service_Statement::loadById($this->getParam('id'));

        if (!$statement) {
            $account = $this->getLoggedUser()->getAccount();
            $statement = Genesis_Service_Statement::loadUnbilledByAccount($account);
        }

        if (!$statement) {
            $this->redirect($this->view->url(['action' => 'list'], 'statement'));
            throw new Exception("You must provide a valid statement id to view this page.");
        }

        // if attempting to view a statement for a diff account as a god, switch user to that account
        if ($statement->getAccountId() == $this->getLoggedUser()->getAccountId()) {
            return $statement;
        }

        if (!$this->getLoggedUser()->isMyFootGod()) {
            throw new Exception('Access denied for statements on other accounts');
        }
        $url = $this->view->url(
            [
                'action' => $this->getRequest()->getActionName(),
                'id' => $this->getParam('id')
            ],
            $this->getRequest()->getControllerName()
        ) . '?account_id=' . $statement->getAccountId();

        $this->redirect($url);
    }

    public function disputeAction()
    {
        $start = microtime(true);
        // don't try to render default action phtml
        $this->_helper->viewRenderer->setNoRender(true);

        $statement = $this->_getStatement();

        $facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Order::ASC('title'), true);
        $facilities_with_bookings = [];
        $view = new Zend_View();

        $view->statementId = $statement->getId();
        $statementBatch = $statement->getStatementBatch();
        $view->statementStartDate = $statementBatch->getStartDate();
        $view->statementEndDate = $statementBatch->getEndDate();

        $account = $statement->getAccount();
        $view->account = $account;
        $view->sisterFacilityList = $statement->getAccount()->getFacilities()->toArray();

        $view->isLtv = ($statement->getStatementType() === Genesis_Entity_BillableInstance::ITEM_BOOKING_RESIDUAL) ? true : false;
        $view->isCpa = ($statement->getStatementType() === Genesis_Entity_BillableInstance::ITEM_BOOKING_CPA) ? true : false;

        // Hybrid Accounts
        if (($account->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT) &&
            $account->getSupportExistingLTVReservations()
        ) {
            $view->isHybrid = true;
            $view->isLtv = false;
            $view->isCpa = false;
        } else {
            $view->isHybrid = false;
        }

        // Getting all the bookings belonging to the statement with the facilities the user has access to
        $facilityBookings = Genesis_Service_Transaction::load(
            Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::equal('statementId', $statement->getId()),
                Genesis_Db_Restriction::in(
                    'facilityId',
                    array_map(function ($item) {
                        return $item->getId();
                    }, $facilities->toArray())
                ),
                Genesis_Db_Restriction::not(
                    Genesis_Db_Restriction::equal('bookingState', 'INVALID')
                )
            )
        );

        $facilityIdsFromBookings = array_map(
            function (Genesis_Entity_Transaction $item) {
                return $item->getFacilityId();
            },
            $facilityBookings->toArray()
        );
        $facilityIdsFromBookings = array_unique($facilityIdsFromBookings);

        // This will be used to populate the `regularBookings` array
        $allManual = true;

        $view->facilityId = $facilityId = $this->getParam('facility', null);
        $facility = false;
        foreach ($facilities as $_facility) {
            if ($facilityId === $_facility->getId()) {
                $facility = $_facility;
            }

            if (in_array($_facility->getId(), $facilityIdsFromBookings)) {
                $facilities_with_bookings[] = $_facility;

                if ($_facility->getSourceId() != Genesis_Entity_Source::ID_MANUAL && $allManual) {
                    $allManual = false;
                }
            }
        }


        if ($facilityId) {
            // If it was not found in the list of facilities with bookings
            if (!$facility) {
                $facility = Genesis_Service_Facility::loadById($facilityId);
            }
        }

        $view->facility = $facility;

        $view->facilities = $facilities_with_bookings;
        $view->facilityCount = count($view->facilities);
        $view->loggedUser = $userAccess = $this->getLoggedUser();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/statement/');
        if (Genesis_Service_Feature::isActive('billing.mirf_enable') === true) {
            $view->mirfElegibleFacilityIds = $this->_getMirfFacilities($view->facilities);
            $view->isMIRFElegible = count($view->mirfElegibleFacilityIds) > 0;
            if ($view->isMIRFElegible) {
                $view->MIRFData = $this->mirfUtil->getMoveInRateFloors(
                    null,
                    $view->mirfElegibleFacilityIds,
                    true
                );
                $view->mirfPercentage = $this->mirfUtil->getAppliedMirf() * 100;
            } else {
                $view->isMIRFElegible = false;
            }
        } else {
            $view->mirfElegibleFacilityIds = [];
            $view->isMIRFElegible = false;
        }


        // load array of source_id's on this account
        $sql = "SELECT DISTINCT source_id FROM account_software WHERE account_id = :account_id ;";
        $params = array("account_id" => $statement->getAccountId());
        $results = Genesis_Db_Connection::getInstance()->findAll($sql, $params);
        $this->view->arr_softwares = array();

        if ($results) {
            foreach ($results as $result) {
                $this->view->arr_softwares[$result['source_id']] = $result['source_id'];
            }
        }

        $confirmedStatementData = $this->_getConfirmedStatementData($statement->getId(), $userAccess);
        $view->confirmations = $confirmedStatementData->confirmations;
        $view->confirmationTime = $confirmedStatementData->confirmationTime;
        $view->allowChanges = $statementBatch->getStatus() == Genesis_Entity_StatementBatch::STATUS_OPEN ? $statement->getStatementBatch()->getReconciliationEndDate() : false;

        $this->view->scripts = ['statement/statements'];

        $bookingExtraMapping = $this->_getExtraInfoBookingsMapping([], $facilityBookings->toArray());

        if ($view->isCpa || $view->isHybrid) {
            $noAutoStateBookings = array_filter(
                $facilityBookings->toArray(),
                function (Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return (
                            $booking->getFacilityId() === $facilityId
                            && ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                            && !($booking->getFree() == 1)
                            && $booking->getAutoState() == null
                            && !$booking->getReviewRuling()
                        );
                    }

                    return (
                        ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                        && !($booking->getFree() == 1)
                        && $booking->getAutoState() == null
                        && !$booking->getReviewRuling()
                    );
                }
            );

            $autoDisputedBookings = array_filter(
                $facilityBookings->toArray(),
                function (Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return (
                            $booking->getFacilityId() === $facilityId
                            && ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                            && $booking->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED
                            && !($booking->getFree() == 1)
                        );
                    }

                    return (
                        ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                        && $booking->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED
                        && !($booking->getFree() == 1)
                    );
                }
            );

            if ($allManual) {
                $view->regularBookings = array_merge($noAutoStateBookings, $autoDisputedBookings);
                $view->autoDisputedBookings = [];
            } else {
                $view->regularBookings = $noAutoStateBookings;
                $view->autoDisputedBookings = $autoDisputedBookings;
            }

            $view->regularBookings = StatementDataUtil::sortItems($view->regularBookings);
            $view->autoDisputedBookings = StatementDataUtil::sortItems($view->autoDisputedBookings);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $view->regularBookings
            );
            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $view->autoDisputedBookings
            );



            $autoConfirmedBookings = array_filter(
                $facilityBookings->toArray(),
                function (Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return (
                            $booking->getFacilityId() === $facilityId
                            && ($booking->getAutoState()  === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                            && !($booking->getFree() == 1)
                            && !$booking->getReviewRuling()
                            && ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                        );
                    }

                    return (
                        ($booking->getAutoState()  === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                        && !($booking->getFree() == 1)
                        && !$booking->getReviewRuling()
                        && ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                    );
                }
            );

            $view->autoConfirmedBookings = StatementDataUtil::sortItems($autoConfirmedBookings);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $view->autoConfirmedBookings
            );

            // The reviewed bookings contain bookings from the current AND PREVIOUS statements
            $reviewedBookingsFromCurrentStatement = array_filter(
                $facilityBookings->toArray(),
                function (Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return (
                            $facilityId === $booking->getFacilityId()
                            && ($booking->getAutoState()  === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                            && ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                            && !($booking->getFree() == 1)
                            && $booking->getReviewRuling()
                        );
                    }

                    return (
                        ($booking->getAutoState()  === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                        && ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                        && !($booking->getFree() == 1)
                        && $booking->getReviewRuling()
                    );
                }
            );

            $previousStatementBatch = Genesis_Service_StatementBatch::loadPreviousStatementBatch();
            $previousStatement = Genesis_Service_Statement::loadByAccountIdAndStatementBatchId(
                $account->getId(),
                $previousStatementBatch->getId()
            );

            if ($previousStatement) {
                $previousStatementBookings = Genesis_Service_Transaction::load(
                    Genesis_Db_Restriction::and_(
                        Genesis_Db_Restriction::equal('statementId', $previousStatement->getId()),
                        Genesis_Db_Restriction::in(
                            'facilityId',
                            array_map(function ($item) {
                                return $item->getId();
                            }, $facilities->toArray())
                        ),
                        Genesis_Db_Restriction::not(
                            Genesis_Db_Restriction::equal('bookingState', 'INVALID')
                        ),
                        Genesis_Db_Restriction::equal(
                            'bidType',
                            Genesis_Entity_Account::BID_TYPE_PERCENT
                        ),
                        Genesis_Db_Restriction::equal(
                            'bookingState',
                            Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED
                        ),
                        Genesis_Db_Restriction::equal(
                            'autoState',
                            Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                        ),
                        Genesis_Db_Restriction::notEqual(
                            'free',
                            '1'
                        )
                    )
                );

                $reviewedBookingsFromPreviousStatement = array_filter(
                    $previousStatementBookings->toArray(),
                    function (Genesis_Entity_Transaction $booking) use ($facilityId) {
                        if ($facilityId) {
                            return (
                                $facilityId === $booking->getFacilityId()
                                && $booking->getReviewRuling()
                            );
                        }

                        return ($booking->getReviewRuling());
                    }
                );

                $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                    $bookingExtraMapping,
                    $previousStatementBookings->toArray()
                );
            } else {
                $reviewedBookingsFromPreviousStatement = [];
            }

            $view->reviewedBookings = array_merge(
                $reviewedBookingsFromCurrentStatement,
                $reviewedBookingsFromPreviousStatement
            );

            $view->reviewedBookings = StatementDataUtil::sortItems($view->reviewedBookings);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $view->reviewedBookings
            );

            $statementBatchStartDate = $statement->getStatementBatch()->getStartDate();
            $dtFirstDay = date('Y-m-d', strtotime('-10 day' . $statementBatchStartDate));
            $dtLastDay = date('Y-m-d', strtotime('-1 day' . $statementBatchStartDate));
            $lateBookings = Genesis_Service_Transaction::load(
                Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::in(
                        'facilityId',
                        array_map(function ($item) {
                            return $item->getId();
                        }, $facilities->toArray())
                    ),
                    Genesis_Db_Restriction::between("moveIn", $dtFirstDay, $dtLastDay),
                    Genesis_Db_Restriction::in("bookingState", [
                        Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED,
                        Genesis_Entity_Transaction::BOOKING_STATE_CANCELLED
                    ]),
                    Genesis_Db_Restriction::isNull("autoState"),
                    Genesis_Db_Restriction::equal(
                        'bidType',
                        Genesis_Entity_Account::BID_TYPE_PERCENT
                    ),
                    Genesis_Db_Restriction::in("bookingType", array(
                        Genesis_Entity_Transaction::BOOKING_TYPE_CPA,
                        Genesis_Entity_Transaction::BOOKING_TYPE_CPA_FLAT,
                        Genesis_Entity_Transaction::BOOKING_TYPE_CPA_TIERED,
                        Genesis_Entity_Transaction::BOOKING_TYPE_STANDARD,
                        Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE
                    )),
                    Genesis_Db_Restriction::notEqual("free", 1)
                )
            );

            $view->lateBookings = StatementDataUtil::sortItems($lateBookings->toArray());

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $lateBookings->toArray(),
                false,
                true
            );

            $freeBookings = array_filter(
                $facilityBookings->toArray(),
                function (Genesis_Entity_Transaction $booking) {
                    return (
                        $booking->getFree() == 1
                        && ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT)
                    );
                }
            );

            $view->freeBookings = StatementDataUtil::sortItems($freeBookings);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $freeBookings,
                true,
                false
            );

            $view->bookingExtraInfo = $bookingExtraMapping;

            $this->view->scripts[] = '../new-ui/js/statement/view-cpa';
            echo $view->render('new-dispute.phtml');
        }

        if ($view->isLtv || $view->isHybrid) {
            $view->ltvBookings = array_filter(
                $facilityBookings->toArray(),
                function (Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return (
                            $facilityId === $booking->getFacilityId()
                            && ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_RESIDUAL)
                            && !($booking->getFree() == 1)
                            && !$booking->getReviewRuling()
                        );
                    } else {
                        return (
                            ($booking->getBidType() === Genesis_Entity_Account::BID_TYPE_RESIDUAL)
                            && !($booking->getFree() == 1)
                            && !$booking->getReviewRuling()
                        );
                    }
                }
            );

            $view->ltvBookings = StatementDataUtil::sortItems($view->ltvBookings);

            $facilityIds = array_map(function ($item) {
                return $item->getId();
            }, $facilities->toArray());

            $existingLtvItems = array_filter(
                $facilityBookings->toArray(),
                function (Genesis_Entity_Transaction $item) use ($facilityId) {
                    if ($facilityId) {
                        return (
                            $facilityId === $item->getFacilityId()
                            && ($item->getBidType() === Genesis_Entity_Account::BID_TYPE_RESIDUAL)
                            && $item->getBookingState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                        );
                    } else {
                        return (
                            ($item->getBidType() === Genesis_Entity_Account::BID_TYPE_RESIDUAL) &&
                            $item->getBookingState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                        );
                    }
                }
            );

            // NOTE: On LTV bookings we charge monthly our clients for every booking that does not move-out
            $additionalLtvItems = $this->_getAdditionalLtvItems(
                ($facilityId) ? [$facilityId] : $facilityIds,
                $view->statementId,
                $existingLtvItems
            );

            $view->existingLtvItems = array_merge($existingLtvItems, $additionalLtvItems);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $view->existingLtvItems
            );

            $view->numExistingLtvItems = count($view->existingLtvItems);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $view->ltvBookings
            );

            $statementEndDate = $statement->getStatementBatch()->getEndDate();
            $statementStartDate = $statement->getStatementBatch()->getStartDate();

            $view->newLateLtvItems = $this->_getNewLateLtvItems(
                ($facilityId) ? [$facilityId] : $facilityIds,
                $statement,
                $statementStartDate,
                $statementEndDate
            );

            $view->newLateLtvItems = StatementDataUtil::sortItems($view->newLateLtvItems);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $view->newLateLtvItems
            );

            $view->nonAutoconfirmedLtvItems = array_filter($view->ltvBookings, function ($item) {
                return (
                    ($item->getBookingState() !== Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                    && ($item->getAutoState() !== Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                    && ($item->getAutoState() !== Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED)
                );
            });

            $view->nonAutoconfirmedLtvItems = StatementDataUtil::sortItems($view->nonAutoconfirmedLtvItems);

            $view->autoConfirmedLtvItems = array_filter(
                $view->ltvBookings,
                function (Genesis_Entity_Transaction $item) {
                    return (
                        $item->getBookingState() !== Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED // Validation coming from `buildItemFromInstance`
                        && $item->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                    );
                }
            );

            $view->autoDisputedLtvItems = array_filter(
                $view->ltvBookings,
                function (Genesis_Entity_Transaction $item) {
                    return (
                        $item->getBookingState() !== Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED // Validation coming from `buildItemFromInstance`
                        && $item->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED
                    );
                }
            );

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $view->nonAutoconfirmedLtvItems
            );

            $view->bookingExtraInfo = $bookingExtraMapping;

            $this->view->scripts[] = 'statement/view-residual';
            $this->view->scripts[] = 'statement/view-residual-handlers';
            echo $view->render('new-view-residual.phtml');
        }
    }

    private function _getExtraInfoBookingsMapping($bookingsMapping, array $bookings, bool $isFree = false, bool $isLate = false)
    {
        $bookingConfCodes = array_reduce(
            $bookings,
            function ($carry, Genesis_Entity_Transaction $item) use ($bookingsMapping) {
                $confCode = $item->getConfirmationCode();
                // Skip from the query any preexistent record
                // This will reduce the time to process the query
                if (!isset($bookingsMapping[$confCode])) {
                    $carry[] = $confCode;
                }

                return $carry;
            }
        );


        if (count($bookingConfCodes)) {
            $dupRecords = Genesis_Service_ReservationDuplicate::load(
                Genesis_Db_Restriction::in(
                    'confirmationCode',
                    $bookingConfCodes
                )
            )->toArray();
        } else {
            $dupRecords = [];
        }

        $dupRecordsByConfCode = [];

        foreach ($dupRecords as $dupRecord) {
            // store it as an array because a single booking can have multiple duplicate records
            $dupRecordsByConfCode[$dupRecord->getConfirmationCode()][] = Genesis_Service_Transaction::loadById(
                $dupRecord->getDupConfirmationCode()
            );
        }

        /** @var Genesis_Entity_Transaction $singleBooking */
        foreach ($bookings as $singleBooking) {
            $bookingConfCode = $singleBooking->getConfirmationCode();

            // if we already processed a confirmation code, skip it
            if (isset($bookingsMapping[$bookingConfCode])) {
                continue;
            }
            $hasDuplicates = false;

            $bookingsMapping[$bookingConfCode] = new stdClass();
            $bookingsMapping[$bookingConfCode]->status = $this->_getBookingStatus($singleBooking, $isFree, $isLate);
            $bookingsMapping[$bookingConfCode]->tenantInfo = \StatementDataUtil::buildTenantInfo($singleBooking);
            $bookingsMapping[$bookingConfCode]->customerInfo = \StatementDataUtil::buildCustomerInfo($singleBooking);

            if (isset($dupRecordsByConfCode[$bookingConfCode])) {
                $facilityId = $singleBooking->getFacility()->getId();

                foreach ($dupRecordsByConfCode[$bookingConfCode] as $reservationDuplicate) {
                    if (!empty($reservationDuplicate) && $reservationDuplicate->getFacility()->getId() == $facilityId) {
                        $hasDuplicates = true;
                        break;
                    }
                }
            }

            $bookingsMapping[$bookingConfCode]->hasDuplicates = $hasDuplicates;
        }

        return $bookingsMapping;
    }

    private function _getBookingStatus(Genesis_Entity_Transaction $booking, bool $isFree = false, bool $isLate = false)
    {
        if ($isFree) {
            return Genesis_Entity_Statement_Item_Booking::STATUS_NO_FEE;
        }

        if ($booking->getReviewStatus()) {
            if ($booking->getReviewStatus() === Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) {
                return Genesis_Entity_Statement_Item_Booking::STATUS_UNDER_REVIEW;
            } elseif ($booking->getReviewStatus() === Genesis_Entity_Transaction::STATUS_REVIEWED) {

                // if booking has been reviewed, then return moved-in or not status as (Reviewed)
                if ($booking->getLatestBillableInstance()->getReason() !== Genesis_Entity_BillableInstance::REASON_CANCELED) {
                    return Genesis_Entity_Statement_Item_Booking::STATUS_MOVED_IN_REVIEWED;
                } else {
                    return Genesis_Entity_Statement_Item_Booking::STATUS_NEVER_MOVED_IN_REVIEWED;
                }
            }
        }

        if ($isLate) {
            return Genesis_Entity_Statement_Item_Booking::STATUS_NEVER_MOVED_IN;
        } elseif (!empty($booking->getLatestBillableInstance()) && $booking->getLatestBillableInstance()->getReason() !== Genesis_Entity_BillableInstance::REASON_CANCELED) {
            return Genesis_Entity_Statement_Item_Booking::STATUS_MOVED_IN;
        } else {
            return Genesis_Entity_Statement_Item_Booking::STATUS_NEVER_MOVED_IN;
        }
    }

    /**
     * copy billing controller, view and js and rename to statement controller, update that in parallel with
     */
    public function viewAction()
    {
        if (Genesis_Service_feature::isActive(AccountMgmt_Models_Features::NEW_STATEMENTS_PAGE, ['account_id' => $this->getLoggedUser()->getAccountId()])) {
            $params = [
                'action' => 'dispute'
            ];
            $idParam = $this->getParam('id');

            if ($idParam) {
                $params['id'] = $idParam;
            }

            $this->redirect($this->view->url($params, 'statement'));
        }

        // dont try to render default action phtml
        $this->_helper->viewRenderer->setNoRender(true);

        // TODO: if BI < 35 forward to old billing controller

        $statement = $this->_getStatement();

        $facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Order::ASC('title'), true);
        $facilities_with_bookings = [];
        foreach ($facilities as $facility) {
            $bookings = Genesis_Service_BillableInstance::loadBookingsByFacilityStatementId($facility->getId(), $statement->getId());
            if (count($bookings) > 0) {
                $facilities_with_bookings[] = $facility;
            }
        }
        $view = new Zend_View();
        $view->facilities = $facilities_with_bookings;
        $view->facilityCount = count($view->facilities);
        $view->selectedFacilityId = $this->getSession()->facilityId;
        $view->loggedUser = $userAccess = $this->getLoggedUser();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/statement/');
        if (Genesis_Service_Feature::isActive('billing.mirf_enable')) {
            $view->mirfElegibleFacilityIds = $this->_getMirfFacilities($view->facilities);
            $view->isMIRFElegible = count($view->mirfElegibleFacilityIds) > 0;
            if ($view->isMIRFElegible) {
                $view->MIRFData = $this->mirfUtil->getMoveInRateFloors(
                    null,
                    $view->mirfElegibleFacilityIds,
                    true
                );
                $view->mirfPercentage = $this->mirfUtil->getAppliedMirf() * 100;
            } else {
                $view->isMIRFElegible = false;
            }
        } else {
            $view->mirfElegibleFacilityIds = [];
            $view->isMIRFElegible = false;
        }

        // Set up an interstitial to display once per session.
        if (!isset($this->getSession()->showInterstitial)) {
            $this->getSession()->showInterstitial = true;
        } elseif ($this->getSession()->showInterstitial) {
            $this->getSession()->showInterstitial = false;
        }
        $view->showInterstitial = $this->getSession()->showInterstitial;
        if ($view->showInterstitial) {
            $restriction = Genesis_Db_Restriction::empty_();
            $restriction->setLimit(Genesis_Db_Limit::limit(5));
            $view->interstitialFacilities = $this->getLoggedUser()->getManagableFacilities($restriction);
        } else {
            $view->interstitialFacilities = array();
        }

        $view->facilityId = $facilityId = $this->getParam('facility', null);
        $facility = false;
        if ($facilityId) {
            $facility = Genesis_Service_Facility::loadById($facilityId);
        }

        // load array of source_id's on this account
        $sql = "SELECT source_id FROM account_software WHERE account_id = :account_id ;";
        $params = array("account_id" => $statement->getAccountId());
        $results = Genesis_Db_Connection::getInstance()->findAll($sql, $params);
        $this->view->arr_softwares = array();
        if ($results) {
            foreach ($results as $result) {
                $this->view->arr_softwares[$result['source_id']] = $result['source_id'];
            }
        }

        $view->clientStatement = Genesis_Entity_Statement_Client::buildClientStatement($userAccess, $statement);
        if ($facility) {
            $view->clientStatement->filterByFacility($facility);
        }
        //for the done/confirm form
        $view->confirmedTime = $view->clientStatement->getConfirmedTime();
        $view->confirmations = $view->clientStatement->getStatementConfirmations();
        $view->allowChanges = $statement->getStatementBatch()->getStatus() == Genesis_Entity_StatementBatch::STATUS_OPEN ? $statement->getStatementBatch()->getReconciliationEndDate() : false;
        $this->view->scripts = ['statement/statements'];
        if ($view->clientStatement->isCpaWithLtv()) {
            $this->view->scripts[] = 'statement/view-residual';
            $this->view->scripts[] = 'statement/view-residual-handlers';
            $this->view->scripts[] = '../new-ui/js/statement/view-cpa';
            echo $view->render('view-cpa.phtml');
            echo $view->render('view-residual.phtml');
        } else if ($view->clientStatement->isLtv()) {
            $this->view->scripts[] = 'statement/view-residual';
            $this->view->scripts[] = 'statement/view-residual-handlers';
            echo $view->render('view-residual.phtml');
        } else {
            $this->view->scripts[] = '../new-ui/js/statement/view-cpa';
            echo $view->render('view-cpa.phtml');
        }
    }

    private function _getAdditionalLtvItems(array $facilityIds, int $statementId, array $skippedBookings)
    {
        $facilityIdsString = implode(",", $facilityIds);
        $skippedConfirmationCodesString = array_reduce($skippedBookings, function ($carry, $item) {
            return ($carry) ? "$carry, '" . $item->getConfirmationCode() . "'" : "'" . $item->getConfirmationCode() . "'";
        });

        $sql = <<<SQL
        SELECT
            bi.confirmation_code
        FROM
            billable_instances bi
            INNER JOIN listing_rent_submission b on b.confirmation_code = bi.confirmation_code
        WHERE
            bi.statement_id = :statement_id
            AND b.listing_avail_id IN($facilityIdsString)
            AND b.booking_state = :booking_state
            AND b.bid_type = :bid_type
SQL;

        if ($skippedConfirmationCodesString) {
            $sql .= "\nAND b.confirmation_code NOT IN($skippedConfirmationCodesString)";
        }

        $params = [
            'statement_id' => $statementId,
            'booking_state' => Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED,
            'bid_type' => Genesis_Entity_Account::BID_TYPE_RESIDUAL
        ];
        $results = Genesis_Db_Connection::getInstance()->findAll($sql, $params, PDO::FETCH_COLUMN);

        $additionalLtvBookings = Genesis_Service_Transaction::load(
            Genesis_Db_Restriction::in(
                'confirmationCode',
                $results
            )
        )->toArray();

        return $additionalLtvBookings;
    }

    function _getNewLateLtvItems($facilityIds, $statement, $statementOpenDate, $statementCloseDate)
    {

        if (!$statement->getStatementBatch()->getStatus() == Genesis_Entity_StatementBatch::STATUS_OPEN) {
            return [];
        }

        $newLateLtvItems = array();
        $lateStartDate = date('Y-m-d', strtotime('-10 day' . $statementOpenDate));
        $lateEndDate = date('Y-m-d', strtotime('-1 day' . $statementCloseDate));

        //get late move-ins
        $lateRestriction = Genesis_Db_Restriction::and_(
            Genesis_Db_Restriction::in('facilityId', $facilityIds),
            Genesis_Db_Restriction::between('moveIn', $lateStartDate, $lateEndDate),
            Genesis_Db_Restriction::equal('bookingState', Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED),
            Genesis_Db_Restriction::equal('bookingType', Genesis_Entity_Transaction::BOOKING_TYPE_RESIDUAL),
            Genesis_Db_Restriction::not(Genesis_Db_Restriction::equal('free', 1))
        );
        $lateRestriction->setOrder(Genesis_Db_Order::asc('moveIn'));
        $lates = Genesis_Service_Transaction::load($lateRestriction);

        foreach ($lates as $l) {
            $newLateLtvItems[] = $l;
        }

        return $newLateLtvItems;
    }

    private function _getConfirmedStatementData($statementId, $userAccess)
    {
        $returnObj = new stdClass();
        $confirmationTime = false;

        // confirmationTime
        if (Genesis_Service_StatementConfirmation::isStatementConfirmed($statementId, $userAccess->getId())) {
            $entity = Genesis_Service_StatementConfirmation::loadByStatementAndUserId($statementId, $userAccess->getId());
            $confirmationTime = $entity->getConfirmationTime();
        }

        $confirmations = Genesis_Service_StatementConfirmation::loadByStatementId($statementId);

        $returnObj->confirmationTime = $confirmationTime;
        $returnObj->confirmations = $confirmations;

        return $returnObj;
    }

    public function viewcsvAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $clientStatement = Genesis_Entity_Statement_Client::buildClientStatement($this->getLoggedUser(), $this->_getStatement());

        // set this to false for debugging. sends output to screen instead of file
        $outputCsv = true;

        $filename = $clientStatement->exportFileName('csv');
        if ($outputCsv) {
            header("Content-type: text/csv");
            header("Cache-Control: no-store, no-cache");
            header('Content-Disposition: attachment; filename="' . $filename . '"');
        } else {
            echo "CSV output:<br/>";
        }

        $logger = new Genesis_Util_ActionLogger();
        $logger->logAction('viewed_statement_csv', "", "", ($this->getLoggedUser() ? $this->getLoggedUser()->getId() : null), "", $this->_getStatement()->getId());

        $outfile = fopen("php://output", 'w');
        $clientStatement->getCsv($outfile);
    }

    public function viewpdfAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $clientStatement = Genesis_Entity_Statement_Client::buildClientStatement($this->getLoggedUser(), $this->_getStatement());

        // set this to false for debugging. sends output to screen instead of file
        $outputPdf = true;

        $filename = $clientStatement->exportFileName('pdf');
        if ($outputPdf) {
            header("Content-type: application/pdf");
            header("Cache-Control: no-store, no-cache");
            header('Content-Disposition: attachment; filename="' . $filename . '"');
        } else {
            echo "PDF output:<br/>";
        }

        //log a pdf view
        $logger = new Genesis_Util_ActionLogger();
        $logger->logAction('viewed_statement_pdf', "", "", ($this->getLoggedUser() ? $this->getLoggedUser()->getId() : null), "", $this->_getStatement()->getId());

        $outfile = fopen("php://output", "w");
        $clientStatement->getPdf($outfile);
    }

    public function changecustomernameAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            if (!$this->getParam('confirmation_code')) {
                $this->redirect('/statement');
            }

            $code = $this->getParam('confirmation_code');
            $trans = Genesis_Service_Transaction::load(Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();
            if (!$trans) {
                throw new Exception("Unable to load booking for confirmation code: " . $code);
            }

            if (!$this->getParam('first_name')) {
                throw new Exception('Please enter a first name.');
            }
            if (!$this->getParam('last_name')) {
                throw new Exception('Please enter a last name.');
            }

            $trans->setFirstName($this->getParam('first_name'));
            $trans->setLastName($this->getParam('last_name'));

            Genesis_Service_Transaction::updateName($trans, $this->getLoggedUser());

            //update billable instance reason
            $bi = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());
            $bi->setReason(Genesis_Entity_BillableInstance::REASON_NAME_CHANGE);
            Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());

            $newItem = Genesis_Entity_Statement_Item_Client::buildItemFromInstance($bi);
            echo $newItem->stringCustomerInfo();
        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }

    public function getbookingAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {

            $confirmationCode = $this->getParam('confirmation_code');
            $statementId = $this->getParam('statement_id');
            $billableInstance = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($confirmationCode, $statementId);
            $bookingItem = Genesis_Entity_Statement_Item_Client::buildItemFromInstance($billableInstance);

            $data = array(
                'sparefoot_fee' => $bookingItem->stringSparefootFee(),
                'status' => $bookingItem->stringStatus(),
            );

            $response = array(
                'success' => 1,
                'msg' => $data,
            );
            echo json_encode($response);
        } catch (Exception $e) {
            $msg = 'Error: ' . $e->getMessage();

            $response = array(
                'success' => 1,
                'msg' => $msg,
            );
            echo json_encode($response);
        }
    }

    public function receiptsAction()
    {
        $this->view->scripts = array('statement/receipts');
        $userAccess = $this->getLoggedUser();

        // Restrict access
        if (!($userAccess->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN ||
            $userAccess->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD)) {
            $this->redirect($this->view->url(['action' => 'list'], 'statement'));
        }

        $this->view->loggedUser = $userAccess;
    }

    public function getAsyncBillingHistoryAction()
    {
        $userAccess = $this->getLoggedUser();
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $dateFrom = $this->getParam('dateFrom');
        $dateTo = $this->getParam('dateTo');
        $params = ["account_id" => $userAccess->getAccountId()];


        if (!empty($dateFrom) && !empty($dateTo)) {
            $dateFrom =  str_replace('/', '/01/', $dateFrom);
            $dateTo = str_replace('/', '/28/', $dateTo);
            $params['start_date'] = (new DateTime($dateFrom))->format('Y-m-d');
            $params['end_date'] = (new DateTime($dateTo))->format('Y-m-d');
        } else {
            $params['start_date'] =  date('Y-m-01', strtotime('-3 months')); # First day three months ago
            $params['end_date'] = date('Y-m-t'); # Last day of this month
        }

        $sql = <<<SQL
        SELECT DISTINCT
            s.statement_id,
            sb.statement_batch_id
        FROM
            statements AS s
            INNER JOIN statement_batches AS sb ON sb.statement_batch_id = s.statement_batch_id
        WHERE
            sb.start_date BETWEEN :start_date AND :end_date
            AND s.account_id = :account_id
            AND sb.statement_batch_id > 35
SQL;

        $results = Genesis_Db_Connection::getInstance()->findAll($sql, $params);
        $data = [];

        if (!empty($results)) {
            $statementIds = array_reduce($results, function ($accumulator, $item) {
                $accumulator[] = $item['statement_id'];
                return $accumulator;
            });
            $statementBatchIds = array_reduce($results, function ($accumulator, $item) {
                $accumulator[] = $item['statement_batch_id'];
                return $accumulator;
            });

            $statements = Genesis_Service_Statement::load(
                Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::in('id', $statementIds),
                    Genesis_Db_Restriction::in('statementBatchId', $statementBatchIds)
                )
            );

            foreach ($statements as $statement) {
                switch ($this->getLoggedUser()->getAccount()->getBidType()) {
                    case Genesis_Entity_Account::BID_TYPE_RESIDUAL:
                        if ($statement->getStatementType() == Genesis_Entity_BillableInstance::ITEM_BOOKING_RESIDUAL) {
                            $clientStatement = Genesis_Entity_Statement_Client::buildClientStatement($userAccess, $statement);
                            $data[] = [
                                'dateRange' => date('F j', strtotime($clientStatement->getStatementStartDate())) . "-" . date('d, Y', strtotime($clientStatement->getStatementEndDate())),
                                'tenantFees' => $clientStatement->stringTotalBookingCharge(),
                                'tenants' => $clientStatement->getNumLtvItemsGettingBill(),
                                'urls' => [
                                    'PDF' => $this->view->url(['action' => 'viewPdf', 'id' => $clientStatement->getStatementId()], 'statement'),
                                    'CSV' => $this->view->url(['action' => 'viewCsv', 'id' => $clientStatement->getStatementId()], 'statement')
                                ]
                            ];
                        }
                        break;
                    case Genesis_Entity_Account::BID_TYPE_TIERED: # AKA CPA
                    default:

                        $clientStatement = Genesis_Entity_Statement_Client::buildClientStatement($userAccess, $statement);

                        if ($userAccess->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD) {
                            $userId = null;
                        } else {
                            $userId = $userAccess->getId();
                        }
                        try {
                            $movedIn = $clientStatement->getNumMovedInCpaItems();
                            $didNotMoveIn = $clientStatement->getNumCpaBookingItems() - $movedIn;
                        } catch (Exception $e) {
                            $movedIn = $didNotMoveIn = '-';
                        }

                        $data[] = [
                            'sortDate' => strtotime($clientStatement->getStatementStartDate()),
                            'dateRange' => date('F j', strtotime($clientStatement->getStatementStartDate())) . "-" . date('d, Y', strtotime($clientStatement->getStatementEndDate())),
                            'movedIn' => $movedIn,
                            'didNotMoveIn' => $didNotMoveIn,
                            'moveInRate' => $clientStatement->stringMoveInRate(),
                            'tenantFees' => $clientStatement->stringTotalCharge(),
                            'urls' => [
                                'PDF' => $this->view->url(['action' => 'viewPdf', 'id' => $clientStatement->getStatementId(), 'user_id' => $userId], 'statement'),
                                'CSV' => $this->view->url(['action' => 'viewCsv', 'id' => $clientStatement->getStatementId(), 'user_id' => $userId], 'statement')
                            ]
                        ];
                        break;
                }
            }
        }

        $this->_helper->json($data);
    }

    public function getAsyncReceiptsAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $dateFrom = $this->getParam('dateFrom');
        $dateTo = $this->getParam('dateTo');
        if (!empty($dateFrom) && !empty($dateTo)) {
            $dates = [];
            $dates['dateFrom'] =  str_replace('/', '/01/', $dateFrom);
            $dates['dateTo'] = str_replace('/', '/28/', $dateTo);
        } else {
            $dates = null;
        }

        // NetSuite Receipts
        $netsuite_receipts = $this->getNetsuiteReceiptInfo($dates);

        $this->_helper->json($netsuite_receipts);
    }

    public function receiptdownloadAction()
    {
        $entity_name = urldecode($this->getParam('entity_name'));
        $statement_period = urldecode($this->getParam('statement_period'));
        $entity_id = urldecode($this->getParam('entity_id'));
        $entity_name = $name = str_replace(' ', '_', $entity_name);
        $statement_period = $name = str_replace(' ', '_', $statement_period);

        $netsuitePdfContentUrl = "https://forms.netsuite.com/app/site/hosting/scriptlet.nl?script=24&deploy=1&compid=3370562&ns-at=AAEJ7tMQHnAfkgOH4EqIuqLTt9EuyKkVfW6id9uk-gsPbP_7dfw&entityId=$entity_id";
        $pdf = file_get_contents($netsuitePdfContentUrl);
        header("Content-Type: application/pdf");
        header('Content-Disposition: attachment; filename="Sparefoot_' . $statement_period . '_' . $entity_name . '_receipt.pdf"');
        print_r($pdf);
        die(); //Needs to die to avoid trying to go the the view template, or if someone knows a better way
    }

    public function getReceipts($entity_ids, $entity_names, $dates = null)
    {
        $receipt_info = array();
        // Note there is a limit of 1000 records returned from Netsuite.
        if (is_array($dates) && !empty($dates)) {
            $timeFrom = (new DateTime($dates['dateFrom']))->modify('+1 months')->format('m/d/Y');
            $timeTo = (new DateTime($dates['dateTo']))->modify('+1 months')->format('m/d/Y');
        } else {
            $timeFrom = (new DateTime('now'))->modify('-2 months')->format('m/d/Y');
            $timeTo = (new DateTime('now'))->format('m/d/Y');
        }

        // Get the transactions from netsuite
        $search = new Genesis_Service_Netsuite_NsRecordSearch('transaction'); //will print receipt if paid, else invoice
        $search->setFilter('mainline', null, 'is', 'T');
        $search->setFilter('entity', null, 'anyof', $entity_ids);

        $search->setFilter('trandate', null, 'within', $timeFrom, $timeTo);
        $search->setFilter('type', null, 'anyof', array('CashSale', 'CustInvc'));
        $search->setColumn('amount');
        // sort on transaction date so we always get the most recent.
        $search->setColumn('trandate', null, null, true);
        $search->setColumn('amountremaining');
        $search->setColumn('amountunbilled');
        $search->setColumn('name');
        $search->setColumn('entity');

        $records = $search->lookup();

        if (isset($records) && !empty($records)) {
            for ($i = count($records) - 1; $i >= 0; $i--) {
                $rec = $records[$i];

                $info = array();
                foreach ($rec->columns as $col) {
                    if ($col->name == 'amount') {
                        $info['amount'] = $col->value;
                    } elseif ($col->name == 'amountremaining') {
                        $info['amountremaining'] = $col->value;
                    } elseif ($col->name == 'trandate') {
                        $tdate = $col->value;
                        $info['trandate'] = $tdate;
                        $tdate = strtotime($tdate . " -30 days");
                        $info['statement_period'] = date("F Y", $tdate);
                    } elseif ($col->name == 'entity') {
                        $info['entity_id'] = $col->value;
                        $info['name'] = $entity_names[$col->value];
                    }
                }
                if ($rec->type != "journalentry") {

                    $info['link'] = "/statement/receiptdownload?entity_id=" . $rec->id . "&entity_name=" . urlencode($info['name']) . "&statement_period=" . urlencode($info['statement_period']);
                }
                $receipt_info[] = $info;
            }
        } else {
            error_log("MyFoot Receipts - Netsuite API Error: payload not on entity ids: " . implode(',', $entity_ids));
        }

        return $receipt_info;
    }

    private function getNetsuiteReceiptInfo($dates = null)
    {
        $user = $this->getLoggedUser();
        $account = $user->getAccount();
        //Look into function getBillableEntitiesId

        $billable_entities = $account->getBillableEntities();

        $receipt_info_total = array();
        $entity_ids = array();
        $entity_names = array();
        foreach ($billable_entities as $entity) {
            $entity_ids[] = $entity->getNsCustId();
            $entity_names[$entity->getNsCustId()] = $entity->getNsName();
        }

        $entity_count = count($entity_ids);
        if ($entity_count > 0) {
            // Dividing entities in different requests
            // because Netsuite API can only handle 281 entities in a single request
            $entities_per_request = 90;

            $netsuite_requests = ceil($entity_count / $entities_per_request);
            for ($request = 0; $request < $netsuite_requests; $request++) {
                $current_request_entity_ids = array_slice($entity_ids, ($request * $entities_per_request), $entities_per_request);
                $current_request_entity_names = array_slice($entity_names, ($request * $entities_per_request), $entities_per_request, true);

                $receipt_info_total = array_merge($receipt_info_total, $this->getReceipts($current_request_entity_ids, $current_request_entity_names, $dates));
            }
        }
        return array_reverse($receipt_info_total);
    }

    public function uploadMilAction()
    {
        $this->view->title = 'Reconcile with WebSelfStorage';
        $this->view->scripts = array('statement/upload-mil');

        try {
            if (!Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_SHOW_WSS_UPLOADER, array('account_id' => $this->getLoggedUser()->getAccount()->getId()))) {
                throw new Exception('Access denied');
            }

            $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Order::ASC('title'));

            $this->view->statementId = $this->getParam('id');
            $facilityId = $this->getParam('facility_id');
            $moveInFile = $this->getParam('wss_file1');
            $customerEmailList = $this->getParam('wss_file2');
            $sourceId = $this->getParam('source_id');

            if ($_FILES) {
                // make sure the integration type attempting to be uploaded is one of the ones this job can handle
                $validSources = array(
                    Genesis_Entity_Source::ID_WEB_SELF_STORAGE,
                );
                if (!in_array($sourceId, $validSources)) {
                    throw new Exception('Cannot upload move-in list for this integration type.');
                }

                // make sure the user has access to manage this facility
                $canManage = false;
                foreach ($this->view->facilities as $manageableFacility) {
                    if ($manageableFacility->getId() == $facilityId) {
                        $canManage = true;
                    }
                }
                if (!$canManage) {
                    throw new Exception('You do not have access to reconcile for this facility.');
                }

                // make sure the statement is still open for reconciliation
                $statement = Genesis_Service_Statement::loadById($this->view->statementId);
                $statementBatch = $statement->getStatementBatch();
                if ($statementBatch->getStatus() !== Genesis_Entity_StatementBatch::STATUS_OPEN) {
                    throw new Exception('The statement period you are attempting to reconcile is already closed. Please contact customer support.');
                }

                // make sure files were uploaded without error
                if ($_FILES["wss_file1"]["error"] > 0) {
                    throw new Exception("Error: " . $_FILES["wss_file1"]["error"]);
                } else {
                    $moveInFile = $_FILES["wss_file1"];
                }
                if ($_FILES["wss_file2"]["error"] > 0) {
                    throw new Exception("Error: " . $_FILES["wss_file2"]["error"]);
                } else {
                    $customerEmailList = $_FILES["wss_file2"];
                }

                $matchRun = new Genesis_Entity_Cdp_MatchRun();

                $matchRun->setFacilityId($facilityId);
                $matchRun->setSourceId($sourceId);
                $matchRun->setRunType(Genesis_Entity_Cdp_MatchRun::RUN_TYPE_IMPORT_AND_MATCH);
                $matchRun->setTenantStartDate($statementBatch->getStartDate());
                $matchRun->setTenantEndDate($statementBatch->getEndDate());
                $matchRun->setBookingStartDate(date('Y-m-d', strtotime('-100 days', strtotime($statementBatch->getStartDate()))));
                $matchRun->setInputFilesObject(array($moveInFile, $customerEmailList));
                $matchRun->setUserId($this->getLoggedUser()->getId());
                $matchRun->setLogEchos(false);
                //$matchRun->setResetExistingMatches(true);
                $matchRun->setCanMoveBookingsUntoOpenStatement(true);

                $matchRun->execute();

                // handle any errors found during the import
                $this->view->cdpErrors = array();
                if (sizeof($matchRun->getErrors()) > 0) {
                    // handle errors
                    $this->view->cdpErrors = $matchRun->getErrors();
                }

                $this->view->matchRun = $matchRun;
            }
        } catch (Exception $e) {
            $this->view->error = "Error: {$e->getMessage()}";
        }
    }

    public function bannersAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $statement_id = $this->getParam('statement_id');
        $facility_id = $this->getParam('facility_id');
        $banners = AccountMgmt_Service_Statement::getBanners($statement_id, $facility_id);
        $this->_helper->json($banners);
    }

    /**
     * let users confirm they are done with their statements
     */
    public function confirmAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $params = $this->getAllParams();
        try {
            if (!$params['statement_id']) {
                throw new Exception('statement_id is required');
            }
            $statement = Genesis_Service_Statement::loadById($params['statement_id']);
            if (!$statement) {
                throw new Exception('no statement found for statement_id ' . $params['statement_id']);
            }
            $clientStatement = Genesis_Entity_Statement_Client::buildClientStatement(
                $this->getLoggedUser(),
                $statement
            );
            if (!$clientStatement) {
                throw new Exception('No statement found with access allowed for user');
            }
            $confirmation = $clientStatement->confirmStatement();

            echo json_encode(array_merge(['success' => true], $confirmation->toArray()));
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }
}
