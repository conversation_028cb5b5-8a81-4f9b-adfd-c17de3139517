<?php
/*
 * Login Controller
 *
 * @copyright 2009 SpareFoot Inc
 * <AUTHOR>
 */

/**
 * Class LoginController
 * This intentionally DOES NOT inherit from the Restricted controller
 * to avoid a redirect loop.
 */

class LoginController extends Zend_Controller_Action
{
    public const LOGIN_CSRF_TOKEN = 'login_token';

    /**
     * Login action
     */
    public function indexAction()
    {
        //are we signed in? because GTFO if so
        if (AccountMgmt_Service_User::getLoggedUser()) {
            $this->redirect($this->view->url([], 'dashboard'));
        }
        $this->view->remember = AccountMgmt_Service_UserRememberMe::getSetRememberMe($this->getParam('remember'), $this->getParam('email'));
        $email = $this->getParam('email') ? $this->getParam('email') : $this->view->remember;
        $this->view->email = htmlspecialchars($email, ENT_QUOTES, 'UTF-8');

        $this->view->error = AccountMgmt_Service_User::getSession()->loginError;
        AccountMgmt_Service_User::getSession()->loginError = '';
        $this->view->csrf_token = CsrfUtil::getToken(self::LOGIN_CSRF_TOKEN);
        $this->_helper->layout->setLayout('login');
        $this->view->scripts = ['login/login'];
    }

    /**
     * Process user login
     * called from login form
     */
    public function processLoginAction()
    {
        $error = false;
        AccountMgmt_Service_UserRememberMe::getSetRememberMe($this->getParam('remember'), $this->getParam('email'));


        try {
            if (!CsrfUtil::validateToken(self::LOGIN_CSRF_TOKEN, $this->getParam('csrf_token'))) {
                throw new Exception("Invalid Login");

            }
            Genesis_Db_Connection::getInstance();
            AccountMgmt_Service_UserOauth::authenticate($this->getParam('email'), $this->getParam('password'));
            $userAccess = AccountMgmt_Service_UserOauth::getUserAccess();

        } catch (Exception $e) {

            $userAccess = false;
            if (stripos($e->getMessage(), 'Invalid login')) {
                $error = 'Incorrect e-mail address or password. Please re-enter your credentials.';
            } else {
                $error = $e->getMessage();
            }
        }


        if (! $userAccess && ! $error) { //see why we need an error message still
            $ua = Genesis_Service_UserAccess::loadByEmail($this->getParam('email'));
            if ($ua && $ua->getAccount() && ! $ua->getMyfootRole()) {
                $error = 'LEGACY Directions to reset your password have been emailed to you. If you do not receive an email, please contact our Support team at 855-427-8193 for further assistance.';
            } elseif ($this->getParam('email') || $this->getParam('password')) {
                $error = 'LEGACY Incorrect e-mail address or password. Please re-enter your credentials.';
            } else {
                $error = 'LEGACY Please enter your e-mail address and password.';
            }
        }

        //login failed, set the error and redirect
        if (! $userAccess) {
            AccountMgmt_Service_User::getSession()->loginError = $error;
            $this->redirect($this->view->url(['action'=>'index'], 'login'));
        }

        Genesis_Service_UserAccess::updateLastLoggedIn($userAccess);

        if ($userAccess->isMyFootGod()) {
            Genesis_Util_FilteredIps::insertIp(Genesis_Util_FilteredIps::getRemoteIp(), null, true);
        } else {
            Genesis_Util_FilteredIps::insertIp(Genesis_Util_FilteredIps::getRemoteIp(), 'client');
        }

        //resume the page we were attempting
        if (AccountMgmt_Service_UserRedirect::needsRedirect()) {

            $this->redirect(AccountMgmt_Service_UserRedirect::getRedirect());
        }
        //normal myfoot entry
        $this->redirect($this->view->url(['action' => 'index'], 'dashboard'));
    }

    /**
     * Logout action
     */
    public function logoutAction()
    {
        //get message before session destroy
        $message = AccountMgmt_Service_User::getSession()->logoutMessage;
        //destroy session
        AccountMgmt_Service_User::logout();

        header('Expires: Thu, 02-July-80 00:00:01 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: no-store, no-cache, must-revalidate');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');

        //if we have message, display the view as part of this
        if (! empty($message)) {
            $this->_helper->layout->setLayout('error');
            $this->view->message = $message;
            return;
        }


        $this->redirect($this->view->url(['action'=>'index'], 'login'));
    }

    /**
     * Reset Password action
     */
    public function resetpasswordAction()
    {
        //disable zend layout so it doesn't return all the html
        $this->_helper->layout->disableLayout();

        //don't render a response
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            $emailAddr = $this->getParam('email');
            $sanitizedEmailAddress = htmlspecialchars($emailAddr, ENT_QUOTES, 'utf-8');

            //validate email address
            if (!(strlen($emailAddr) > 0) || !preg_match("/[a-zA-Z0-9_%-.]+@[a-zA-Z0-9.-]+\.[a-zA-Z]+/", $emailAddr)) {
                throw new Exception($sanitizedEmailAddress . ' is not a valid email address.');
            }

            // Only load the user if they're an account management user
            $user = Genesis_Service_UserAccess::loadByEmail($emailAddr);
            if (! $user) {
                throw new Exception('Directions to reset your password have been emailed to you. If you do not receive an email, please contact our Support team at 855-427-8193 for further assistance.');
            }

            //if user does not have myfoot access, then error out and say so
            if (! $user->getMyfootRole()) {
                if ($user->getAccount()) {
                    throw new Exception('Directions to reset your password have been emailed to you. If you do not receive an email, please contact our Support team at 855-427-8193 for further assistance.');
                } else {
                    throw new Exception('You do not have a MySpareFoot.com account.');
                }
            }

            $user->sendForgotPasswordEmail('facility/forgot-password');

        } catch (Exception $e) {
            http_response_code(400);
            echo $e->getMessage()."\n";
        }
    }

    public function forgotPasswordAction()
    {
        try {
            $data['email'] = $this->getParam('email', $this->getParam('e'));

            $user = Genesis_Service_User::loadByEmail($data['email']);
            if (! $user) {
                throw new Exception("Unknown email address");
            }

            $data['auth_key'] = $this->getParam('k');

            if (! $this->getParam('k') || $this->getParam('k') != $user->getPasswordResetAuthKey()) {
                throw new Exception("Oops, this link is broken or expired! Please make sure you're clicking the most recent password reset link we emailed you.");
            }
            $this->view->email = $data['email'];
            $this->view->k = $this->getParam('k');
            $this->_helper->layout->setLayout('login');
            $this->view->scripts = ['login/login'];
        } catch (Exception $e) {
            AccountMgmt_Service_User::getSession()->loginError = $e->getMessage();
            $this->redirect($this->view->url(['action'=>'index'], 'login'));
        }
    }

    public function loginFromPasswordResetAction()
    {
        //disable zend layout so it doesn't return all the html
        $this->_helper->layout->disableLayout();

        //don't render a response
        $this->_helper->viewRenderer->setNoRender(true);
        $authKey = $this->getParam('k');
        $email = $this->getParam('email');
        $password = $this->getParam('password');
        $confirmPassword = $this->getParam('confirm-password');
        $user = Genesis_Service_User::loadByEmail($email);

        try {

            if (!$authKey || $authKey != $user->getPasswordResetAuthKey()) {
                throw new Exception("There was a weird internal error, please contact SpareFoot for assistance.");
            }
            if (!$email) {
                throw new Exception('Internal error please contact SpareFoot.');
            }
            if (!$password) {
                throw new Exception('New password is required.');
            }
            if (!$confirmPassword) {
                throw new Exception('Confirm password is required.');
            }
            if (!$user) {
                throw new Exception("No user found for $email.");
            }
            if ($password != $confirmPassword) {
                throw new Exception("Passwords must match");
            }

            Genesis_Service_User::updatePassword($user, $password, false); //this will sha1 whatever you put in here

            $user->setPasswordResetAuthKey(null);
            Genesis_Service_User::save($user);

            $this->processLoginAction();

        } catch (Exception $e) {
            $this->view->k = $this->getParam('k');
            $this->view->email = $this->getParam('email');
            $this->view->errorMessage = $e->getMessage();
            $this->forward('forgot-password');
        }
    }

}
