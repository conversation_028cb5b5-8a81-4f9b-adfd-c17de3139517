<?php
/*
 * Account Controller
 *
 * @copyright 2009 SpareFoot Inc
 * <AUTHOR> <PERSON>
 */
use AccountMgmt_Models_BidIncreaseBannerValidation;

class DashboardController extends AccountMgmt_Controller_Restricted
{
    protected function _init()
    {
        if (! count($this->getLoggedUser()->getManagableFacilities())) {
            $this->redirect($this->view->url(['action' => 'add-first'], 'features'));
        }

        $this->view->hasOnlineMoveInFmsSoftware = AccountMgmt_Service_Account::accountHasFmsSupportingOnlineMoveins($this->getLoggedUser()->getAccount());

        $this->view->banner = [
            'showMoveInsBanner' => Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::MOVE_IN_BANNER, []),
            'showMoveInOnlineBanner' => Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::MOVE_IN_ONLINE_BANNER, []),
            'showNotificationBanner' => AccountMgmt_Models_BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount())
        ];
    }

    /**
     * Index Action
     */
    public function indexAction()
    {
        $this->view->account = $this->getLoggedUser()->getAccount();


        if ($this->getSession()->facilityId) {
            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $this->view->facility = $facility;

            // Show welcome widget if facility is NOT reconciled
            $this->view->showWelcomeWidget = !AccountMgmt_Service_Facility::hasReconciled($facility);
        }

        // Gets 4 most recent messages
        $this->view->messages = Genesis_Service_AccountMgmtMessage::loadByNumber(4);
        $this->view->scripts = [
            'facility/global-functions',
            '../vendors/chartist/dist/chartist.min',
            '../sparefoot/plugins/current-bid-widget/script',
            '../sparefoot/plugins/customer-reviews-widget/script',
            '../sparefoot/plugins/inventory-widget/script',
            '../sparefoot/plugins/move-in-rate-chart/script',
            '../sparefoot/plugins/views-and-reservations-chart/script',
            'dashboard/index'
        ];
        $this->view->title = 'Dashboard';
    }
}
