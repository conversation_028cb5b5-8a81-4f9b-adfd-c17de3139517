<?php
/*
 * Settings Controller
 *
 * @copyright SpareFoot Inc
 * <AUTHOR>
 */

class SettingsController extends AccountMgmt_Controller_Restricted
{

    /**
     * Show settings
     */
    public function indexAction()
    {
        $this->forward('home');
    }

    public function homeAction()
    {
    }

    /**
     * Show settings
     */
    public function myaccountAction()
    {
        $this->view->user = $this->getLoggedUser();
        $this->view->account = $this->getLoggedUser()->getAccount();

        $location = $this->getLoggedUser()->getAccount()->getLocation();

        if ($location) {
            $this->view->zip = $location->getZip();
            $this->view->city = $location->getCity();
            $this->view->address = $location->getAddress1();
            $this->view->state = $location->getState();
        } else {
            $this->view->zip = "";
            $this->view->city = "";
            $this->view->address = "";
            $this->view->state = "";
        }

        $this->view->erroredFields = array();

        if ($_POST) {

            try {

                //update user data
                $user = $this->getLoggedUser();

                if (strlen($this->getParam('fname')) > 0) {
                    $input = $this->getParam('fname');
                    $user->setFirstName($input);
                } else {
                    $this->view->erroredFields = array('fname');
                    throw new Exception('Please enter a first name.');
                }

                if (strlen($this->getParam('lname')) > 0) {
                    $input = $this->getParam('lname');
                    $user->setLastName($input);
                } else {
                    $this->view->erroredFields = array('lname');
                    throw new Exception('Please enter a last name.');
                }

                if (strlen($this->getParam('email')) > 0 ) {
                    if (!filter_var($this->getParam('email'), FILTER_VALIDATE_EMAIL)) {
                        $this->view->erroredFields = array('email');
                        throw new Exception('Please enter a valid email address.');
                    }
                    $user->setEmail($this->getParam('email'));
                } else {
                    $this->view->erroredFields = array('email');
                    throw new Exception('Please enter an email address');
                }

                if (strlen($this->getParam('phone')) > 0) {
                    if (!preg_match("/[0-9]{7,14}/",preg_replace("/[^0-9]/", "", $this->getParam('phone')))) {
                        $this->view->erroredFields = array('phone');
                        throw new Exception('Phone number is invalid');
                    }
                    $user->setPhone(preg_replace("/[^0-9]/", "", $this->getParam('phone')));
                }
//                else {
//                    $this->view->erroredFields = array('phone');
//                    throw new Exception('Please enter a phone number.');
//                }

                if (strlen($this->getParam('aboutme')) > 0) {
                    $user->setAboutMe($this->getParam('aboutme'));
                }

                Genesis_Service_User::save($user);

                //now update password stuff
                if (strlen($this->getParam('old_password')) > 0) {
                    $oldPassword = $this->getParam('old_password');
                    $newPassword = $this->getParam('new_password');
                    $newPasswordConfirm = $this->getParam('new_password_confirm');

                    if ( ! $user->checkRawPassword($oldPassword)) { //the model owns this
                        $this->view->erroredFields = array('old_password');
                        throw new Exception('Current password is incorrect.');
                    }

                    if ($newPassword != $newPasswordConfirm) {
                        $this->view->erroredFields = array('new_password','new_password_confirm');
                        throw new Exception('Passwords do not match.');
                    }

                    Genesis_Service_User::updatePassword($this->getLoggedUser(), $newPassword);
                    $this->getLoggedUser()->setRawPassword($newPassword);
                }

                $this->view->user = $this->getLoggedUser();

                $this->view->alert = 'Changes saved.';
                $this->view->alertClass = 'alert-success';

            } catch (Exception $e) {
                $this->view->alert = '<strong>Error</strong>: ' . $e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        $this->view->title = 'Your Account - Settings';

        /*
         //TODO: this is the integration creds change, put it in later with accounts interface
         else if ($this->getParam('sourceId')) {
            //update credentials for integration
            $this->_helper->layout()->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            try {
                $corp = $this->getLoggedUser()->getCorporation();

                //update the credentials based on source id
                if ($this->getParam('sourceId') == Genesis_Entity_Source::ID_CENTERSHIFT) {
                    $corp->setSourceId(Genesis_Entity_Source::ID_CENTERSHIFT);

                    if (strlen($this->getParam('cs_username')) > 0) {
                        $corp->setCsUsername($this->getParam('cs_username'));
                    } else {
                        throw new Exception('Please enter a Centershift username.');
                    }

                    if (strlen($this->getParam('cs_pin')) > 0) {
                        $corp->setCsPassword($this->getParam('cs_pin'));
                    } else {
                        throw new Exception('Please enter a Centershift PIN.');
                    }

                    $corp->setEndpoint($this->getParam('cs_datacenter'));

                    //check if corp already exists
                    $existingCorp = Genesis_Service_Corporation::load(
                            Genesis_Db_Restriction::and_(
                                    Genesis_Db_Restriction::equal('csUsername', $this->getParam('cs_username')),
                                    Genesis_Db_Restriction::equal('csPassword', $this->getParam('cs_pin'))))->uniqueResult();

                    if ($existingCorp && $existingCorp->getId() != $corp->getId()) {
                        throw new Exception('A Centershift corporation with username ' . $this->getParam('cs_username') .
                                ' and password ' . $this->getParam('cs_password') . ' is already in our system.');
                    }

                } else if ($this->getParam('sourceId') == Genesis_Entity_Source::ID_SITELINK) {
                    $corp->setSourceId(Genesis_Entity_Source::ID_SITELINK);

                    if (strlen($this->getParam('sl_corpcode')) > 0) {
                        $corp->setSitelinkCorpCode($this->getParam('sl_corpcode'));
                    } else {
                        throw new Exception('Please enter a SiteLink corporation code.');
                    }

                    if (strlen($this->getParam('sl_username')) > 0) {
                        $corp->setSitelinkUsername($this->getParam('sl_username'));
                    } else {
                        throw new Exception('Please enter a SiteLink username.');
                    }

                    if (strlen($this->getParam('sl_password')) > 0) {
                        $corp->setSitelinkPassword($this->getParam('sl_password'));
                    } else {
                        throw new Exception('Please enter a SiteLink password.');
                    }

                    //check if corp code already exists
                    $existingCorp = Genesis_Service_Corporation::load(Genesis_Db_Restriction::equal('sitelinkCorpCode', $this->getParam('sl_corpcode')))->uniqueResult();
                    if ($existingCorp && $existingCorp->getId() != $corp->getId()) {
                        throw new Exception('A SiteLink corporation with corp code ' . $this->getParam('sl_corpcode') . ' is already in our system.');
                    }

                } else {
                    throw new Exception('Source ID not match. Contact <EMAIL> for help.');
                }

                Genesis_Service_Corporation::save($corp);

                //send an email to let us know that someone changed their integration
                $msg = new Genesis_Entity_EmailMessage();
                $msg->setSubject('An integrated user changed their credentials, sync them');
                $msg->setBody('This corporation modified their integration credentials.  Attempt a re-sync:<br/>
                    Name:' . $this->getLoggedUser()->getCorporation()->getCorpname() . '<br/>
                    ID:' . $this->getLoggedUser()->getCorporationId() .'');

                Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $msg, array());
            } catch (Exception $e) {
                echo "Error: ",  $e->getMessage(), "\n";
            }
        }*/
    }

    /**
     * Create vars for the settings/corporate view
     */
    public function corporateAction()
    {
        $user = $this->getLoggedUser();
        $role = $user->getMyfootRole();
        if (!in_array(
                $role,
                [
                    Genesis_Entity_UserAccess::ROLE_GOD,
                    Genesis_Entity_UserAccess::ROLE_ADMIN
                ]
            )
        ) {
            $this->redirect($this->view->url(['action' => 'myaccount'], 'settings'));
            return;
        }

        $account = $user->getAccount();
        $this->view->user = $user;
        $this->view->account = $account;
        $this->view->erroredFields = array();

        if ($_POST) {
            try {
                if (strlen($this->getParam('address')) == 0) {
                    throw new Exception('Please enter an address.');
                } elseif (strlen($this->getParam('city')) == 0) {
                    throw new Exception('Please enter a city.');
                } elseif (strlen($this->getParam('zip')) == 0) {
                    throw new Exception('Please enter a zip code.');
                } elseif (strlen($this->getParam('company_name')) == 0) {
                    throw new Exception('Please enter a company name.');
                }

                $account->setName($this->getParam('company_name'));

                $location = Genesis_Service_Location::geoCodePhysicalAddress($this->getParam('address') . " "
                    . $this->getParam('city') . " " . $this->getParam('state') . " " . $this->getParam('zip'), $this->getParam('address'), $this->getParam('zip'));            
                $location = Genesis_Service_Location::save($location);

                $account->setLocationId($location->getId());
                Genesis_Service_Account::save($account);

                // Requery the Account and set it!
                $account = $this->getLoggedUser()->getAccount();
                $this->view->account = $account;

                $this->view->alert = 'Changes saved.';
                $this->view->alertClass = 'alert-success';

            } catch (Exception $e) {
                $this->view->alert = '<strong>Error</strong>: ' . $e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        // Check to see if the user has a terms sheet envelope
        $display_terms_download = false;
        $user = $this->getLoggedUser();
        if ($user->getAccount()->getTermsEnvelopeId() !== NULL && $user->getAccount()->getTermsEnvelopeId() != '') {
            $display_terms_download = true;
        }
        $this->view->display_terms_download = $display_terms_download;

        // Display current account terms agreement.
        $this->view->terms_link = '/document/terms';


        if ($account->getTermsAddendum()) {
            $this->view->termsAddendumLink = '/document/terms-addendum';
        }

        $this->view->title = 'Corporate Account - Settings';
    }

    // Updates the facility list to whatever user is selected
    public function updatefacilitylistAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if ($this->getParam('userid')) {
            $acctMgmtUser = Genesis_Service_UserAccess::loadById($this->getParam('userid'));

            //get managable facilities and save id's in an array
            $restriction = Genesis_Db_Restriction::equal('published', 1);
            $restriction->setOrder(Genesis_Db_Order::asc('title'));
            $managableFacs = $acctMgmtUser->getManagableFacilities($restriction);

            $managableArray = array();
            foreach ($managableFacs as $managableFac) {
                $managableArray[] = $managableFac->getId();
            }

            $facilities = Genesis_Service_Facility::loadByAccountId($this->getLoggedUser()->getAccountId(), $restriction);

            //print all facilities and select managable ones by this user
            print '<ul><li><label for="all"><input type="checkbox" id="all" onclick="checkAllFacilities();"/>SELECT ALL</label></li><hr/>';
            foreach ($facilities as $fac) {
                print '<li><label for="'.$fac->getId().'"><input type="checkbox" id="'.$fac->getId().'" name="fac_id" value="'.$fac->getId().'" ';
                if (in_array($fac->getId(), $managableArray)) {
                    print 'checked="checked"';
                }
                print '>'.$fac->getTitle().(!$fac->getActive() ? ' [Hidden]' : '').'</label></li>';
            }
            print '</ul>';
        } else {
            print "(select a user first)";
        }
    }

    public function billingAction()
    {
        $this->view->account = $this->getLoggedUser()->getAccount();
    }

    //this is not used yet, it is commented out in the view
    /*public function verifybillingAction() {
        $verified = false;
        $userBusiness = Genesis_Service_UserBusiness::loadById($this->getLoggedUser()->getId());

        $userId = $userBusiness->getUserId();
        $bizName = $userBusiness->getBusinessName();

        $type = $this->getParam('cardType');
        $number = $this->getParam('number');
        $cvv2 = $this->getParam('cvv2');
        $name = $this->getParam('name');
        $month = $this->getParam('month');
        $year = $this->getParam('year');
        $address1 = $this->getParam('address1');
        $address2 = $this->getParam('address2');
        $city = $this->getParam('city');
        $state = $this->getParam('state');
        $zip = $this->getParam('zip');

        //TODO: use paypal api to confirm credit card is valid, integrate with billing system

        $verified = true;

        //if so, email chuck the info and set billingverified to 1
        if ($verified) {

            //send email to chuck with data
            $msg = new Genesis_Entity_EmailMessage();
            $msg->setSubject("Billing Info Verified");
            $msg->setBody("USER INFO:" .
                    "<br/>User Id: " . $userId .
                    "<br/>Business Name: " . $bizName .
                    "<br/><br/>CREDIT CARD INFO:" .
                    "<br/>Type: " . $type .
                    "<br/>Number: " . $number .
                    "<br/>CVV2: " . $cvv2 .
                    "<br/>Name: " . $name .
                    "<br/>Month: " . $month .
                    "<br/>Year: " . $year .
                    "<br/>Address Line 1: " . $address1 .
                    "<br/>Address Line 2: " . $address2 .
                    "<br/>City: " . $city .
                    "<br/>State: " . $state .
                    "<br/>Zip: " . $zip  .
                    "<br/><br/><br/>1. Validate this card.<br/>2. Save this data in your records.<br/>3. Set the 'billing_verified' flag in the user_business table if credit card is valid." .
                    "<br/><br/>When we have our biling system, this will become automatic.");

            $params = array();

            Genesis_Service_Mailer::sendInternalMessage("<EMAIL>", $msg, $params);
        }
    }*/

    protected function getSideBarContent()
    {
        $view = new Zend_View();
        $view->loggedUser = $this->getLoggedUser();
        $view->selectedAction = Zend_Controller_Front::getInstance()->getRequest()->getActionName();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/settings/');

        return $view->render('left-sidebar-content.phtml');
    }

    protected function getTab()
    {
        return self::TAB_SETTINGS;
    }

    //updates the facility list to whatever user is selected
    public function insertAccountSoftwareAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if ($this->getParam('integration_type') && $this->getParam('software_account_id') ) {
            $software_arr = $this->getParam('integration_type');

            if (is_array($software_arr)) {
                foreach ($software_arr as $key => $val) {

                    $software = new Genesis_Entity_AccountSoftware();
                    $software->setAccountId($this->getParam('software_account_id'));
                    $software->setSourceId($val);
                    $software->setUserId($this->getParam('software_user_id'));
                    Genesis_Service_AccountSoftware::save($software);
                }
            }
        } else {
            print 'Error! Please select a software';

            return true;
        }
    }
}
