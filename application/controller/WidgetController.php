<?php
/*
 * Widget Controller
 *
 * @copyright 2010 SpareFoot Inc
 * <AUTHOR>
 *
 * TODO: DELETE THIS AND RELATED TEMPLATES
 */

class WidgetController extends AccountMgmt_Controller_Restricted
{
    protected function _init()
    {
        if ($this->getParam('fid')) {
            $facilityId = $this->getParam('fid');
        } else {
            $facilityId = $this->getSession()->facilityId;
        }

        $this->getSession()->facilityId = ($facilityId == -1 ? null : $facilityId);
    }

    public function indexAction()
    {
        $this->forward('overview');
    }

    public function overviewAction()
    {
        $this->getSession()->facilityId = 'all';
        $this->view->title = 'Booking Widget - Settings';
    }

    public function reservationsAction()
    {
        $this->view->reservations = $this->_fetchReservationsData();
        $this->view->facilities = Genesis_Service_Facility::loadByAccountId($this->getLoggedUser()->getAccountId(), Genesis_Db_Restriction::equal('published', 1));
        $this->view->facility_id = $this->getSession()->facilityId;

        $this->view->scripts = array('widget/reservations');
    }

    public function setupAction()
    {
        $this->view->facilities = Genesis_Service_Facility::loadByAccountId($this->getLoggedUser()->getAccountId(), Genesis_Db_Restriction::equal('published', 1));
        $this->view->account = Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());

        $this->view->scripts = array('widget/setup');
    }

    public function analyticsAction()
    {
        $this->view->mtdVisitsFlot = new AccountMgmt_Flot_MtdVisitsBar('mtd_visits_flot', $this->getLoggedUser()->getAccountId());
        $this->view->mtdClicksFlot = new AccountMgmt_Flot_MtdClicksBar('mtd_clicks_flot', $this->getLoggedUser()->getAccountId());
        $this->view->mtdBookingsFlot = new AccountMgmt_Flot_MtdBookingsBar('mtd_bookings_flot', $this->getLoggedUser()->getAccountId());
        $this->view->mtdConversionsFlot = new AccountMgmt_Flot_MtdConversionsLine('mtd_conversions_flot', $this->getLoggedUser()->getAccountId());
        $this->view->scripts = array('widget/analytics');
    }

    public function flotjsonAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $flots = array(
            'mtd_bookings_flot' => new AccountMgmt_Flot_MtdBookingsBar('mtd_bookings_flot', $this->getLoggedUser()->getAccountId()),
            'mtd_visits_flot' => new AccountMgmt_Flot_MtdVisitsBar('mtd_visits_flot', $this->getLoggedUser()->getAccountId()),
            'mtd_clicks_flot' => new AccountMgmt_Flot_MtdClicksBar('mtd_clicks_flot', $this->getLoggedUser()->getAccountId()),
            'mtd_conversions_flot' => new AccountMgmt_Flot_MtdConversionsLine('mtd_conversions_flot', $this->getLoggedUser()->getAccountId()),
        );

        echo json_encode($flots[$this->getParam('id')]->getjson());
    }

    //method to create a csv of the widget/analytics code to download
    public function downloadsetupAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $account = $this->getLoggedUser()->getAccount();
        $facilities = Genesis_Service_Facility::loadByAccountId($this->getLoggedUser()->getAccountId(), Genesis_Db_Restriction::equal('published', 1));
        $filename = $account->getName() . "_SpareFoot_Instructions.csv";

        //replace spaces
        $filename = str_replace (" ", "", $filename);

        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control: private");
        header("Content-type: application/octet-stream");
        header("Content-Disposition: attachment; filename= \"" . $filename."\"");
        header("Accept-Ranges: bytes");
        echo 'SpareFoot analytics and booking widget instructions for ' . $account->getName() . ',
,
Demo:,http://my.sparefoot.com/static/bookingWidgetDemo.php,
,
STEP 1: Place the following div tag on every page of your site in the body. We use this as a handle for the tracking information. It must come before any of the code in the other steps.,
"<div id=""__sf""></div> ",
,
STEP 2: Place the following code on your pages after the code from step 1. This includes our analytics library so we can tell you what your customers are doing!,
"<script type=""text/javascript"" src=""https://service.sparefoot.com/js/sfanalytics.js""></script><script type=""text/javascript"">var __sf = new SFAnalytics(' . $account->getId() . ', document.getElementById(\'__sf\'));__sf.renderVisit();</script> ",
,
STEP 3: Place the following code on your page one time. These lines are required only once regardless of how many facility reservation buttons you put on a page.,
"<link href=""https://service.sparefoot.com/css/booking-widget.css"" rel=""stylesheet"" type=""text/css""/><script type=""text/javascript"" src=""https://service.sparefoot.com/js/booking-widget-package.js""></script> ",
,
"IMPORTANT: YOU MUST LEAVE THE LINK APPENDED TO THE BOOKING WIDGET CODE INTACT. REMOVAL OF THE LINK VIOLATES THE SPAREFOOT BOOKING WIDGET TERMS OF USE. While we don\'t charge anything to use the widget, the link is required (it helps us with our optimization).",
,
"STEP 4: Here is the booking widget button code for each facility.  Place it where you want the reserve button to appear. You can replace the text ""Reserve Unit"" with whatever you want the button to say. You can place multiple buttons on the same page or put then on seperate pages (provided that you complete the steps above for each page).",
';

foreach ($facilities as $fac) {
echo $fac->getTitle() . ',"<script type=""text/javascript"" src=""https://service.sparefoot.com/syndication/booking/proxy?fid=' . $fac->getId() . '""></script><a class=""client-hold-button"" style=""background-color: #088A09;"" id=""sparefootBooking_' . $fac->getId() . '"">Reserve Unit</a> <p class=""sparefoot-hold-link""><a href=""' . $fac->getFacilityCityPageLink() . '"">' . $fac->getSiteVerifyText() . '</a> Powered by <a href=""http://www.sparefoot.com"">SpareFoot</a></p> "
';
}

echo ',
STEP 5: Publish the changes to your website so the code is live.,
';
        exit;

    }

    protected function getTab()
    {
        return self::TAB_WIDGET;
    }

    /**
     * Fetch and organize all of the data to populate the reservations screen
     *
     * @return array
     */
    private function _fetchReservationsData()
    {
        $startDate = $this->getBeginDate();
        $endDate   = $this->getEndDate();

        $reservationDetails = array();

        $facilityId = $this->getSession()->facilityId;

        $accountId = $this->getLoggedUser()->getAccountId();

        $impData = Genesis_Service_Transaction::loadWidgetByAccountId(
                $accountId, $startDate, $endDate);

        foreach ($impData as $id => $dataArray) {
            //skip invalid booking state
            if ($dataArray['booking_state'] == 'INVALID') {
                continue;
            }

            //if a facility id was specified, only get those bookings
            if ($facilityId && $facilityId != 'all' && ($facilityId != $dataArray['facility_id'])) {
                continue;
            }

            $reservationDetails[$id]['last_name']      = $dataArray['last_name'];
            $reservationDetails[$id]['first_name']     = $dataArray['first_name'];
            $reservationDetails[$id]['unit_number']    = $dataArray['unit_number'];
            $reservationDetails[$id]['monthly_rent']   = $dataArray['monthly_rent'];
            $reservationDetails[$id]['timestamp']      = $dataArray['timestamp'];
            $reservationDetails[$id]['move_in']        = $dataArray['move_in'];
            $reservationDetails[$id]['size_w']         = $dataArray['size_w'];
            $reservationDetails[$id]['size_d']         = $dataArray['size_d'];
            $reservationDetails[$id]['facility_id']    = $dataArray['facility_id'];
            $reservationDetails[$id]['title']          = $dataArray['title'];
            $reservationDetails[$id]['email']          = $dataArray['email'];
            $reservationDetails[$id]['phone']          = $dataArray['phone'];
            $reservationDetails[$id]['traffic_source'] = $dataArray['traffic_source'];
        }

        return $reservationDetails;
    }
}
