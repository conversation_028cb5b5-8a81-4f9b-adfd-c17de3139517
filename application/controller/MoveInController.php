<?php
/**
 * MoveIn Controller
 *
 * @copyright SpareFoot
 * <AUTHOR>
 */
class MoveInController extends Zend_Controller_Action
{
    public function init()
    {
        $this->_helper->layout->setLayout('login');
    }

    public function yesAction()
    {
        $confCode = $this->getParam('confirmation');
        $confirmingUserEmail = urldecode($this->getParam('email'));

        $user = Genesis_Service_User::loadByEmail($confirmingUserEmail);
        $transaction = Genesis_Service_Transaction::loadById($confCode);

        if (empty($transaction) || empty($user)) {
            //throw new Exception('Cannot find confirmation code '.$confCode);
            $this->redirect('/login');
        }

        $transaction->facilityConfirmMovein($user);

        $this->_helper->viewRenderer->renderScript('move-in/index.phtml');
    }
    //my.sparefoot.localhost/move-in/no/email/<EMAIL>/confirmation_code/FM7ENTW8
    public function noAction()
    {
        $confCode = $this->getParam('confirmation');
        $confirmingUserEmail = urldecode($this->getParam('email'));

        $user = Genesis_Service_User::loadByEmail($confirmingUserEmail);
        $transaction = Genesis_Service_Transaction::loadById($confCode);

        if (empty($transaction) || empty($user)) {
            //throw new Exception('Cannot find confirmation code '.$confCode);
            $this->redirect('/login');
        }

        $transaction->facilityConfirmNoMovein($user);
        $this->view->noMovein = true;

        $this->_helper->viewRenderer->renderScript('move-in/no.phtml');
    }
    //my.sparefoot.localhost/move-in/maybe/email/<EMAIL>/confirmation_code/FM7ENTW8
    public function maybeAction()
    {
        //don't save another action log if this is a result of the GIZMO crap...
        if (!$this->getParam('sg_sessionid')) {
            $confCode = $this->getParam('confirmation');
            $confirmingUserEmail = urldecode($this->getParam('email'));

            $user = Genesis_Service_User::loadByEmail($confirmingUserEmail);
            $transaction = Genesis_Service_Transaction::loadById($confCode);

            if (empty($transaction) || empty($user)) {
                //throw new Exception('Cannot find confirmation code '.$confCode);
                $this->redirect('/login');
            }

            $transaction->facilityConfirmFutureMovein($user);

            $this->_helper->viewRenderer->renderScript('move-in/maybe.phtml');
        }
    }

    public function optOutAction()
    {
        $templateLabel = $this->_getParam('t');
        $emailAddress = $this->_getParam('e');
        $statementBatchId = $this->_getParam('sb');

        try {

            if (! $statementBatchId) {
                $openStatementBatch = Genesis_Service_StatementBatch::loadByStatus(Genesis_Entity_StatementBatch::STATUS_OPEN);
                if ($openStatementBatch) {
                    $statementBatchId = $openStatementBatch->getId();
                } else {
                    // this is incase this is hit outside of an open statement period, it should add the opt-out, but be ignored
                    $statementBatchId = '00';
                }
            }

            $optOutableTemplates = array(
                'reconciliation-warning' => 'facility/reconciliation-warning.'.$statementBatchId,
            );

            if (isset($optOutableTemplates[$templateLabel])) {
                $messageName = $optOutableTemplates[$templateLabel];
            } else {
                $messageName = $optOutableTemplates['reconciliation-warning'];
            }

            $optout = Genesis_Service_EmailOptOut::add($emailAddress, Genesis_Service_EmailOptOut::buildFromTemplatePath($messageName));
            if (!$optout) {
                throw new Exception('Could not opt out');
            }

            $this->view->email = $emailAddress;

        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }
}
