<?php
/**
 * Abstract Restricted Controller
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR>
 */

abstract class AccountMgmt_Controller_Restricted extends Zend_Controller_Action
{
    /**
     * @var Zend_Session_Namespace
     */
    private $_session;

    private $_endDate;
    private $_beginDate;

    const TAB_HOME          = 'home';
    const TAB_BILLING       = 'billing';
    const TAB_FACILITY      = 'facilities';
    const TAB_SETTINGS      = 'settings';
    const TAB_WIDGET        = 'widget';
    const TAB_HOSTEDWEBSITE = 'hostedwebsite';
    const TAB_MOVE_INS      = 'move-ins';

    /**
     * @override
     */
    final public function init()
    {

        //this will set session->facilityId and session->accountId
        $this->_initAuthorization();

        //_init is for extended controllers to auto call
        $this->_init();
        $this->_initDateRange();
        $this->_initTrueDateRange();
        $this->_initUiState();

        $this->view->errorMessages = array();
        $this->view->successMessages = array();

    }

    private function _clear()
    {
        $this->_loggedUser = null;
        $this->_session = null;
        $this->_beginDate = null;
        $this->_endDate = null;
        $this->_trueBeginDate = null;
        $this->_trueEndDate = null;
    }

    private function _forceTerms()
    {
        //rules only apply to these lesser account roles
        if ($this->getLoggedUser()->isMyFootGod()) {
            return;
        }

        //certain urls can work without terms, let the accounts controller pass
        //or else they can never add payment methods and pass this point
        if ($this->getRequest()->getControllerName() === 'accounts') {
            return;
        }
        //let the error controller fire
        if ($this->getRequest()->getControllerName() === 'error') {
            return;
        }


        //force agree to the pre-terms
        if (! Genesis_Service_Account::termsAccepted($this->getLoggedUser()->getAccount())) {
            if (! $this->getLoggedUser()->isMyfootAdmin()) {
                return; //tech debt. Fix this
                $message = '(terms incomplete) Please ask your account admin to sign in. '
                    .' Your account admins are: ';
                /**
                 * @var $adminUser Genesis_Entity_User
                 */
                foreach ($this->getLoggedUser()->getAccount()->getAdmins() as $adminUser) {
                    $message .= $adminUser->getEmail() . " ";
                }

                throw new Exception($message);
            }
            if ($this->getRequest()->getControllerName() !== 'signup-end' &&
                $this->getParam('action') !== 'terms'
            ) {
                $this->redirect('/signup-end/terms');
            }
        }

        //encourage to setup a billable entity,
        if ($this->getLoggedUser()->getAccount()->getNumBillableEntities() === 0) {
            if ($this->getLoggedUser()->isMyfootAdmin() &&
                $this->getRequest()->getControllerName() === 'login' &&
                $this->getParam('action') === 'process-login') {
                    $this->redirect('/signup-end/billing');
            }
        }
    }

    private function _initAuthorization()
    {
        //the user is not signed in. make them go sign in
        if (! $this->getLoggedUser() instanceof Genesis_Entity_UserAccess) {
            /* Grab the URL and jam into a cookie so we can redirect on login */
            // Only set this if the URL is a GET, otherwise we might end up somewhere
            // and we won't have the parameters in the original POST, causing errors
            // (See mantis 1769)
            AccountMgmt_Service_UserRedirect::setRedirect($_SERVER["REQUEST_URI"]);
            $this->redirect($this->view->url([], 'login'));
        }

        $this->_forceTerms();

        if ($this->getLoggedUser()->isMyFootGod()) {
            Genesis_Util_FilteredIps::insertIp(Genesis_Util_FilteredIps::getRemoteIp(), null, true);
        } else {
            Genesis_Util_FilteredIps::insertIp(Genesis_Util_FilteredIps::getRemoteIp(), 'client');
        }

        $request = new Genesis_Util_Request();
        Genesis_Service_ActivityLog::logMyFootPageview($request, $this->getLoggedUser());

        //fwd them to signup if they either do not have an account or do not have facilities yet
        if ($this->getLoggedUser() && (!$this->getLoggedUser()->getAccountId() || ! $this->getLoggedUser()->getMyfootRole())) {
            if ($this->getRequest()->getControllerName() !== "signup") {
                if ($this->getLoggedUser()->getAccount() && $this->getLoggedUser()->getAccount()->getNumFacilities() == 0) {
                    $this->redirect($this->view->url(['action'=>'type'], 'features'));
                } else if ($this->getRequest()->getControllerName() !== "booking") {
                    $this->redirect('/signup-start');
                }
            }
        }

        //we have an account ID now, but we might not have a valid Facility
        $this->view->accountId = $this->getSession()->accountId;

        $this->view->facilityId = $this->getSession()->facilityId; //used by the menu

        // The controller names a facility editor should have access to
        $this->view->sitesControllers = ['sites', 'settings'];

        //set these so JS can also access
        $authBearerToken = AccountMgmt_Service_UserOauth::getToken();
        if ($authBearerToken) {
            // Unfortunately, we have to reference 'SF_ENV' here since servicesBaseUrl is used many times throughout client-side
            // JS. Also, we can't change getDomain() inside authorization service's code to locally use the FQDN instead of the
            // docker link. This is because we can't reference the FQDN for local authorization service inside MyFoot's container.
            $this->view->servicesBaseUrl = getenv('SERVICES_BASE_URL');
            $this->view->authBearerToken = $authBearerToken->__toString();
        }
    }

    private function _initDateRange()
    {
        if ($this->getParam('date_range')) {
            $this->_session->dateRange = $this->getParam('date_range');
        } elseif (!$this->_session->dateRange) {
            $this->_session->dateRange = 'week';
        }

        $this->view->dateRange = $this->_session->dateRange;

        $this->_endDate   = date('Y-m-d H:i:s');

        switch ($this->_session->dateRange) {
            case 'week':
                $this->_beginDate = date('Y-m-d', strtotime('-1 week'));
                break;
            case 'month':
                $this->_beginDate = date('Y-m-d', strtotime('-1 month'));
                break;
            case 'year':
                $this->_beginDate = date('Y-m-d', strtotime('-1 year'));
                break;
        }

    }

    private function _initTrueDateRange()
    {
        if ($this->getParam('true_date_range')) {
            $this->_session->trueDateRange = $this->getParam('true_date_range');
        } else {
            $this->_session->trueDateRange = date('M j, Y', strtotime('-1 month')) . ' - ' . date('M j, Y');
        }

        $this->view->trueDateRange = $this->_session->trueDateRange;
        $dates = explode(' - ', $this->view->trueDateRange);

        if (count($dates) == 2) {
            $this->_trueBeginDate = $dates[0];
            $this->_trueEndDate = $dates[1] . " 23:59:59";
        } else {
            $this->_trueBeginDate = $dates[0];
            $this->_trueEndDate = $this->_trueBeginDate . " 23:59:59";
        }

        $this->view->trueBeginDate = $this->_trueBeginDate;
        $this->view->trueEndDate = $this->_trueEndDate;
    }

    private function _initUiState()
    {
        $this->view->selectedTab = $this->getTab();

        if ($this->getParam('welcome')) {
            $this->view->welcomeMessage = true;
        }
    }

    /**
     * Initialize controller
     */
    protected function _init()
    {
    }

    private $_loggedUser = false;

    /**
     * @return Genesis_Entity_UserAccess
     */
    protected function getLoggedUser()
    {
        if (! $this->_loggedUser) {
            //the account id the user posed should be in session by now, if they are a god this will modify
            //user access and allow it
            try {
                $this->_loggedUser = AccountMgmt_Service_User::getLoggedUser();
            } catch (Exception $e) {
                //logout user if there is no auth bearer token in cookie
                $message = 'Auth token is invalid or has expired.';
                if (! Genesis_Config_Server::isProduction()) {
                    $message .= "<br/>Exception: " . $e->getMessage();
                }
                $this->getSession()->logoutMessage = $message;
                $this->_helper->getHelper('Redirector')->gotoSimple('logout','login');
            }
        }

        return $this->view->loggedUser = $this->_loggedUser;
    }

    /**
     * @return Zend_Session_Namespace
     */
    protected function getSession()
    {
        if (! $this->_session) {
            $this->view->session = $this->_session = AccountMgmt_Service_User::getSession();
        }

        return $this->_session;
    }

    protected function getBeginDate()
    {
        return $this->_beginDate;
    }

    protected function getEndDate()
    {
        return $this->_endDate;
    }

    protected function getTrueBeginDate()
    {
        return date('Y-m-d H:i:s',strtotime($this->_trueBeginDate));
    }

    protected function getTrueEndDate()
    {
        return date('Y-m-d H:i:s',strtotime($this->_trueEndDate));
    }

    protected function getSideBarContent()
    {
        return '';
    }

    protected function getTab()
    {
        return '';
    }

    protected function dispatchError($messages)
    {
        //$this->view->errorMessages[] = $message;
        $view = new Zend_View();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/error/');
        $view->errorMessages = array($messages);
        echo $view->render('redbox.phtml');
    }

    protected function dispatchSuccess($messages)
    {
        //$this->view->successMessages[] = $message;
        $view = new Zend_View();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/error/');
        $view->successMessages = array($messages);
        echo $view->render('greenbox.phtml');
    }

    /**
     * We need to sanitize the params to prevent xss attacks
     *
     * @param string $paramName
     * @param mixed $default
     * @return mixed
     */
    public function getParam($paramName, $default = null)
    {
        $param = parent::getParam($paramName, $default);
        return is_array($param) ? $param : htmlspecialchars($param, ENT_QUOTES, 'UTF-8');
    }

    public function __destruct()
    {
        $this->_clear();
    }
}
