<?php
/**
 * Base Error Controller
 *
 * @copyright SpareFoot Inc
 * <AUTHOR>
 */
class ErrorController extends Zend_Controller_Action
{
    public function errorAction()
    {
        $acceptType = getallheaders()['Accept'];
        $errors = $this->getParam('error_handler');
        $statusCode = 500;

        switch ($errors->type) {
            case Zend_Controller_Plugin_ErrorHandler::EXCEPTION_NO_CONTROLLER:
            case Zend_Controller_Plugin_ErrorHandler::EXCEPTION_NO_ACTION:
                // 404 error -- controller or action not found
                $statusCode = 404;
                $this->view->message = 'Page not found';
                break;
            default:
                // application error
                $this->view->message = 'Application error';

                $logger = new Zend_Log();
                $writer = new Zend_Log_Writer_Stream('php://stderr');
                $formatter = new Zend_Log_Formatter_Simple();
                $writer->setFormatter($formatter);
                $logger->addWriter($writer);
                $logger->registerErrorHandler();

                $logger->log(
                    json_encode(
                        [
                            'requestURI' => method_exists($this->getRequest(), 'getRequestUri') ? $this->getRequest()->getRequestUri() : null,
                            'message' => $errors->exception->getMessage(),
                            'code' => $errors->exception->getCode(),
                            'file' => $errors->exception->getFile(),
                            'line' => $errors->exception->getLine(),
                            'stacktrace' => $errors->exception->getTraceAsString()
                        ]),
                    Zend_Log::ERR
                );
                break;
        }

        $this->getResponse()->setHttpResponseCode($statusCode);

        // Accept application/json
        if (strpos($acceptType, 'application/json') !== false) {
            $this->getResponse()->setHeader('Content-Type', 'application/json');
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);
            $this->_helper->json([
                'errors' => [
                    [
                        'status' => $statusCode,
                        'title' => ucwords(strtolower($this->view->message))
                    ]
                ]
            ]);
        // Accept all other types including text/html
        } else {
            if (AccountMgmt_Service_User::getLoggedUser()) {
                $this->_helper->layout->setLayout('layout');

                $this->view->loggedUser = AccountMgmt_Service_User::getLoggedUser();
                $this->view->accountId = $this->getParam('account_id') ?? null;
            } else {
                $this->_helper->layout->setLayout('error');
            }

            $this->view->exception = $errors->exception;
            $requestParams = $errors->request->getParams();
            $newRequestParams = [];
            foreach ($requestParams as $key => $value) {
                $newKey = htmlspecialchars($key, ENT_QUOTES, 'UTF-8');
                $newValue = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                $newRequestParams[$newKey] = $newValue;
            }
            $this->view->requestParams = $newRequestParams;
        }
    }
}
