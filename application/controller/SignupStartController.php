<?php

/**
 * SignupStartController
 *
 * @copyright 2013 SpareFoot
 * <AUTHOR>
 */
class SignupStartController extends Zend_Controller_Action
{
    public const SIGNUP_COMPANY_TOKEN = 'signup_company_token';
    public const SIGNUP_CSRF_TOKEN = 'signup_token';
    final public function init()
    {
        $this->_helper->layout->setLayout('signup-layout');
        //give the layout what action we're on (for knowing what step)
        $this->view->action = $this->getParam('action');
    }

    protected function getSession()
    {
        return AccountMgmt_Service_User::getSession();
    }

    public function indexAction()
    {
        $this->redirect('signup-start/code');
    }

    //1st page, enter a signup code
    public function codeAction()
    {
        $this->view->scripts = array('signup-start/code');
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_CSRF_TOKEN);
        $this->view->signupCode = $this->getSession()->code ? $this->getSession()->code->getCode() : '';
        $this->view->userId = $this->getSession()->userId;
    }

    //collect company details
    public function companyAction()
    {

        // IMPORTANT: For only this time, the token for the login process will be generated on another controller
        $this->view->login_csrf_token = CsrfUtil::getToken(LoginController::LOGIN_CSRF_TOKEN);
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_COMPANY_TOKEN);
        $this->view->scripts = array('signup-start/company');
        $this->view->backlink = '/signup-start/code/';

        $this->view->signupCode = $this->getSession()->code ? $this->getSession()->code->getCode() : '';
        $this->view->userId = $this->getSession()->userId;

        //prepopulate if account id (only happens when back button is used)
        $user = Genesis_Service_UserAccess::loadById($this->getSession()->userId);
        $this->view->first = $user ? $user->getFirstName() : $this->getSession()->firstName;
        $this->view->last  = $user ? $user->getLastName() : $this->getSession()->lastName;
        $this->view->phone = $user ? $user->getPhone() : $this->getSession()->phone;
        $this->view->email = $user ? $user->getEmail() : $this->getSession()->email;
        $this->view->userId = $user ? $user->getId() : null;

        $account = false;
        if ($user) {
            $account = $user->getAccount();
        } else {
            if (! $this->getSession()->code) {
                throw new Exception('We cannot determine your sign up code. Please go back and enter a valid sign up code.');
            }
        }
        $this->view->companyName = $account ? $account->getName() : $this->getSession()->companyName;

        $location = false;
        if ($account) {
            $location = $account->getLocation();
        }

        $this->view->address = $location ? $location->getAddress1() : $this->getSession()->companyAddress;
        $this->view->city   = $location ? $location->getCity() : $this->getSession()->companyCity;
        $this->view->state  = $location ? $location->getState() : $this->getSession()->companyState;
        $this->view->zip    = $location ? $location->getZip() : $this->getSession()->companyZip;
    }

    /**
     * Validates the sign-up code.
     *
     * Allows codes like: R3X3,R5X2,C0F5,CN5F9,C52P1
     */
    public function validateCodeAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if (!CsrfUtil::validateToken(self::SIGNUP_CSRF_TOKEN, $this->getParam('csrf_token'))) {
            echo json_encode([
                'success' => false,
                'message' => 'Request structure is invalid. Refresh the page and try again.'
            ]);

            return;
        }

        // Return true if the user already made an account. you can't change terms
        $user = Genesis_Service_UserAccess::loadById($this->getSession()->userId);
        if ($user && $user->getAccount() && $user->getAccount()->getBidType()) {
            echo json_encode(['success' => true]);

            return;
        }

        try {
            $code = new AccountMgmt_Models_SignupCode($this->getParam('signup_code'));
            $this->getSession()->code = $code;
            echo json_encode(['success' => true]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //creates the new account
    public function addCompanyAction()
    {
        $time = date('U');
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {

            $csrfToken = $this->getSession()->csrf_token    = $this->getParam('csrf_token');

            if (!CsrfUtil::validateToken(self::SIGNUP_COMPANY_TOKEN, $csrfToken)) {
                throw new Exception('Request structure is invalid. Refresh the page and try again.');
            }

            $companyName =      $this->getSession()->companyName    = $this->getParam('company_name');
            $companyAddress =   $this->getSession()->companyAddress = $this->getParam('address');
            $companyCity =      $this->getSession()->companyCity    = $this->getParam('city');
            $companyState =     $this->getSession()->companyState   = $this->getParam('state');
            $companyZip =       $this->getSession()->companyZip     = $this->getParam('zip');
            $signupCode =       $this->getSession()->code;
            $password =         trim($this->getParam('password'));
            $passwordConfirm =  trim($this->getParam('password_confirm'));
            $firstName =        $this->getSession()->firstName      = trim($this->getParam('first_name'));
            $lastName =         $this->getSession()->lastName       = trim($this->getParam('last_name'));
            $phone =            $this->getSession()->phone          = preg_replace('/[^0-9]/', '', $this->getParam('phone'));
            $email =            $this->getSession()->email          = trim($this->getParam('email'));

            //if we have a user, and the password does not match... BOOM
            $user = Genesis_Service_User::loadByEmail($email);
            if ($user && Genesis_Service_User::loadByLogin($email, $password) === null) {
                throw new Exception("The account for '{$email}' is associated with a different password.");
            }

            $userAccess = Genesis_Service_UserAccess::loadByEmail($email);
            //check to see if the email used is a god admin account, throw an exception if so

            if ($userAccess && $userAccess->getMyfootRole() === Genesis_Entity_UserAccess::ROLE_GOD) {
                throw new Exception('Admins cannot be signed up as facilities.');
            }
            //this will only exist if they have come back from terms page and are re-submitting
            $userId = $this->getSession()->userId;

            if ($password !== $passwordConfirm) {
                throw new Exception('Passwords are not the same.');
            }
            if (strlen($password) < 6) {
                throw new Exception('Password must be longer than 6 characters.');
            }

            if (! (strlen($companyZip) > 0) || !preg_match("/^\d{5}$|^\d{5}-\d{4}$/", $companyZip)) {
                throw new Exception('Please enter a valid zip code.');
            }

            if (! (strlen($phone) > 0) || preg_match('/[a-zA-Z\.]/', $phone) ||
                ! preg_match('/[0-9]{7,14}/', preg_replace('/[^0-9]/', '', $phone))) {
                throw new Exception($phone . ' is an invalid phone number');
            }

            //create user and user access
            $userAccess = $this->_signupUser(array(
                'email'     => $email,
                'firstName' => $firstName,
                'lastName'  => $lastName,
                'password'  => $password,
                'phone'     => $phone
            ));
            //set the userid in session
            $this->getSession()->userId = $userAccess->getId();

            Genesis_Service_UserAccess::updateLastLoggedIn($userAccess);

            //create a new account as long as they weren't here because of the back button
            if (! $userId || ! $userAccess->getAccount()) {
                $account = new Genesis_Entity_Account();
                $account->setCpa(1);
                $account->setGeopage(0);
                $account->setBillingVerified(0);
                $account->setTimeCreated(date("Y-m-d H:i:s", time()));
                $signupCode->configureAccount($account, $userAccess);
            } else {
                $account = $userAccess->getAccount();
            }

            $account->setName($companyName);

            //create a new location
            $location = Genesis_Service_Location::loadByAddress($companyAddress, $companyCity, $companyState, $companyZip);
            if (!$location) {
                $location = Genesis_Service_Location::geoCodePhysicalAddress($companyAddress . " " . $companyCity . " " . $companyState . " " . $companyZip);
            }

            $location = Genesis_Service_Location::save($location);
            $locationId = $location->getId();
            $account->setLocationId($locationId);

            $account = Genesis_Service_Account::save($account);

            //update account id on user access
            $userAccess->setAccountId($account->getId());
            $userAccess = Genesis_Service_UserAccess::save($userAccess);
            $this->getSession()->userId = $userAccess->getId();

            //log signup
            $logger = new Genesis_Util_ActionLogger();
            $logger->logChanges(new Genesis_Entity_Account(), $account);
            $logger->logAction('account_signup_code', null, $signupCode->getCode(), $userAccess, null, $account->getId());

            echo json_encode(array('success' => true));

        } catch (Exception $e) {
            echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        }
    }

    //create user and user access account
    private function _signupUser(array $params)
    {
        // A user might already exist if this person's booked with SpareFoot before.
        // If this is the case, use the existing user; otherwise, make a new one.
        $user = Genesis_Service_User::load(Genesis_Db_Restriction::equal('email', $params['email']))->uniqueResult();
        if (! $user) {
            $user = new Genesis_Entity_User();
        }

        $user->setUsername(preg_replace("/[^A-Za-z0-9]/", "", $params['email']));
        $user->setFirstName($params['firstName']);
        $user->setLastName($params['lastName']);
        $user->setRawPassword($params['password']);
        $user->setEmail($params['email']);
        $user->setPhone($params['phone']);
        $user = Genesis_Service_User::save($user);

        //only make a user access profile if they don't already have one
        $acctMgmtUser = $user->getUserAccess();
        if (! $acctMgmtUser) {
            $acctMgmtUser = new Genesis_Entity_UserAccess();
            $acctMgmtUser->setUserId($user->getId());
            $acctMgmtUser->setMyfootRole(Genesis_Entity_UserAccess::ROLE_ADMIN); //person signing up should be admin
            $acctMgmtUser->setGetsStatements(1);
            $acctMgmtUser->setGetsEmails(1);
            $acctMgmtUser->setAllFacilities(1);

            //$acctMgmtUser->setHowdYouHear($params['howdYouHear']);
            //$acctMgmtUser->setTermsVersion($params['termsVersion']);
            $acctMgmtUser = Genesis_Service_UserAccess::save($acctMgmtUser);
        } else {
            //TODO: what do we do if they already have a user access profile?
            //could be they used the back button or were formerly employed at another storage company
        }

        return $acctMgmtUser;
    }
}
