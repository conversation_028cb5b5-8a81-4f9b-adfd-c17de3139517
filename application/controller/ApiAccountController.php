<?php
use AccountMgmt_Models_ApiException as ApiException;
class ApiAccountController extends AccountMgmt_Controller_ApiBaseController
{
    public function init()
    {
        parent::init();
    }

    public function indexAction()
    {

        // GET - handle get requests
        if (! $this->_request->isGet()) {
            throw new ApiException(ApiException::NOT_IMPLEMENTED);
        }

        // path: /api/accounts/{ ACCOUNT_ID }
        if ($this->getParam('account_id')) {
            try {
                $account = AccountMgmt_Service_Account::validateAccountId($this->getParam('account_id'));
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            try {
                AccountMgmt_Service_User::validateAccountAccess($account);
            } catch (Exception $e) {
                throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
            }
            $results = [
                'meta' => [
                    'count' => 1
                ],
                'data' => AccountMgmt_Service_Account::toArray($account),
                'links' => []
            ];
            $this->_helper->json($results);
        }

        // path: /api/accounts/
        // if user is not logged in, not allowed to get all
        $user = $this->getLoggedUser();
        if ($user && !$user->isMyFootGod()) {
            throw new ApiException(ApiException::FORBIDDEN);
        }

        $accounts = Genesis_Dao_Account::selectBriefAccountsForMenu();
        $results = [
            'meta' => [
                'count' => count($accounts)
            ],
            'data' => $accounts,
            'links' => []
        ];

        $this->_helper->json($results);
    }

    public function specialsAction()
    {
        if (! $this->_request->isGet()) {
            throw new ApiException(ApiException::NOT_IMPLEMENTED);
        }
        // path: /api/accounts/{ ACCOUNT_ID }/specials
        $response = [];
        $accountId = $this->_getParam('account_id');
        $type = $this->_getParam('type');
        try {
            $account = AccountMgmt_Service_Account::validateAccountId($accountId);
        } catch (Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }

        try {
            AccountMgmt_Service_User::validateAccountAccess($account);
        } catch (Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
        }

        $accountSpecials = Genesis_Service_Special::loadByAccountId($account->getId());
        foreach ($accountSpecials as $special) {
            if ($special->getDefault()) {
                continue;
            }
            switch ($type) {
                case 'discount':
                    if ($special->isDiscount()) {
                        $response[] = AccountMgmt_Service_Unit::serializeUnitSpecial($special);
                    }
                    break;
                case 'promo':
                    if ($special->isPromo() && $type != 'discount') {
                        $response[] = AccountMgmt_Service_Unit::serializeUnitSpecial($special);
                    }
                    break;
                default:
                    $response[] = AccountMgmt_Service_Unit::serializeUnitSpecial($special);
            }
        }
        $this->_helper->json(['data' => $response]);
    }
}
