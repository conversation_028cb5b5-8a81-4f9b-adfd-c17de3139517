<?php
/*
 * Customer Controller
 *
 * @copyright 2015 SpareFoot Inc
 * <AUTHOR>
 */
use AccountMgmt_Models_BidIncreaseBannerValidation;
class CustomerController extends AccountMgmt_Controller_Restricted
{
    protected function _init()
    {
        // Redirect if they have no manageable facilities
        if (!count($this->getLoggedUser()->getManagableFacilities())) {
            $this->redirect($this->view->url(['action' => 'add-first'], 'features'));
        }
        $this->view->hasOnlineMoveInFmsSoftware = AccountMgmt_Service_Account::accountHasFmsSupportingOnlineMoveins($this->getLoggedUser()->getAccount());

        $this->view->banner = [
            'showMoveInsBanner' => Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::MOVE_IN_BANNER, []),
            'showMoveInOnlineBanner' => Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::MOVE_IN_ONLINE_BANNER, []),
            'showNotificationBanner' => AccountMgmt_Models_BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount())
        ];
    }

    public function indexAction()
    {
        $this->redirect($this->view->url(['action'=>'reservations'], 'customers'));
    }

    public function inquiriesAction()
    {
        $this->view->facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->inquiries = $this->_fetchInquiries();
        $this->view->scripts = array('facility/reservations');
    }

    // Single Page App Logic - Below
    // TODO: Can we dry this up? All URL requests to "customers/:anything" handled by one "action"
    public function tenantsAction()
    {
        $this->forward('reservations');
    }

    public function reservationsAction()
    {
        $facilityId = $this->getSession()->facilityId;
        $this->view->facility = Genesis_Service_Facility::loadById($facilityId);
        $this->view->includeTenantConnectCalls = ($this->view->facility->getTenantConnect() == 1);
        $reservations = $this->_fetchReservationsData($this->view->includeTenantConnectCalls);
        $this->view->reservations = $reservations;
        $this->view->bookingMeta = $this->_fetchBookingMeta($reservations);
    }

    /**
     * Fetch and organize all of the data to populate the reservations screen
     *
     * @return array
     */
    private function _fetchReservationsData($includeTenantConnectCalls = false)
    {
        $startDate = $this->getTrueBeginDate();
        $endDate   = $this->getTrueEndDate();

        $facilityId = $this->getSession()->facilityId;

        return Genesis_Service_Transaction::loadByFacility($facilityId, $startDate, $endDate, $includeTenantConnectCalls, Genesis_Db_Order::desc('timestamp'));
    }

    private function _fetchBookingMeta($reservations)
    {
        $bookingMeta = [];
        foreach($reservations as $reservation) {
            $confCode = $reservation['confirmation_code'];
            $billingPriceMeta = Genesis_Service_BookingMeta::loadByConfirmationCodeAndKey(
                    $confCode, Genesis_Entity_BookingMeta::BILLING_PRICE_FOR_TOS_BILLING
                )->current();
            $discountPriceMeta = Genesis_Service_BookingMeta::loadByConfirmationCodeAndKey(
                    $confCode, Genesis_Entity_BookingMeta::DISCOUNT_PRICE_FOR_TOS_BILLING
                )->current();
            $bookingMeta[$confCode] = [
                'billing_price' => $billingPriceMeta ? $billingPriceMeta->getValue() : null,
                'discount_price' => $discountPriceMeta ? $discountPriceMeta->getValue() : null
            ];
        }
        return $bookingMeta;
    }

    private function _fetchInquiries()
    {
        $startDate = $this->getTrueBeginDate();
        $endDate   = $this->getTrueEndDate();

        $facilityId = $this->getSession()->facilityId;

        return Genesis_Service_Inquiry::loadUniqueByFacilityIdAndDateRange($facilityId, $startDate, $endDate);
    }
}
