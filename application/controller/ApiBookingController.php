<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 7/29/15
 * Time: 3:38 PM
 */
use AccountMgmt_Models_ApiException as ApiException;

class ApiBookingController extends AccountMgmt_Controller_ApiBaseController{

    public function indexAction() {
        $confirmation_code = $this->getParam('confirmation_code');
        $booking = $this->validateAndGetBooking($confirmation_code);
        $facility_id = $this->getParam('facility_id');
        if( $facility_id && $booking->getFacilityId() != $facility_id ) {
            throw new ApiException(ApiException::BAD_REQUEST,'Booking does not belong to given facility');
        }

        $json_body = $this->getRequest()->getRawBody();
        if ($this->_request->isGet()) {
            $this->_helper->json(['data' => AccountMgmt_Service_Booking::toArray($booking)]);
        }

        if($this->_request->isPut()) {
            $this->_helper->json(['data' =>AccountMgmt_Service_Booking::updateFromJson($booking, $json_body)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function validateAndGetBooking($confirmation_code) {
        try {
            $booking = AccountMgmt_Service_Booking::validateConfirmationCode($confirmation_code);
        } catch (Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }
        try {
            $facility = $booking->getFacility();
            AccountMgmt_Service_User::validateFacilityAccess($facility);
        } catch (Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED);
        }

        return $booking;
    }
}