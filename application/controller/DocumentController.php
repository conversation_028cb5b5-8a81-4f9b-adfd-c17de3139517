<?php
/*
 * Account Controller
 *
 * @copyright 2009 SpareFoot Inc
 * <AUTHOR> <PERSON>
 */

class DocumentController extends AccountMgmt_Controller_Restricted
{
    /**
     * Terms Addendum action
     */
    public function termsAddendumAction()
    {
        $account = $this->getLoggedUser()->getAccount();

        // If account has no terms addendum, then send them back home
        if (! $account->getTermsAddendum()) {
            throw new \InvalidArgumentException('No terms addendum PDF available for account ' . $account->getSfAccountId());
        }

        // Determine filename and absolute path to the terms addendum file
        $absFilePath = APPLICATION_PATH . "/views/pdf/terms-addenda/{$account->getSfAccountId()}.pdf";

        $this->showPdf($absFilePath, $account->getSfAccountId(), 'terms addendum');
    }

    public static function isValidLtvVersion($termsVersion)
    {
        $validLtvVersions = [
            '********',
            '********',
            '********',
            '********',
            '********'
        ];

        return in_array($termsVersion, $validLtvVersions);
    }

    /**
     * Terms action
     */
    public function termsAction()
    {
        $account = $this->getLoggedUser()->getAccount();

        if ($account->getId() != 16) { // not cubesmart
            $domain = Genesis_Config_Server::getEnvDomain();
            $termsVersion = $account->getTermsVersion();
            if (!$termsVersion) {
                // This should never happen, but show a link at least.
                $url = "https://www.sparefoot.{$domain}/legal/client.html";
            } else {
                $termsProgram = 'client';
                if ($account->getBidType() == Genesis_Entity_Account::BID_TYPE_RESIDUAL
                && self::isValidLtvVersion($termsVersion)) {
                    $termsProgram = 'ltv';
                }
                $url = "https://www.sparefoot.{$domain}/legal/{$termsProgram}/{$termsVersion}.html";
            }
            $this->redirect($url);
            return;
        }

        // Determine filename and absolute path to the terms addendum file
        $absFilePath = APPLICATION_PATH . "/views/pdf/tos/{$account->getSfAccountId()}.pdf";

        $this->showPdf($absFilePath, $account->getSfAccountId(), 'terms of service');
    }

    private function showPdf($absFilePath, $accountId, $description)
    {
        // Ensure the file exists
        if (! file_exists($absFilePath)) {
            throw new \InvalidArgumentException("No '$description' PDF available for account $accountId");
        }

        // Read file contents
        $content = file_get_contents($absFilePath);
        if (! $content) {
            throw new Exception("Empty '$description' PDF detected for account $accountId");
        }

        // Disable view rendering
        $this->getHelper('viewRenderer')->setNoRender(true);
        $this->getHelper('layout')->disableLayout();

        // Send PDF file response
        $this->getResponse()
            ->setHeader('Content-Length', filesize($absFilePath))
            ->setHeader('Content-Type', 'application/pdf')
            ->setBody($content);
    }
}
