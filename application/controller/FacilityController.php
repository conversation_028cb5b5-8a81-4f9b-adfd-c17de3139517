<?php
/*
 * Facility Controller
 *
 * @copyright 2009 SpareFoot Inc
 * <AUTHOR>
 */

use AccountMgmt_Models_ApiException as ApiException;
use AccountMgmt_Models_BidIncreaseBannerValidation;
use BidOptimizer_Clients_BidOptimizerClient as BidOptimizerClient;
use \GuzzleHttp\Client;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Sparefoot\ServiceBundle\Stats\StatsInterface;
use Sparefoot\ServiceBundle\Stats\StatsD;
use DataDog\DogStatsd;
use \GuzzleHttp\Exception\BadResponseException;
use Psr\Http\Message\ResponseInterface;
use MoveinsController;

class FacilityController extends AccountMgmt_Controller_Restricted
{
    public const ADD_FACILITY_CSRF_TOKEN = 'add_facility_token';
    public const AMENITIES_CSRF_TOKEN = 'amenities_csrf_token';
    public const UPDATE_BID_CSRF_TOKEN = 'update_bid_csrf_token';
    public const RANK_BID_CSRF_TOKEN = 'rank_bid_csrf_token';
    
    /** @var StatsInterface $statsClient */
    private $statsClient;

    /** @var Client */
    private $phidoClient;

    const ADD_CS4_FACILITY_TOKEN = 'add_cs4_token';

    const PERRYVILLE_DUMMY_LOCATION = ********;

    protected function _init()
    {
        //redirect if its not a fac signup route, but they have no manageable facilities
        if (! count($this->getLoggedUser()->getManagableFacilities())) {
            switch ($this->getRequest()->getActionName()) {
                case 'add-first':
                case 'type':
                case 'selectionhandler':
                case 'addcentershift4':
                case 'synccentershift4': //sync button in signup
                case 'addsummary':
                case 'add': //edomico
                case 'addquickstore':
                case 'addselfstoragemanager':
                case 'syncselfstoragemanager': //sync button in signup
                case 'addsitelink':
                case 'addstoredge':
                case 'syncsitelink': //sync button in signup
                case 'syncstoredge': //sync button in signup
                case 'synceasystoragesolutions': //sync button in signup
                case 'addeasystoragesolutions':
                case 'add_facility':
                    break;
                default:
                    $this->redirect($this->view->url(['action' => 'add-first'], 'features'));
            }
        }

        //bypass this for JS only routes and excel, append for all other routes
        switch ($this->getRequest()->getActionName()) {
            case 'add_facility':
            case 'update_facility':
            case 'addfacilityclosure':
            case 'removefacilityclosure':
            case 'synccentershift4':
            case 'syncissn':
            case 'syncselfstoragemanager':
            case 'syncsitelink':
            case 'syncstoredge':
            case 'synceasystoragesolutions':
            case 'export':
            case 'exportbidopps':
            case 'unitexport':
            case 'save_bid':
            case 'update_bid':
            case 'close_bid_modal':
            case 'check_site_verify':
            case 'resync':
            case 'download_bid_opportunities':
            case 'poll_bid_opps_report':
            case 'bid_opps_report':
                break;
            default:
                $this->getResponse()->appendBody(
                '<script> var controller = "/features/";</script>'
                );
        }

        if ($this->getParam('search_term')) {
            $this->getSession()->searchTerm = $this->getParam('search_term');
        }

        if ($this->getParam('clear_search_term')) {
            $this->getSession()->searchTerm = null;
        }

        $this->statsClient = new StatsD(new DogStatsd());
    }

    public function redirectoldroutesAction() {
        $fid = $this->getParam('fid');
        $actionName = $this->getParam('actionName');
        $this->redirect('/facilities/'.$fid.'/'.$actionName);
    }

    public function setCommonViewFields() {
        $this->view->hasOnlineMoveInFmsSoftware = AccountMgmt_Service_Account::accountHasFmsSupportingOnlineMoveins($this->getLoggedUser()->getAccount());

        $this->view->banner = [
            'showMoveInsBanner' => Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::MOVE_IN_BANNER, []),
            'showMoveInOnlineBanner' => Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::MOVE_IN_ONLINE_BANNER, []),
            'showCovidMsgBanner' => Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::COVID_BANNER),
            'showNotificationBanner' => AccountMgmt_Models_BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount())
        ];
    }

    public function indexAction()
    {
        //$this->getSession()->facilityId = null;

        // Set up an interstitial to display once per session.
        if (Genesis_Service_Feature::isActive ( 'bidInterstitial' )) {
            if (! isset($this->getSession()->showBidInterstitial)) {
                $this->getSession()->showBidInterstitial = true;
            } elseif ($this->getSession()->showBidInterstitial) {
                $this->getSession()->showBidInterstitial = false;
            }
            $this->view->showBidInterstitial = $this->getSession()->showBidInterstitial;
        }
        //$this->view->showBidInterstitial = TRUE;

        $facilities = $this->getLoggedUser()->getAccount()->getFacilities();

        if (count($facilities->toArray()) == 0) {
            $this->redirect($this->view->url(['action'=>'add-first'], 'features'));
        } else {
            $this->redirect($this->view->url(['action'=>'units'], 'features'));
        }
    }

    public function addFirstAction()
    {
        if ($this->getLoggedUser()->getAccount()->getNumFacilities()) {
            $this->redirect($this->view->url(['action'=>'type'], 'features'));
        }
    }

    public function closebidmodalAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        Genesis_Service_ActionMeta::associateBid($_POST['action_id'], $_POST['current_pos'], $_POST['suggested_pos'], 0);
    }

    /**
     * List all facilities that logged user has access to.
     */
    public function listAction()
    {
        if (AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::OVERVIEW_SINGLE_PAGE)) {
            $this->_helper->layout->setLayout('layout-singlepage');
            $this->_helper->viewRenderer->setNoRender(true);

            $this->view->scripts = [
                '../dist/ember/features/assets/vendor',
                '../dist/ember/features/assets/features'
            ];
        } else {
            $userAccess = $this->getLoggedUser()->getUserAccess();
            //clear session data when they hit the list page
            $this->getSession()->facIds = null;

            $searchTerm = $this->getSession()->searchTerm;
            $this->view->searchTerm = $searchTerm;
            $this->view->loggedUser = $userAccess;

            $restriction = Genesis_Db_Restriction::equal('published', 1);

            if ($searchTerm) {
                $restriction = Genesis_Db_Restriction::and_(
                    $restriction,
                    Genesis_Db_Restriction::or_(
                        Genesis_Db_Restriction::like('title', '%'.$searchTerm .'%'),
                        Genesis_Db_Restriction::like('companyCode', '%'.$searchTerm .'%')
                    )
                );
            } else {
                $page = 1;
                $limit = 20;

                $limitParam = preg_replace('[^0-9]', '', $this->getParam('limit'));
                if ($limitParam && $limitParam > 0) {
                    $limit = $limitParam;
                }
                $pageParam = preg_replace('[^0-9]', '', $this->getParam('page'));
                if ($pageParam > 0)  {
                    $page = $pageParam;
                }

                // Get Number of Facilities (published)
                $facilityIds = $this->getLoggedUser()->getManageableFacilityIds(null, false);
                $totalItems = count($facilityIds);
                $offset = ($page - 1) * $limit;

                $restriction->setLimit(Genesis_Db_Limit::limitOffset($limit, $offset));

                $this->view->page = $page;
                $this->view->limit = $limit;
                $this->view->totalItems = $totalItems;
            }
            $restriction->setOrder(Genesis_Db_Order::asc('title'));

            $this->view->masterSwitch = null;

            $facilities = $this->_fetchFacilitiesData($restriction);
            usort($facilities, function ($a, $b) {
                return strcmp($a['title'], $b['title']);
            });
            $this->view->facilities = $facilities;
            $this->view->title = 'Facilities';
        }

        $this->view->accountId = $this->getParam('account_id') ?? null;

        $this->setCommonViewFields();
    }

    // path: features/units?fid={facility_id}
    public function inventoryAction()
    {
        //go to the new page if FF is on
        $this->view->facility = $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);

        $integrationId = $facility->getCorporation()->getSourceId();

        $isFSS = $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
        if ($integrationId == Genesis_Entity_Source::ID_MANUAL
            || $isFSS) {
            $this->redirect($this->view->url([],'features-listings').'?fid='.$facility->getId());
        }

        $this->view->integratedFields = $this->_getIntegratedFields();

        // Sitelink or Centershift 4 account then load grouped unit view
        if ($facility->canGroupUnits()) {
            $this->forward('groupedinventory');
            return;
        }

        $this->view->promoSync = (int) $facility->getCorporation()->getPullPromos();
        $this->view->sourceType = $integrationId;
        $this->view->inventory = $this->_fetchInventoryData();
        $this->view->customClosuresBlogPost = "https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility";
        $this->view->covidModal = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::COVID_MODAL);
        $this->view->customClosures = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::CUSTOM_CLOSURES);
        $this->view->isBidOptimizerActive = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::BID_OPTIMIZER); //added check

        if (!$this->view->inventory) {
            /* If no units are returned, check to see if there are truly no
             * units associated with that facility, or if the facility has units,
             * but they're publish = 0.
             */

            $units = Genesis_Service_StorageSpace::load(
                Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::equal('facilityId', $this->getSession()->facilityId),
                    Genesis_Db_Restriction::equal('publish', 0)
                )
            );
            if ($units->current()) {
                $this->view->unpublishedUnits = 1;
            } else {
                $this->view->unpublishedUnits = 0;
            }
        }

        $this->setCommonViewFields();

        $this->view->scripts = [
            'facility/global-functions',
            'facility/hide-facility-reason-modal',
            'inventory/units-shared',
            'facility/inventory'
        ];
    }

    public function groupedinventoryAction()
    {
        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $integrationId = $facility->getCorporation()->getSourceId();

        if (!isset($this->view->integratedFields)) {
            $this->view->integratedFields = $this->_getIntegratedFields();
        }

        $this->view->customClosuresBlogPost = "https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility";
        $this->view->covidModal = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::COVID_MODAL);
        $this->view->customClosures = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::CUSTOM_CLOSURES);
        $this->view->isBidOptimizerActive = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::BID_OPTIMIZER);

        $this->view->facility  = $facility;
        $this->view->sourceType = $integrationId;
        $this->view->promoSync = (int) $facility->getCorporation()->getPullPromos();
        $this->view->passedIds = null;

        $this->setCommonViewFields();

        //if there are specific unit ids then load individual for those
        $unitIds = $this->getParam('unitIds') ?? null;
        if ($unitIds) {
            $this->view->passedIds = $unitIds;
            $this->view->inventory = $this->_fetchInventoryData($unitIds);

            if (!$this->view->inventory) {
                $units = Genesis_Service_StorageSpace::load(
                    Genesis_Db_Restriction::and_(
                        Genesis_Db_Restriction::equal('facilityId', $this->view->facility->getId()),
                        Genesis_Db_Restriction::equal('publish', 0)
                    )
                );
                if ($units->current()) {
                    $this->view->unpublishedUnits = 1;
                } else {
                    $this->view->unpublishedUnits = 0;
                }
            }
        }

        // getGroupedUnits uses the Genesis_Dao_StorageSpace::selectGroupedByFacility() which uses the Facility's
        // Integration type to choose the final SQL query method. The Group is made here in the Facility level.
        $this->view->groupedUnits = $facility->getGroupedUnits(true);

        //map the groups and get some extra info
        /**
         * @var $gUnit Genesis_Entity_StorageSpace
         */
        foreach ($this->view->groupedUnits as $gUnit) {
            //we have stored unit id's and names in the id field, parse out.  Ex:
            $id = uniqid();

            if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER
                && $gUnit->getGroupedNumAvailable() < 1 ) {
                continue;
            }

            //instantiate to make sure all indices are there
            $gUnitDetails[$id]['source_id'] = $facility->getCorporation()->getSourceId();
            $gUnitDetails[$id]['driveUp'] = null;
            $gUnitDetails[$id]['vehicle'] = null;
            $gUnitDetails[$id]['door_w'] = null;
            $gUnitDetails[$id]['door_h'] = null;
            $gUnitDetails[$id]['climate'] = null;
            $gUnitDetails[$id]['humidity'] = null;
            $gUnitDetails[$id]['alarm'] = null;
            $gUnitDetails[$id]['power'] = null;
            $gUnitDetails[$id]['outdoorAccess'] = null;
            $gUnitDetails[$id]['doorType'] = null;
            $gUnitDetails[$id]['covered'] = null;

            $gUnitDetails[$id]['id']                 = $gUnit->getId();
            $gUnitDetails[$id]['dimensions']         = $gUnit->stringDimensions(false);
            $gUnitDetails[$id]['export_dimensions']  = $gUnit->stringDimensions(false);
            $gUnitDetails[$id]['type']               = $gUnit->stringType();
            $gUnitDetails[$id]['type_num']           = $gUnit->getType();
            $gUnitDetails[$id]['amenities']          = $gUnit->stringAmenities();
            $gUnitDetails[$id]['floor']              = $gUnit->stringFloor();
            $gUnitDetails[$id]['rawFloor']           = $gUnit->getFloor();
            $gUnitDetails[$id]['sparefoot_price']    = "";
            if ($gUnit->getSparefootPrice() > 0) {
                $gUnitDetails[$id]['sparefoot_price']    = $gUnit->getSparefootPrice();
            }
            $gUnitDetails[$id]['list_price']         = $gUnit->getRegularPrice();
            $gUnitDetails[$id]['reservation_days']   = $gUnit->getReservationDays();
            $gUnitDetails[$id]['quantity']           = $gUnit->getQuantity();
            $gUnitDetails[$id]['approved']           = $gUnit->getApproved() ? true : false;
            $gUnitDetails[$id]['published']          = $gUnit->getPublish() ? true : false;

            // special stuff just for grouped units
            $gUnitDetails[$id]['groupIdsStr'] =
                is_array($gUnit->getGroupedUnitIds())
                    ? implode(", ", $gUnit->getGroupedUnitIds())
                    : "";

            if ($gUnitDetails[$id]['groupIdsStr']) {

                $groupUnits = Genesis_Service_StorageSpace::load(
                    Genesis_Db_Restriction::in('id', $gUnit->getGroupedUnitIds())
                    // ->setOrder(Genesis_Db_Order::asc('unitName'))
                )->toArray();

                // sort by unit name
                // this should be done above when loading the list from the db but
                // unit_name is currently a supplemental field and thus cannot be sorted by
                // TODO: add unit_name as a column to listing_avail_space_2
                usort($groupUnits, function(
                    Genesis_Entity_StorageSpace $a,
                    Genesis_Entity_StorageSpace $b
                ) {
                    return strcmp($a->getUnitName(), $b->getUnitName());
                });

                $gUnitDetails[$id]['groupUnits'] = $groupUnits;
            }

            $gUnitDetails[$id]['unit_names'] =
                is_array($gUnit->getGroupedUnitNames())
                    ? implode(", ", $gUnit->getGroupedUnitNames())
                    : "";
            $gUnitDetails[$id]['classType'] =
                is_array($gUnit->getGroupedApiTypes())
                    ? implode(", ", $gUnit->getGroupedApiTypes())
                    : ""
            ;
            $gUnitDetails[$id]['numRentable']        = $gUnit->getGroupedNumAvailable();

            if ($gUnit->getActive() == 1) {
                $gUnitDetails[$id]['hidden']         = false; // active in frontends; won't actually appear unless publish=1
            } else {
                $gUnitDetails[$id]['hidden']         = true;
            }

            $gUnitDetails[$id]['deposit']          = $gUnit->getDeposit();
            $gUnitDetails[$id]['unit_w']           = $gUnit->getWidth();
            $gUnitDetails[$id]['unit_l']           = $gUnit->getLength();
            $gUnitDetails[$id]['unit_h']           = $gUnit->getHeight();

            $gUnitDetails[$id]['special']          = $gUnit->getSpecialString();
            $gUnitDetails[$id]['desc']             = $gUnit->getDescription();

            // Supp unit data
            $gUnitDetails[$id]['stacked']          = $gUnit->getSkybox();
            $gUnitDetails[$id]['premium']          = $gUnit->getPremiumUnit();
            $gUnitDetails[$id]['heated']           = $gUnit->getHeatedOnly();
            $gUnitDetails[$id]['aircooled']        = $gUnit->getAirCooledOnly();
            $gUnitDetails[$id]['ada']              = $gUnit->getAdaAccessible();
            $gUnitDetails[$id]['unitlights']       = $gUnit->getUnitLights();
            $gUnitDetails[$id]['twentyfourhouraccess']  = $gUnit->getTwentyFourHourAccess();

            $gUnitDetails[$id]['shelvesinunit']     = $gUnit->getShelvesInUnit();
            $gUnitDetails[$id]['basement']          = $gUnit->getBasement();
            $gUnitDetails[$id]['parkingwarehouse']  = $gUnit->getParkingWarehouse();
            $gUnitDetails[$id]['pullthru']          = $gUnit->getPullThrough();

            $gUnitDetails[$id]['sitelinkunit']                 = 'N';
            if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SITELINK) {
                $gUnitDetails[$id]['sitelinkunit']                 = 'Y';
            }

            // TODO: enable this once we also update UI to match
//            // special handling for integrations that give us a record for each individual unit, but only the rentable ones
//            if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) {
//                // what we think is quantity is actually num rentable
//                $gUnitDetails[$id]['numRentable'] = $gUnitDetails[$id]['quantity'];
//                // and we dont know real quantity
//                $gUnitDetails[$id]['quantity'] = null;
//            }

            //this section does not apply to parking or outdoor
            if ($gUnit->getType() != Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE &&
                $gUnit->getType() != Genesis_Entity_StorageSpace::TYPE_OUTDOOR) {
                $gUnitDetails[$id]['door_w'] = $gUnit->getDoorWidth();
                $gUnitDetails[$id]['door_h'] = $gUnit->getDoorHeight();

                if ($gUnit->getClimateControlled() == 1) { $gUnitDetails[$id]['climate'] = true; } else { $gUnitDetails[$id]['climate'] = false; }
                if ($gUnit->getHumidityControlled() == 1) { $gUnitDetails[$id]['humidity'] = true; } else { $gUnitDetails[$id]['humidity'] = false; }
                if ($gUnit->getAlarm() == 1) { $gUnitDetails[$id]['alarm'] = true; } else { $gUnitDetails[$id]['alarm'] = false; }
                if ($gUnit->getPower() == 1) { $gUnitDetails[$id]['power'] = true; } else { $gUnitDetails[$id]['power'] = false; }
                if ($gUnit->getOutdoorAccess() == 1) { $gUnitDetails[$id]['outdoorAccess'] = true; } else { $gUnitDetails[$id]['outdoorAccess'] = false; }
                if ($gUnit->getDoorType() == Genesis_Entity_StorageSpace::DOOR_ROLL_UP) {
                    $gUnitDetails[$id]['doorType'] = 'roll_up';
                } elseif ($gUnit->getDoorType() == Genesis_Entity_StorageSpace::DOOR_SWING) {
                    $gUnitDetails[$id]['doorType'] = 'swing';
                } else {
                    $gUnitDetails[$id]['doorType'] = 'none';
                }
            } elseif ($gUnit->getType() == Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE) {
                if ($gUnit->getPower() == 1) { $gUnitDetails[$id]['power'] = true; } else { $gUnitDetails[$id]['power'] = false; }
            }

            //this section does not apply to parking, wine or locker
            if ($gUnit->getType() != Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE &&
                $gUnit->getType() != Genesis_Entity_StorageSpace::TYPE_LOCKER &&
                $gUnit->getType() != Genesis_Entity_StorageSpace::TYPE_WINE) {
                if ($gUnit->getDriveUp() == 1) { $gUnitDetails[$id]['driveUp'] = true; } else { $gUnitDetails[$id]['driveUp'] = false; }
                if ($gUnit->getVehicle() == 1) { $gUnitDetails[$id]['vehicle'] = true; } else { $gUnitDetails[$id]['vehicle'] = false; }
            }

            //covered does not apply to wine or locker
            if ($gUnit->getType() != Genesis_Entity_StorageSpace::TYPE_LOCKER &&
                $gUnit->getType() != Genesis_Entity_StorageSpace::TYPE_WINE &&
                $gUnit->getType() != Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT) {
                $gUnitDetails[$id]['covered'] = $gUnit->getCovered();
            }

        }

        if (isset($gUnitDetails)) {
            $this->view->groupedUnits = $gUnitDetails;
        }

        $this->view->scripts = [
            'facility/global-functions',
            'facility/hide-facility-reason-modal',
            'inventory/units-shared',
            'facility/inventory'
        ];
    }

    public function unitexportAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $facility = Genesis_Service_Facility::loadById($this->getParam('fid'));
        if (! $facility) {
            return;
        }
        header('Content-type: text/csv');
        header('Content-disposition:  attachment; filename="'.$this->getLoggedUser()->getAccount()->getName() . ' ' . date("Y-m-d") .' unitExport.csv"');

        $out = '';

        //add more info for sitelink
        if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SITELINK) {
            $headers = array('Unit Name','SiteLink Type','Active/Hidden', 'Approved?', 'Size', 'Sparefoot Type', 'Amenities', 'Floor', 'SpareFoot Price', 'List Price', 'Unit Promo?');
        } else {
            $headers = array('Active/Hidden', 'Approved?', 'Size', 'Type', 'Amenities', 'Floor', 'SpareFoot Price', 'List Price', 'Unit Promo?', 'Quantity');
        }

        $out .= '"' . implode('","', $headers) . '"' . "\n";

        $facilityName = $facility->getTitle();
        $inventory = $this->_fetchInventoryData();

        if (!$inventory) {
            echo $out;

            return;
        }

        //add more info for sitelink
        if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SITELINK) {
            foreach ($inventory as $unit) {
                $unitArr = array(
                    $unit['unitName'],
                    $unit['classType'],
                    (($unit['hidden'] == 1) ? 'Hidden' : 'Active'),
                    ($unit['approved'] ? 'Yes' : 'No'),
                    $unit['export_dimensions'],
                    $unit['type'],
                    $unit['amenities'],
                    $unit['floor'],
                    $unit['sparefoot_price'],
                    $unit['list_price'],
                    ($unit['special'] ? 'Yes' : 'No'),
                );
                $out .= '"' . implode('","', $unitArr) . '"' . "\n";
            }
        } else {
            foreach ($inventory as $unit) {
                $unitArr = array(
                    (($unit['hidden'] == 1) ? 'Hidden' : 'Active'),
                    ($unit['approved'] ? 'Yes' : 'No'),
                    $unit['export_dimensions'],
                    $unit['type'],
                    $unit['amenities'],
                    $unit['floor'],
                    $unit['sparefoot_price'],
                    $unit['list_price'],
                    ($unit['special'] ? 'Yes' : 'No'),
                    $unit['quantity']
                );
                $out .= '"' . implode('","', $unitArr) . '"' . "\n";
            }
        }

        echo $out;
    }

    private function getUnitsRestriction(
        $facilityIdColumnKey,
        Genesis_Entity_Facility $facility,
        $unitIdColumnKey,
        $unitIds
    ) {
        if ($unitIds) {
                $restriction = Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::equal($facilityIdColumnKey, $facility->getId()),
                    Genesis_Db_Restriction::in($unitIdColumnKey, explode(",", $unitIds))
                );
        } else {
            $restriction = Genesis_Db_Restriction::equal($facilityIdColumnKey, $facility->getId());
        }
        return $restriction;
    }

    /**
     * Fetch and organize all of the data to populate the inventory screen
     *
     * @param array $unitIds
     * @return array
     * @throws Exception
     */
    private function _fetchInventoryData($unitIds = null)
    {
        $facilityId = $this->getSession()->facilityId;

        /** @var Genesis_Entity_Facility $facility */
        $facility = Genesis_Service_Facility::loadById($facilityId);

        switch ($facility->getCorporation()->getSourceId()) {
            case Genesis_Entity_Source::ID_SITELINK:
                $units = Genesis_Service_SitelinkFullUnit::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'unitId',
                        $unitIds
                    )
                );
                break;
            case Genesis_Entity_Source::ID_CENTERSHIFT4:
            case Genesis_Entity_Source::ID_CENTERSHIFT4_LEADS360:
                $units = Genesis_Service_Centershift4Unit::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'id',
                        $unitIds
                    )
                );
                break;
            case Genesis_Entity_Source::ID_SELFSTORAGEMANAGER:
                $units = Genesis_Service_SelfStorageManagerUnit::load(
                    $this->getUnitsRestriction(
                        'listingAvailId',
                        $facility,
                        'listingAvailSpaceId',
                        $unitIds
                    )
                );
                break;
            case Genesis_Entity_Source::ID_DOORSWAP:
                $units = Genesis_Service_DoorswapUnit::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'id',
                        $unitIds
                    )
                );
                break;
            case Genesis_Entity_Source::ID_STOREDGE:
                $units = Genesis_Service_StoredgeUnit::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'id',
                        $unitIds
                    )
                );
                break;
            case Genesis_Entity_Source::ID_ESS:
                $units = Genesis_Service_StorageSpace::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'id',
                        $unitIds
                    )
                );
                break;

            default:
                $units = $facility->getUnits();
        }

        if ($units) {

            foreach ($units as $unit) {
                $id = $unit->getId();

                //some instantiation for ones that don't always get set
                $unitDetails[$id]['driveUp'] = null;
                $unitDetails[$id]['vehicle'] = null;
                $unitDetails[$id]['door_w'] = null;
                $unitDetails[$id]['door_h'] = null;
                $unitDetails[$id]['climate'] = null;
                $unitDetails[$id]['humidity'] = null;
                $unitDetails[$id]['alarm'] = null;
                $unitDetails[$id]['power'] = null;
                $unitDetails[$id]['outdoorAccess'] = null;
                $unitDetails[$id]['doorType'] = null;
                $unitDetails[$id]['covered'] = null;

                $unitDetails[$id]['id']                 = $unit->getId();
                $unitDetails[$id]['dimensions']         = $unit->stringDimensions(false);
                $unitDetails[$id]['export_dimensions']  = $unit->stringDimensions(false);
                $unitDetails[$id]['type']               = $unit->stringType();
                $unitDetails[$id]['type_num']           = $unit->getType();
                $unitDetails[$id]['amenities']          = $unit->stringAmenities();
                $unitDetails[$id]['floor']              = $unit->stringFloor();
                $unitDetails[$id]['rawFloor']           = $unit->getFloor();
                $unitDetails[$id]['sparefoot_price']    = "";
                if ($unit->getSparefootPrice() > 0) {
                    $unitDetails[$id]['sparefoot_price']    = $unit->getSparefootPrice();
                }
                $unitDetails[$id]['list_price']         = $unit->getRegularPrice();
                $unitDetails[$id]['quantity']           = $unit->getQuantity();
                $unitDetails[$id]['approved']           = $unit->getApproved() ? true : false;
                $unitDetails[$id]['published']          = $unit->getPublish() ? true : false;

                if ($unit->getActive() == 1) {
                    $unitDetails[$id]['hidden']         = false; //active=1 then it's shown on the site
                } else {
                    $unitDetails[$id]['hidden']         = true;
                }

                $unitDetails[$id]['deposit']          = $unit->getDeposit();
                $unitDetails[$id]['unit_w']           = $unit->getWidth();
                $unitDetails[$id]['unit_l']           = $unit->getLength();
                $unitDetails[$id]['unit_h']           = $unit->getHeight();

                $unitDetails[$id]['qty'] = $unit->getQuantity();
                $unitDetails[$id]['special'] = $unit->getSpecialString();
                $unitDetails[$id]['desc'] = $unit->getDescription();

                // Supp unit data
                $unitDetails[$id]['stacked'] = $unit->getSkybox();
                $unitDetails[$id]['premium'] = $unit->getPremiumUnit();
                $unitDetails[$id]['heated'] = $unit->getHeatedOnly();
                $unitDetails[$id]['aircooled'] = $unit->getAirCooledOnly();
                $unitDetails[$id]['ada'] = $unit->getAdaAccessible();
                $unitDetails[$id]['unitlights'] = $unit->getUnitLights();
                $unitDetails[$id]['twentyfourhouraccess'] = $unit->getTwentyFourHourAccess();

                $unitDetails[$id]['shelvesinunit'] = $unit->getShelvesInUnit();
                $unitDetails[$id]['basement'] = $unit->getBasement();
                $unitDetails[$id]['parkingwarehouse'] = $unit->getParkingWarehouse();
                $unitDetails[$id]['pullthru'] = $unit->getPullThrough();

                //this section does not apply to parking or outdoor
                if ($unit->getType() != Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE &&
                    $unit->getType() != Genesis_Entity_StorageSpace::TYPE_OUTDOOR) {
                    $unitDetails[$id]['door_w'] = $unit->getDoorWidth();
                    $unitDetails[$id]['door_h'] = $unit->getDoorHeight();

                    if ($unit->getClimateControlled() == 1) { $unitDetails[$id]['climate'] = true; } else { $unitDetails[$id]['climate'] = false; }
                    if ($unit->getHumidityControlled() == 1) { $unitDetails[$id]['humidity'] = true; } else { $unitDetails[$id]['humidity'] = false; }
                    if ($unit->getAlarm() == 1) { $unitDetails[$id]['alarm'] = true; } else { $unitDetails[$id]['alarm'] = false; }
                    if ($unit->getPower() == 1) { $unitDetails[$id]['power'] = true; } else { $unitDetails[$id]['power'] = false; }
                    if ($unit->getOutdoorAccess() == 1) { $unitDetails[$id]['outdoorAccess'] = true; } else { $unitDetails[$id]['outdoorAccess'] = false; }
                    if ($unit->getDoorType() == Genesis_Entity_StorageSpace::DOOR_ROLL_UP) {
                        $unitDetails[$id]['doorType'] = 'roll_up';
                    } elseif ($unit->getDoorType() == Genesis_Entity_StorageSpace::DOOR_SWING) {
                        $unitDetails[$id]['doorType'] = 'swing';
                    } else {
                        $unitDetails[$id]['doorType'] = 'none';
                    }
                } elseif ($unit->getType() == Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE) {
                    if ($unit->getPower() == 1) { $unitDetails[$id]['power'] = true; } else { $unitDetails[$id]['power'] = false; }
                }

                //this section does not apply to parking, wine or locker
                if ($unit->getType() != Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE &&
                    $unit->getType() != Genesis_Entity_StorageSpace::TYPE_LOCKER &&
                    $unit->getType() != Genesis_Entity_StorageSpace::TYPE_WINE) {
                    if ($unit->getDriveUp() == 1) { $unitDetails[$id]['driveUp'] = true; } else { $unitDetails[$id]['driveUp'] = false; }

                    if ($unit->getVehicleStorageOnly()) {
                        $unitDetails[$id]['vehicle'] = 'only';
                    } elseif ($unit->getVehicle() == 1) {
                        $unitDetails[$id]['vehicle'] = true;
                    } else {
                        $unitDetails[$id]['vehicle'] = false;
                    }
                }

                //covered does not apply to wine or locker
                if ($unit->getType() != Genesis_Entity_StorageSpace::TYPE_LOCKER &&
                    $unit->getType() != Genesis_Entity_StorageSpace::TYPE_WINE &&
                    $unit->getType() != Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT) {
                    $unitDetails[$id]['covered'] = $unit->getCovered();
                }

                $unitDetails[$id]['unitName'] = $unit->getUnitName();

                if ($unit instanceof Genesis_Entity_SitelinkFullUnit) {
                    $unitDetails[$id]['classType'] = $unit->getSitelinkType();
                }
                if ($unit instanceof Genesis_Entity_Centershift4Unit) {
                    $unitDetails[$id]['classType'] = $unit->getClassDesc();
                }
                if ($unit instanceof Genesis_Entity_SelfStorageManagerUnit) {
                    $unitDetails[$id]['classType'] = $unit->getUnitTypeCode();
                }
                if ($unit instanceof Genesis_Entity_ExtraspaceUnit) {
                    $unitDetails[$id]['unitType'] = $unit->getUnitTypeDisplay();
                }

            }

            if(isset($unitDetails))

                return $unitDetails;
        }
    }

    public function exportAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        header('Content-type: text/csv');
        header('Content-disposition:  attachment; filename=' . '"' . $this->getLoggedUser()->getAccount()->getName() . '-' . date("Y-m-d") . '.csv"');

        $restriction = Genesis_Db_Restriction::equal('published', 1);
        $restriction->setOrder(Genesis_Db_Order::asc('title'));
        $facilities = $this->_fetchFacilitiesData($restriction);

        echo '"Facility Name","Facility Company Code","Active or Hidden","Bid","Reservations","Average Cost Per Reservation","Total Cost","Source","SpareFoot URL"' . "\n";

        foreach ($facilities as $facility) {
            if (! $facility['published']) {
                continue;
            }

            $line = array(
                $facility['title'],
                $facility['code'],
                $facility['hidden'] ? 'Hidden' : 'Active',
                $facility['bid_string'],
                $facility['num_reservations'],
                '$' . number_format($facility['cost_per_reservation'], 2),
                '$' . number_format($facility['total_cost'], 2),
                $facility['source_name'],
                $facility['url'],
            );

            echo '"' . implode('","', $line) . '"' . "\n";
        }
    }

    private function _saveBid($facilityId)
    {
        $facility = Genesis_Service_Facility::loadById($facilityId);

        // Gotta check this now because it gets changed in the else if it's not
        $isBidFlat = $facility->isBidFlat();

        $bidAmount = $this->getParam('bid_amount', $facility->getMinBid());
        $this->view->bidAmount = $bidAmount;
        $this->view->facility = $facility;
        $this->view->chart = new AccountMgmt_Flot_SearchPositionLine('chart', $facility, $this->getBeginDate(), $this->getEndDate());

        try {
            $facility->setEffectiveBidAmount($bidAmount);
            Genesis_Service_Facility::save($facility, $this->getLoggedUser());
            $this->view->bidAmount = $facility->getEffectiveBidAmount();

            if (!$isBidFlat) {
                $this->forward('bid');
            }
        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
            //$this->dispatchError($e->getMessage());
        }
    }

    public function updatebidAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $params = $this->_getAllParams();
        if (!CsrfUtil::validateToken(self::RANK_BID_CSRF_TOKEN, $this->getParam('csrf_token'))) {
            echo json_encode(['Error' => 'Request structure is invalid. Refresh the page and try again.']);
            return;
        }
        echo $this->_updateBid($params);
    }

    private function _updateBid($params = array())
    {
        try {
            /* @var $facility Genesis_Entity_Facility */
            $facility = Genesis_Service_Facility::loadById($params['fid']);

            $bidAmount = $this->getParam('bid_amount', $facility->getMinBid());
            $facility->setEffectiveBidAmount($bidAmount);
            $facility->validateBid($this->getLoggedUser());

            $this->view->bidAmount = $facility->getEffectiveBidAmount();
            $this->view->facility = $facility;
            $this->view->chart = new AccountMgmt_Flot_SearchPositionLine('chart', $facility, $this->getBeginDate(), $this->getEndDate());

            $response = array(
                'bidSet' => array(),
                'unitAvgs' => $facility->getAvgUnitPrices(),
            );

            $cityLocation = $facility->getLocation()->getCity().', '.$facility->getLocation()->getState();
            $queries = [
                new AccountMgmt_Models_SearchRankQuery('city', 'city', $cityLocation),
                new AccountMgmt_Models_SearchRankQuery('zip', 'zip', $facility->getLocation()->getZip())
            ];

            if (isset($params['custom_zip']) && strlen($params['custom_zip'])) {
                $queries []= new AccountMgmt_Models_SearchRankQuery('customZip', 'zip', $params['custom_zip']);
            }

            if (isset($params['custom_city']) && strlen($params['custom_city'])) {
                $queries []= new AccountMgmt_Models_SearchRankQuery('customCity', 'city', $params['custom_city']);
            }

            $searchClient = new AccountMgmt_Clients_SearchClient('bid-rank');
            $ranks = $searchClient->getSearchRanks($facility, $bidAmount, $queries);

            $response['bidSet']['city'] = array(
                'location' => $facility->getLocation()->getCity().', '.$facility->getLocation()->getState(),
                'rank' => $ranks['city']
            );

            $response['bidSet']['zip'] = array(
                'location' => $facility->getLocation()->getZip(),
                'rank' => $ranks['zip']
            );

            if (isset($params['custom_zip']) && strlen($params['custom_zip'])) {
                $response['bidSet']['customZip'] = array(
                    'location' => $this->getParam('custom_zip'),
                    'rank' => $ranks['customZip']
                );
            }

            if (isset($params['custom_city']) && strlen($params['custom_city'])) {
                $response['bidSet']['customCity'] = array(
                    'location' => $this->getParam('custom_city'),
                    'rank' => $ranks['customCity']
                );
            }

            return json_encode($response);

        } catch (Exception $e) {
            return json_encode(['Error' => $e->getMessage()]);
        }
    }

    public function bidOpportunitiesAction()
    {
        $this->redirect('facility/bid');
    }

    public function bidAction()
    {
        $fid = $this->getParam('fid');

        /*
        $isBidOptimizerActive = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::BID_OPTIMIZER);
        if($isBidOptimizerActive){
            $this->redirect($this->view->url(['action'=>'bidoptimizer'], 'features'));
        }
        */

        $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(
            Genesis_Db_Restriction::equal('published', 1)->setOrder(Genesis_Db_Order::asc('title'))
        );

        if ($fid) {
            $facility = Genesis_Service_Facility::loadById($fid);
        }

        if (!$facility && $this->getSession()->facilityId && $this->getSession()->facilityId !== 'all') {
            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        }

        if (!$facility) {
            $facility = $this->view->facilities[0];
        }

        if (! $facility) {
            $this->redirect($this->view->url([], 'dashboard'));
        }

        $this->view->update_bid_csrf_token = CsrfUtil::getToken(self::UPDATE_BID_CSRF_TOKEN);
        $this->view->rank_bid_csrf_token = CsrfUtil::getToken(self::RANK_BID_CSRF_TOKEN);
        $this->view->customClosuresBlogPost = "https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility";
        $this->view->covidModal = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::COVID_MODAL);
        $this->view->customClosures = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::CUSTOM_CLOSURES);
        $this->view->isBidOptimizerActive = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::BID_OPTIMIZER); //added to get the bid flag
        

        if (! $this->getLoggedUser()->getUserAccess()->canAccessFacility($facility)) {
            throw new Exception('Not allowed to edit that facility (#' . $facility->getId().')');
        }

        switch ($facility->getAccount()->getBidType()) {
            case Genesis_Entity_Account::BID_TYPE_TIERED:
            case Genesis_Entity_Account::BID_TYPE_FLAT:
            case Genesis_Entity_Account::BID_TYPE_PERCENT:
                break;
            default:
                $this->redirect($this->view->url([], 'features').'?fid='.$facility->getId());
        }

        if (! $facility->getEffectiveBidAmount()) {
            $this->forward('bidsetup');
        }

        if (! isset($facility) || ! $facility) {
            foreach ($this->view->facilities as $selectedFacility) {
                $facility = $selectedFacility;
                break;
            }
        }

        if ($this->getParam('bid_amount')) {
            $bidAmount = $this->getParam('bid_amount', $facility->getMinBid());
            $this->view->bidAmount = $bidAmount;

            try {
                $facility->setEffectiveBidAmount($bidAmount);
                Genesis_Service_Facility::save($facility, $this->getLoggedUser());
                $this->view->alert = 'Changes saved. Please allow 15 minutes for the changes to take effect.';
                $this->view->alertClass = 'alert-success';
            } catch (Exception $e) {
                $this->view->alert = '<strong<>Error</strong>: '.$e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        $this->view->facility = $facility;
        $this->view->bidType = $this->getLoggedUser()->getAccount()->getBidType();

        if ($this->getLoggedUser()->isMyFootGod() || $this->getLoggedUser()->isMyfootAdmin()) {
            $this->view->canChangeBid = true;
        } else {
            $this->view->canChangeBid = false;

            $acct = $this->getLoggedUser()->getAccount();
            $admins = $acct ? $acct->getAdmins()->toArray() : null;

            $adminEmails = array();
            foreach ($admins as $admin) {
                $adminEmails[] = "<a href=\"mailto:{$admin->getEmail()}\">{$admin->getEmail()}</a>";
            }

            $count = count($adminEmails);
            if ($count == 0) {
                $this->view->adminEmails = 'an admin';
            } elseif ($count == 1) {
                $this->view->adminEmails = $adminEmails[0];
            } elseif ($count == 2) {
                $this->view->adminEmails = $adminEmails[0] . ' or ' . $adminEmails[1];
            } elseif ($count > 2) {
                $this->view->adminEmails = implode(', ', array_slice($adminEmails,0,-1)) . ', or ' . $adminEmails[$count-1];
            }
        }

        if ($this->getParam('save_bid')) {
            $this->_saveBid($fid);
        }

        $this->setCommonViewFields();

        $this->view->scripts = array('facility/bid');
        $this->view->title = 'Bidding';

        $authToken = AccountMgmt_Service_UserOauth::getToken();
        $this->view->sparefootSearchCityUrl = AccountMgmt_Service_UrlUtil::getSparefootSearchUrl(['location' => $facility->getLocation()->getCity().", ".$facility->getLocation()->getState()]);
        $this->view->sparefootSearchZipUrl = AccountMgmt_Service_UrlUtil::getSparefootSearchUrl(['location' => $facility->getLocation()->getZip()]);

        $clientApi = new AccountMgmt_Clients_ClientApiClient();
        $bidOppsResponses = $clientApi->getCityAndZipBidOpps($facility->getAccountId(), $facility->getId(), $authToken);
        $zipBidOppsResponse = $bidOppsResponses['zipBidOpps'];
        $cityBidOppsResponse = $bidOppsResponses['cityBidOpps'];

        if ($this->isValidClientApiResponse($zipBidOppsResponse)) {
            $this->view->facilityZipBidOpps = $zipBidOppsResponse;
        } else {
            if (strpos($zipBidOppsResponse['message'], 'Search service data unavailable') !== false) {
                $this->view->zipBidOppsErrorMessage = 'Facility not found in search results for this zip code.';
            } else {
                $this->view->zipBidOppsErrorMessage = "Unable to get bid opportunities for this zip code. Please try again momentarily.";
            }
        }

        if ($this->isValidClientApiResponse($cityBidOppsResponse)) {
            $this->view->facilityCityBidOpps = $cityBidOppsResponse;
        } else {
            if (strpos($cityBidOppsResponse['message'], 'Search service data unavailable') !== false) {
                $this->view->cityBidOppsErrorMessage = 'Facility not found in search results for this city.';
            } else {
                $this->view->cityBidOppsErrorMessage = "Unable to get bid opportunities for this city. Please try again momentarily.";
            }
        }

        $this->view->currentBid = number_format($facility->getEffectiveBidAmount(), 2);
        $this->view->minBid = $facility->getMinBid();
        $this->view->bidDelta = $this->view->currentBid - $this->view->minBid;
    }

    public function bidOptimizerAction(){

        $isBidOptimizerActive = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::BID_OPTIMIZER);
        if(!$isBidOptimizerActive){
            $this->redirect($this->view->url(['action'=>'bid'], 'features'));
        }

        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        if (! $facility) {
            $this->redirect($this->view->url([], 'dashboard'));
        }
        $this->view->scripts = [
            'statement/bid-optimizer'
        ];
        $this->view->facility = $facility;
        $this->view->bidSummaryData = $this->getBidOptimizerData();
    }
    /*
    * To get the bidoptimizer data based on the account id.
    */
    public function getBidOptimizerData(){
        $user_id = $this->getLoggedUser()->getId();
        $bidApi = new BidOptimizerClient();
        $params = [];
        $endpoint = '/bids/summary/' . $user_id;
        try {
            if($this->getLoggedUser()->getMyfootRole() === Genesis_Entity_UserAccess::ROLE_GOD)
            {
                $account_id = $this->getLoggedUser()->getAccount()->getAccountId();
                $params = [
                    'account_id' => $account_id
                ];
            } 
            $res = $bidApi->get($endpoint, $params); 
            if (isset($res['facilities']) && is_array($res['facilities'])) {
                return $res['facilities'];
            }
        }
        catch (Exception $e) {
            $this->view->errorMessages [] = "Something went wrong. Please try again.";
            $logger = new Logger('bid-optimizer-data');
            $logger->pushHandler(new StreamHandler('php://stdout', Logger::ERROR));
            $logger->error("Failed to load facilities Data.");
        }
        return [];
    }

    public function bidOptimizerBidsExportAction() {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        
        $searchType = filter_var($this->_getParam('searchType'), FILTER_SANITIZE_SPECIAL_CHARS);
    
        if ($this->_request->isGet()) {
             $bidOptimizerClient = new BidOptimizerClient();
             $userId = $this->getLoggedUser()->getId();
             $params = ['searchType' => $searchType];
             if ($this->getLoggedUser()->getMyfootRole() === Genesis_Entity_UserAccess::ROLE_GOD) {
                $params['accountId'] = $this->getLoggedUser()->getAccount()->getAccountId();
            }    
             try {
                $response = $bidOptimizerClient->getBidsExport($userId, $params);
                $this->getResponse()->setHeader('Content-Type', 'application/json');
                $this->_helper->json($response);
             } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
             }
         }

    }

    public function bidOptimizerBidUpdateAction() {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
    
        $bidOptClient = new BidOptimizerClient();
        $bids = [];
        
        if ($this->_request->isPost()) {
            $isJsonRequest = strpos($this->_request->getHeader('Content-Type'), 'application/json') !== false;
            if ($isJsonRequest) {
                $jsonData = file_get_contents('php://input');
                $bids = json_decode($jsonData, true);
            } else {
                if (empty($_FILES['bids']['tmp_name'])) {
                    throw new Exception('No valid file uploaded. Please upload a valid .csv file.');
                }
                $bidCsvPath = $_FILES['bids']['tmp_name'];
                $csvData = array_map('str_getcsv', file($bidCsvPath));

                $header = array_map('trim', $csvData[0]);
                unset($csvData[0]);

                $errored = 0;
                $errors = [];

                foreach ($csvData as $row) {
                    $rowData = array_combine($header, $row);
                    $facilityId = $this->sanitizeId($rowData['facility_id']);
                    $maxBid = $this->sanitizeMaxBid($rowData['max_bid']);
                    
                    if (empty($facilityId)) {
                        $errored++;
                        $errors[] = "Invalid facility_id: {$rowData['facility_id']} is not a valid.";
                    }

                    if (empty($maxBid)) {
                        $errored++;
                        $errors[] = "Invalid max_bid: {$rowData['max_bid']} is not a valid value.";
                    }

                    if ($errored > 0) {
                        $this->getResponse()->setHeader('Content-Type', 'application/json');
                        $response = [
                            'errored' => $errored,
                            'error_count' => $errored,
                            'errors' => $errors
                        ];
                        $this->_helper->json(json_encode($response));
                        return;
                    }

                    $bids[] = [
                        'facility_id' => $facilityId,
                        'max_bid' => $maxBid,
                        'bid_strategy' => 'max_bid'
                    ];
                }
            }

            $requestBody = [
                'user_id' => $this->getLoggedUser()->getId(),
                'bids' => $bids
            ];
    
            $resp = $bidOptClient->postBidUpdate($requestBody);
    
            $this->getResponse()->setHeader('Content-Type', 'application/json');
            $this->_helper->json($resp);
        }
    }
    
    public static function sanitizeId(string $id): ?int
    {
        $sanitized = preg_replace('/[^0-9]/', '', $id);
        if (empty($sanitized) || !ctype_digit($sanitized)) {
            return null; 
        }
        return (int) $sanitized;
    }
    

    public static function sanitizeMaxBid(string $input, $nearest = 0.05): ?float
    {
        $number = filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);

        if (empty($number) || !is_numeric($number)) {
            return null;
        }
        $number = (float) $number;
        $rounded = round($number / $nearest) * $nearest;

        return (float) number_format($rounded, 2, '.', '');
    }

    public function bulkbidAction()
    {

        /*
        $isBidOptimizerActive = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::BID_OPTIMIZER);
        if($isBidOptimizerActive){
            $this->redirect($this->view->url(['action'=>'bidoptimizer'], 'features'));
        }
        */

        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        if (! $facility) {
            $this->redirect($this->view->url([], 'dashboard'));
        }
        $this->view->facility = $facility;
        $this->view->loggedUser = $this->getLoggedUser();
        $this->view->title = 'Bulk Bid Update';
        $this->view->isBidOptimizerActive = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::BID_OPTIMIZER); //added bidoptimizer check
        if ($this->_request->isPost()) {
            $bidCsvPath = $_FILES['bids']['tmp_name'];
            $logger = new Logger('bulk-bid-update');
            $logger->pushHandler(new StreamHandler('php://stdout', Logger::INFO));
            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            if (! $facility) {
                $this->redirect($this->view->url([], 'dashboard'));
            }
            $accountId = $facility->getAccountId();
            $bidUpdater = new Genesis_Service_BulkBidUpdate($logger, $this->getLoggedUser(), $accountId, $bidCsvPath);
            try {
                $result = $bidUpdater->run();
                if ($result === false) {
                    $this->view->errorMessages []= "Unable to update bid modifiers. Troubleshooting available via our <a href='https://support.sparefoot.com/hc/en-us/articles/************-Bulk-Bidding' target=”_blank”> Help Center ></a>";
                } else {
                    $this->view->successMessages []= "Bulk bid modifiers have been updated. Please allow 15 minutes for the updates to take effect.";
                }
            } catch (Exception $e) {
                $this->view->errorMessages []= "Unable to update bid modifiers. Troubleshooting available via our <a href='https://support.sparefoot.com/hc/en-us/articles/************-Bulk-Bidding' target = ”_blank”> Help Center ></a>.";
                $this->view->errorMessages []= 'Reason: ' . $e->getMessage();
            }
        }
        $this->view->scripts = array('facility/bid');
    }

    public function isValidClientApiResponse($response)
    {
        if ($response['exception']) {
            return false;
        }

        if (strpos($response['message'], 'Search service data unavailable') !== false) {
            return false;
        }

        return true;
    }

    public function downloadbidopportunitiesAction()
    {
        $accountId = $this->getLoggedUser()->getAccount()->getId();
        $oppType = $this->getParam('oppType');
        $authToken = AccountMgmt_Service_UserOauth::getToken();
        if (!$accountId) {
            throw new Exception('No account found for logged in user');
        }
        $clientApi = new AccountMgmt_Clients_ClientApiClient();
        $this->_helper->json($clientApi->getBidsReportByAccount($accountId, $authToken, $oppType));
    }

    public function pollbidoppsreportAction()
    {
        $jobId = $this->getParam('id');
        $authToken = AccountMgmt_Service_UserOauth::getToken();
        $clientApi = new AccountMgmt_Clients_ClientApiClient();
        $this->_helper->json($clientApi->getJobById($jobId, $authToken));
    }

    public function bidoppsreportAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        header("Content-Type: text/csv");
        header("Content-Disposition: attachment; filename=abc.csv");
        header('Cache-Control: max-age=0');

        $jobId = $this->getParam('id');
        $authToken = AccountMgmt_Service_UserOauth::getToken();
        $clientApi = new AccountMgmt_Clients_ClientApiClient();

        echo $clientApi->getJobResultById($jobId, $authToken);
    }

    /**
     * Prepare the bid opps we will show to the end user
     * - remove > 500
     * - remove duplicates
     * - round up to next 0.05/5 increment steps
     * @param array $bidOpportunities
     */
    private function _prepareBidOpportunities(array &$bidOpportunities)
    {
        $uniqueBidCosts = array();
        $currBid = $bidOpportunities['bid'];
        $currRank = $bidOpportunities['rank'];

        $maxBidAmount = Genesis_Service_BidMgmt::getMaxBidAmountByType(
            $bidOpportunities[Genesis_Service_BidMgmt::FIELD_BID_TYPE]
        );

        // Get the amount to bump the bid to next increment.
        $bidAdder = ($bidOpportunities[Genesis_Service_BidMgmt::FIELD_BID_TYPE] === Genesis_Entity_Account::BID_TYPE_PERCENT)
            ? Genesis_Service_BidMgmt::BID_ROUND_PERCENT
            : Genesis_Service_BidMgmt::BID_ROUND;

        $currBidRound = Genesis_Service_BidMgmt::roundBidOppAmount($currBid, $bidOpportunities[Genesis_Service_BidMgmt::FIELD_BID_TYPE]);

        foreach ($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION] as $rankPosition => $bidCost) {
            // Round up the bid to the next 0.05/5 increment, so they will get that position.
            $bidCostRound = Genesis_Service_BidMgmt::roundBidOppAmount($bidCost, $bidOpportunities[Genesis_Service_BidMgmt::FIELD_BID_TYPE]);

            // Go up a increment if needed.
            if (abs($bidCostRound - $bidCost) <= 0.00001) {
                $bidCostRound += $bidAdder;
            }

            // Dont show if Ranking is higher number.
            if ($rankPosition >= $currRank) {
                unset($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            }
            // Dont show if bids are the same.
            else if ($currBidRound == $bidCostRound) {
                unset($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            }
            // Dont show if bid is empty, (should not happen)
            else if (! $bidCostRound) {
                unset($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            }
            // Dont show if over max bid.
            else if ($bidCostRound > $maxBidAmount) {
                $bidOpportunities['max_bid_exceeded'] = Genesis_Service_BidMgmt::MAX_BIDDABLE; // flag for seeing why
                unset($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            }
            // Dont show if we already have a bid of this value (no dupe bids)
            else if (isset($uniqueBidCosts["$bidCostRound"])) {
                unset($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            } else {
                // Save new value if passed all checks.
                $bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition] = $bidCostRound;

                // Save so we dont show duplicate bids.
                $uniqueBidCosts["$bidCostRound"] = true;
            }

        }

        if (count($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION]) === 0) {
            $bidOpportunities['no_opportunities'] = true; // flag for seeing why
        }
    }

    /**
     * Summarize three options to send the view, using these rules
     * if there are 3 or more in the opportunity array, we should have 3 returned
     * high rankers (1st, 2nd) and mid-packers with low scores will have only 1 or 2 options
     * first: best position
     * second: next position
     * third: the floor midpoint between first and currentPosition
     *
     * @param array $bidOpportunities
     */
    private function _summaryThreeOpportunities(array &$bidOpportunities)
    {
        $count = count($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION]);

        // If less than 4 results just display the three we have, as is.
        if ($count < 4) {
            return;
        }

        // For the third choice, get one in mid range for them.
        $keys = array_keys($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION]);
        $firstPosition = $keys[0];
        $secondPosition = $keys[1];
        $midPoint = (int) floor(count($bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION]) / 2) + 1;
        $midPosition = $keys[$midPoint];

        $bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION] = [
            $firstPosition  => $bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$firstPosition],
            $secondPosition => $bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$secondPosition],
            $midPosition    => $bidOpportunities[Genesis_Service_BidMgmt::ARRAY_POSITION][$midPosition]
        ];
    }

    public function bidCustomAction()
    {
        $this->redirect($this->view->url(['action'=>'bid'], 'features').'?fid='.$this->getParam('fid'));
    }
    
    public function bidOptimizerHealthAction(){
        $bidOptClient = new BidOptimizerClient();
        $resp = $bidOptClient->getHealthStatus();

        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $this->getResponse()->setHeader('Content-Type', 'application/json');
        $this->_helper->json($resp);
    }

    public function bidsetupAction()
    {
        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        if (! $facility) {
            $this->redirect($this->view->url([], 'dashboard'));
        }
        $this->view->facility = $facility;

        switch ($facility->getAccount()->getBidType()) {
            case Genesis_Entity_Account::BID_TYPE_TIERED:
            case Genesis_Entity_Account::BID_TYPE_FLAT:
                break;
            default:
                $this->redirect($this->view->url([], 'features').'?fid='.$facility->getId());
        }

        //if this facility has a bid amount setup already go there
        if ($facility->getEffectiveBidAmount()) {
            $this->forward('bid');
        }
        if ($this->getParam('save_bid')) {
            $this->_saveBid($facility->getId());
        }
    }

    public function savebidAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if ($this->_request->isPost()) {
            if (!CsrfUtil::validateToken(self::UPDATE_BID_CSRF_TOKEN, $this->getParam('csrf_token'))) {
                throw new Exception('Request structure is invalid. Refresh the page and try again.');
            }
            $this->_saveBid($this->getParam('fid'));
            return;
        }

        throw new Exception(ApiException::NOT_IMPLEMENTED);
    }

    public function detailsAction()
    {
        $this->view->facility = $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);

        $isFSS = $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
        if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_MANUAL
            || $isFSS) {
            $this->redirect($this->view->url(['action'=>'units'], 'features').'?fid=' . $facility->getId());
        }

        $integratedFields = $this->_getIntegratedFields();

        if (in_array('promotion', $integratedFields)) {
            unset($integratedFields['promotion']);
            $integratedFields[] = 'facility_promotions';
        }

        $this->view->integratedFields = $integratedFields;
        $this->view->customClosuresBlogPost = "https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility";
        $this->view->covidModal = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::COVID_MODAL);
        $this->view->customClosures = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::CUSTOM_CLOSURES);

        unset($integratedFields);

        $officeHrs = Genesis_Service_FacilityHours::loadById($this->getSession()->facilityId, 'office');
        if ($officeHrs) {
            $this->view->officeHours = $officeHrs;
        } else {
            $this->view->officeHours = new Genesis_Entity_FacilityHours();
        }

        $accessHrs = Genesis_Service_FacilityHours::loadById($this->getSession()->facilityId, 'access');
        if ($accessHrs) {
            $this->view->accessHours = $accessHrs;
        } else {
            $this->view->accessHours = new Genesis_Entity_FacilityHours();
        }

        $this->view->facility = $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->units = $units = $facility->getActiveGroupedUnits(true); //this will get whichever is right.
        $this->view->loggedUser = $this->getLoggedUser();

        $admins = $this->getLoggedUser()->getAccount()->getAdmins()->toArray();
        $this->view->admins = $admins;

        //contacts other than admins
        //Note: getEmailAddresses() is inefficient, also used in bookings.  refactor? -EH 8/27/11
        $otherContacts = $facility->getEmailAddresses();

        //remove admins from list so we don't have to list twice
        foreach ($admins as $a) {
            unset($otherContacts[array_search($a->getEmail(),$otherContacts)]);
        }

        $this->view->contacts = $otherContacts;
        $this->view->corporation = $corporation = Genesis_Service_Corporation::loadById($facility->getCorporationId());

        //get info on the current integration, in case we let them self report
        $this->view->source = Genesis_Service_Source::loadById(
            $corporation->getSourceId()
        );
        //decide if the user gets to pick to self report a software
        switch ((int) $corporation->getSourceId()) {
            //get the self-reported software options
            case Genesis_Entity_Source::ID_MANUAL:
                $this->view->sources = Genesis_Service_Source::load(
                    Genesis_Db_Restriction::equal('manualIntegration', 1)->setOrder(
                        Genesis_Db_Order::asc('source')
                    )
                );

                break;
            default:

                $this->view->sources = false;
        }

        $this->view->erroredFields = array();
        $this->view->errors = array();

        if ($this->getRequest()->isPost()) {
            try {

                //validate zip
                if (!(strlen($this->getParam('facility_zip')) > 0) || !preg_match("/^\d{5}$|^\d{5}-\d{4}$/", $this->getParam('facility_zip'))) {
                    $this->view->erroredFields = array('facility_zip');
                    throw new Exception('Please enter a valid zip code.');
                }

                //create new location
                $location = Genesis_Service_Location::loadByAddress(
                    $this->getParam('facility_address1'),
                    $this->getParam('facility_city'),
                    $this->getParam('facility_state'),
                    $this->getParam('facility_zip')
                );

                //does this location already exist
                if ($location) {
                    //yes, do nothing
                } else {
                    //call geocoder
                    $location = Genesis_Service_Location::geoCodePhysicalAddress(
                        $this->getParam('facility_address1')
                        . " " . $this->getParam('facility_city')
                        . " " . $this->getParam('facility_state')
                        . " " . $this->getParam('facility_zip')
                    );
                    $location = Genesis_Service_Location::save($location);
                }

                //if this facility integration allows editing reswindow
                if ($facility->canEditReservationWindow()) {
                    // if reservation window days is set
                    $facilityResWindowRuleChange = $unitResWindowRuleChange = false;

                    $setRules = [];
                    $resWindowDays = $this->getParam('res-win-days');

                    if ($resWindowDays[0] !== '' || $resWindowDays === '') { # if isset and is blank, rules are set to blank

                        $facilityResWindowRuleChange = true;
                        if ($facility->getSourceId() == Genesis_Entity_Source::ID_MANUAL) { // manual
                            $setRules = (empty($resWindowDays)) ? [] : ['0' => $resWindowDays[0]];
                            $occupancyType = Genesis_Entity_ReservationWindowRule::OCCUPANCY_TYPE_PERCENT;
                        } else { // integrated
                            $resWindowPercents = $this->getParam('res-win-low');
                            $occupancyType = Genesis_Entity_ReservationWindowRule::OCCUPANCY_TYPE_PERCENT;
                            foreach ($resWindowDays as $i => $day) {
                                $percent = $resWindowPercents[$i];
                                if ($day) {
                                    $key = (string)$percent / 100;
                                    $setRules['' . $key] = $day;
                                }
                            }
                        }

                        Genesis_Service_ReservationWindowRule::saveRules(
                            $facility->getAccountId(),
                            $facility->getId(),
                            null,
                            $occupancyType,
                            $setRules,
                            AccountMgmt_Service_User::getLoggedUser()->getId()
                        );
                    }
                    //if the user opened the unit overrides options, allow, save and apply unit reservation window overrides
                    /**
                     * @var $unit Genesis_Entity_StorageSpace
                     */
                    foreach ($units as $unit) {
                        $unitResWindowRuleChange = true;
                        $rule = [];//empty rule, so this always exists on save
                        $newWindowDays = $this->getParam('unit-reservation-override-' . $unit->getId());
                        if ($newWindowDays || $newWindowDays === '') {
                            $rule = (empty($newWindowDays)) ? [] : [0 => $newWindowDays];
                        }

                        //save single unit rule
                        //the applyRulesByFacilityId will propagate to all units in a unit group
                        Genesis_Service_ReservationWindowRule::saveRules(
                            $facility->getAccountId(),
                            $facility->getId(),
                            $unit->getId(),
                            Genesis_Entity_ReservationWindowRule::OCCUPANCY_TYPE_PERCENT,
                            $rule,
                            AccountMgmt_Service_User::getLoggedUser()->getId()
                        );
                    }
                    /**
                     * update facility rules if we updated either units or the facility
                     * and reload the units because they changed res windows
                     */
                    if ($facilityResWindowRuleChange || $unitResWindowRuleChange) {
                        Genesis_Service_ReservationWindowRule::applyRulesByFacilityId($facility->getId());
                        $this->view->units = $facility->getActiveGroupedUnits(true);
                    }
                }
                //$this->_logFacilityAttributeChange('edit_facility_name', $facility->getTitle(), $this->getParam('facility_name'), $facility);
                $facility->setTitle($this->getParam('facility_name'));
                if (! in_array('facility_description', $integratedParams)) {
                    $facility->setDescription($this->getParam('facility_description'));
                }

                if ($this->hasParam('facility_promotions')
                    && ! in_array('facility_promotions', $integratedParams)) {
                    if (strlen($this->getParam('facility_promotions')) > 100) {
                        $this->view->erroredFields = array('facility_promotions');
                        throw new Exception('Special Offer must be less than 100 characters long.');
                    }

                    if (! $corporation->getPullPromos()) {
                        $facility->setSpecials($this->getParam('facility_promotions'));
                    }
                }

                //Check to see if description or promo has phone number or email in it
                if(Genesis_Util_Validator::containsPhoneNumber($this->getParam('facility_description')) or Genesis_Util_Validator::containsEmailAddress($this->getParam('facility_description'))
                and ! $this->getLoggedUser()->isMyFootGod()) {
                    array_push($this->view->erroredFields,'facility_description');
                }
                if(Genesis_Util_Validator::containsPhoneNumber($this->getParam('facility_promotions')) or Genesis_Util_Validator::containsEmailAddress($this->getParam('facility_promotions'))
                and ! $this->getLoggedUser()->isMyFootGod()) {
                    array_push($this->view->erroredFields,'facility_promotions');
                }

                if (strlen($this->getParam('facility_admin_fee')) > 0) {
                    $facility->setAdminFee($this->getParam('facility_admin_fee'));
                } else {$facility->setAdminFee(null);}

                if (strlen($this->getParam('facility_phone')) > 0) {
                    if (!preg_match("/[0-9]{7,14}/",preg_replace("/[^0-9]/", "", $this->getParam('facility_phone')))) {
                        $this->view->erroredFields = array('facility_phone');
                        throw new Exception('Phone number is invalid');
                    }
                    $facility->setPhone(preg_replace("/[^0-9]/", "", $this->getParam('facility_phone')));
                } else {$facility->setPhone(null);}

                if (strlen($this->getParam('facility_phone')) > 0) {
                    if (!preg_match("/[0-9]{7,14}/",preg_replace("/[^0-9]/", "", $this->getParam('facility_phone')))) {
                        $this->view->erroredFields = array('facility_phone');
                        throw new Exception('Phone number is invalid');
                    }
                    $facility->setPhone(preg_replace("/[^0-9]/", "", $this->getParam('facility_phone')));
                } else {$facility->setPhone(null);}

                if (Genesis_Service_Feature::isActive('tenant_connect_sms')) {
                    if (strlen($this->getParam('facility_tenant_connect_sms_number')) > 0) {
                        $smsNumber = preg_replace("/[^0-9]/", "", $this->getParam('facility_tenant_connect_sms_number'));
                        if (!preg_match("/[0-9]{7,14}/",$smsNumber)) {
                            $this->view->erroredFields = array('facility_tenant_connect_sms_number');
                            throw new Exception('SMS phone number is invalid');
                        }
                        $facility->setTenantConnectSMSNumber($smsNumber);
                    } else {$facility->setTenantConnectSMSNumber(0);}
                }

                if (strlen($this->getParam('facility_tenant_connect')) > 0) {
                    $facility->setTenantConnect($this->getParam('facility_tenant_connect'));
                }

                if (strlen($this->getParam('facility_code')) > 0) {
                    $facility->setCompanyCode($this->getParam('facility_code'));
                } else {
                    $facility->setCompanyCode(null);
                }

                //check valid structure of a url
                $facilityUrl = $this->getParam('facility_url');
                if (isset($facilityUrl)) {
                    if (!preg_match("/^[A-Za-z]+:\/\//", $facilityUrl)) {
                        $testUrl = 'http://' . $facilityUrl;
                    } else {
                        $testUrl = $facilityUrl;
                    }

                    if ($this->_validateUrl($testUrl)) {
                        $facility->setUrl($testUrl);
                    } elseif ($facilityUrl == '') {
                        $facility->setUrl(null);
                        $facility->setUrlVerified(0);
                    } else {
                        $this->view->erroredFields = array('facility_url');
                        throw new Exception('The URL supplied appears to be invalid.');
                    }
                }

                //check for another published facility in this location that is not this facility, for manual users only
                if ($corporation->getSourceId() == Genesis_Entity_Source::ID_MANUAL) {
                    $exist_facility = Genesis_Service_Facility::load(
                        Genesis_Db_Restriction::and_(
                            Genesis_Db_Restriction::equal('locationId', $location->getId()),
                            Genesis_Db_Restriction::equal('published', 1),
                            Genesis_Db_Restriction::not(Genesis_Db_Restriction::equal('directoryFacility', 1))
                        )
                    )->uniqueResult();

                    if ($exist_facility && ($exist_facility->getId() != $facility->getId())) {
                        // instead of throwing an exception here, just email support a notification
                        // Only send the email if the facility's location has changed since the last update.

                        if ($facility->getLocationId() && $facility->getLocationId() != $location->getId()) {
                            $msg = new Genesis_Entity_EmailMessage();
                            if ($exist_facility->getDirectoryFacility()) {
                                $msg->setSubject('MyFoot: New Facility with same location as a Directory Facility');
                            } else {
                                $msg->setSubject('MyFoot: Duplicate Facility Location Edited');
                            }

                            $existingFacAccount = $exist_facility->getCorporation()->getAccount();
                            $existName = "";
                            if ($existingFacAccount) {
                                $existName = $existingFacAccount->getName();
                            }

                            if ($this->getParam('facility_address_change') == 1) {
                                $user_account_name = ($this->getLoggedUser()->getAccount()) ? $this->getLoggedUser()->getAccount()->getName() : '';

                                //instead of throwing an exception here, just email support a notification
                                $msg->setSubject('MyFoot: Duplicate Facility Location Edited');
                                $msg_str = 'A user changed an address of a facility to one already in use by another facility.<br/><br/>' .
                                    'Updated Facility Details:<br/>' .
                                    'Account: ' . $this->getLoggedUser()->getAccount()->getName() . '<br/>' .
                                    'Name: ' . $facility->getTitle() . '<br/>' .
                                    'Entered Address: ' . $this->getParam('facility_address1') . " " .
                                    $this->getParam('facility_city') . " " .
                                    $this->getParam('facility_state') . " " .
                                    $this->getParam('facility_zip') . '<br/><br/>' .
                                    'Existing Facility Details:<br/>' .
                                    'Account: ' . $existName . '<br/>' .
                                    'Name: ' . $exist_facility->getTitle() . '<br/>' .
                                    'Address: ' . $exist_facility->getLocation()->getAddress1() . " " .
                                    $exist_facility->getLocation()->getCity() . " " .
                                    $exist_facility->getLocation()->getState() . " " .
                                    $exist_facility->getLocation()->getZip() . '<br/><br/>' .
                                    'User Details:<br/>' .
                                    'Account: ' . $user_account_name . '<br/>' .
                                    'Name: ' . $this->getLoggedUser()->getFirstName() . ' ' . $this->getLoggedUser()->getLastName() .'<br/>' .
                                    'Email: ' . $this->getLoggedUser()->getEmail() .'<br/>';

                                $msg->setBody(Genesis_Util_Formatter::formatSalesForceEmailContent($msg_str));
                                $mailto = ($this->getLoggedUser()->isMyFootGod()) ? '' : '<EMAIL>' ;
                                Genesis_Service_Mailer::sendInternalMessage($mailto, $msg, array(), $this->getLoggedUser()->getFirstName() . ' ' . $this->getLoggedUser()->getLastName(),$this->getLoggedUser()->getEmail());
                            }

                        }
                    }
                }

                $altAddress = array();
                if ( $this->getParam('has_alt_address') ) {
                    if ( $this->getParam('alt_facility_address1') ) $altAddress['address'] = $this->getParam('alt_facility_address1');
                    if ( $this->getParam('alt_facility_city') ) $altAddress['city'] = $this->getParam('alt_facility_city');
                    if ( $this->getParam('alt_facility_state') ) $altAddress['state'] = $this->getParam('alt_facility_state');
                    if ( $this->getParam('alt_facility_zip') ) $altAddress['zip'] = $this->getParam('alt_facility_zip');
                    $throwError = false;
                    $this->view->erroredFields = array();
                    if (!isset($altAddress['address'])) {
                        $this->view->erroredFields[] = 'alt_facility_address1';
                        $throwError = true;
                    }
                    if (!isset($altAddress['city'])) {
                        $this->view->erroredFields[] = 'alt_facility_city';
                        $throwError = true;
                    }
                    if (!isset($altAddress['state'])) {
                        $this->view->erroredFields[] = 'alt_facility_state';
                        $throwError = true;
                    }
                    if (!isset($altAddress['zip'])) {
                        $this->view->erroredFields[] = 'alt_facility_zip';
                        $throwError = true;
                    }
                    if ($throwError) {
                        throw new Exception('Please specify an alternate address for receiving packages');
                    }
                    $facility->setAltAddress($altAddress);

                } elseif ($this->getParam('has_alt_address') === '0') {
                    $facility->setAltAddress(0);
                } else {
                    $this->view->erroredFields[] = 'has_alt_address';
                }


                if (strlen($this->getParam('onsite_office_at_facility')) > 0) {
                    $facility->setOnsiteOfficeAtFacility($this->getParam('onsite_office_at_facility'));
                } else {
                    array_push($this->view->erroredFields, 'onsite_office_at_facility');
                }

                //TODO: do we want to remove the street view POV here if the address changed?
                $facility->setLocationId($location->getId());

                //set the software (which is called Source, and stored in key_source)
                $software = $this->getParam('facility_software', null);
                if ((int) $software > 0) {
                    $facility->setSelfReportedSourceId((int) $this->getParam('facility_software'));
                } elseif ($software !== null) { //was actually in the form
                    $this->view->erroredFields[] = 'facility_software';
                }

                if (! strlen($this->getParam('facility_active')) > 0) {
                    array_push($this->view->erroredFields, 'facility_active');
                } else {
                    $facility->setActive($this->getParam('facility_active') ? 1 : 0);

                    if ($facility->getActive()) {
                        $facility->setAutomaticReactivationDate(null);
                    } elseif ($this->getParam('automatic_reactivation_date') == '') {
                        $facility->setAutomaticReactivationDate(null);
                    } else {
                        $automaticReactivationDate = strtotime($this->getParam('automatic_reactivation_date'));
                        if ($automaticReactivationDate <= time()) {
                            array_push($this->view->erroredFields, 'automatic_reactivation_date');
                        } elseif ($automaticReactivationDate >= strtotime("+1 year")) {
                            array_push($this->view->erroredFields, 'automatic_reactivation_date');
                        } else {
                            $facility->setAutomaticReactivationDate(date('Y-m-d', $automaticReactivationDate));
                        }
                    }
                }

                $facility = Genesis_Service_Facility::save($facility, $this->getLoggedUser());
                $this->view->facility = $facility;
                $this->view->alert = 'Changes saved.';
                $this->view->alertClass = 'alert-success';

            } catch (Exception $e) {
                $this->view->alert = '<strong<>Error</strong>: '.$e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        if ($facility->canEditReservationWindow()) {
            # grab facility rules
            $facilityResRules = [];
            foreach ($facility->getReservationWindowRules() as $rule) {
                $facilityResRules[] = [
                    'id' => $rule->getId(),
                    'occupancyType' => $rule->getOccupancyType(),
                    'occupancyValue' => $rule->getOccupancyValue(),
                    'reservationWindowDays' => $rule->getReservationWindowDays()
                ];
            }

            $this->view->facilityResRules = $facilityResRules;
            $this->view->units = $facility->getActiveGroupedUnits(true);
        }

        $this->setCommonViewFields();

        $this->view->scripts = array(
            'facility/global-functions',
            SPAREFOOT_JS_PATH.'/plugins/res-win-widget/script',
            'facility/hide-facility-reason-modal',
            'facility/details');
    }

    public function amenitiesAction()
    {
        $this->view->csrf_token = CsrfUtil::getToken(self::AMENITIES_CSRF_TOKEN);
        $this->view->customClosuresBlogPost = "https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility";
        $this->view->covidModal = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::COVID_MODAL);
        $this->view->customClosures = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::CUSTOM_CLOSURES);

        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $integrationId = $facility->getCorporation()->getSourceId();

        $this->view->facility = $facility;
        $this->view->params = array('app' => 'myfoot', 'user' => $this->getLoggedUser());

        if ($integrationId != Genesis_Entity_Source::ID_MANUAL) {
            $integratedFields = $this->_getIntegratedFields();
            $this->view->depositIsDisabled = $depositIsDisabled = in_array('security_deposit_required', $integratedFields);
            unset($integratedFields);
        } else {
            $this->view->depositIsDisabled = $depositIsDisabled = false;
        }

        $this->view->erroredFields = array();

        if ($this->getRequest()->isPost()) {
            try {
                // csrf token
                if (!CsrfUtil::validateToken(self::AMENITIES_CSRF_TOKEN, $this->getParam('csrf_token'))) {
                    throw new Exception('Request structure is invalid. Refresh the page and try again.');
                }

                // Moving options
                if (strlen($this->getParam('truck_rental')) > 0) {
                    $facility->setTruckRental($this->getParam('truck_rental'));
                } else {
                    array_push($this->view->erroredFields, 'truck_rental');
                }

                if (strlen($this->getParam('free_truck_rental')) > 0) {
                    $facility->setFreeTruckRental($this->getParam('free_truck_rental'));
                } else {
                    array_push($this->view->erroredFields, 'free_truck_rental');
                }

                if ($this->getParam('free_truck_rental') == 1) {

                    if (strlen($this->getParam('truck_distance_limit')) == 0) {
                        array_push($this->view->erroredFields, 'truck_distance_limit');
                    }

                    if ($this->getParam('truck_distance_limit') == 1) {
                        $facility->setTruckDistanceLimit($this->getParam('truck_max_mileage'));
                    } elseif (strlen($this->getParam('truck_distance_limit')) > 0) {
                        $facility->setTruckDistanceLimit(0);
                    }

                    if ($this->getParam('truck_distance_limit') == 1 && strlen($this->getParam('truck_max_mileage')) == 0) {
                        array_push($this->view->erroredFields, 'truck_distance_limit');
                        array_push($this->view->erroredFields, 'truck_max_mileage');
                    }

                    if (strlen($this->getParam('truck_insurance_required')) == 0) {
                        array_push($this->view->erroredFields, 'truck_insurance_required');
                    }

                    if ($this->getParam('truck_insurance_required') == 1) {
                        $facility->setTruckInsurance($this->getParam('truck_insurance_amount'));
                    } elseif (strlen($this->getParam('truck_insurance_required')) > 0) {
                        $facility->setTruckInsurance(0);
                    }

                    if ($this->getParam('truck_insurance_required') == 1 && strlen($this->getParam('truck_insurance_amount')) == 0) {
                        array_push($this->view->erroredFields, 'truck_insurance_required');
                        array_push($this->view->erroredFields, 'truck_insurance_amount');
                    }

                    if (strlen($this->getParam('truck_fuel_refill')) > 0) {
                        $facility->setTruckRefuelPolicy($this->getParam('truck_fuel_refill'));
                    } else {
                        array_push($this->view->erroredFields, 'truck_fuel_refill');
                    }
                }

                if (strlen($this->getParam('truck_access')) > 0) {
                    $facility->setTruckAccess($this->getParam('truck_access'));
                } else {
                    array_push($this->view->erroredFields, 'truck_access');
                }

                if ($this->getParam('truck_access') == 1) {
                    if (strlen($this->getParam('truck_access_size')) > 0) {
                        $facility->setTruckAccessSize($this->getParam('truck_access_size'));
                    } else {
                        array_push($this->view->erroredFields, 'truck_access');
                        array_push($this->view->erroredFields, 'truck_access_size');
                    }
                }

                if (Genesis_Service_Feature::isActive('myfoot.allow_18wheeler_dropoff')) {
                    if (strlen($this->getParam('allow_18wheeler_dropoff')) > 0) {

                        $facility->setAllow18WheelerDropoff($this->getParam('allow_18wheeler_dropoff'));

                        //second question response depends on if first question was answered
                        if (strlen($this->getParam('has_18wheeler_alleys')) > 0) {
                            $facility->setHas18WheelerAlleys($this->getParam('has_18wheeler_alleys'));
                        } else {
                            array_push($this->view->erroredFields, 'has_18wheeler_alleys');
                        }
                    } else {
                        array_push($this->view->erroredFields, 'allow_18wheeler_dropoff');
                    }
                }

                if (strlen($this->getParam('handcarts')) > 0) {
                    $facility->setHandcarts($this->getParam('handcarts'));
                } else {
                    array_push($this->view->erroredFields, 'handcarts');
                }

                if (strlen($this->getParam('elevator')) > 0) {
                    $facility->setElevator($this->getParam('elevator'));
                } else {
                    array_push($this->view->erroredFields, 'elevator');
                }

                if (strlen($this->getParam('sell_moving_supplies')) > 0) {
                    $facility->setSellsMovingSupplies($this->getParam('sell_moving_supplies'));
                } else {
                    array_push($this->view->erroredFields, 'sell_moving_supplies');
                }

                // Security options
                if (strlen($this->getParam('surveillance')) > 0) {
                    $facility->setSurveillance($this->getParam('surveillance'));
                } else {
                    array_push($this->view->erroredFields, 'surveillance');
                }

                if (strlen($this->getParam('egate_access')) > 0) {
                    $facility->setEGateAccess($this->getParam('egate_access'));
                } else {
                    array_push($this->view->erroredFields, 'egate_access');
                }

                if (strlen($this->getParam('fenced_lighted')) > 0) {
                    $facility->setFencedLighted($this->getParam('fenced_lighted'));
                } else {
                    array_push($this->view->erroredFields, 'fenced_lighted');
                }

                if (strlen($this->getParam('resident_manager')) > 0) {
                    $facility->setResidentManager($this->getParam('resident_manager'));
                } else {
                    array_push($this->view->erroredFields, 'resident_manager');
                }

                if (strlen($this->getParam('kiosk')) > 0) {
                    $facility->setKiosk($this->getParam('kiosk'));
                } else {
                    array_push($this->view->erroredFields, 'kiosk');
                }

                if (! strlen($this->getParam('bilingual_manager_available')) > 0) {
                    array_push($this->view->erroredFields, 'bilingual_manager_available');
                } elseif ($this->getParam('bilingual_manager_available') == '0') {
                    $facility->setBilingualManager(0);
                } elseif (! strlen($this->getParam('bilingual_language')) > 0) {
                    array_push($this->view->erroredFields, 'bilingual_language');
                } else {
                    $facility->setBilingualManager($this->getParam('bilingual_language'));
                }

                if (strlen($this->getParam('accept_tenant_mail')) > 0) {
                    $facility->setAcceptTenantMail($this->getParam('accept_tenant_mail'));
                } else {
                    array_push($this->view->erroredFields, 'accept_tenant_mail');
                }

                // Billing
                if (strlen($this->getParam('email_invoicing')) > 0) {
                    $facility->setEmailInvoicingAvailable($this->getParam('email_invoicing'));
                } else {
                    array_push($this->view->erroredFields, 'email_invoicing');
                }

                if (strlen($this->getParam('auto_payments')) > 0) {
                    $facility->setAutoPayAvailable($this->getParam('auto_payments'));
                } else {
                    array_push($this->view->erroredFields, 'auto_payments');
                }

                if (strlen($this->getParam('charge_date')) > 0) {
                    $facility->setChargeDate($this->getParam('charge_date'));
                } else {
                    array_push($this->view->erroredFields, 'charge_date');
                }

                if (strlen($this->getParam('charge_date')) > 0) {
                    $facility->setChargeDate($this->getParam('charge_date'));
                } else {
                    array_push($this->view->erroredFields, 'charge_date');
                }

                $securityDepositRequired = $this->getParam('security_deposit_required');
                $securityDepositRefundable = $this->getParam('security_deposit_refundable');
                $securityDepositType = $this->getParam('security_deposit_type');
                $securityDepositPercent = $this->getParam('security_deposit_percent');
                $securityDepositDollar = $this->getParam('security_deposit_dollar');

                // save security deposit required
                if (strlen($securityDepositRequired) == 0) {
                    array_push($this->view->erroredFields, 'security_deposit_required');
                } elseif ($securityDepositRequired == 1 && !$depositIsDisabled) {

                    $facility->setSecurityDeposit($securityDepositRequired);

                    // save security deposit refundable
                    if (strlen($securityDepositRefundable) == 0) {
                        array_push($this->view->erroredFields, 'security_deposit_refundable');
                    } else {
                        $facility->setSecurityDepositRefundable($securityDepositRefundable);
                    }

                    // save security deposit type
                    if (strlen($securityDepositType) == 0) {
                        array_push($this->view->erroredFields, 'security_deposit_type');
                    } else {
                        $facility->setSecurityDepositType($securityDepositType);

                        // save security deposit type percent
                        if ($securityDepositType === 'percent') {
                            if (strlen($securityDepositPercent) == 0) {
                                array_push($this->view->erroredFields, 'security_deposit_percent');
                            } else {
                                $facility->setSecurityDepositPercent($securityDepositPercent);
                                $facility->setSecurityDepositDollar(0);
                            }
                        }

                        // save security deposit type dollar
                        if ($securityDepositType === 'dollar') {
                            if (strlen($securityDepositDollar) == 0) {
                                array_push($this->view->erroredFields, 'security_deposit_dollar');
                            } else {
                                $facility->setSecurityDepositDollar($securityDepositDollar);
                                $facility->setSecurityDepositPercent(0);
                            }
                        }

                    }
                } elseif ($securityDepositRequired == 0 && !$depositIsDisabled) {
                    $facility->setSecurityDeposit(0);
                }

                // Discounts
                if (strlen($this->getParam('military_discount_available')) == 0) {
                    array_push($this->view->erroredFields, 'military_discount_available');
                } elseif ($this->getParam('military_discount_available') == 1) {
                    if (is_numeric($this->getParam('military_discount_amount'))) {
                        $facility->setMilitaryDiscount($this->getParam('military_discount_amount'));
                    } else {
                        array_push($this->view->erroredFields, 'military_discount_amount');
                        throw new Exception('Military discount must be a number percentage');
                    }
                    if (is_numeric($this->getParam('military_discount_amount'))) {
                        $facility->setMilitaryDiscount($this->getParam('military_discount_amount'));
                    } else {
                        array_push($this->view->erroredFields, 'military_discount_amount');
                        array_push($this->view->erroredFields, 'military_discount_available');
                    }

                    $militaryDiscountAppliesToReserves = $this->getParam('military_discount_reserves');
                    if (strlen($militaryDiscountAppliesToReserves) == 0) {
                        array_push($this->view->erroredFields, 'military_discount_reserves');
                    } elseif ($militaryDiscountAppliesToReserves == 1 || $militaryDiscountAppliesToReserves == 0) {
                        $facility->setMilitaryDiscountAppliesToReserves($militaryDiscountAppliesToReserves);
                    }

                    $militaryDiscountAppliesToVeterans = $this->getParam('military_discount_veterans');
                    if (strlen($militaryDiscountAppliesToVeterans) == 0) {
                        array_push($this->view->erroredFields, 'military_discount_veterans');
                    } elseif ($militaryDiscountAppliesToVeterans == 1 || $militaryDiscountAppliesToVeterans == 0) {
                        $facility->setMilitaryDiscountAppliesToVeterans($militaryDiscountAppliesToVeterans);
                    }

                } elseif (strlen($this->getParam('military_discount_available')) > 0) {
                    $facility->setMilitaryDiscount(0);
                }

                if ($this->getParam('military_discount_available') == 1 && strlen($this->getParam('military_discount_amount')) == 0) {
                    array_push($this->view->erroredFields, 'military_discount_available');
                    array_push($this->view->erroredFields, 'military_discount_amount');
                }

                if (strlen($this->getParam('senior_discount_available')) == 0) {
                    array_push($this->view->erroredFields, 'senior_discount_available');
                } elseif ($this->getParam('senior_discount_available') == 1) {
                    if (is_numeric($this->getParam('senior_discount_amount'))) {
                        $facility->setSeniorDiscount($this->getParam('senior_discount_amount'));
                    } else {
                        array_push($this->view->erroredFields, 'senior_discount_available');
                        array_push($this->view->erroredFields, 'senior_discount_amount');
                    }
                } elseif (strlen($this->getParam('senior_discount_available')) > 0) {
                    $facility->setSeniorDiscount(0);
                }

                if ($this->getParam('senior_discount_available') == 1 && strlen($this->getParam('senior_discount_amount')) == 0) {
                    array_push($this->view->erroredFields, 'senior_discount_available');
                    array_push($this->view->erroredFields, 'senior_discount_amount');
                }

                if (strlen($this->getParam('student_discount_available')) == 0) {
                    array_push($this->view->erroredFields, 'student_discount_available');
                } elseif ($this->getParam('student_discount_available') == 1) {
                    if (is_numeric($this->getParam('student_discount_amount'))) {
                        $facility->setStudentDiscount($this->getParam('student_discount_amount'));
                    } else {
                        array_push($this->view->erroredFields, 'student_discount_available');
                        array_push($this->view->erroredFields, 'student_discount_amount');
                    }
                } elseif (strlen($this->getParam('student_discount_available')) > 0) {
                    $facility->setStudentDiscount(0);
                }

                if ($this->getParam('student_discount_available') == 1 && strlen($this->getParam('student_discount_amount')) == 0) {
                    array_push($this->view->erroredFields, 'student_discount_available');
                    array_push($this->view->erroredFields, 'student_discount_amount');
                }

                // Insurance
                if (strlen($this->getParam('insurance_required')) > 0) {
                    $facility->setInsuranceRequired($this->getParam('insurance_required'));
                } else {
                    array_push($this->view->erroredFields, 'insurance_required');
                }

                if (strlen($this->getParam('insurance_available')) > 0) {
                    $facility->setInsuranceAvailable($this->getParam('insurance_available'));
                } else {
                    array_push($this->view->erroredFields, 'insurance_available');
                }

                if (AccountMgmt_Service_User::isFeatureActive('myfoot.protection_plan_facility_amenities')) {

                    if (strlen($this->getParam('protection_plan_required')) > 0) {
                        $facility->setProtectionPlanRequired($this->getParam('protection_plan_required'));
                    } else {
                        array_push($this->view->erroredFields, 'protection_plan_required');
                    }

                    if (strlen($this->getParam('protection_plan_available')) > 0) {
                        $facility->setProtectionPlanAvailable($this->getParam('protection_plan_available'));
                    } else {
                        array_push($this->view->erroredFields, 'protection_plan_available');
                    }

                }

                if (strlen($this->getParam('homeowners_insurance_accepted')) > 0) {
                    $facility->setHomeownersInsuranceAccepted($this->getParam('homeowners_insurance_accepted'));
                } else {
                    array_push($this->view->erroredFields, 'homeowners_insurance_accepted');
                }

                // Other amenities
                if (strlen($this->getParam('band_practice_allowed')) > 0) {
                    $facility->setBandPracticeAllowed($this->getParam('band_practice_allowed'));
                } else {
                    array_push($this->view->erroredFields, 'band_practice_allowed');
                }

                // Remote paperwork
                if (strlen($this->getParam('remote_paperwork')) > 0) {
                    $facility->setRemotePaperwork($this->getParam('remote_paperwork'));
                } else {
                    array_push($this->view->erroredFields, 'remote_paperwork');
                }

                // wash
                if (strlen($this->getParam('wash_station')) > 0) {
                    $facility->setWashStationAvailable($this->getParam('wash_station'));
                } else {
                    array_push($this->view->erroredFields, 'wash_station');
                }

                // dump
                if (strlen($this->getParam('dump_station')) > 0) {
                    $facility->setDumpStationAvailable($this->getParam('dump_station'));
                } else {
                    array_push($this->view->erroredFields, 'dump_station');
                }


                // Payment Types
                if (strlen($this->getParam('payment_accept_cash')) > 0) {
                    $paymentAcceptCash = $this->getParam('payment_accept_cash');
                } else {
                    array_push($this->view->erroredFields, 'payment_accept_cash');
                }
                if (strlen($this->getParam('payment_accept_check')) > 0) {
                    $paymentAcceptCheck = $this->getParam('payment_accept_check');
                } else {
                    array_push($this->view->erroredFields, 'payment_accept_check');
                }
                if (strlen($this->getParam('payment_accept_credit')) > 0) {
                    $paymentAcceptCredit = $this->getParam('payment_accept_credit');
                } else {
                    array_push($this->view->erroredFields, 'payment_accept_credit');
                }

                $paymentAcceptedCards = $this->getParam('payment_accepted_cards');

                $facility->setPaymentOptions(
                    $paymentAcceptCash,
                    $paymentAcceptCheck,
                    $paymentAcceptCredit,
                    $paymentAcceptedCards
                );

                // Vehicle Storage

                if (strlen($this->getParam('vehicle_require_title')) > 0) {
                    $vehicleRequireTitle = $this->getParam('vehicle_require_title');
                } else {
                    array_push($this->view->erroredFields, 'vehicle_require_title');
                }

                if (strlen($this->getParam('vehicle_require_registration')) > 0) {
                    $vehicleRequireRegistration = $this->getParam('vehicle_require_registration');
                } else {
                    array_push($this->view->erroredFields, 'vehicle_require_registration');
                }
                if (strlen($this->getParam('vehicle_require_insurance')) > 0) {
                    $vehicleRequireInsurance = $this->getParam('vehicle_require_insurance');
                } else {
                    array_push($this->view->erroredFields, 'vehicle_require_insurance');
                }
                if (strlen($this->getParam('vehicle_require_running')) > 0) {
                    $vehicleRequireRunning = $this->getParam('vehicle_require_running');
                } else {
                    array_push($this->view->erroredFields, 'vehicle_require_running');
                }

                if (strlen($this->getParam('maintenance_allowed')) > 0) {
                    $facility->setMaintenanceAllowed($this->getParam('maintenance_allowed'));
                } else {
                    array_push($this->view->erroredFields, 'maintenance_allowed');
                }

                $vehicleRenterTitled = $this->getParam('vehicle_renter_titled');

                $facility->setVehicleStorage(
                    $vehicleRequireTitle,
                    $vehicleRenterTitled,
                    $vehicleRequireRunning,
                    $vehicleRequireInsurance,
                    $vehicleRequireRegistration
                );

                if (strlen($this->getParam('minimum_stay')) > 0 && $this->getParam('minimum_stay') > 0) {
                    $facility->setMinimumStayRequired($this->getParam('minimum_stay'));
                }

                if (strlen($this->getParam('general_maintenance')) > 0) {
                    $generalMaintenance = $this->getParam('general_maintenance');
                } else {
                    array_push($this->view->erroredFields, 'general_maintenance');
                }

                if (strlen($this->getParam('propane')) > 0) {
                    $propane = $this->getParam('propane');
                } else {
                    array_push($this->view->erroredFields, 'propane');
                }

                if (strlen($this->getParam('diesel_and_gas')) > 0) {
                    $dieselAndGas = $this->getParam('diesel_and_gas');
                } else {
                    array_push($this->view->erroredFields, 'diesel_and_gas');
                }

                if (strlen($this->getParam('does_state_inspections')) > 0) {
                    $doesStateInspections = $this->getParam('does_state_inspections');
                } else {
                    array_push($this->view->erroredFields, 'does_state_inspections');
                }

                if (strlen($this->getParam('auto_cleaning_and_detailing')) > 0) {
                    $autoCleaningAndDetailing = $this->getParam('auto_cleaning_and_detailing');
                } else {
                    array_push($this->view->erroredFields, 'auto_cleaning_and_detailing');
                }

                if (strlen($this->getParam('air_pump')) > 0) {
                    $airPump = $this->getParam('air_pump');
                } else {
                    array_push($this->view->erroredFields, 'air_pump');
                }

                if (strlen($this->getParam('vacuum_station')) > 0) {
                    $vacuumStation = $this->getParam('vacuum_station');
                } else {
                    array_push($this->view->erroredFields, 'vacuum_station');
                }

                if (strlen($this->getParam('ice_machine')) > 0) {
                    $iceMachine = $this->getParam('ice_machine');
                } else {
                    array_push($this->view->erroredFields, 'ice_machine');
                }

                if (strlen($this->getParam('water_hose_spigot')) > 0) {
                    $waterHoseSpigot = $this->getParam('water_hose_spigot');
                } else {
                    array_push($this->view->erroredFields, 'water_hose_spigot');
                }

                $facility->setPremiumVehicleStorage(
                    $generalMaintenance,
                    $propane,
                    $dieselAndGas,
                    $doesStateInspections,
                    $autoCleaningAndDetailing,
                    $airPump,
                    $vacuumStation,
                    $iceMachine,
                    $waterHoseSpigot
                );

                if ($paymentAcceptCredit && count($paymentAcceptedCards) == 0) {
                    array_push($this->view->erroredFields, 'payment_accepted_cards');
                }

                Genesis_Service_Facility::save($facility, $this->getLoggedUser());

                $this->view->facility = $facility;

                if (count($this->view->erroredFields) == 1) {
                    $this->view->alert = 'You missed a spot. Tell us a little more.';
                    $this->view->alertClass = 'alert-danger';
                } elseif (count($this->view->erroredFields) > 1) {
                    $this->view->alert = 'You missed a few spots. Tell us a little more.';
                    $this->view->alertClass = 'alert-danger';
                } else {
                    $this->view->alert = 'Changes saved.';
                    $this->view->alertClass = 'alert-success';
                }

                //Discount errors
                if (count($this->view->erroredFields) > 0) {

                    if (in_array('military_discount_amount',$this->view->erroredFields)) {
                        $this->view->alert = 'Military discount must be a numeric percent';
                        $this->view->alertClass = 'alert-danger';
                    } elseif (in_array('student_discount_amount',$this->view->erroredFields)) {
                        $this->view->alert = 'Student discount must be a numeric percent';
                        $this->view->alertClass = 'alert-danger';
                    } elseif (in_array('senior_discount_amount',$this->view->erroredFields)) {
                        $this->view->alert = 'Senior discount must be a numeric percent';
                        $this->view->alertClass = 'alert-danger';

                    }
                }

            } catch (Exception $e) {
                $this->view->alert = $e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        // Get the payment types
        list(
            $this->view->paymentAcceptCash,
            $this->view->paymentAcceptCheck,
            $this->view->paymentAcceptCredit,
            $this->view->paymentAcceptedCards
            ) = $facility->getPaymentOptions();

        // Populate View w/ Vehicle Options
        list(
            $this->view->vehicleRequireTitle,
            $this->view->vehicleRenterTitled,
            $this->view->vehicleRequireRunning,
            $this->view->vehicleRequireInsurance,
            $this->view->vehicleRequireRegistration
            ) = $facility->getVehicleStorage();

        $this->setCommonViewFields();

        $this->view->scripts = array('facility/amenities');
    }

    public function hoursAction()
    {
        $this->view->customClosuresBlogPost = "https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility";
        $this->view->covidModal = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::COVID_MODAL);
        $this->view->customClosures = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::CUSTOM_CLOSURES);

        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->facility = $facility;
        $this->view->params = array('app' => 'myfoot', 'user' => $this->getLoggedUser());
        $this->view->erroredFields = array();

        //create new blank hours records if needed, view tries to use
        $officeHours = Genesis_Service_FacilityHours::loadById($this->getSession()->facilityId, 'office');
        if (! $officeHours) {
            $officeHours = new Genesis_Entity_FacilityHours();
            $officeHours->setFacilityId($facility->getId());
            $officeHours->setType(Genesis_Entity_FacilityHours::HOURS_TYPE_OFFICE);
            $officeHours->setManualEdit(0);
            Genesis_Service_FacilityHours::save($officeHours);
        }
        $this->view->officeHours = $officeHours;

        $accessHours = Genesis_Service_FacilityHours::loadById($this->getSession()->facilityId, 'access');
        if (! $accessHours) {
            $accessHours = new Genesis_Entity_FacilityHours();
            $accessHours->setFacilityId($facility->getId());
            $accessHours->setType(Genesis_Entity_FacilityHours::HOURS_TYPE_ACCESS);
            $accessHours->setManualEdit(0);
            Genesis_Service_FacilityHours::save($accessHours);
        }
        $this->view->accessHours = $accessHours;

        $this->view->usHolidays = Genesis_Util_Holidays::getHolidays();

        if ($_POST) {
            try {
                $obseredHolidays = $this->getParam('observed');
                if (! is_array($obseredHolidays)) {
                    $obseredHolidays = [];
                }
                $facility->setObservedHolidays(array_keys($obseredHolidays));

                $appointmentOnlyOfficeHours = array(
                    'mon'=>$this->getParam('appointment_only_office_hours_mon', 0),
                    'tue'=>$this->getParam('appointment_only_office_hours_tue', 0),
                    'wed'=>$this->getParam('appointment_only_office_hours_wed', 0),
                    'thu'=>$this->getParam('appointment_only_office_hours_thu', 0),
                    'fri'=>$this->getParam('appointment_only_office_hours_fri', 0),
                    'sat'=>$this->getParam('appointment_only_office_hours_sat', 0),
                    'sun'=>$this->getParam('appointment_only_office_hours_sun', 0),
                );
                $facility->setAppointmentOnlyOfficeHours($appointmentOnlyOfficeHours);

                //setup new office hours objects and save then,  save will update automaticaly if they already exist
                $officeHours = new Genesis_Entity_FacilityHours();
                $officeHours->setFacilityId($facility->getId());
                $officeHours->setType(Genesis_Entity_FacilityHours::HOURS_TYPE_OFFICE);
                $officeHours->setManualEdit(true);
                $officeHours->setSunStart($officeHours->formatTime($this->getParam('hrs_o_sun_s')));
                $officeHours->setSunEnd($officeHours->formatTime($this->getParam('hrs_o_sun_e')));
                $officeHours->setMonStart($officeHours->formatTime($this->getParam('hrs_o_mon_s')));
                $officeHours->setMonEnd($officeHours->formatTime($this->getParam('hrs_o_mon_e')));
                $officeHours->setTueStart($officeHours->formatTime($this->getParam('hrs_o_tue_s')));
                $officeHours->setTueEnd($officeHours->formatTime($this->getParam('hrs_o_tue_e')));
                $officeHours->setWedStart($officeHours->formatTime($this->getParam('hrs_o_wed_s')));
                $officeHours->setWedEnd($officeHours->formatTime($this->getParam('hrs_o_wed_e')));
                $officeHours->setThuStart($officeHours->formatTime($this->getParam('hrs_o_thu_s')));
                $officeHours->setThuEnd($officeHours->formatTime($this->getParam('hrs_o_thu_e')));
                $officeHours->setFriStart($officeHours->formatTime($this->getParam('hrs_o_fri_s')));
                $officeHours->setFriEnd($officeHours->formatTime($this->getParam('hrs_o_fri_e')));
                $officeHours->setSatStart($officeHours->formatTime($this->getParam('hrs_o_sat_s')));
                $officeHours->setSatEnd($officeHours->formatTime($this->getParam('hrs_o_sat_e')));
                $this->view->officeHours = $officeHours;
                $facility->setOfficeHours($officeHours);

                $accessHours = new Genesis_Entity_FacilityHours();
                $accessHours->setFacilityId($facility->getId());
                $accessHours->setType(Genesis_Entity_FacilityHours::HOURS_TYPE_ACCESS);
                $accessHours->setManualEdit(true);
                $accessHours->setSunStart($accessHours->formatTime($this->getParam('hrs_a_sun_s')));
                $accessHours->setSunEnd($accessHours->formatTime($this->getParam('hrs_a_sun_e')));
                $accessHours->setMonStart($accessHours->formatTime($this->getParam('hrs_a_mon_s')));
                $accessHours->setMonEnd($accessHours->formatTime($this->getParam('hrs_a_mon_e')));
                $accessHours->setTueStart($accessHours->formatTime($this->getParam('hrs_a_tue_s')));
                $accessHours->setTueEnd($accessHours->formatTime($this->getParam('hrs_a_tue_e')));
                $accessHours->setWedStart($accessHours->formatTime($this->getParam('hrs_a_wed_s')));
                $accessHours->setWedEnd($accessHours->formatTime($this->getParam('hrs_a_wed_e')));
                $accessHours->setThuStart($accessHours->formatTime($this->getParam('hrs_a_thu_s')));
                $accessHours->setThuEnd($accessHours->formatTime($this->getParam('hrs_a_thu_e')));
                $accessHours->setFriStart($accessHours->formatTime($this->getParam('hrs_a_fri_s')));
                $accessHours->setFriEnd($accessHours->formatTime($this->getParam('hrs_a_fri_e')));
                $accessHours->setSatStart($accessHours->formatTime($this->getParam('hrs_a_sat_s')));
                $accessHours->setSatEnd($accessHours->formatTime($this->getParam('hrs_a_sat_e')));
                $this->view->accessHours = $accessHours;
                $facility->setAccessHours($accessHours);

                if (strlen($this->getParam('twentyfour_hr_access')) > 0) {
                    $facility->setTwentyFourHourAccess($this->getParam('twentyfour_hr_access') ? 1 : 0);

                    if ($this->getParam('twentyfour_hr_access') == 1) {

                        // 24-hr access without restrictions
                        // overwrite hours with midnight-to-midnight
                        $accessHours = Genesis_Service_FacilityHours::buildTwentyFourHours($facility->getId());
                        $this->view->accessHours = $accessHours;
                        $facility->setAccessHours($accessHours);
                        $facility->setTwentyFourHourAccessRestricted(0);
                        $facility->setTwentyFourHourAccessSupplemental(array());

                    } if ($this->getParam('twentyfour_hr_access') == 2) {

                        // yes with restrictions
                        if ($this->getParam('twentyfour_hr_access_type')) {
                            $facility->setTwentyFourHourAccessRestricted(1);
                            $facility->setTwentyFourHourAccessSupplemental($this->getParam('twentyfour_hr_access_type'));
                        } else {
                            $facility->setTwentyFourHourAccessRestricted(0);
                            // if 24-hr access restricted is set, some restrictions MUST be specified
                            //array_push($this->view->erroredFields, 'twentyfour_hr_access_restricted');
                        }

                        if (in_array('for additional $%s per month', $this->getParam('twentyfour_hr_access_type'))) {
                            if ($this->getParam('twentyfour_hr_access_fee') > 0) {
                                $facility->setTwentyFourHourAccessFee($this->getParam('twentyfour_hr_access_fee'));
                            } else {
                                array_push($this->view->erroredFields, 'twentyfour_hr_access_fee');
                            }
                        }
                    } else {
                        // no 24-hr access
                        $facility->setTwentyFourHourAccessRestricted(0);
                        $facility->setTwentyFourHourAccessSupplemental(array());
                    }
                } else {
                    array_push($this->view->erroredFields, 'twentyfour_hr_access');
                }

                Genesis_Service_Facility::save($facility, $this->getLoggedUser());

                $this->view->alert = 'Changes saved.';
                $this->view->alertClass = 'alert-success';

            } catch (Exception $e) {
                $this->view->alert = '<strong<>Error</strong>: '.$e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        $closures = array();
        foreach ($facility->getUpcomingClosures() as $facClsObj) {

            $closures[] = array('closureId' => $facClsObj->getId(),
                'closedDateStart' => $facClsObj->getClosedDateStart(),
                'closedDateEnd' => $facClsObj->getClosedDateEnd(),
                'dateString' => $facClsObj->getDisplayDate()
            );
        }

        $this->view->observedHolidays = (array) $facility->getObservedHolidays();
        $this->view->upcomingClosures = $closures;

        $this->setCommonViewFields();

        $this->view->scripts = array('facility/hours');
    }

    public function addfacilityclosureAction()
    {
        //disable zend layout so it doesn't return all the html
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $startDate = $this->getParam('startDate');
        $endDate = $this->getParam('endDate');
        $facilityId = $this->getParam('fid');

        try {

            $facClosure = Genesis_Service_FacilityClosure::createFromFacilityIdStartDateAndEndDate($facilityId, $startDate, $endDate, $this->getLoggedUser());

            // TODO: add context
            $facility = Genesis_Service_Facility::loadById($facClosure->getFacilityId());
            $user = $this->getLoggedUser();
            $context['account_id'] = $facility->getAccountId();
            $context['closure_start_date'] = $facClosure->getClosedDateStart();
            $context['closure_end_date'] = $facClosure->getClosedDateEnd();
            $context['closure_active'] = $facClosure->getActive();
            $this->statsClient->increment('myfoot.features.add_custom_closure', $context);

            //return response
            header('Content-Type: application/json');
            echo json_encode(array(
                'closureId' => $facClosure->getId(),
                'closedDateStart' => $facClosure->getClosedDateStart(),
                'closedDateEnd' => $facClosure->getClosedDateEnd(),
                'dateString' => $facClosure->getDisplayDate()
            ));

        } catch (Exception $e) {
            http_response_code(400);
            echo "Error: ",  $e->getMessage(), "\n";
            Genesis_Util_ErrorLogger::exceptionToHipChat($e);
        }
    }

    public function updatefacilityclosureAction()
    {
        //disable zend layout so it doesn't return all the html
        $this->_helper->layout->disableLayout();

        //don't render a response
        $this->_helper->viewRenderer->setNoRender(true);

        $facClosure = Genesis_Service_FacilityClosure::loadById($this->getParam('closureId'));
        $facClosure->setClosedDateStart($this->getParam('startDate'));
        if ($this->getParam('endDate') && $this->getParam('endDate') != $this->getParam('startDate')) {
            $facClosure->setClosedDateEnd($this->getParam('endDate'));
        }

        try {
            $facClosure = Genesis_Service_FacilityClosure::save($facClosure);

            // TODO: add context
            $facility = Genesis_Service_Facility::loadById($facClosure->getFacilityId());
            $user = $this->getLoggedUser();
            $context['account_id'] = $facility->getAccountId();
            $context['closure_start_date'] = $facClosure->getClosedDateStart();
            $context['closure_end_date'] = $facClosure->getClosedDateEnd();
            $context['closure_active'] = $facClosure->getActive();
            $this->statsClient->increment('myfoot.features.edit_custom_closure', $context);

            //return response
            echo json_encode(array('closureId' => $facClosure->getId(),
                'closedDateStart' => $facClosure->getClosedDateStart(),
                'closedDateEnd' => $facClosure->getClosedDateEnd(),
                'dateString' => $facClosure->getDisplayDate()
            ));
        } catch (Exception $e) {
            Genesis_Util_ErrorLogger::exceptionToHipChat($e);
        }
    }

    public function removefacilityclosureAction()
    {
        //disable zend layout so it doesn't return all the html
        $this->_helper->layout->disableLayout();

        //don't render a response
        $this->_helper->viewRenderer->setNoRender(true);
        $closureID = $this->getParam('closureId');

        $facClosure = Genesis_Service_FacilityClosure::loadById($closureID);
        if ($facClosure) {
            $facClosure->setActive(0);
            Genesis_Service_FacilityClosure::save($facClosure, $this->getLoggedUser());

            // TODO: add context
            $facility = Genesis_Service_Facility::loadById($facClosure->getFacilityId());
            $user = $this->getLoggedUser();
            $context['account_id'] = $facility->getAccountId();
            $context['closure_start_date'] = $facClosure->getClosedDateStart();
            $context['closure_end_date'] = $facClosure->getClosedDateEnd();
            $context['closure_active'] = $facClosure->getActive();
            $this->statsClient->increment('myfoot.features.remove_custom_closure', $context);
        } else {
            http_response_code(400);
            echo $string =  "Error: closure id (" . $closureID . ") not found.\n";
            $e = new Exception($string);
            Genesis_Util_ErrorLogger::exceptionToHipChat($e);
        }

        http_response_code(200);
        echo "OK";
    }

    public function updateinventoryAction()
    {
        //disable zend layout so it doesn't return all the html
        $this->_helper->layout->disableLayout();

        //don't render a response
        $this->_helper->viewRenderer->setNoRender(true);

        // TODO: Kaleb added
        Genesis_Util_Debug::startProfile();

        try {
            $type = $this->getParam('type');

            //check what type of call this is
            if ($type == 'price') {
                $unitId = $this->getParam('unit_id');
                $sparefootPrice = $this->getParam('sf_price');
                $unit = Genesis_Service_StorageSpace::loadById($unitId);

                //TODO: some server side checking
                //sparefoot price cannot be higher than monthly rent
                if ($sparefootPrice <= $unit->getRegularPrice()) {
                    //$this->_logUnitAttributeChange('edit_sf_price', $unit->getSparefootPrice(), $sparefootPrice, $unit);
                    $unit->setSparefootPrice($sparefootPrice);
                    $unit = Genesis_Service_StorageSpace::save($unit, $this->getLoggedUser()->getId());
                } else {
                    throw new Exception('Error: SpareFoot price must be <= regular price.');
                }
            } elseif ($type === 'show' || $type === 'hide') {
                $unitIds = $this->getParam('unit_ids'); //comma separated list
                $unitIds = explode(",", $unitIds);

                $facilities = array();
                $active = $type === 'show' ? 1 : 0;

                foreach ($unitIds as $unitId) {
                    $unit = Genesis_Service_StorageSpace::loadById($unitId);
                    if (! $unit) {
                        continue;
                    }

                    if ($this->getLoggedUser()->isMyFootGod() || $this->getLoggedUser()->canAccessFacility($unit->getFacility())) {
                        $facilities[$unit->getFacilityId()] = $unit->getFacility();

                        $unit->setActive($active);

                        $unit = Genesis_Service_StorageSpace::save($unit, $this->getLoggedUser()->getId());

                        // side effect
                        // make the facility active if its off, and we turned a unit on.
                        // ...not in the service since we don't want the behavior from api sync
                        if ($active && ! $unit->getFacility()->getActive()) {
                            $facility = $unit->getFacility();
                            $facility->setActive(1);
                            Genesis_Service_Facility::save($facility);
                            Genesis_Util_ActionLogger::logAction(
                                'unhide_facility_reason',
                                null,
                                'Auto activate for available inventory',
                                $this->getLoggedUser()->getId(),
                                $facility->getId(),
                                $unit->getId());
                        }

                        echo $unit->getActive();
                    }
                }

            } else {
                throw new Exception ('unknown update action: '. $type);
            }
        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
        // TODO: Kaleb added
        echo Genesis_Util_Debug::endProfile();
    }

    public function updatefacilityAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        try {

            $type = $this->getParam('type');

            $facilityIds = explode(",", $this->getParam('facility_ids'));

            foreach ($facilityIds as $facilityId) {

                try {
                    $facility = Genesis_Service_Facility::loadById($facilityId);
                } catch (Exception $e) {
                    continue;
                }

                if ($this->getLoggedUser()->isMyFootGod() || $this->getLoggedUser()->canAccessFacility($facility)) {
                    if ($type === 'show') {
                        //$this->_logFacilityAttributeChange('edit_facility_visibility', $facility->getActive(), '1', $facility);
                        $facility->setActive(1);
                    } elseif ($type === 'hide') {
                        //$this->_logFacilityAttributeChange('edit_facility_visibility', $facility->getActive(), '0', $facility);
                        $facility->setActive(0);
                    }
                }
                $logReasons = array(
                    'hide_facility_reason' => $this->getParam('hide_facility_reason'),
                    'hide_facility_when_can_reactivate' => $this->getParam('hide_facility_when_can_reactivate'),
                    'hide_facility_how_full' => $this->getParam('hide_facility_how_full'),
                    'hide_facility_unhappy_because' => $this->getParam('hide_facility_unhappy_because'),
                    'hide_facility_new_owner' => $this->getParam('hide_facility_new_owner'),
                );
                foreach ($logReasons as $action_name => $reason) {
                    if (!$reason) {
                        continue;
                    }
                    $action_log = new Genesis_Entity_ActionLog();
                    $action_log->setFacilityId($facilityId);
                    $action_log->setParam($action_name);
                    $action_log->setPreValue(null);
                    $action_log->setPostValue($reason);
                    $action_log->setUserId($this->getLoggedUser()->getId());
                    $action_log->setTimestamp(date("Y-m-d H:i:s"));
                    Genesis_Service_ActionLog::save($action_log);
                }
                //check to see if the user wants to reactivate on a certain date
                if ($this->getParam('reactivation_select') === 'yes') {
                    $date = DateTime::createFromFormat('m-d-Y', $this->getParam('reactivation_date'));
                    $reactivation_date = $date->format('Y-m-d');
                    $facility->setAutomaticReactivationDate($reactivation_date);
                }

                $facility = Genesis_Service_Facility::save($facility, $this->getLoggedUser());

                // Create ticket if customer is unhappy
                $reasonUnhappy = $logReasons['hide_facility_unhappy_because'];
                if ($reasonUnhappy) {
                    AccountMgmt_Service_Facility::createUnhappyFacilityTicket($facility, $reasonUnhappy, $this->getLoggedUser());
                }

                // or sold the facility
                $reasonNewOwner = $logReasons['hide_facility_new_owner'];
                if ($reasonNewOwner) {
                    AccountMgmt_Service_Facility::createChangeOfOwnershipTicket($facility, $reasonNewOwner, $this->getLoggedUser());
                }
            }

            // The following line works for god users too, because listAction sets this dynamically for gods.
            $acct = $this->getLoggedUser()->getAccount();
            $nameEmail = $this->getLoggedUser()->getFirstName() .
                ' ' . $this->getLoggedUser()->getLastName() .
                ' (' . $this->getLoggedUser()->getEmail() . ')';

            if ($acct->getNumBillableEntities() > 0 && $acct->getNumActiveFacilities() == 0) {
                $msg = new Genesis_Entity_EmailMessage();

                $user_account_name = ($this->getLoggedUser()->getAccount()) ? $this->getLoggedUser()->getAccount()->getName() : '';
                $msg->setSubject($acct->getName() . ' has hidden all their facilities.');
                $msg_str =
                    'The MyFoot user ' . $nameEmail . ' has hidden all the facilities for the account ' .
                    $acct->getInfoString() . '. Please check to see whether or not this is still the case.';

                $msg->setBody(Genesis_Util_Formatter::formatSalesForceEmailContent($msg_str));
                $mailto = ($this->getLoggedUser()->isMyFootGod()) ? '' : '<EMAIL>';
                Genesis_Service_Mailer::sendInternalMessage($mailto, $msg, array(), $this->getLoggedUser()->getFirstName() . ' ' . $this->getLoggedUser()->getLastName(), $this->getLoggedUser()->getEmail());
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo $e->getMessage();
        }
    }

    public function photosAction()
    {

        // Ensure the user has access to this facility
        $facilityId = $this->getParam('fid');
        $user = $this->getLoggedUser();
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) { 
            $this->view->errorMessages [] = "You do not have permission to access this facility.";
            throw new Exception('Unauthorized access to facility.', 403);
        }

        if ($_POST) {
            $maxsize=5242880; // 5MB

            $fileCount = count($_FILES['image']['name']);

            for ($i = 0; $i < $fileCount; $i++) {

                if ($_FILES['image']['size'][$i] < $maxsize) {
                    $extension = explode('.', $_FILES['image']['name'][$i]);
                    $extension = $extension[count($extension) - 1];

                    $facilityId = $this->getParam('fid');

                    $failure = 0;
                    $savedImage = null;

                    try {
                        if ($this->getParam('logo')) {
                            Genesis_Service_FacilityImage::append($facilityId, $extension, $_FILES['image']['tmp_name'][$i], true);
                        } else {
                            $savedImage = Genesis_Service_FacilityImage::append($facilityId, $extension, $_FILES['image']['tmp_name'][$i]);
                        }
                    } catch (Exception $e) {
                        $this->view->errorMessages[] = 'This image cannot be uploaded. Please upload another image.';
                        $this->view->errorMessages[] = 'Reason: ' . $e->getMessage();
                        $failure = 1;
                    }

                    if (!$failure) {
                        $this->view->successMessages[] = 'New image uploaded.';
                        // images uploaded by god-level users are auto-reviewed
                        $savedImage->setReviewed($this->getLoggedUser()->isMyFootGod() ? 1 : 0);
                        $savedImage->setUploadedByUserId($this->getLoggedUser()->getUserId());
                        $savedImage = Genesis_Service_FacilityImage::save($savedImage);
                    }

                } else {
                    $this->view->errorMessages[] = 'Image too big! Uploads must be smaller than 5 MB.';
                }
            }
        }
        $this->view->facility =$facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->customClosuresBlogPost = "https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility";
        $this->view->covidModal = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::COVID_MODAL);
        $this->view->customClosures = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::CUSTOM_CLOSURES);

        if ($this->getParam('logo')) {
            $this->forward('setup', 'Sites', null, null);
        }

        $this->setCommonViewFields();

        $this->view->scripts = array('facility/photos');
    }

    public function deleteimageAction()
    {
        $facilityId    = $this->getParam('fid');
        $pictureNumber = $this->getParam('number');

        // Ensure the user has access to this facility
        $user = $this->getLoggedUser();
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) { 
            $this->view->errorMessages [] = "You do not have permission to access this facility.";
            throw new Exception('Unauthorized access to facility.', 403);
        }

        Genesis_Service_FacilityImage::delete($facilityId, $pictureNumber);
        $this->redirect($this->view->url(['action'=> 'photos'], 'features').'?fid='.$facilityId);
    }

    public function defaultimageAction()
    {
        $facilityId    = $this->getParam('fid');
        $pictureNumber = $this->getParam('number');

         // Ensure the user has access to this facility
        $user = $this->getLoggedUser();
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) { 
            $this->view->errorMessages [] = "You do not have permission to access this facility.";
            throw new Exception('Unauthorized access to facility.', 403);
        }

        Genesis_Service_FacilityImage::setDefault($facilityId, $pictureNumber);
        $this->redirect($this->view->url(['action'=> 'photos'], 'features').'?fid='.$facilityId);
    }

    public function addAction()
    {
        //clear active facilityID
        if ($this->getParam('new') == true) {
            $this->getSession()->facilityId = false;
        }

        //clear session data when they hit the page to add a manual
        $this->getSession()->facIds = null;

        $this->view->csrf_token = CsrfUtil::getToken(self::ADD_FACILITY_CSRF_TOKEN);
        $this->view->facName = "";
        $this->view->facCoCode = "";
        $this->view->facURL = "";
        $this->view->facAdminFee = "";
        $this->view->facAddress = "";
        $this->view->facPhone = "";
        $this->view->facCity = "";
        $this->view->facState = "";
        $this->view->facZip = "";
        $this->view->facPromo = "";
        $this->view->facDesc = "";
        $this->view->update = 0;

        //prepopulate if there is an fid passed in
        if ($this->getSession()->facilityId) {
            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $location = $facility->getLocation();
            $this->view->update = 1;
            $this->view->facName = $facility->getTitle();
            $this->view->facCoCode = $facility->getCompanyCode();
            $this->view->facURL = $facility->getUrl();
            $this->view->facAdminFee = $facility->getAdminFee();
            if ($location) {
                $this->view->facAddress = $location->getAddress1();
                $this->view->facCity = $location->getCity();
                $this->view->facState = $location->getState();
                $this->view->facZip = $location->getZip();
            }
            $this->view->facPhone = $facility->getPhone();
            $this->view->facPromo = $facility->getSpecials();
            $this->view->facDesc = $facility->getDescription();
        }

        //pass user list that will have access to this facility by default
        $account = $this->getLoggedUser()->getAccount();

        //admins and full users will initially have access to this facility

        $accessUAs = Genesis_Service_UserAccess::load(
            Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::equal('accountId', $account->getId()),
                Genesis_Db_Restriction::or_(
                    Genesis_Db_Restriction::equal('myfootRole', Genesis_Entity_UserAccess::ROLE_ADMIN),
                    Genesis_Db_Restriction::equal('myfootRole', Genesis_Entity_UserAccess::ROLE_FULL)
                )
            )
        );

        $accessEmails = array();

        foreach ($accessUAs as $ua) {
            $accessEmails[] = $ua->getEmail();
        }

        $accessEmails = array_unique($accessEmails);
        $this->view->accessemails = implode(", ", $accessEmails);

        $reservationUAs = Genesis_Service_UserAccess::load(
            Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::equal('accountId', $account->getId()),
                Genesis_Db_Restriction::equal('getsEmails', 1),
                Genesis_Db_Restriction::or_(
                    Genesis_Db_Restriction::equal('myfootRole', Genesis_Entity_UserAccess::ROLE_ADMIN),
                    Genesis_Db_Restriction::equal('myfootRole', Genesis_Entity_UserAccess::ROLE_FULL)
                )
            )
        );

        $reservationEmails = array();

        foreach ($reservationUAs as $ua) {
            $reservationEmails[] = $ua->getEmail();
        }

        $reservationEmails = array_unique($reservationEmails);
        $this->view->reservationemails = implode(", ", $reservationEmails);

        $this->view->scripts = array('facility/add');
    }

    //called from add page to create a new facility
    public function addfacilityAction()
    {
        // Genesis_Util_Debug::startProfile();
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $update = false;

        //see if this is an update
        if ($this->getParam('update')) {
            $update = true;
        }

        try {
            $emails = array();

            //check valid email addresses
            if (! $update && strlen($this->getParam('reservation_emails')) > 0) {
                //format check emails
                $emails = explode(",", $this->getParam('reservation_emails'));
                $emails = array_unique($emails);

                foreach ($emails as $email) {
                    if (!(strlen($email) > 0)) {
                        continue;
                    }

                    $email = trim($email);

                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception($email . " is an invalid email address");
                    }
                }
            }

            //validate zip
            if (!(strlen($this->getParam('zip')) > 0) || !preg_match("/^\d{5}$|^\d{5}-\d{4}$/", $this->getParam('zip'))) {
                throw new Exception('Please enter a valid zip code.');
            }

            //validate phone
            if (!(strlen($this->getParam('phone')) > 0) || !preg_match("/[0-9]{7,14}/",preg_replace("/[^0-9]/", "", $this->getParam('phone')))) {
                throw new Exception('Please enter a valid phone number');
            }


            //if this account does not have a manual integration, then add one
            $corp = $this->getLoggedUser()->getAccount()->getManualIntegration();
            if (!$corp) {
                $corp = new Genesis_Entity_ManualCorporation();
                $corp->setAccountId($this->getLoggedUser()->getAccountId());
                $corp->setCreated(date("Y-m-d H:i:s", time()));
                $corp->setSourceId(Genesis_Entity_Source::ID_MANUAL);
                $corp->setCorpname($this->getLoggedUser()->getAccount()->getName());

                $corp = Genesis_Service_Corporation::save($corp);
            }

            //check that a facility with this name and corp doesn't already exist
            $exist_facility = Genesis_Service_Facility::load(
                Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::equal('title', stripslashes($this->getParam('name'))),
                    Genesis_Db_Restriction::equal('corporationId', $corp->getId())))->uniqueResult();
            if ($exist_facility) {
                throw new Exception('Facility with this name already exists in your account.');
            }

            //create new location
            $location = Genesis_Service_Location::loadByAddress(
                $this->getParam('address'),
                $this->getParam('city'),
                $this->getParam('state'),
                $this->getParam('zip')
            );

            //does this location already exist
            if (! $location) {
                //call geocoder
                $location = Genesis_Service_Location::geoCodePhysicalAddress(
                    $this->getParam('address') . " " . $this->getParam('city') . " " . $this->getParam('state') . " " . $this->getParam('zip')
                );
                $location = Genesis_Service_Location::save($location);
            }


            //$locationId = $location->getId();

            //create new facility or update w/session id if update = true
            if ($update && $this->getSession()->facilityId) {
                $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            } else {
                $facility = new Genesis_Entity_Facility();
            }
            //pull this out of session. it was set during
            if (isset($this->getSession()->selfReportedSourceId)) {
                $facility->setSelfReportedSourceId($this->getSession()->selfReportedSourceId);
            }
            $facility->setTitle(stripslashes($this->getParam('name')));
            $facility->setPhone(preg_replace("/[^0-9]/", "", $this->getParam('phone')));
            $facility->setLocationId($location->getId());
            $facility->setLocation($location);
            $facility->setActive(0);
            $facility->setPublished(1);
            $facility->setApproved(1);
            $facility->setSourceId(Genesis_Entity_Source::ID_MANUAL);

            $facility->setNetworkSubscriptionFee($this->getLoggedUser()->getAccount()->getNewFacilitySubscriptionFee());

            //configure products to account defaults
            $cpa = $this->getLoggedUser()->getAccount()->getCpa();
            $facility->setTenantConnect($cpa); // CPA facility Tenant Connect enabled by default
            $facility->setCpa($cpa);

            //turn on insights feature for all new signups
            $facility->setInsights(1);

            //if they are adding facility here then it is to the manual integration.  Only 1 manual integration per account
            $facility->setCorporationId($this->getLoggedUser()->getAccount()->getManualIntegration()->getId());

            //check for another published facility in this location
            $exist_facility = Genesis_Service_Facility::load(
                Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::equal('locationId', $location->getId()),
                    Genesis_Db_Restriction::equal('published', 1)
                )
            )->uniqueResult();

            if ($exist_facility && $exist_facility->getLocationId() &&
                $exist_facility->getId() != $facility->getId()) {

                $user_account_name = ($this->getLoggedUser()->getAccount()) ? $this->getLoggedUser()->getAccount()->getName() : '';

                //instead of throwing an exception here, just email support a notification
                $msg = new Genesis_Entity_EmailMessage();
                $msg->setSubject('MyFoot: Duplicate Facility Location Created');
                $msg_str = 'A user created a facility at an address where another facility is already on file.<br/><br/>' .
                    'New Facility Details:<br/>' .
                    'Account: ' . $this->getLoggedUser()->getAccount()->getName() . '<br/>' .
                    'Name: ' . $facility->getTitle() . '<br/>' .
                    'Entered Address:' . $this->getParam('address') . " " .
                    $this->getParam('city') . " " .
                    $this->getParam('state') . " " .
                    $this->getParam('zip') . '<br/><br/>' .
                    'Existing Facility Details:<br/>' .
                    'Account: ' . $this->getLoggedUser()->getAccount()->getName() . '<br/>' .
                    'Name: ' . $exist_facility->getTitle() . '<br/>' .
                    'Address: ' . $exist_facility->getLocation()->getAddress1() . " " .
                    $exist_facility->getLocation()->getCity() . " " .
                    $exist_facility->getLocation()->getState() . " " .
                    $exist_facility->getLocation()->getZip() . '<br/><br/>' .
                    'User Details:<br/>' .
                    'Account: ' . $user_account_name . '<br/>' .
                    'Name: ' . $this->getLoggedUser()->getFirstName() . ' ' . $this->getLoggedUser()->getLastName() .'<br/>' .
                    'Email: ' . $this->getLoggedUser()->getEmail() .'<br/>';

                $msg->setBody(Genesis_Util_Formatter::formatSalesForceEmailContent($msg_str));
                $mailto = ($this->getLoggedUser()->isMyFootGod()) ? '' : '<EMAIL>' ;
                Genesis_Service_Mailer::sendInternalMessage($mailto, $msg, array(), $this->getLoggedUser()->getFirstName() . ' ' . $this->getLoggedUser()->getLastName(),$this->getLoggedUser()->getEmail());
            }

            $facility = Genesis_Service_Facility::save($facility, $this->getLoggedUser());

            //create new users for any emails put in (validated previously), on creation only, to edit users they'll have to do it in users tab
            if (!$update && count($emails) > 0) {
                foreach ($emails as $email) {
                    $email = trim($email);

                    $user = Genesis_Service_User::load(Genesis_Db_Restriction::equal('email', $email))->uniqueResult();

                    if (!$user) {
                        $user = new Genesis_Entity_User();
                        $user->setEmail($email);
                        $user = Genesis_Service_User::save($user);
                    }

                    $newUserAccess = Genesis_Service_UserAccess::loadById($user->getId());

                    //setup user access if needed
                    if (!$newUserAccess) {
                        $newUserAccess = new Genesis_Entity_UserAccess();
                        $newUserAccess->setUserId($user->getId());
                        $newUserAccess->setGetsEmails(1);
                        $newUserAccess->setAccountId($this->getLoggedUser()->getAccountId());
                    } else {
                        //skip existing user if not on this account
                        if ($newUserAccess->getAccountId() != $this->getLoggedUser()->getAccountId()) {
                            continue;
                            //throw new Exception('User ' . $email . ' already has a MySpareFoot signin for another account.  <NAME_EMAIL> for help.');
                        }

                        //confirm existing user gets emails
                        $newUserAccess->setGetsEmails(1);
                    }

                    $newUserAccess = Genesis_Service_UserAccess::save($newUserAccess);


                    //give them access to this facility in fac restrictions if not already there
                    if (!$newUserAccess->getAllFacilities() &&
                        !Genesis_Service_UserFacilityRestrictions::loadById($facility->getId(), $user->getId())
                    ) {
                        $facRes = new Genesis_Entity_UserFacilityRestrictions();
                        $facRes->setFacilityId($facility->getId());
                        $facRes->setUserId($user->getId());
                        Genesis_Service_UserFacilityRestrictions::save($facRes);
                    }

                }
            }

            //set up a manual facility
            $db_connection = Genesis_Db_Connection::getInstance();
            $stmt = $db_connection->prepare("SELECT * FROM manual_facilities WHERE listing_avail_id ='" . $facility->getId() . "'");
            $stmt->execute();
            if ($stmt->fetch()) {
                //update contact
                $stmt = $db_connection->prepare("UPDATE manual_facilities SET email = '', manual_corp_id = '" . $this->getLoggedUser()->getAccount()->getManualIntegration()->getId() . "' WHERE listing_avail_id = ". $facility->getId());
                $stmt->execute();
            } else {
                //write new contact
                $stmt = $db_connection->prepare("INSERT INTO manual_facilities (listing_avail_id, manual_corp_id, email) VALUES (" . $facility->getId() . "," . $this->getLoggedUser()->getAccount()->getManualIntegration()->getId() . ",'')");
                $stmt->execute();
            }

            //send emails if new facilities exist
            try {
                $this->notifyOnNewFacilities($facility);
            } catch (Exception $e) {
                echo "Error: " . $e->getMessage() . "\n";
            }
            //die ("Error: ".Genesis_Util_Debug::endProfile());
            //send back facility id for re-direction url
            echo $facility->getId();

        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }

    //called from add page to create and units
    public function unitAction()
    {
        //disable zend layout so it doesn't return all the html
        $this->_helper->layout->disableLayout();

        //don't render a response
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            if (!$this->hasParam('unit_id')) {
                throw new Exception('You must bring a unit_id to edit');
            }

            //put unit ids into array in case there are a lot of them
            if ($this->getParam('unit_id')) {
                $unitIds = explode(",", $this->getParam('unit_id'));
            } else {
                //create new space based on type if no ids passed in (MANUALS do this)
                switch ($this->getParam('unit_type')) {
                    case Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT:
                        $space = new Genesis_Entity_StorageUnit();
                        break;
                    case Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                        $space = new Genesis_Entity_ParkingSpace();
                        break;
                    case Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                        $space = new Genesis_Entity_Workspace();
                        break;
                    case Genesis_Entity_StorageSpace::TYPE_WINE:
                        $space = new Genesis_Entity_WineStorage();
                        break;
                    case Genesis_Entity_StorageSpace::TYPE_LOCKER:
                        $space = new Genesis_Entity_StorageLocker();
                        break;
                    case Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                        $space = new Genesis_Entity_StorageOutdoor();
                        break;
                    default:
                        throw new Exception('Error selecting the unit type');
                        break;
                }

                //set active and published for newly added units
                $space->setActive(1);
                $space->setPublish(1);
                $space->setQuantity(1);

                $space->setFacilityId($this->getSession()->facilityId);

                if ($this->getParam('unit_type')) {
                    $space->setType($this->getParam('unit_type'));
                }

                // set price for new unit
                $space->setRegularPrice($this->getParam('price_regular'));

                $savedSpace = Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());

                $al = new Genesis_Util_ActionLogger();
                $al->logAction('add_unit', 0, 0, $this->getLoggedUser()->getId(), $this->getSession()->facilityId, $savedSpace->getId());

                //put id in unitids array
                $unitIds = array($savedSpace->getId());
            }

            $integratedFields = $this->_getIntegratedFields();

            //now we have $unitIds array to update rest of params
            foreach ($unitIds as $unitId) {

                //determine if this is a new creation or an update
                $space = Genesis_Service_StorageSpace::loadById($unitId);
                if(isset($space) && !empty($space)) {
                    $corporation = $space->getFacility()->getCorporation();
                    $spaceOriginal = clone $space;

                    // make sure existing supp data gets lazy loaded
                    $space->getSupplementalData();

                    //populate fields that all types of units have
                    if ($this->hasParam('unit_width')) {
                        $space->setWidth($this->getParam('unit_width'));
                    }

                    if ($this->hasParam('unit_length')) {
                        $space->setLength($this->getParam('unit_length'));
                    }

                    if ($this->hasParam('floor')) {
                        $space->setFloor($this->getParam('floor')); //form passes in 1 when outdoor
                    }

                    if ($this->hasParam('description')) {
                        $space->setDescription(
                            strlen($this->getParam('description')) ? $this->getParam('description') : null
                        );
                    }

                    //if we aren't syncing promos....
                    if ($this->hasParam('promotion') && $corporation && !$corporation->getPullPromos()) {
                        if (strlen($this->getParam('promotion')) > 100) {
                            throw new Exception('Promotion must be less than 100 characters.');
                        }

                        $space->setSpecialString(
                            strlen($this->getParam('promotion')) ? $this->getParam('promotion') : null
                        );
                    }

                    ($this->hasParam('deposit')) ? $space->setDeposit($this->getParam('deposit') ? $this->getParam('deposit') : null) : '';

                    //only update/change regular price for manuals
                    if ($this->hasParam('price_regular') && $this->getParam('price_regular') &&
                        $this->hasParam('source_type') &&
                        $this->getParam('source_type') == Genesis_Entity_Source::ID_MANUAL) {
                        $space->setRegularPrice($this->getParam('price_regular'));
                    }

                    if ($this->hasParam('price_sf')) {
                        if (is_numeric($this->getParam('price_sf'))) {
                            if ($this->getParam('price_sf') <= 0) {
                                throw new Exception('SpareFoot price must be greater than 0.');
                            }

                            if ($this->getParam('price_sf') > $space->getRegularPrice()) {
                                throw new Exception('SpareFoot price must be less than or equal to regular price.');
                            } else {
                                //$this->_logUnitAttributeChange('edit_sf_price', $space->getSparefootPrice(), $this->getParam('price_sf'), $space);
                                $space->setSparefootPrice($this->getParam('price_sf'));
                            }
                        } else {
                            $space->setSparefootPrice(null);
                        }
                    }

                    if(!in_array('stacked', $integratedFields)) {
                        $space->setSkybox($this->getParam('stacked') == 'on' ? 1 : 0);
                    }

                    if(!in_array('premium', $integratedFields)) {
                        $space->setPremiumUnit($this->getParam('premium') == 'on' ? 1 : 0);
                    }

                    if(!in_array('heated', $integratedFields)) {
                        $space->setHeatedOnly($this->getParam('heated') == 'on' ? 1 : 0);
                    }

                    if(!in_array('aircooled', $integratedFields)) {
                        $space->setAirCooledOnly($this->getParam('aircooled') == 'on' ? 1 : 0);
                    }

                    if(!in_array('ada', $integratedFields)) {
                        $space->setAdaAccessible($this->getParam('ada') == 'on' ? 1 : 0);
                    }

                    if(!in_array('unitlights', $integratedFields)) {
                        $space->setUnitLights($this->getParam('unitlights') == 'on' ? 1 : 0);
                    }

                    if(!in_array('twentyfourhouraccess', $integratedFields)) {
                        $space->setTwentyFourHourAccess($this->getParam('twentyfourhouraccess') == 'on' ? 1 : 0);
                    }

                    if(!in_array('shelvesinunit', $integratedFields)) {
                        $space->setShelvesInUnit($this->getParam('shelvesinunit') == 'on' ? 1 : 0);
                    }

                    if(!in_array('basement', $integratedFields)) {
                        $space->setBasement($this->getParam('basement') == 'on' ? 1 : 0);
                    }

                    if(!in_array('parkingwarehouse', $integratedFields)) {
                        $space->setParkingWarehouse($this->getParam('parkingwarehouse') == 'on' ? 1 : 0);
                    }

                    if(!in_array('pullthru', $integratedFields)) {
                        $space->setPullThrough($this->getParam('pullthru') == 'on' ? 1 : 0);
                    }

                    if ($this->hasParam('unit_type') && $this->getParam('unit_type') != $space->getType()) {
                        $space->setType($this->getParam('unit_type'));
                        // We have to save the space here to "transform" it into the
                        // proper class (Genesis_Entity_ParkingSpace, etc.)
                        $space = Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                    }

                    //populate fields based on type
                    switch ($this->getParam('unit_type')) {
                        case Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                            if(!in_array('covered', $integratedFields)) {
                                $space->setCovered($this->getParam('covered') == 'true' ? 1 : 0);
                            }

                            ($this->hasParam('unit_height')) ? $space->setHeight(($this->getParam('unit_height')) ? $this->getParam('unit_height') : null) : '';

                            if(!in_array('power', $integratedFields)) {
                                $space->setPower($this->getParam('power') == 'on' ? 1 : 0);
                            }

                            //NOTE: vehicle/driveup set to 1 in class constructor
                            break;
                        case Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                            if(!in_array('covered', $integratedFields)) {
                                $space->setCovered($this->getParam('covered') == 'true' ? 1 : 0);
                            }

                            if(!in_array('driveup', $integratedFields)) {
                                $space->setDriveUp($this->getParam('driveup') == 'true' ? 1 : 0);
                            }

                            ($this->hasParam('unit_height')) ? $space->setHeight(($this->getParam('unit_height')) ? $this->getParam('unit_height') : null) : '';

                            if ($this->hasParam('vehicle')) {
                                if ($this->getParam('vehicle') == 'only') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(1);
                                } elseif ($this->getParam('vehicle') == 'true') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(0);
                                } else {
                                    $space->setVehicle(0);
                                    $space->setVehicleStorageOnly(0);
                                }
                            }

                            break;
                        case Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT:
                        case Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                            ($this->hasParam('unit_height')) ? $space->setHeight(($this->getParam('unit_height')) ? $this->getParam('unit_height') : null) : '';
                            ($this->hasParam('door_height')) ? $space->setDoorHeight(($this->getParam('door_height')) ? $this->getParam('door_height') : null) : '';
                            ($this->hasParam('door_width')) ? $space->setDoorWidth(($this->getParam('door_width')) ? $this->getParam('door_width') : null) : '';

                            if(!in_array('covered', $integratedFields)) {
                                $space->setCovered($this->getParam('covered') == 'true' ? 1 : 0);
                            }

                            if(!in_array('climate', $integratedFields)) {
                                $space->setClimateControlled($this->getParam('climate') ? 1 : 0);
                            }

                            if(!in_array('alarm', $integratedFields)) {
                                $space->setAlarm($this->getParam('alarm') ? 1 : 0);
                            }

                            if(!in_array('humidity', $integratedFields)) {
                                $space->setHumidityControlled($this->getParam('humidity') ? 1 : 0);
                            }

                            if(!in_array('power', $integratedFields)) {
                                $space->setPower($this->getParam('power') == 'on' ? 1 : 0);
                            }

                            if(!in_array('outdoor_access', $integratedFields)) {
                                $space->setOutdoorAccess($this->getParam('outdoor_access') == 'true' ? 1 : 0);
                            }

                            if(!in_array('driveup', $integratedFields)) {
                                $space->setDriveUp($this->getParam('driveup') == 'true' ? 1 : 0);
                            }

                            ($this->hasParam('door_type')) ? $space->setDoorType($this->getParam('door_type')) : '';

                            if ($this->hasParam('vehicle')) {
                                if ($this->getParam('vehicle') == 'only') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(1);
                                } elseif ($this->getParam('vehicle') == 'true') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(0);
                                } else {
                                    $space->setVehicle(0);
                                    $space->setVehicleStorageOnly(0);
                                }
                            }

                            break;
                        case Genesis_Entity_StorageSpace::TYPE_WINE:
                        case Genesis_Entity_StorageSpace::TYPE_LOCKER:
                            ($this->hasParam('unit_height')) ? $space->setHeight(($this->getParam('unit_height')) ? $this->getParam('unit_height') : null) : '';
                            ($this->hasParam('door_height')) ? $space->setDoorHeight(($this->getParam('door_height')) ? $this->getParam('door_height') : null) : '';
                            ($this->hasParam('door_width')) ? $space->setDoorWidth(($this->getParam('door_width')) ? $this->getParam('door_width') : null) : '';

                            if(!in_array('climate', $integratedFields)) {
                                $space->setClimateControlled($this->getParam('climate') ? 1 : 0);
                            }

                            if(!in_array('alarm', $integratedFields)) {
                                $space->setAlarm($this->getParam('alarm') ? 1 : 0);
                            }

                            if(!in_array('humidity', $integratedFields)) {
                                $space->setHumidityControlled($this->getParam('humidity') ? 1 : 0);
                            }

                            if(!in_array('power', $integratedFields)) {
                                $space->setPower($this->getParam('power') ? 1 : 0);
                            }

                            if(!in_array('outdoor_access', $integratedFields)) {
                                $space->setOutdoorAccess($this->getParam('outdoor_access') == 'true' ? 1 : 0);
                            }

                            if(!in_array('driveup', $integratedFields)) {
                                $space->setDriveUp($this->getParam('driveup') == 'true' ? 1 : 0);
                            }

                            ($this->hasParam('door_type')) ? $space->setDoorType($this->getParam('door_type')) : '';

                            break;
                        default:
                            throw new Exception('Could not match type of storage space');
                            break;
                    }

                    $this->_resetUnitIntegratedInputs($space, $spaceOriginal, $integratedFields);

                    Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());

                    //throw new Exception('upstairs access: '.$savedSpace->getUpstairsViaLift());
                }
            }
        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
            echo "Trace: ",  $e->getTraceAsString(), "\n";
        }
    }

    private function _logUnitAttributeChange($attributeName, $oldValue, $newValue, $space)
    {
        $al = new Genesis_Util_ActionLogger();

        if ($oldValue === true || $oldValue == 'true' || $oldValue == 'on') $oldValue = 1;
        if ($newValue === true || $newValue == 'true' || $newValue == 'on') $newValue = 1;
        if ($oldValue === false || $oldValue == 'false' || $oldValue == 'off' || is_null($oldValue)) $oldValue = 0;
        if ($newValue === false || $newValue == 'false' || $newValue == 'off' || is_null($newValue)) $newValue = 0;

        if ($oldValue != $newValue) {
            $al->logAction($attributeName, $oldValue, $newValue, $this->getLoggedUser()->getId(), $space->getFacilityId(), $space->getId());
        }

    }

    private function _logFacilityAttributeChange($attributeName, $oldValue, $newValue, $facility)
    {
        $al = new Genesis_Util_ActionLogger();

        if ($oldValue === true || $oldValue == 'true' || $oldValue == 'on') $oldValue = 1;
        if ($newValue === true || $newValue == 'true' || $newValue == 'on') $newValue = 1;
        if ($oldValue === false || $oldValue == 'false' || $oldValue == 'off' || is_null($oldValue)) $oldValue = 0;
        if ($newValue === false || $newValue == 'false' || $newValue == 'off' || is_null($newValue)) $newValue = 0;

        if ($oldValue != $newValue) {
            $al->logAction($attributeName, $oldValue, $newValue, $this->getLoggedUser()->getId(), $facility->getId());
        }

    }

    //this is used when user selects multiple units and hits "edit selected"
    public function multiunitAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        try {
            //put unit ids into array in case there are a lot of them
            if ($this->getParam('unit_ids')) {
                $unitIds = explode(",", $this->getParam('unit_ids'));
            } else {
                throw new Exception('Please select units to update.');
            }

            $integratedInputs = $this->_getIntegratedFields();

            //now we have $unitIds array to update rest of params
            foreach ($unitIds as $unitId) {

                //determine if this is a new creation or an update
                $space = Genesis_Service_StorageSpace::loadById($unitId);

                if ($space) {
                    $spaceOriginal = clone $space;

                    //if type was set then save that 1st
                    if ($this->getParam('change_type')) {
                        $space->setType($this->getParam('unit_type'));
                        $space = Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                    }

                    //populate fields based on type
                    switch ($space->stringType()) {
                        case 'Parking':
                            if($this->getParam('change_location') == 'on' && !in_array('covered', $integratedInputs)) {
                                    $space->setCovered(($this->getParam('covered') == 'true') ? 1 : 0);
                            }

                            if($this->getParam('change_dimensions') == 'on' && !in_array('unit_height', $integratedInputs)) {
                                    $space->setHeight($this->getParam('unit_height'));
                            }

                            break;
                        case 'Land/open lot':
                            if($this->getParam('change_location') == 'on' && !in_array('covered', $integratedInputs)) {
                                    $space->setCovered(($this->getParam('covered') == 'true') ? 1 : 0);
                            }

                            if($this->getParam('change_dimensions') == 'on' && !in_array('unit_height', $integratedInputs)) {
                                    $space->setHeight($this->getParam('unit_height'));
                            }


                            if ($this->getParam('change_access')) {
                                if ($this->getParam('vehicle') && $this->getParam('vehicle') !== 'false') {
                                    $space->setVehicle(1);
                                } else if($this->getParam('vehicle') == 'false') {
                                    $space->setVehicle(0);
                                }

                                if ($this->getParam('driveup') == 'true') {
                                    $space->setDriveUp(1);
                                } else if ($this->getParam('driveup') == 'false') {
                                    $space->setDriveUp(0);
                                }
                            }

                            break;
                        case 'Unit':
                        case 'Workspace':
                            if ($this->getParam('change_dimensions')) {

                                if(!in_array('unit_height', $integratedInputs)) {
                                        $space->setHeight($this->getParam('unit_height'));
                                }

                                if(!in_array('door_height', $integratedInputs)) {
                                        $space->setDoorHeight($this->getParam('door_height'));
                                }

                                if(!in_array('door_width', $integratedInputs)) {
                                        $space->setDoorWidth($this->getParam('door_width'));
                                }

                            }

                            if($this->getParam('change_location') == 'on' && !in_array('covered', $integratedInputs)) {
                                    $space->setCovered(($this->getParam('covered') == 'true') ? 1 : 0);
                            }

                            if ($this->getParam('change_amenities')) {

                                if(!in_array('climate', $integratedInputs)) {
                                    $space->setClimateControlled(($this->getParam('climate') == 'on') ? 1 : 0);
                                }

                                if(!in_array('alarm', $integratedInputs)) {
                                    $space->setAlarm(($this->getParam('alarm') == 'on') ? 1 : 0);
                                }

                                if(!in_array('humidity', $integratedInputs)) {
                                    $space->setHumidityControlled(($this->getParam('humidity') == 'on') ? 1 : 0);
                                }

                                if(!in_array('power', $integratedInputs)) {
                                    $space->setPower(($this->getParam('power') == 'on') ? 1 : 0);
                                }

                            }

                            if ($this->getParam('change_access')) {

                                if(!in_array('vehicle', $integratedInputs)) {
                                    $space->setVehicle(($this->getParam('vehicle') == 'true') ? 1 : 0);
                                }

                                if(!in_array('driveup', $integratedInputs)) {
                                    $space->setDriveUp(($this->getParam('driveup') == 'true') ? 1 : 0);
                                }

                                if(!in_array('outdoor_access', $integratedInputs)) {
                                    $space->setOutdoorAccess(($this->getParam('outdoor_access') == 'true') ? 1 : 0);
                                }

                            }

                            if($this->getParam('change_door') == 'on' && !in_array('door_type', $integratedInputs)) {
                                    $space->setDoorType($this->getParam('door_type')); //ROLL_UP,SWING,NONE
                            }

                            break;
                        case 'Wine Storage':
                        case 'Locker':
                            if ($this->getParam('change_dimensions')) {

                                if(!in_array('unit_height', $integratedInputs)) {
                                    $space->setHeight($this->getParam('unit_height'));
                                }

                                if(!in_array('door_height', $integratedInputs)) {
                                    $space->setDoorHeight($this->getParam('door_height'));
                                }

                                if(!in_array('door_width', $integratedInputs)) {
                                    $space->setDoorWidth($this->getParam('door_width'));
                                }

                            }

                            $space->setCovered(1); //covered is assumed

                            if ($this->getParam('change_amenities')) {

                                if(!in_array('climate', $integratedInputs)) {
                                    $space->setClimateControlled(($this->getParam('climate') == 'on') ? 1 : 0);
                                }

                                if(!in_array('alarm', $integratedInputs)) {
                                    $space->setAlarm(($this->getParam('alarm') == 'on') ? 1 : 0);
                                }

                                if(!in_array('humidity', $integratedInputs)) {
                                    $space->setHumidityControlled(($this->getParam('humidity') == 'on') ? 1 : 0);
                                }

                                if(!in_array('power', $integratedInputs)) {
                                    $space->setPower(($this->getParam('power') == 'on') ? 1 : 0);
                                }
                            }

                            if ($this->getParam('change_access')) {

                                if(!in_array('driveup', $integratedInputs)) {
                                    $space->setDriveUp(($this->getParam('driveup') == 'true') ? 1 : 0);
                                }

                                if(!in_array('outdoor_access', $integratedInputs)) {
                                    $space->setOutdoorAccess(($this->getParam('outdoor_access') == 'true') ? 1 : 0);
                                }

                            }

                            $space->setVehicle(0);

                            if($this->getParam('change_door') == 'on' && !in_array('door_type', $integratedInputs)) {
                                    $space->setDoorType($this->getParam('door_type')); //ROLL_UP,SWING,NONE
                            }

                            break;
                        default:
                            throw new Exception('Could not match type of storage space');
                            break;
                    }

                    //populate fields that all types of units have

                    if ($this->getParam('change_dimensions') == 'on') {

                        if(!in_array('driveup', $integratedInputs)) {
                            $space->setDriveUp(($this->getParam('driveup') == 'true') ? 1 : 0);
                        }

                        ($this->hasParam('unit_width')) ? $space->setWidth($this->getParam('unit_width')) : '';

                        ($this->hasParam('unit_length')) ? $space->setLength($this->getParam('unit_length')) : '';
                    }

                    ($this->hasParam('quantity')) ? $space->setQuantity($this->getParam('quantity')) : '';

                    if($this->getParam('change_location') == 'on'
                        && !in_array('floor', $integratedInputs)) {
                            $space->setFloor($this->getParam('floor'));
                    }

                    if($this->getParam('change_description') == 'on'
                        && !in_array('description', $integratedInputs)) {
                            $space->setDescription($this->getParam('description'));
                    }

                    if ($this->getParam('change_promotion') == 'on') {
                        if (strlen($this->getParam('promotion')) > 100) {
                            throw new Exception('Promotion must be less than 100 characters.');
                        } else {
                            $space->setSpecialString($this->getParam('promotion'));
                        }
                    }

                    if ($this->getParam('change_pricing') == 'on') {
                        //only update/change regular price for manuals
                        if ($this->getParam('source_type') == Genesis_Entity_Source::ID_MANUAL) {
                            $space->setRegularPrice($this->getParam('price_regular'));
                        }

                        ($this->getParam('deposit')) ? $space->setDeposit($this->getParam('deposit')) : "";

                        ($this->hasParam('price_sf')) ? ($this->getParam('price_sf') <= $space->getRegularPrice()) ? : $space->setSparefootPrice($this->getParam('price_sf')) : "";

                        if ($this->hasParam('price_sf') && $this->getParam('price_sf') <= $space->getRegularPrice()) {
                            $space->setSparefootPrice($this->getParam('price_sf'));
                        } else if ($this->getParam('price_sf')) {
                            throw new Exception('Error: SpareFoot price must be <= regular price.');
                        }

                    }

                    $this->_resetUnitIntegratedInputs($space, $spaceOriginal, $integratedInputs);
                    $savedSpace = Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                }
            }
        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }

    /**
     * @return array
     * @throws Exception
     */
    private function _getIntegratedFields()
    {
        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);

        $integrationId = $facility->getCorporation()->getSourceId();

        // If not integrated, do nothing
        if ($integrationId == Genesis_Entity_Source::ID_MANUAL) {
            return [];
        }

        $integratedFields = AccountMgmt_Service_IntegratedFields::getBaseIntegratedFields( $integrationId );


        if ($integrationId == Genesis_Entity_Source::ID_SITELINK) {
            if (AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::SITELINK_STRIKETROUGH_PRICE)) {
                $integratedFields[] = AccountMgmt_Service_IntegratedFields::$mappedFields['sparefootPrice'];
            }

            if (! $facility->getCorporation()->getPullPromos()) {
                $integratedFields = array_diff($integratedFields, ['promotion']);
            }
        }

        // It makes sure that returns an indexed array
        return array_values($integratedFields);
    }

    /**
     * Sets request parameters to actual DB values.
     *
     * This only applies on Integrated facilities based on Quicktagger validations.
     *
     * This is made to avoid user to modify the integrated input fields.
     *
     * @param Genesis_Entity_StorageUnit $modelInstance
     * @return bool
    */
    private function _resetUnitIntegratedInputs($modelInstance, $modelInstanceCopy, $integratedFields)
    {

        // set integrated fields to actual DB values
        foreach ($integratedFields as $field) {
            $columnName = AccountMgmt_Service_IntegratedFields::getDbColumnName($field);
            $getterMethod = "get" . ucfirst($columnName);
            $setterMethod = "set" . ucfirst($columnName);

            if (!is_null($columnName) && method_exists($modelInstance, $setterMethod)) {
                $modelValue = $modelInstanceCopy->{"get{$columnName}"}();
                $modelInstance->{$setterMethod}($modelValue);
            }
        }

        return true;
    }

    public function siteVerifyAction()
    {
        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->facility = $facility;
        $this->view->scripts = array('facility/site-verify');
    }

    public function checksiteverifyAction()
    {
        //disable zend layout so it doesn't return all the html
        $this->_helper->layout->disableLayout();

        //don't render a response
        $this->_helper->viewRenderer->setNoRender(true);

        $testUrl = $this->getParam('url');
        $facilityId = $this->getParam('id');

        $facility = Genesis_Service_Facility::loadById($facilityId);

        //check valid structure of a url
        if (strlen($testUrl) > 0) {
            if (!preg_match("/^[A-Za-z]+:\/\//", $testUrl)) {
                $testUrl = 'http://' . $testUrl;
            }

            if ($this->_validateUrl($testUrl)) {
                $facility->setUrl($testUrl);

                //check that this url has the sparefoot link
                if ($facility->hasSparefootLink()) {
                    $facility->setUrlVerified(1);
                } else {
                    $facility->setUrlVerified(0);
                    print "Error: the SpareFoot link was not found on your site";
                }
            } else {
                print "Error: The given URL does not appear to be valid";
            }
        } else {
            $facility->setUrlVerified(0);
            print "Error: Please enter a web address";
        }

        //write whatever data we validated
        $facility = Genesis_Service_Facility::save($facility, $this->getLoggedUser());
    }



    protected function getSideBarContent()
    {
        $view = new Zend_View();
        $view->facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Order::ASC('title'));
        $view->selectedFacilityId = $this->getSession()->facilityId;
        $view->loggedUser = $this->getLoggedUser();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/facility/');

        return $view->render('left-sidebar-content.phtml');
    }

    protected function getTab()
    {
        return self::TAB_FACILITY;
    }

    /**
     * Fetch and organize all of the data to populate the facilities screen
     *
     * Data comes back in form:
     *
     * array(
     *      facility_id => array (
     *          entity => Genesis_Entity_Facility,
     *          num_impressions => int,
     *          avg_position => double
     *      )
     * )
     *
     * @return array
     */
    private function _fetchFacilitiesData(Genesis_Db_Sqlable $s = null)
    {
        $facilities = $this->getLoggedUser()->getManagableFacilities($s);
        $startDate = $this->getTrueBeginDate();
        $endDate   = $this->getTrueEndDate();

        $facilityDetails = array();

        /**
         * @var $facility Genesis_Entity_Facility
         */

        foreach ($facilities as $facility) {

            if ($facility->getSourceId() == Genesis_Entity_Source::ID_MANUAL) {
                $sourceName = 'MySpareFoot';
                if ($facility->getSelfReportedSourceId()) {
                    $sourceName .= ' ('.$facility->getSelfReportedSource()->getSource().')';
                }
            } else {
                $sourceName = $facility->getSource()->getName();
            }

            $consumerContacts = Genesis_Service_ConsumerContact::loadUniqueByFacilityIdAndDateRange($facility->getId(), $startDate, $endDate);

            $array = array(
                'id' => $facility->getId(),
                'title' => $facility->getTitleWithCompanyCode(),
                'code' => $facility->getCompanyCode(),
                'url' => Genesis_Util_Url::facilityUrl($facility),
                'source_name' => $sourceName,
                'source_id' => $facility->getSourceId(),
                'self_reported_source_id' => $facility->getSelfReportedSourceId(),
                'bid_string' => $facility->isBidFlat() ? $facility->getBidString() :  'N/A',
                'num_impressions' => 0,
                'avg_position' => '-',
                'num_clicks' => 0,
                'num_reservations' => 0,
                'cost_per_reservation' => 0,
                'total_cost' => 0,
                'reservation_rate' => 0,
                'hidden' => ! $facility->getActive(),
                'published' => $facility->getPublished(),
                'consumer_contacts' => $consumerContacts->count(),
            );

            $facilityDetails[$facility->getId()] = array_merge($array,
                Genesis_Service_Reporting::getAllReservationDataByFacility($facility->getId(), $startDate, $endDate));

        }

        return $facilityDetails;
    }

    public function mapAction()
    {
        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->facility = $facility;

        $facilityPov = Genesis_Service_StreetViewPov::loadById($facility->getId());

        //send pov if it exists
        if ($facilityPov) {
            $this->view->latitude = $facilityPov->getLatitude();
            $this->view->longitude = $facilityPov->getLongitude();
            $this->view->pov = $facilityPov;
        } elseif ($facility->getLocationId()) {
            $this->view->latitude = $facility->getLocation()->getLatitude();
            $this->view->longitude = $facility->getLocation()->getLongitude();
        } else {
            $this->view->latitude = 0;
            $this->view->longitude = 0;
        }

        if ($_POST) {

            try {
                $streetView = new Genesis_Entity_StreetViewPov();

                $streetView->setFacilityId($this->getParam('fid'));
                $streetView->setLatitude($this->getParam('lat'));
                $streetView->setLongitude($this->getParam('lng'));
                $streetView->setYaw($this->getParam('yaw'));
                $streetView->setPitch($this->getParam('pitch'));
                $streetView->setZoom($this->getParam('zoom'));
                $streetView->setShowStreetView(1);

                Genesis_Service_StreetViewPov::save($streetView);

                $facilityPov = Genesis_Service_StreetViewPov::loadById($facility->getId());
                $this->view->latitude = $facilityPov->getLatitude();
                $this->view->longitude = $facilityPov->getLongitude();
                $this->view->pov = $facilityPov;

                $this->view->alert = 'Changes saved.';
                $this->view->alertClass = 'success';
            } catch (Exception $e) {
                $this->view->alert = '<strong<>Error</strong>: '.$e->getMessage();
                $this->view->alertClass = 'error';
            }
        }

        $this->view->scripts = array('facility/map');
    }

    //display the integration types to add a new faclity with
    public function typeAction()
    {
        //confirm session vars are clear
        //$this->getSession()->facilityId = null;
        $this->getSession()->facIds = null;

        $account = $this->getLoggedUser()->getAccount();

        if ($account->getWholesale()) {
            throw new Exception("Facilities can only be added to a Wholesale account in PITA.");
        }

        //comma separated list of integration types
        $this->view->accountId = $account->getId();
        $this->view->integrations = explode(",", $account->getIntegrationsString()); //MAN, SL, CS, QS, DOM
        $this->view->corporations = $account->getCorporations();

        $this->view->scripts = array('facility/type');

        $this->view->newIntegrationChoices = Genesis_Service_Source::load(
            Genesis_Db_Restriction::equal('manualIntegration', 1)->setOrder(
                Genesis_Db_Order::asc('source')
            )
        );
    }


    /**
     * resyncs the passed in corporation & puts new facility ids in session
     *
     * The js is checking for int (count of new facilities) or "0" as the response for no new facilities.
     * Any echo's here will break that function.
     *
     * Only return int or 0 to the caller
     *
     * @throws Exception
     */
    public function resyncAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        try {
            if (!$this->getParam('corpId')) {
                throw new Exception("No corp ID passed");
            }
            //load corp details for re-sync
            $corp = Genesis_Service_Corporation::loadById($this->getParam('corpId'));

            //this can throw too, don't swallow it
            $facilityResult = Genesis_Service_Sync::sync($corp);

            //return new facility id's
            $facs = $facilityResult->getNewFacilities();
            if (!$facs) {
                exit(print("0"));
            }

            $client = new Genesis_Client_Omnom();
            $facIds = [];
            //sync units
            //@todo this shouldn't be here, put this in a queue
            foreach ($facs as $fac) {
                $facIds[] = $fac->getId();
                try {   //let omnom errors go
                    $client->enqueueUnitPull($fac);
                } catch (Exception $e) {
                    error_log($e->getMessage());
                }
            }

            $this->getSession()->facIds = implode(",", $facIds);

            // send emails if new facilities exist
            //@todo this shouldn't be here, put this in a queue
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (Exception $e) {
                    error_log($e->getMessage());
                }
            }

            //return number new facilities as an int
            exit(count($facIds));
        } catch (Exception $e) {
            header('', true, 400);
            exit(print('Error: ' . $e->getMessage()));
        }
    }

    //process the  type of integration todo and fwd to the correct view
    public function selectionhandlerAction()
    {
        $entity = Genesis_Service_Source::loadByShortName(
            $this->getParam('integrationType')
        );

        //we don't have a facility to save to yet, so put this in the session for later
        if ($entity) {
            $this->getSession()->selfReportedSourceId = $entity->getId();
        }
        switch ($this->getParam('integrationType')) {
            case Genesis_Entity_Source::ID_SITELINK:
            case "sl_new":
                $this->forward('addsitelink');
                break;
            case Genesis_Entity_Source::ID_CENTERSHIFT4:
            case "cs4_new":
                $this->forward('addcentershift4');
                break;
            case Genesis_Entity_Source::ID_SELFSTORAGEMANAGER:
            case "ssm":
                $this->forward('addselfstoragemanager');
                break;

            case Genesis_Entity_Source::ID_QUIKSTOR:
                //@todo add the next button for the domico people
                $this->forward('addquickstore');
                break;

            case Genesis_Entity_Source::ID_STOREDGE:
                $this->forward('addstoredge');
                break;

            case Genesis_Entity_Source::ID_ESS:
                $this->forward('addeasystoragesolutions');
                break;
            //break;
            //case "man_new":
            //case Genesis_Entity_Source::ID_NONE:
            //case Genesis_Entity_Source::ID_OTHER:
            //case "man_exist":
            //case Genesis_Entity_Source::ID_MANUAL:
            default:
                //exit('woop '.$this->getParam('integrationType'));

                $this->forward('add', 'facility', 'default', ['new'=> true]);
                break;

        }

        $usesIssn = $this->getParam('usesIssn');
        if ($usesIssn == 1) {
            $this->forward('addissn');

        }
    }

    public function addsitelinkAction()
    {
        $this->view->scripts = array('facility/addsitelink');
    }

    //NOTE: this is also called in the signup process, if you change it, test signup!
    public function syncsitelinkAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $corpCode = $this->getParam('corpcode');
        $username = $this->getParam('username');
        $password = $this->getParam('password');

        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!strlen($corpCode) > 0) {
                throw new Exception('Please enter a corporation code.');
            } if (!strlen($username) > 0) {
                throw new Exception('Please enter a user name.');
            } if (!strlen($password) > 0) {
                throw new Exception('Please enter a password.');
            }

            //check to see if this integration already exists
            $sitelink_corp = Genesis_Service_Corporation::load(Genesis_Db_Restriction::equal('corpCode', $corpCode))->uniqueResult();

            //if corp doesn't already exist, create it
            if ($sitelink_corp) {
                throw new Exception('We already have this SiteLink corporation code (' . $corpCode .') in an account.  Contact <EMAIL> for help.');
            } else {
                $sitelink_corp = new Genesis_Entity_SitelinkCorporation();
                $sitelink_corp->setAccountId($account->getId());
                $sitelink_corp->setCorpname($account->getName());
                $sitelink_corp->setCreated(date("Y-m-d H:i:s", time()));
                $sitelink_corp->setSitelinkCorpCode($corpCode);
                $sitelink_corp->setSitelinkPassword($password);
                $sitelink_corp->setSitelinkUsername($username);

                $sitelink_corp = Genesis_Service_Corporation::save($sitelink_corp);
            }

            //this determines which type of sync do (centershift/sitelink) to and syncs all facilities
            try {
                $facilityResult = Genesis_Service_Sync::sync($sitelink_corp);
            } catch (Exception $e) {
                //delete the corp if something messed up on sync
                Genesis_Service_Corporation::delete($sitelink_corp);
                echo "Error: " . $e->getMessage() . "\n";

                return;
            }

            if ($facilityResult->getErrors()) {
                $errorMessages = [];
                /** @var Exception $e */
                foreach ($facilityResult->getErrors() as $i=>$e) {
                    $errorMessages[] = "Error #" . ($i+1) . ": " . $e->getMessage();
                }
                throw new Exception(
                    "There were " . sizeof($facilityResult->getErrors()) . " error(s) syncing facilities: \n"
                    . implode("\n", $errorMessages)
                );
            }

            //return new facility id's
            $facs = $facilityResult->getNewFacilities();

            $client = new Genesis_Client_Omnom();

            $facIds = array();

            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();

                    //kick off the unit sync
                    try {
                        $response = $client->enqueueUnitPull($fac);
                    } catch (Exception $e) {
                        //let go
                    }
                }
            } else {
                throw new Exception('We did not find any facilities in sitelink.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (Exception $e) {
                    echo "Error: " . $e->getMessage() . "\n";
                }
            }

            print implode("," , $facIds);
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    public function addcentershift4Action()
    {
        $this->view->cs4_csrf_token = CsrfUtil::getToken(self::ADD_CS4_FACILITY_TOKEN);
        $this->view->scripts = array('facility/addcentershift4');
    }

    //NOTE: this is also called in the signup process, if you change it, test signup!
    public function synccentershift4Action()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $username = $this->getParam('username');
        $password = $this->getParam('pin');
        $dataCenter = $this->getParam('datacenter');
        $channel = $this->getParam('channel');
        $signature = $this->getParam('signature');
        $syncInactive = ((int) $this->getParam('syncInactive') == 1 ? false : true);

        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!CsrfUtil::validateToken(self::ADD_CS4_FACILITY_TOKEN, $this->getParam('csrf_token'))) {
                throw new Exception('Request structure is invalid. Refresh the page and try again.');
            }
            if (!strlen($username) > 0) {
                throw new Exception('Please enter a username.');
            } if (!strlen($password) > 0) {
                throw new Exception('Please enter a PIN.');
            } if (!strlen($signature) > 0) {
                throw new Exception('Please enter your name in the signature box.');
            }

            //check to see if user already exists
            $centershift_corp = Genesis_Service_Corporation::load(Genesis_Db_Restriction::and_(Genesis_Db_Restriction::equal('csPassword', $password), Genesis_Db_Restriction::equal('csUsername', $username)))->uniqueResult();

            //if corp doesn't already exist, create it
            if ($centershift_corp) {
                throw new Exception('We already an account with your centershift credentials('. $username . ',' . $password .').  Contact <EMAIL> for help.');
            } else {
                $centershift_corp = new Genesis_Entity_Centershift4Corporation();
                $centershift_corp->setAccountId($account->getId());
                $centershift_corp->setCorpname($account->getName());
                $centershift_corp->setCreated(date("Y-m-d H:i:s", time()));
                $centershift_corp->setCsUsername($username);
                $centershift_corp->setCsPassword($password);
                $centershift_corp->setChannel($channel);
                $centershift_corp->setEndpoint($dataCenter);
                $centershift_corp->setSignature($signature);

                $client = new Genesis_Client_Centershift4();

                if ($centershift_corp->getEndpoint() == "den") {
                    $centershift_corp->setWsdl(Genesis_Client_Centershift4::DEN_WSDL);
                    $client->switchWsdl( $centershift_corp->getWsdl() );
                }

                $csOrgs = $client->GetOrgList($username, $password, $channel);
                if ($csOrgs[0]) {
                    $centershift_corp->setOrgId($csOrgs[0]);
                }

                $centershift_corp = Genesis_Service_Corporation::save($centershift_corp);
            }

            //this determines which type of sync do (centershift/sitelink) to and syncs all facilities
            try {
                $facilityResult = Genesis_Service_Sync::sync($centershift_corp, null, $syncInactive);
            } catch (Exception $e) {
                //delete the corp if something messed up on sync
                Genesis_Service_Corporation::delete($centershift_corp);
                echo "Error: " . $e->getMessage() . "\n";

                return;
            }

            //now we have to save corp id on the facilities that were sync'd
            $facs = $facilityResult->getNewFacilities();

            $client = new Genesis_Client_Omnom();

            $facIds = array();

            //only try to sync units if we got some facility results
            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();

                    //kick off the unit sync
                    try {
                        $response = $client->enqueueUnitPull($fac);
                    } catch (Exception $e) {
                        //let go
                    }
                }
            } else {
                throw new Exception('We did not find any facilities in centershift.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (Exception $e) {
                    echo "Error: " . $e->getMessage() . "\n";
                }
            }

            print implode("," , $facIds);
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    public function addselfstoragemanagerAction()
    {
        $this->view->scripts = array('facility/addselfstoragemanager');
    }

    //NOTE: this is also called in the signup process, if you change it, test signup!
    public function syncselfstoragemanagerAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $wsdlAddress = $this->getParam('wsdl');
        $username = $this->getParam('username');
        $password = $this->getParam('password');

        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!strlen($wsdlAddress) > 0) {
                throw new Exception('Please enter a WSDL address.');
            } if (!strlen($username) > 0) {
                throw new Exception('Please enter a user name.');
            } if (!strlen($password) > 0) {
                throw new Exception('Please enter a password.');
            }

            //check to see if this integration already exists
            $ssm_corp = Genesis_Service_Corporation::load(Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::equal('accountId', $account->getId()),
                Genesis_Db_Restriction::equal('wsdl', $wsdlAddress),
                Genesis_Db_Restriction::equal('ssmUsername', $username)
            ))->uniqueResult();

            //if corp doesn't already exist, create it
            if (!$ssm_corp) {
                $ssm_corp = new Genesis_Entity_SelfStorageManagerCorporation();
                $ssm_corp->setAccountId($account->getId());
                $ssm_corp->setCorpname($account->getName());
                $ssm_corp->setCreated(date("Y-m-d H:i:s", time()));
                $ssm_corp->setWsdl($wsdlAddress);
                $ssm_corp->setSsmPassword($password);
                $ssm_corp->setSsmUsername($username);
                $ssm_corp = Genesis_Service_Corporation::save($ssm_corp);
            } elseif ($ssm_corp->getSsmPassword() !== $password) {
                // update password if different
                $ssm_corp->setSsmPassword($password);
            }

            //this determines which type of sync do (centershift/sitelink) to and syncs all facilities
            try {
                $facilityResult = Genesis_Service_Sync::sync($ssm_corp);
            } catch (Exception $e) {
                //delete the corp if something messed up on sync
                Genesis_Service_Corporation::delete($ssm_corp);
                echo "Error: " . $e->getMessage() . "\n";

                return;
            }

            //return new facility id's
            $facs = $facilityResult->getNewFacilities();
            $client = new Genesis_Client_Omnom();

            $facIds = array();
            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();

                    //kick off the unit sync
                    try {
                        $response = $client->enqueueUnitPull($fac);
                    } catch (Exception $e) {
                        //let go
                    }
                }
            } else {
                throw new Exception('We did not find any facilities in SelfStorageManager.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (Exception $e) {
                    echo "Error: " . $e->getMessage() . "\n";
                }
            }

            print implode("," , $facIds);
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    public function addquickstoreAction()
    {
    }

    public function addstoredgeAction()
    {
        $this->view->scripts = array('facility/addstoredge');
    }

    public function addeasystoragesolutionsAction()
    {
        $this->view->scripts = array('facility/addeasystoragesolutions');
    }

    //NOTE: this is also called in the signup process, if you change it, test signup!
    public function syncstoredgeAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $corpCode = $this->getParam('corpcode');
        $username = Genesis_Client_Storedge::SPAREFOOT_API_KEY;
        $password = Genesis_Client_Storedge::SPAREFOOT_API_SECRET;

        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!strlen($corpCode) > 0) {
                throw new Exception('Please enter a corporation code.');
            }

            //check to see if this integration already exists
            $storedge_corp = Genesis_Service_Corporation::load(Genesis_Db_Restriction::equal('corpCode', $corpCode))->uniqueResult();

            //if corp doesn't already exist, create it
            if ($storedge_corp) {
                throw new Exception('We already have this storEDGE corporation code (' . $corpCode .') in an account.  Contact <EMAIL> for help.');
            } else {
                $storedge_corp = new Genesis_Entity_StoredgeCorporation();
                $storedge_corp->setAccountId($account->getId());
                $storedge_corp->setCorpname($account->getName());
                $storedge_corp->setCreated(date("Y-m-d H:i:s", time()));
                $storedge_corp->setCorpCode($corpCode);
                $storedge_corp->setPassword($password);
                $storedge_corp->setUsername($username);

                $storedge_corp = Genesis_Service_Corporation::save($storedge_corp);
            }

            //this determines which type of sync do (centershift/sitelink...) to and syncs all facilities
            try {
                $facilityResult = Genesis_Service_Sync::sync($storedge_corp);
            } catch (Exception $e) {
                //delete the corp if something messed up on sync
                Genesis_Service_Corporation::delete($storedge_corp);
                echo "Error: " . $e->getMessage() . "\n";

                return;
            }

            if ($facilityResult->getErrors()) {
                $errorMessages = [];
                /** @var Exception $e */
                foreach ($facilityResult->getErrors() as $i=>$e) {
                    $errorMessages[] = "Error #" . ($i+1) . ": " . $e->getMessage();
                }
                throw new Exception(
                    "There were " . sizeof($facilityResult->getErrors()) . " error(s) syncing facilities: \n"
                    . implode("\n", $errorMessages)
                );
            }

            //return new facility id's
            $facs = $facilityResult->getNewFacilities();

            $client = new Genesis_Client_Omnom();

            $facIds = array();

            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();

                    //kick off the unit sync
                    try {
                        $response = $client->enqueueUnitPull($fac);
                    } catch (Exception $e) {
                        //let go
                    }
                }
            } else {
                throw new Exception('We did not find any facilities in storEDGE.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (Exception $e) {
                    echo "Error: " . $e->getMessage() . "\n";
                }
            }

            print implode("," , $facIds);
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }


    /**
     * @return Client
     */
    private function getPhidoClient(): Client
    {
        if ( !$this->phidoClient) {
            $this->phidoClient = new Client();
        }
        return $this->phidoClient;
    }

    /**
     * @param string $essIdentifier
     * @return bool
     * @throws Exception
     */
    private function isEssIdentifierValid(string $essIdentifier): bool
    {
        $client = $this->getPhidoClient();
        $urlString = getenv('URL_PHIDO') . "/ess/facility/$essIdentifier/lookup";

        $response = $client->get($urlString, [
            'headers' => [ 'x-sparefoot-app' => 'myfoot' ],
        ]);

        return $response->getStatusCode() == 200;
    }

    /**
     * @param int $manualFacilityId
     * @param string $essIdentifier
     * @param int $corporationId
     * @param int $userId
     * @return ResponseInterface
     * @throws Exception
     */
    public function updateFacilityFromESS(int $manualFacilityId, string $essIdentifier, int $corporationId, int $userId): ResponseInterface
    {
        $client = $this->getPhidoClient();
        $urlString = getenv('URL_PHIDO') . '/update/ess/facility';

        return $client->put($urlString, [
            'headers' => [ 'x-sparefoot-app' => 'myfoot' ],
            'json' => [
                'manualFacilityId' => $manualFacilityId,
                'essUniqueIdentifier' => $essIdentifier,
                'corporationId' => $corporationId,
                'userId' => $userId,
            ],
        ]);
    }

    /**
     * @param int $facilityId
     * @return ResponseInterface
     * @throws Exception
     */
    public function triggerUnitSync(int $facilityId): ResponseInterface
    {
        $client = $this->getPhidoClient();
        $urlString = getenv('URL_PHIDO') . "/sync/units/$facilityId";

        return $client->get($urlString, [
            'headers' => [ 'x-sparefoot-app' => 'myfoot' ],
        ]);
    }

    public function synceasystoragesolutionsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $account = $this->getLoggedUser()->getAccount();

        //Strip out any non alpha numeric chars
        $essIdentifier = preg_replace('/[^A-Za-z0-9]/', '', $this->getParam('essidentifier'));

        try {
            if (!isset($essIdentifier) || empty($essIdentifier)) {
                throw new Exception('Easy Storage Solutions invalid identifier. Contact <EMAIL> for help.');
            }

            //Since facility doesn't exist, corp doesn't exist
            //Fetch data from Marketplace API, create facility and corp
            try {
                $essFacilityValid = $this->isEssIdentifierValid($essIdentifier);
                if(!$essFacilityValid) {
                    throw new Exception('Easy Storage Solutions invalid identifier. Contact <EMAIL> for help.');
                }


                //Create corporation
                $newEssCorp = new Genesis_Entity_EasyStorageSolutionsCorporation();
                $newEssCorp->setAccountId($account->getId());
                $newEssCorp->setCorpname($account->getName());
                $newEssCorp->setCreated(date("Y-m-d H:i:s", time()));
                $newEssCorp->setCorpCode($essIdentifier);
                $newEssCorp = Genesis_Service_Corporation::save($newEssCorp);

                // Use 400 Brooks Rd, Perryville AK for now; Phido will update this to whatever location ESS sends
                $location = Genesis_Service_Location::loadById(self::PERRYVILLE_DUMMY_LOCATION);

                //Create facility
                $newEssFacility = new Genesis_Entity_Facility();
                $newEssFacility->setCorporationId($newEssCorp->getId());
                $newEssFacility->setSourceId(Genesis_Entity_Source::ID_ESS);
                $newEssFacility->setExternalId($essIdentifier);
                $newEssFacility->setLocation($location);
                $newEssFacility->setLocationId($location->getId());

                //This is what makes the new ESS Facility show in the dropdown
                //How would we determine how these are set?
                $newEssFacility->setActive(0);
                $newEssFacility->setPublished(1);
                $newEssFacility->setApproved(1);

                //Save this facility early so we can have access to the facility_id for office hours
                $newEssFacility = Genesis_Service_Facility::save($newEssFacility);

                $this->updateFacilityFromESS($newEssFacility->getId(), $essIdentifier, $newEssCorp->getId(), $this->getLoggedUser()->getId());
                $this->triggerUnitSync($newEssFacility->getId());

                print implode("," , [$newEssFacility->getId()]);

            } catch (BadResponseException $e) {
                $response = json_decode($e->getResponse()->getBody()->getContents(), true);

                if (isset($response['message'])) {
                    throw new Exception($response['message']);
                }

                throw new Exception('Unexpected error encountered. <NAME_EMAIL> for help.');
            } catch (Exception $e) {
                throw new Exception('Unexpected error encountered. <NAME_EMAIL> for help.');
            }
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    /**
     * @throws Exception
     */
    public function addsummaryAction()
    {
        $facIdArry = [];

        //if the new facilities are from an integration sync
        if ($this->getParam('syncd_fac_ids')) {
            $facilityIds = $this->getParam('syncd_fac_ids');

            //put these ids in the session so we know what the new facilities are
            $this->getSession()->facIds = $facilityIds;

            $facIdArry = explode("," , $facilityIds);
            $this->view->facIds = $facIdArry;

            $facility = Genesis_Service_Facility::loadById($facIdArry[0]); //ok since all facs are same integration

        } elseif ($this->getSession()->facIds) {
            $facilityIds = $this->getSession()->facIds;
            $facIdArry = explode("," , $facilityIds);
            $this->view->facIds = $facIdArry;

            $facility = Genesis_Service_Facility::loadById($facIdArry[0]);
        } elseif ($this->getSession()->facilityId) {
            //if the new facility was from an manual add
            //only a manual add would have set the facility in the session
            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $this->view->facility = $facility;
            $this->view->facIds = '';
        } else {
            throw new Exception('Could not determine what integration new facilities came from!');
        }


        //pass facilities to setup to the view
        $this->view->facilities = Genesis_Service_Facility::load(Genesis_Db_Restriction::equal('id', $facility->getId()));

        if (count($facIdArry) > 0) {
            $this->view->facilities = Genesis_Service_Facility::load(Genesis_Db_Restriction::in('id', $facIdArry));
        }

        //tell the back button were to go
        switch ($facility->getSourceId()) {
            case Genesis_Entity_Source::ID_MANUAL:
                $this->view->backView = 'add';
                break;
            case Genesis_Entity_Source::ID_SITELINK:
            case Genesis_Entity_Source::ID_CENTERSHIFT4:
            case Genesis_Entity_Source::ID_STOREDGE:
            case Genesis_Entity_Source::ID_SELFSTORAGEMANAGER:
            case Genesis_Entity_Source::ID_OPENTECH:
            case Genesis_Entity_Source::ID_ESS:
            case Genesis_Entity_Source::ID_CENTERSHIFT:
                $this->view->backView = 'type';
                break;
            default:
                throw new Exception('Could not determine what integration new facilities came from!');
        }

        $account = $this->getLoggedUser()->getAccount();

        $billableEntities = $account->getBillableEntities()->toArray();

        $output = array();

        //get each payment details from NS
        foreach ($billableEntities as $be) {

            if ((int) $be->getPublished() == 0) {
                continue;
            }

            $output[$be->getId()]['id'] = $be->getId();

            // Comment out this entire switch statement if you're trying to make new Facilities locally.
            switch ($be->getTerms()) {
                case 'Credit Card':
                    //get the credit card to display last for of CC in display string
                    try {
                        $cc = $be->getCreditCard();
                    } catch (Exception $e) {
                        //nothing
                    }

                    if ($cc instanceof Genesis_Entity_Netsuite_CreditCard) {
                        $output[$be->getId()]['displayStr'] = "CC - " . $cc->getCcNumber() . ", " . $be->getNsName();
                    } else {
                        $output[$be->getId()]['displayStr'] = "CC - No Card On File" . ", " . $be->getNsName();
                    }
                    break;
                case "ACH":
                    $output[$be->getId()]['displayStr'] = "ACH - " . $be->getNsName();
                    break;
                case "Net 30":
                    $output[$be->getId()]['displayStr'] = "Net 30 - " . $be->getNsName();
                    break;
                case "Net 10":
                    $output[$be->getId()]['displayStr'] = "Net 10 - " . $be->getNsName();
                    break;
                default:
                    $output[$be->getId()]['displayStr'] = "n/a";
                    break;
            }
        }

        $this->view->payment_methods = $output;

        $this->view->scripts = array('facility/addsummary');
    }

    public function addproductsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);


        $this->getLoggedUser()->getAccount();

        if ($this->getSession()->facilityId) {

            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $facility->setCpa(true);
            $facility->setActive(true);
            $facility = Genesis_Service_Facility::save($facility, $this->getLoggedUser());

            $isFSS = $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
            if ($facility->getSourceId() == Genesis_Entity_Source::ID_MANUAL
                || $isFSS) {
                $nextStep = $this->view->url(['action' => 'inventory'], 'features').'?fid='.$this->getSession()->facilityId; //fid is already in session so no need to pass
            }

        } else {
            //pass along the facility id's from the integration
            if ($this->getParam('syncd_fac_ids')) {
                $facilityIds = $this->getParam('syncd_fac_ids');
                $facIdArry = explode("," , $facilityIds);
                $facilities = Genesis_Service_Facility::load(Genesis_Db_Restriction::in('id', $facIdArry))->toArray();
                foreach ($facilities as $facility) {
                    $facility->setCpa(true);
                    $facility->setActive(true);
                    Genesis_Service_Facility::save($facility, $this->getLoggedUser());
                }

            } else {
                throw new Exception('Could not get the facility list that were synced!');
            }
            $nextStep = '/user';
        }

        $this->redirect($nextStep);
    }

    private function _validateUrl($url)
    {
        // Taken from http://php.net/manual/en/function.preg-match.php
        // PHP's filter_var() sounds nice, but it's broken

        $nastyUrlRegex = "((https?)\:\/\/)?"; // SCHEME
        $nastyUrlRegex .= "([a-zA-Z0-9+!*(),;?&=\$_.-]+(\:[a-zA-Z0-9+!*(),;?&=\$_.-]+)?@)?"; // User and Pass
        $nastyUrlRegex .= "([a-zA-Z0-9-.]*)\.([a-zA-Z]{2,6})"; // Host or IP
        $nastyUrlRegex .= "(\:[0-9]{2,5})?"; // Port
        $nastyUrlRegex .= "(\/([a-zA-Z0-9+\$_-]\.?)+)*\/?"; // Path
        $nastyUrlRegex .= "(\?[a-zA-Z+&\$_.-][a-zA-Z0-9;:@&%=+\/\$_.-]*)?"; // GET Query
        $nastyUrlRegex .= "(#[a-zA-Z_.-][a-zA-Z0-9+\$_.-]*)?"; // Anchor

        return (preg_match("/^$nastyUrlRegex$/", $url)) ? true : false;
    }

    //just set billing start dates on this facilitye
    //happens when they add a new facility
    public function setbillingstartdateAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $product = $this->getParam('prod');
        $facId = $this->getParam('facId');

        $facility = Genesis_Service_Facility::loadById($facId);

        switch ($product) {
            case "cpa":
                if ($facility->getCpa()) {
                    $prodMeta = new Genesis_Entity_FacilityProductsMeta();
                    $prodMeta->setFacilityId($facId);
                    $prodMeta->setBillingStartDate(date("Y-m-d"));
                    $prodMeta->setProduct("AD_NETWORK");
                    Genesis_Service_FacilityProductsMeta::save($prodMeta, true, $this->getLoggedUser());
                }
                break;
            case "geo":
                if ($facility->getHostedWebsite()) {
                    $prodMeta = new Genesis_Entity_FacilityProductsMeta();
                    $prodMeta->setFacilityId($facId);
                    $prodMeta->setBillingStartDate(date("Y-m-d"));
                    $prodMeta->setProduct("HOSTED_WEBSITE");
                    Genesis_Service_FacilityProductsMeta::save($prodMeta, true, $this->getLoggedUser());
                }
                break;
            default:
                break;
        }
    }

    /**
     * For a prefix of 'Address: ', would return a string like:
     * Address: address line 1
     *          address line 2
     *          city, state, zip
     * @param string $prefix
     * @param Genesis_Entity_Location $location
     * @return string
     */
    private function formatAddress(string $prefix, Genesis_Entity_Location $location): string
    {
        $prefixLength = strlen($prefix);
        $linePrefixPaddingAfterFirst = str_repeat(' ', $prefixLength);

        $addressLines = array();
        $usePrefixPadding = false;

        if ($location->getAddress1()) {
            $addressLines[] = $location->getAddress1();
            $usePrefixPadding = true;
        }
        if ($location->getAddress2()) {
            $line = '';
            if ($usePrefixPadding) {
                $line .= $linePrefixPaddingAfterFirst;
            }
            $line .= $location->getAddress2();
            $addressLines[] = $line;
            $usePrefixPadding = true;
        }

        $line = '';
        if ($usePrefixPadding) {
            $line .= $linePrefixPaddingAfterFirst;
        }
        $zip = $location->getZip();
        $line .= $location->getCity() . ', ' . $location->getState() . ($zip ? ' ' . $zip : '');
        $addressLines[] = $line;

        return implode(PHP_EOL, $addressLines);
    }

    private function formatFacilityForEmail(Genesis_Entity_Facility $facility): string
    {
        /**
         * @var Genesis_Entity_Location
         */
        $location = $facility->getLocation();

        return 'Facility Name: ' . $facility->getTitle() . PHP_EOL .
            $this->formatAddress('Facility Address: ', $location) . PHP_EOL .
            'Facility ID: ' . $facility->getId() . PHP_EOL .
            'Facility Bid: ' . $facility->getBidValue() . PHP_EOL;
    }

    function formatFooterForNewFacilityEmail(Genesis_Entity_UserAccess $user): string
    {
        return PHP_EOL .
            'Email: ' . $user->getEmail() . PHP_EOL .
            PHP_EOL .
            'mysparefoot_new-facility_7yikV2' . PHP_EOL;
    }


    // send email to sales team when new facilities are added to an existing account

    /**
     * @param $facilities array|Genesis_Entity_Facility
     * @throws Exception
     */
    private function notifyOnNewFacilities($facilities)
    {
        $user = $this->getLoggedUser();
        // short circuit: do not send email if user is god level access on myfoot
        if ($user->isMyFootGod()) {
            return;
        }

        $emailMessage = new Genesis_Entity_EmailMessage();
        $emailMessage->setName("new_facility_sales_notification");
        $emailMessage->setMarketingMail(0);
        $body = '';

        switch ($facilities) {
            case (is_array($facilities)): // for SiteLink and Centershift
                $subject = '';
                $count = 0;
                foreach ($facilities as $facility) {
                    $count++;
                    if (empty($subject)) {
                        $accountName = $facility->getAccount()->getName();
                        $subject = "$accountName - New Facilities Added";
                    }
                    $body .= $this->formatFacilityForEmail($facility);
                }
                if ($count > 0) {
                    $body .= 'Number of new facilities: ' . $count . PHP_EOL;
                    $body .= $this->formatFooterForNewFacilityEmail($user);
                    $emailMessage->setSubject($subject);
                    $emailMessage->setBody($body);
                }
                break;
            case ($facilities instanceof Genesis_Entity_Facility): // for manuals
                $facility = $facilities; // reassign name
                $facilityName = $facility->getTitle();
                $emailMessage->setSubject("$facilityName - New Facility Notice");
                $body = $this->formatFacilityForEmail($facility) .
                    $this->formatFooterForNewFacilityEmail($user);
                $emailMessage->setBody($body);
                break;
            default:
                throw new Exception('Invalid facilities type given.');
                break;
        }

        $emailMessage->validateInput();
        Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $emailMessage, array(), 'Sales Team', '<EMAIL>');
        Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $emailMessage, array(), 'Sales Team', '<EMAIL>');
    }

    public function addissnAction()
    {
        $this->view->scripts = array('facility/addissn');

        return;
    }

    public function syncissnAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $opentechAccountId = $this->getParam('accountId');
        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!strlen($opentechAccountId) > 0) {
                throw new Exception('Please enter an Account Id.');
            }

            //check to see if user already exists

            $opentech_corp = Genesis_Service_Corporation::load(Genesis_Db_Restriction::equal('corpCode', $opentechAccountId))->uniqueResult();

            //if corp doesn't already exist, create it
            if ($opentech_corp) {
                throw new Exception('We already an account with your opentech account id.  Contact <EMAIL> for help.');
            } else {
                $opentech_corp = new Genesis_Entity_OpentechCorporation();
                $opentech_corp->setAccountId($account->getId());
                $opentech_corp->setCorpname($account->getName());
                $opentech_corp->setCreated(date("Y-m-d H:i:s", time()));
                $opentech_corp->setOpentechAccountId($opentechAccountId);

                $opentech_corp = Genesis_Service_Corporation::save($opentech_corp);

            }

            //this determines which type of sync do (centershift/sitelink) to and syncs all facilities
            try {
                $facilityResult = Genesis_Service_Sync::sync($opentech_corp);
            } catch (Exception $e) {
                //delete the corp if something messed up on sync
                Genesis_Service_Corporation::delete($opentech_corp);
                echo "Error: " . $e->getMessage() . "\n";

                return;
            }

            $facs = $facilityResult->getNewFacilities();
            $facIds = array();

            //only try to sync units if we got some facility results
            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();
                }
            } else {
                throw new Exception('We did not find any facilities in opentech.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (Exception $e) {
                    echo "Error: " . $e->getMessage() . "\n";
                }
            }
            print implode("," , $facIds);
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    public function listingsAction()
    {
        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $isFSS = $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
        $isManual = $facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_MANUAL;

        // Redirect NON-Manual and NON-FSS
        if (!$isManual && !$isFSS) {
            $this->redirect($this->view->url(['action'=>'units'], 'features').'?fid=' . $facility->getId());
        }

        $this->setCommonViewFields();

        $this->view->facility = $facility;
        $this->view->customClosuresBlogPost = "https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility";
        $this->view->covidModal = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::COVID_MODAL);
        $this->view->customClosures = AccountMgmt_Service_User::isFeatureActive(AccountMgmt_Models_Features::CUSTOM_CLOSURES);
        $this->view->accountId = $this->getParam('account_id') ?? null;

        $this->_helper->layout->setLayout('layout-singlepage');
        $this->_helper->viewRenderer->renderScript('features/index.phtml');

        $this->view->scripts = [
            '../dist/ember/features/assets/vendor',
            '../dist/ember/features/assets/features'
        ];
    }
}
