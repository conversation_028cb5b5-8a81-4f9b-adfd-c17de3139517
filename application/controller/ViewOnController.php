<?php

use AccountMgmt_Models_ApiException as ApiException;

/*
 * View On
 *
 * Serves as a smart redirect for a facility to a site
 * TODO: Support other
 *
 * @copyright 2015 SpareFoot Inc
 * <AUTHOR> <<EMAIL>>
 */
class ViewOnController extends Zend_Controller_Action
{
    protected $_redirector = null;

    public function init()
    {
        $this->_redirector = $this->_helper->getHelper('Redirector');
    }

    public function facilityAction()
    {
        $facilityId = $this->getParam('facility_id');
        try {
            $facility = AccountMgmt_Service_Facility::validateFacilityId($facilityId);
        } catch (Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }

        $siteId = $this->getParam('site_id');
        // TODO: Add support in the future for more sites besides SFDC

        // SpareFoot.com URL
        $url = Genesis_Util_Url::facilityUrl($facility);

        // Redirect
        $this->_redirector->gotoUrl($url);
    }
}
