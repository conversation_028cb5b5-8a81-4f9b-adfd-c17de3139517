<?php
/*
 * Account Controller
 *
 * @copyright 2013 SpareFoot
 * <AUTHOR>
 */

class ReportsController extends AccountMgmt_Controller_Restricted
{
    /**
     * @var SoapClient
     */
    private $soap;

    public function indexAction()
    {
        $this->view->title = 'Reports';
        $domain = Genesis_Config_Server::getEnvDomain();
        if ($domain === 'localhost:8888') { //meh
            $domain = 'localhost';
        }

        $wsdl = "https://pita.sparefoot." . $domain . "/quicksoap?wsdl";
        try {
            $this->soap = new SoapClient($wsdl, array('cache_wsdl' => WSDL_CACHE_NONE, 'trace' => true));
            $account = Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
            $this->view->otherReports = $this->soap->getReports($account->getSfAccountId());
        } catch (SoapFault $e) {
            $errorDetails = [
                'ErrorType' => 'SoapFault',
                'Message' => $e->getMessage(),
                'Code' => $e->getCode(),
                'File' => $e->getFile(),
                'Line' => $e->getLine(),
                'Trace' => $e->getTraceAsString()
            ];
            error_log(json_encode([
                'Timestamp' => date('Y-m-d H:i:s'),
                'ErrorDetails' => $errorDetails,
            ]));
        } catch (Exception $e) {
            $errorDetails = [
                'ErrorType' => 'GeneralException',
                'Message' => $e->getMessage(),
                'Code' => $e->getCode(),
                'File' => $e->getFile(),
                'Line' => $e->getLine(),
                'Trace' => $e->getTraceAsString()
            ];        
            error_log(json_encode([
                'Timestamp' => date('Y-m-d H:i:s'),
                'ErrorDetails' => $errorDetails,
            ]));
        }


    }

    public function tenantConnectAction()
    {
        $this->view->startDate = $this->getTrueBeginDate();
        $this->view->endDate = $this->getTrueEndDate();

        $data = Genesis_Service_Reporting::getTenantConnectCallsByFacility($this->view->startDate, $this->view->endDate, $this->getLoggedUser());

        if ($this->getParam('export')) {
            $this->_helper->layout()->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);
            header('Content-type: text/csv');
            header('Content-disposition:  attachment; filename=' . '"Tenant Connect Report - ' . $this->getLoggedUser()->getAccount()->getName() . '-' . date("Y-m-d") . '.csv"');

            echo '"Facility Name","Response Rate","Call Attempts","Facility Answered","Facility Responded to Call Prompts","Connected to Tenant"' . "\n";

            foreach ($data as $facTCData) {
                $line = array($facTCData['facilityTitle'],
                        (round(($facTCData['facilityRespondedCalls']/$facTCData['totalCalls']),2)*100).'%',
                        $facTCData['totalCalls'],
                        $facTCData['answeredCalls'],
                        $facTCData['facilityRespondedCalls'],
                        $facTCData['connectedCalls']
                );

                echo '"' . implode('","', $line) . '"' . "\n";
            }
        } else {
            $this->view->callData = $data;
        }

        $this->view->scripts = array('reports/tenant-connect');
    }

    public function tenantConnectDetailAction()
    {
        $this->view->startDate = $this->getTrueBeginDate();
        $this->view->endDate = $this->getTrueEndDate();

        $facility = Genesis_Service_Facility::loadById($this->getParam('fid'), $restriction);
        $data = Genesis_Service_TenantConnectCall::loadByFacilityIdAndDate($facility->getId(), $this->view->startDate, $this->view->endDate);

        if ($this->getParam('export')) {
            $this->_helper->layout()->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);
            header('Content-type: text/csv');
            header('Content-disposition:  attachment; filename=' . '"Tenant Connect Report - ' . $facility->getTitle() . '-' . date("Y-m-d") . '.csv"');

            echo '"Phone Number","Date/Time","Customer","Email","Call Status","Listen to Call"' . "\n";

            foreach ($data as $tcCall) {
                $audioLink = ($tcCall->getRecordingUrl () ? $tcCall->getRecordingUrl() : 'No audio');
                $line = array($tcCall->getBooking()->stringPhone(),
                        $tcCall->stringDate(),
                        trim(ucwords($tcCall->getBooking()->getFirstName())).' '.trim(ucwords($tcCall->getBooking()->getLastName())),
                        $tcCall->getBooking()->getUser()->getEmail(),
                        $tcCall->stringStatus(),
                        $audioLink
                );

                echo '"' . implode('","', $line) . '"' . "\n";
            }
        } else {
            $this->view->callData = $data;
            $this->view->facility = $facility;
        }

        $this->view->scripts = array('reports/tenant-connect-detail');
    }

}
