<?php
use AccountMgmt_Models_ApiException as ApiException;

class ApiLocationController extends AccountMgmt_Controller_ApiBaseController
{
    /**
     * expects POST'ed JSON:
     * { zipCodes: [78701, ...] }
     *
     * passes through to location service
     *
     * POST because request uri too long with large zip set
     * /polygon returns combined shape, which is what we want
     * /polygons returns error message about which zips failed
     */
    public function polygonAction()
    {
        $input = json_decode(file_get_contents("php://input"));
        $this->_helper->json(self::getPolygonDataForZipCodes($input->zipCodes), true, array('encodeData' => true));
    }

    /**
     * Get the combined polygon for many zips.
     * Get invalid zipcodes that may have been sent.
     *
     * @param array $zips [78701, ...]
     * @return array [ 'polygon' => string, 'invalidZips' => array]
     * @throws Exception
     *
     */
    public static function getPolygonDataForZipCodes($zips)
    {
        $guzzle = new \GuzzleHttp\Client();
        $combinedUrl = getenv('URL_LOCATION_SERVICE').'/polygon?token=iamsparefoot';
        try {
            $response = $guzzle->post($combinedUrl, ['json' => [
                'zipCodes' => $zips,
                'timeout' => 60 * 3
            ]]);
        } catch (\Exception $e) {
            // Record exception in Phlow
            AccountMgmt_Service_Phlow::getClient()->increment(
                'get_polygon_exception',
                AccountMgmt_Service_Phlow::getClient()->arrayToCsv([
                    'service' => 'location',
                    'path' => 'polygon'
                ])
            );
            throw($e);
        }
        $respBody = $response->getBody();

        return [
            'polygon' => "$respBody"
        ];
    }
}
