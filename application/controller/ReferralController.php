<?php
/*
 * Referral Controller
 *
 * @copyright 2010 SpareFoot Inc
 * <AUTHOR>
 */

class ReferralController extends AccountMgmt_Controller_Restricted
{
    public function indexAction()
    {
        $this->forward('home');
    }

    public function homeAction()
    {
        if ($_POST) {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            //get current logged in users account info
            $loggedInUser = $this->getLoggedUser();
            $account = $loggedInUser->getAccount();
            $accountId = $account->getSfAccountId();
            $facility = $loggedInUser->getManagableFacilities()->uniqueResult();

            //create xml string and email it to salesforce parser
            $firstName = $this->getParam('referral_firstname');
            $lastName = $this->getParam('referral_lastname');
            $email = $this->getParam('referral_email');
            $phone = $this->getParam('referral_phone');
            $businessName = $this->getParam('referral_businessname');

            //send email to referral
            try {
                $notificationParams = array('firstName' => $firstName,
                                                'lastName' => $lastName,
                                                'referrerName' => $loggedInUser->getFirstName().' '.$loggedInUser->getLastName(),
                                                'referrerFacility' => $facility->getTitle(),
                                                'site_id' => Genesis_Entity_Site::ID_SPAREFOOT);

                Genesis_Service_Emailer::sendMessage($email, 'consumer/referral-notification', $notificationParams);
            } catch (Exception $e) {
                Genesis_Util_ErrorLogger::exceptionToHipChat($e);
            }

            //send email to salesforce to parse...
            try {
                $leadPushParams = array('firstName' => $firstName,
                                            'lastName' => $lastName,
                                            'email' => $email,
                                            'phone' => $phone,
                                            'businessName' => $businessName,
                                            'accountId' => $accountId,
                                            'userId' => $loggedInUser->getId());

                // $salesForceLeadPushAddress = '<EMAIL>';
                $salesForceLeadPushAddress = '<EMAIL>';
                Genesis_Service_Emailer::sendMessage($salesForceLeadPushAddress, 'internal/referral-salesforce-lead-push', $leadPushParams);
            } catch (Exception $e) {
                Genesis_Util_ErrorLogger::exceptionToHipChat($e);
            }

            // $this->view->alert = "Thanks.  Your referral has been recieved.";
            // $this->view->alertClass = "alert-success";
            echo json_encode(array('alert' => "Thanks.  Your referral has been received.", 'alertClass' => "alert-success"));
            // return json_encode(array('alert' => "Thanks.  Your referral has been recieved.", 'alertClass' => "alert-success"));
            return true;
        }

        $this->view->scripts = array('referral/home');
    }
}
