<?php
/*
 * Insights Controller
 *
 * @copyright 2011 SpareFoot, Inc.
 * <AUTHOR>
 */

class InsightsController extends AccountMgmt_Controller_Restricted
{
    /**
     * @var $soap SoapClient
     */
    private $soap;


    protected function _init()
    {
        $domain = Genesis_Config_Server::getEnvDomain();
        if ($domain === 'localhost:8888') { //meh
            $domain = 'localhost';
        }

        $this->soap = new SoapClient(
            "http://pita.sparefoot." . $domain . "/quicksoap?wsdl",
            array('cache_wsdl' => WSDL_CACHE_NONE)
        );
        $this->view->user = $this->getLoggedUser();
        $this->view->accountId = $this->getLoggedUser()->getAccountId();
        $this->view->allReports = $this->soap->getReports($this->getLoggedUser()->getAccount()->getSfAccountId());
    }

    // get report parameters and display option to view as HTML table or download
    public function customAction()
    {
        $reportClassName = $this->getParam('report');
        $account = Genesis_Service_Account::loadById( $this->getLoggedUser()->getAccountId() );
        $this->view->inputs = $this->soap->getReportParameters($reportClassName, $account->getSfAccountId());
        $this->view->reportClassName = $reportClassName;
        $this->view->reportName = $this->soap->getReportName($reportClassName);

        if ($this->getRequest()->isGet()) {

            if ($this->getParam('csv') === 'Download') {

                $this->_helper->layout->disableLayout();
                $this->_helper->viewRenderer->setNoRender(true);
                header('Content-type: text/csv');
                header('Content-disposition:  attachment; filename="report.csv"');
                echo $this->soap->renderReport($reportClassName, $this->_getAllParams(), $account->getSfAccountId());

            } else {

                $this->view->renderedTable = $this->soap->renderReportTable($reportClassName, $this->_getAllParams(), $account->getSfAccountId());
            }

        }

        $this->view->scripts = array('insights/custom');
    }

    public function customtableAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $account = Genesis_Service_Account::loadById( $this->getLoggedUser()->getAccountId() );
        echo $this->soap->renderReportTable($this->getParam('report'), $this->_getAllParams(), $account->getSfAccountId());
    }

    // download as CSV file
    public function downloadAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        header('Content-type: text/csv');
        header('Content-disposition:  attachment; filename="report.csv"');
        echo $this->soap->renderReport($this->getParam('report'), $this->_getAllParams());
    }

    public function indexAction()
    {
        $this->view->scripts = array('insights/index');
        $this->view->title = 'Pricing Data';

        $facilities = $this->view->user->getManagableFacilities();
        $show_report = false;
        $reports_count = 0;
        $str_report_row = '';
        $facilityIds = array();
        $hasInsightsFacNoneEnabled = $hasInsightsFacNoData = $hasInsightsFacNoBe = $stillProcessingReport = false;
        $dayOfMonth = date('j');

        $reportDate = $this->getParam('report_date');
        if (! empty($reportDate)) {
            $insights_date = $reportDate;
        } elseif ($dayOfMonth < Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY) {
            $insights_date = date("Y-m-" . Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY, strtotime('-1 month'));
        } else {
            $insights_date = date("Y-m-" . Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY);
        }


        if ($dayOfMonth < Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY) {
            $this->view->nextReport = date('F Y');
            $this->view->nextReportDays = Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY - $dayOfMonth;
        } else {
            $this->view->nextReport = date('F Y', strtotime('+1 month'));
            $this->view->nextReportDays = date('t') - $dayOfMonth + Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY;
        }

        $output = array('report_date' => $insights_date);

        $str_csv = "Facility Name,Facility Company Code,Facility Address,City, State,Zip,Sample Distance,Facilities Sampled,CC 5x5,CC 5x10,CC 5x15,CC 10x10,CC 10x15,CC 10x20,CC 10x30,non-CC 5x5,non-CC 5x10,non-CC 5x15,non-CC 10x10,non-CC 10x15,non-CC 10x20,non-CC 10x30\n";
        /**
         * @var $facility Genesis_Entity_Facility
         */

        foreach ($facilities as $facility) {

            // We add the facility ID even if we don't show the report for this facility so that
            // we can properly populate the date dropdown picker.
            $facilityIds[] = $facility->getId();

            if (! $facility->getInsights()) {
                continue;
            }
            $hasInsightsFacNoneEnabled = false;

            if ($facility->getLocationId() == Genesis_Entity_Location::BLANK_LOCATION_ID) {
                continue;
            }
            $hasInsightsFacNoData = false;

            if (! $facility->getBillableEntityId()) {
                continue;
            }
            $hasInsightsFacNoBe = false;

            $reportType = Genesis_Entity_Insights::REPORT_TYPE_ORIGINAL;
            if ($this->getParam('report_type') && $this->getParam('report_type') == Genesis_Entity_Insights::REPORT_TYPE_LOCAL) {
                $reportType = Genesis_Entity_Insights::REPORT_TYPE_LOCAL;
            }


            $insight_data = $facility->getInsightsReport($insights_date, $reportType);

            if (! $insight_data) {
                //only show "still processing" message if passed all the other gates above and no facility...
                //insights reports are ready or DB issues causing report data to return empty
                $stillProcessingReport = true;
                continue;
            }

            $logFacilities[] = $facility->getId(); // add facility id to be logged

            $show_report = true;
            $reports_count++;

            $view = new Zend_View();
            $view->setScriptPath(APPLICATION_PATH . '/views/scripts/');

            $view->facilityTitle = $facility->getTitle();
            $view->facilityAddress = $facility->getLocation()->getAddress1() . ", " .
                        $facility->getLocation()->getCity() . ", " .  $facility->getLocation()->getState() . " " . $facility->getLocation()->getZip();

            $view->companyCode = $facility->getCompanyCode();
            $view->listingAvailId = $insight_data->getListingAvailId();
            $view->id = $insight_data->getId();

            if ($insight_data->getReportType() == Genesis_Entity_Insights::REPORT_TYPE_ORIGINAL) {
                $view->searchRadius = $insight_data->getSearchRadius();
            } elseif ($insight_data->getReportType() == Genesis_Entity_Insights::REPORT_TYPE_LOCAL) {
                $view->searchRadius = $insight_data->getMaxDistance();
            }

            $view->numSampledFacilities = $insight_data->getNumSampledFacilities();

            $view->reportMonth = date("F",strtotime("01-".$insight_data->getReportMonth()."-".$insight_data->getReportYear()));
            $view->reportYear = $insight_data->getReportYear();
            $view->cc25 = ($insight_data->getCc25() > 0) ? $insight_data->getCc25() : 'Not enough data';
            $view->noncc25 = ($insight_data->getNoncc25() > 0) ? $insight_data->getNoncc25() : 'Not enough data';
            $view->cc50 = ($insight_data->getCc50() > 0) ? $insight_data->getCc50() : 'Not enough data';
            $view->noncc50 = ($insight_data->getNoncc50() > 0) ? $insight_data->getNoncc50() : 'Not enough data';
            $view->cc75 = ($insight_data->getCc75() > 0) ? $insight_data->getCc75() : 'Not enough data';
            $view->noncc75= ($insight_data->getNoncc75() > 0) ? $insight_data->getNoncc75() : 'Not enough data';
            $view->cc100 = ($insight_data->getCc100() > 0) ? $insight_data->getCc100() : 'Not enough data';
            $view->noncc100 = ($insight_data->getNoncc100() > 0) ? $insight_data->getNoncc100() : 'Not enough data';
            $view->cc150 = ($insight_data->getCc150() > 0) ? $insight_data->getCc150() : 'Not enough data';
            $view->noncc150 = ($insight_data->getNoncc150() > 0) ? $insight_data->getNoncc150() : 'Not enough data';
            $view->cc200 = ($insight_data->getCc200() > 0) ? $insight_data->getCc200() : 'Not enough data';
            $view->noncc200 = ($insight_data->getNoncc200() > 0) ? $insight_data->getNoncc200() : 'Not enough data';
            $view->cc500 = ($insight_data->getCc500() > 0) ? $insight_data->getCc500() : 'Not enough data';
            $view->noncc500 = ($insight_data->getNoncc500() > 0) ? $insight_data->getNoncc500() : 'Not enough data';

            $yourInsightsData = Genesis_Service_Insights::buildYourInsightsData($facility);

            $view->your_cc25 = ($yourInsightsData['cc']['25'] > 0) ? trim($yourInsightsData['cc']['25']) : 'N/A';
            $view->your_cc50 = ($yourInsightsData['cc']['50'] > 0) ? trim($yourInsightsData['cc']['50']) : 'N/A';
            $view->your_cc75 = ($yourInsightsData['cc']['75'] > 0) ? trim($yourInsightsData['cc']['75']) : 'N/A';
            $view->your_cc100 = ($yourInsightsData['cc']['100'] > 0) ? trim($yourInsightsData['cc']['100']) : 'N/A';
            $view->your_cc150 = ($yourInsightsData['cc']['150'] > 0) ? trim($yourInsightsData['cc']['150']) : 'N/A';
            $view->your_cc200 = ($yourInsightsData['cc']['200'] > 0) ? trim($yourInsightsData['cc']['200']) : 'N/A';
            $view->your_cc500 = ($yourInsightsData['cc']['500'] > 0) ? trim($yourInsightsData['cc']['500']) : 'N/A';
            $view->your_noncc25 = ($yourInsightsData['noncc']['25'] > 0) ? trim($yourInsightsData['noncc']['25']) : 'N/A';
            $view->your_noncc50 = ($yourInsightsData['noncc']['50'] > 0) ? trim($yourInsightsData['noncc']['50']) : 'N/A';
            $view->your_noncc75 = ($yourInsightsData['noncc']['75'] > 0) ? trim($yourInsightsData['noncc']['75']) : 'N/A';
            $view->your_noncc100 = ($yourInsightsData['noncc']['100'] > 0) ? trim($yourInsightsData['noncc']['100']) : 'N/A';
            $view->your_noncc150 = ($yourInsightsData['noncc']['150'] > 0) ? trim($yourInsightsData['noncc']['150']) : 'N/A';
            $view->your_noncc200 = ($yourInsightsData['noncc']['200'] > 0) ? trim($yourInsightsData['noncc']['200']) : 'N/A';
            $view->your_noncc500 = ($yourInsightsData['noncc']['500'] > 0) ? trim($yourInsightsData['noncc']['500']) : 'N/A';
            $view->your_edit_link = $this->view->url(['action'=> 'inventory'], 'features').'?fid='.$facility->getId();

            $str_csv .= $view->facilityTitle.","
                . $view->companyCode.","
                . $facility->getLocation()->getAddress1().","
                . $facility->getLocation()->getCity().","
                . $facility->getLocation()->getState().","
                . $facility->getLocation()->getZip().","
                . $view->searchRadius.","
                . $view->numSampledFacilities.","
                . $view->cc25.","
                . $view->cc50.","
                . $view->cc75.","
                . $view->cc100.","
                . $view->cc150.","
                . $view->cc200.","
                . $view->cc500.","
                . $view->noncc25.","
                . $view->noncc50.","
                . $view->noncc75.","
                . $view->noncc100.","
                . $view->noncc150.","
                . $view->noncc200.","
                . $view->noncc500."\n";

            $str_report_row .= str_replace("\n", '', $view->render('insights/insightsdata.phtml'));

        }

        $availableReports = Genesis_Service_Insights::getAvailableReports($facilityIds);


        $monthNames = array(
            '1' => 'January',
            '2' => 'February',
            '3' => 'March',
            '4' => 'April',
            '5' => 'May',
            '6' => 'June',
            '7' => 'July',
            '8' => 'August',
            '9' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        );
        $this->view->availableReports = array();
        foreach ($availableReports as $year => $months) {
            foreach ($months as $month) {
                $this->view->availableReports[$year . '-' . $month] = $monthNames[$month] . ' ' . $year;
            }
        }

        $this->view->outputstr = $output;

        $this->view->showreport = $show_report;
        $this->view->reports_count = $reports_count;
        $logger = new Genesis_Util_ActionLogger();
        if ($show_report) {

            if ($this->_hasParam('report_date')) {
            $this->_helper->layout()->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);
            if (empty($str_report_row)) {
                echo "<div class='error'>Data is only available for months you have purchased Insights. Data is not available for this month.</span>";
            } else {
                if ($this->getParam('export')) {
                    $file_name = "InsightsReport-".$insights_date;

                    header("Pragma: public");
                    header("Expires: 0");
                    header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
                    header("Cache-Control: private",false);
                    header("Content-Type: application/octet-stream");
                    header("Content-Disposition: attachment; filename=\"$file_name.csv\";" );
                    header("Content-Transfer-Encoding: binary");

                    echo $str_csv;
                } else {
                    echo($str_report_row);
                    foreach ($logFacilities as $facId) {
                        $logger->logAction('view_insights', null, ' ', $this->getLoggedUser()->getId(), $facId, null);
                    }
                }
            }
        } else {
            $this->view->scripts = array('insights/insightsreport');
                $this->render("insightsreport");
            }
        } elseif ($stillProcessingReport) {
            $this->view->scripts = array('insights/reportgenerating');
            $this->render("reportgenerating");
        } else {
            //see if all facilities have insights turned on but don't have address or billable entity
            $this->view->askInsightsEnabled = $hasInsightsFacNoneEnabled ? true : false;
            $this->view->askAddress = $hasInsightsFacNoData ? true : false;
            $this->view->askBilling = $hasInsightsFacNoBe ? true : false;

            $this->render("index");
        }
    }

    public function generatenewAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $accountId = $this->getParam('account_id');
        $facilities = Genesis_Service_Facility::loadByAccountId($accountId);
        $facilities->setRewindable(false);

        $success = 0;
        $searchClient = new AccountMgmt_Clients_SearchClient('Myfoot-Insights', false);
        foreach ($facilities as $facility) {
            try {
                $facility->generateInsightsReport(false, Genesis_Entity_Insights::REPORT_TYPE_ORIGINAL, $searchClient);
                $facility->generateInsightsReport(false, Genesis_Entity_Insights::REPORT_TYPE_LOCAL, $searchClient);
                $success = 1;
            } catch (Exception $e) {
                continue;
            }
        }

        echo json_encode(array(
            'success' => $success,
        ));
    }

    // list available custom reports
    public function reportsAction()
    {
    }

    // render as HTML table
    public function tableAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $params = $this->_getAllParams();
        $reportName = $params['report'];
        $renderedTable = $this->soap->renderReportTable($reportName, $params);
        echo $renderedTable;
    }

    public function tellmeAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $msg = new Genesis_Entity_EmailMessage();
        $msg->setSubject('Insights product information requested');

        $msg->setBody(
            'First Name: ' . $this->view->user->getFirstName() . '<br/>' .
            'Last Name: ' . $this->view->user->getLastName() . '<br/><br/>' .
            'Phone: ' . $this->view->user->getPhone() . '<br/>' .
            'Email: ' . $this->view->user->getEmail() . '<br/><br/>' .
            'User Id: ' . $this->view->user->getId() . '<br/>' .
            'Member Since: ' . $this->view->user->getMemberSince() . '<br/>'
            );

        Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $msg, array());
        echo 'Email Sent';
    }

    public function termsAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $acctMgmtUser = Genesis_Service_UserAccess::loadById($this->view->user->getId());
        $acctMgmtUser->setTermsVersion(Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION);
        Genesis_Service_UserAccess::save($acctMgmtUser);

        $this->getLoggedUser()->setTermsVersion(Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION);

        $this->view->scripts = array('insights/terms');
    }
}
