<?php
/**
 * @Author: k<PERSON><PERSON><PERSON>
 * @Date:   2015-10-30 15:19:27
 */

use AccountMgmt_Models_ApiException as ApiException;

class ApiReviewController extends AccountMgmt_Controller_ApiBaseController
{
    public function indexAction()
    {
        $review_id = $this->getParam('review_id');
        $facility_id = $this->getParam('facility_id');
        if (!$review_id) {
            throw new ApiException(ApiException::NOT_ACCEPTABLE . 'Review id must be passed');
        }

        if ($this->_request->isGet()) {
            $review = AccountMgmt_Service_Review::validateAndGetReview($review_id, $facility_id);
            $this->_helper->json(['data'=>AccountMgmt_Service_Review::toArray($review)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }


}