<?php
use AccountMgmt_Models_ApiException as ApiException;
/**
 *   // Batch of data
 *   status: 500
 *   errors: [
 *      code: 'SERVER_FAILURE'
 *      id: 100024
 *      title: 'Server has failed'
 *   ],
 *   facilities:
 *     data: [{}, {}, {}],
 *     meta:
 *       totalRecords: 0,
 *       limit: 0,
 *       offset: 0
 *   units:
 *     data: [],
 *     meta:
 *       totalRecords: 0,
 *       limit: 0,
 *       offset: 0
 *
 *   // Single record
 *   status: 500
 *   errors: [
 *      code: 'SERVER_FAILURE'
 *      id: 100024
 *      title: 'Server has failed'
 *   ],
 *   data: {}
 *   meta:
 *     totalRecords: 0,
 *     limit: 0,
 *     offset: 0
 *
 *   // Array of records but ONLY single resource
 *   status: 500
 *   errors: [
 *      code: 'SERVER_FAILURE'
 *      id: 100024
 *      title: 'Server has failed'
 *   ],
 *   data: [{}, {}, {}]
 *   meta:
 *     totalRecords: 0,
 *     limit: 0,
 *     offset: 0
 **/
abstract class AccountMgmt_Controller_ApiBaseController extends Zend_Controller_Action
{
    public function init($requireAuth = true)
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $this->getResponse()->setHeader('Content-Type', 'application/json');
        Zend_Controller_Front::getInstance()->throwExceptions(true);
        //set our custom exception handler for json
        set_exception_handler(['AccountMgmt_Models_ApiException','apiExceptionHandler']);

        if (! $requireAuth) {
            return;
        }

        if (! AccountMgmt_Service_User::getLoggedUser() && $this->getRequest()->getControllerName() !== 'api-login') {

            throw new ApiException(ApiException::UNAUTHORIZED, 'you are not signed in');
        }
    }

    protected function getLoggedUser()
    {
        //special for api, bypasses setting fac and accountId
        return AccountMgmt_Service_UserOauth::getUserAccess();
    }

    protected static function sendOKEmptyResponse()
    {
        Zend_Controller_Front::getInstance()->getResponse()
            ->setHttpResponseCode(204)
            ->sendResponse();
        exit();
    }

    public function validateAndGetFacility($facilityId, $checkWriteAccess = false) {
        try {
            $facility = AccountMgmt_Service_Facility::validateFacilityId($facilityId);
        } catch (Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }
        try {
            AccountMgmt_Service_User::validateFacilityAccess($facility, $checkWriteAccess);
        } catch (Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED);
        }

        return $facility;
    }

    public function validateAndGetUser($userId,$action = NULL) {
        try {
           $user = AccountMgmt_Service_User::validateUserId($userId);
           AccountMgmt_Service_User::validateUserAccess($user,$action);
        } catch (Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED);
        }
        return $user;
    }

    public function validateIsoDate($date,$paramName) {
        if (! AccountMgmt_Service_Util::isValidIsoDate($date)) {
            throw new ApiException(ApiException::NOT_ACCEPTABLE,'Invalid ISO date for parameter ' . $paramName);
        }
    }

    public function validateStartEndDate($startDate,$endDate) {
        if ($endDate !== NULL) {
            $this->validateIsoDate($endDate,'endDate');
        }
        if ($startDate !== NULL) {
            $this->validateIsoDate($startDate,'startDate');
        }
        if(strtotime($startDate) > strtotime($endDate) && ($endDate !== NULL && $startDate != NULL)) {
            throw new ApiException(ApiException::BAD_REQUEST,'startDate must by earlier than endDate');
        }
    }
}
