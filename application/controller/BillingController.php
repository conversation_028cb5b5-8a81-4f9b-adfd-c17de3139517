<?php
/*
 * Billing Controller
 *
 * @copyright 2010 SpareFoot Inc
 * <AUTHOR>
 */

class BillingController extends AccountMgmt_Controller_Restricted
{

    /**
     * Initialize controller
     */
    protected function _init()
    {
        if (!$this->getLoggedUser()->canUseBilling()) {
            throw new Exception('This user is not allowed to use billing.');
        }
        $this->view->nocache = 1;
    }

    public function indexAction()
    {
        $this->redirect('billing/home');
    }

    public function homeAction()
    {
        $statements = Genesis_Service_Statement::loadByAccount($this->getLoggedUser()->getAccount());

        foreach ($statements as $statement) {
            if ($statement->getStatementBatch()->getStatus() === Genesis_Entity_StatementBatch::STATUS_OPEN) {
                $this->redirect('billing/viewstatement/id/'.$statement->getId());

                return;
            }
        }

        $this->redirect('billing/statements');
    }

    public function pdfAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if (!$this->getParam('statement_id')) {
            $this->redirect('/billing');
        }

        $statement = Genesis_Service_Statement::loadById($this->getParam('statement_id'));

        $acct = $statement->getAccount();

        if ($acct->getBidType() === Genesis_Entity_Account::BID_TYPE_RESIDUAL) {
            $fileName = $this->_exportFileName($acct->getName(), $statement->getStatementBatch()->getLabel("Rent Collected:"), 'pdf');
        } else {
            $fileName = $this->_exportFileName($acct->getName(), $statement->getStatementBatch()->getLabel(), 'pdf');
        }

        header('Content-type: application/pdf');
        header('Content-disposition:  attachment; filename="' . $fileName . '"');

        $pdf = $statement->getPdf($this->getLoggedUser());

        //log a pdf view
        $logger = new Genesis_Util_ActionLogger();
        $logger->logAction('viewed_statement_pdf', "", "", ($this->getLoggedUser() ? $this->getLoggedUser()->getId() : null), "", $statement->getId());

        echo $pdf->Output('file.pdf','S');
    }

    public function checkbookingAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        if ($this->getParam('state')) {
            Genesis_Dao_Transaction::check($this->getParam('confirmation_code'));
        } else {
            Genesis_Dao_Transaction::unCheck($this->getParam('confirmation_code'));
        }
    }

    public function csvAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if (!$this->getParam('statement_id')) {
            $this->redirect('/billing');
        }

        $statement = Genesis_Service_Statement::loadById($this->getParam('statement_id'));
        $user = Genesis_Service_UserAccess::loadById($this->getParam('user_id'));

        $acct = $statement->getAccount();

        if ($acct->getBidType() === Genesis_Entity_Account::BID_TYPE_RESIDUAL) {
            $fileName = $this->_exportFileName($acct->getName(), $statement->getStatementBatch()->getLabel("Rent Collected:"), 'csv');
        } else {
            $fileName = $this->_exportFileName($acct->getName(), $statement->getStatementBatch()->getLabel(), 'csv');
        }

        header('Content-type: text/csv');
        header('Content-disposition:  attachment; filename="' . $fileName . '"');

        if ($user) {
            $csv = $statement->getCsv($user);
        } else {
            $csv = $statement->getCsv();
        }

        $logger = new Genesis_Util_ActionLogger();
        $logger->logAction('viewed_statement_csv', "", "", ($this->getLoggedUser() ? $this->getLoggedUser()->getId() : null), "", $statement->getId());

        echo $csv;
    }

    private function _exportFileName($acctName, $batchName, $extension)
    {
        $name = 'SpareFoot-' . str_replace(' ', '', $acctName) . '-' . $batchName . '.' . $extension;
        $name = str_replace(' ', '_', $name);
        $name = preg_replace('/[^a-zA-Z0-9\.\-\_]/', '', $name);

        return $name;
    }

    public function statementsAction()
    {
        //never let anyone hit old billing controller anymore
        $this->redirect('statement');

        $account = $this->getLoggedUser()->getAccount();
        $this->view->account = $account;

        if ($this->getLoggedUser()->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD) {
            $this->view->userId = null;
        } else {
            $this->view->userId = $this->getLoggedUser()->getId();
        }

        $statements = Genesis_Service_Statement::loadByAccount($account);

        $this->view->statementPairs = array();

        foreach ($statements as $statement) {
            array_unshift($this->view->statementPairs,array(
                'STATEMENT' => $statement,
                'BATCH' => Genesis_Service_StatementBatch::loadById($statement->getStatementBatchId())
                ));
        }
        $this->view->scripts = array('billing/statements');
    }

    public function viewstatementAction()
    {
        $account = $this->getLoggedUser()->getAccount();
        if ($account->getBidType() === Genesis_Entity_Account::BID_TYPE_RESIDUAL) {
            $this->forward('residualstatement');
        }

        $this->view->selectedAction = Zend_Controller_Front::getInstance()->getRequest()->getActionName();

        // Set up an interstitial to display once per session.

        if (!isset($this->getSession()->showInterstitial)) {
            $this->getSession()->showInterstitial = true;
        } elseif ($this->getSession()->showInterstitial) {
            $this->getSession()->showInterstitial = false;
        }

        $this->view->showInterstitial = $this->getSession()->showInterstitial;

        if ($this->view->showInterstitial) {
            $restriction = Genesis_Db_Restriction::empty_();
            $restriction->setLimit(Genesis_Db_Limit::limit(5));
            $this->view->interstitialFacilities = $this->getLoggedUser()->getManagableFacilities($restriction);
        } else {
            $this->view->interstitialFacilities = array();
        }

        $this->view->statementId = $this->getParam('id');
        $this->view->statement = Genesis_Service_Statement::loadById($this->view->statementId);
        if (!$this->view->statement) {
            throw new Exception("You  must provide a valid statement id to view this page.");
        }

        $this->view->batch = $this->view->statement->getStatementBatch();

        //this is null if they're on the page that lists all facilities
        $this->view->facilityId = $this->getParam('facility');

        $this->view->allTableList = array();
        $this->view->freeTableList = array();
        $this->view->autoTableList = array();
        $this->view->earlyTableList = array();
        $this->view->lateTableList = array();
        $this->view->possibleDups = array();




        $facilities = $this->getLoggedUser()->getManagableFacilities(null, true);
        $this->view->facilities = $facilities;
        $this->view->availableSisterFacilities = $this->getLoggedUser()->getAccount()->getFacilities();

        foreach ($facilities as $facility) {
            //if we're looking at only 1 facility; skip all untill we get to the one we want...
            if (isset($this->view->facilityId) && $facility->getId() != $this->view->facilityId) {
                continue;
            }

            $freeTransList = array();
            $paidTransList = array();
            $autoTransactions = array();

            $this->view->facility = Genesis_Service_Facility::loadById($facility->getId());

            //build the statement from billable instances for 36 on
            if ($this->view->statement->getStatementBatchId() > 35) {
                $transactions = Genesis_Service_BillableInstance::loadBookingsByFacilityStatementId($facility->getId(), $this->view->statement->getId());
            } else {
                $transactions = Genesis_Service_Transaction::loadByFacilityStatementId($facility->getId(), $this->view->statement->getId());
            }

            if ($transactions) {
                foreach ($transactions as $confirmationCode => $trans) {
                    if ($trans['free'] != 1) {
                        //skip transaction if INVALID booking
                        if ($trans['booking_state'] == Genesis_Entity_Transaction::BOOKING_STATE_INVALID) {
                            continue;
                        }

                        $bi = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($confirmationCode, $this->view->statementId);

                        if ($trans['auto_state'] != Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) {
                            //check that its not delayed in the BI before putting it in list


                            if ($bi->getReason() != Genesis_Entity_BillableInstance::REASON_MOVE_IN_DELAY) {
                                $paidTransList[] = $trans;
                            }
                        } elseif ($trans['auto_state'] == Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) {
                            $autoTransactions[$confirmationCode] = $trans;
                        }

                    } else {
                        $freeTransList[] = $trans;
                    }

                    //look for a duplicate
                    $dupRecord = Genesis_Service_ReservationDuplicate::load(
                            Genesis_Db_Restriction::equal('confirmationCode', $confirmationCode))->uniqueResult();

                    //if found get the dup transaction
                    if ($dupRecord) {
                        $possibleDup = Genesis_Service_Transaction::loadById($dupRecord->getDupConfirmationCode());

                        //only tag dups in same facility
                        if ($possibleDup->getFacility()->getId() == $facility->getId() ) {
                            $this->view->possibleDups[] = $confirmationCode;
                        }
                    }
                }

                if (count($paidTransList) > 0) {
                    $this->view->allTableList[$facility->getId()] = array('FACILITY' => $facility, 'TRANSACTIONS' => $paidTransList);
                }

                if (count($freeTransList) > 0) {
                    $this->view->freeTableList[$facility->getId()] = array('FACILITY' => $facility, 'TRANSACTIONS' => $freeTransList);
                }

                if (count($autoTransactions) > 0) {
                    $this->view->autoTableList[$facility->getId()] = array('FACILITY' => $facility, 'TRANSACTIONS' => $autoTransactions);
                }
            }

            //get this facility early/late move ins
            $earlyMoves = $this->_getFacilityEarlyMoveIns($facility, $this->view->statement);
            if ($earlyMoves) {
                $this->view->earlyTableList[$facility->getId()] = array('FACILITY' => $facility, 'TRANSACTIONS' => $earlyMoves);
            }

            $lateMoves = $this->_getFacilityLateMoveIns($facility, $this->view->statement);
            if ($lateMoves) {
                $this->view->lateTableList[$facility->getId()] = array('FACILITY' => $facility, 'TRANSACTIONS' => $lateMoves);
            }
        }

        $db_connection = Genesis_Db_Connection::getInstance();
        $stmt = $db_connection->prepare("SELECT source_id FROM account_software WHERE account_id ='" . $account->getId() . "'");
        $stmt->execute();
        $results = $stmt->fetchAll();
        $this->view->arr_softwares = array();
        if ($results) {
            foreach ($results as $result) {
                $this->view->arr_softwares[$result['source_id']] = $result['source_id'];
            }
        }

        $this->view->scripts = array('billing/viewstatement');
    }

    public function residualstatementAction()
    {
        $this->view->statementId = $this->getParam('id');
        $this->view->statement = Genesis_Service_Statement::loadById($this->view->statementId);
        if (!$this->view->statement) {
            throw new Exception("You  must provide a valid statement id to view this page.");
        }

        $this->view->account = $this->view->statement->getAccount();
        $this->view->batch = $this->view->statement->getStatementBatch();

        //this is null if they're on the page that lists all facilities
        $this->view->facilityId = $this->getParam('facility');

        // build tables for output
        $tenantList = array();
        $reservationList = array();
        $earlyList = array();
        $lateList = array();
        $possibleDups = array();

        $earlyDate = date ( 'Y-m-d', strtotime ($this->view->batch->getEndDate() ) );
        $lateStartDate = date ( 'Y-m-d', strtotime ( '-10 day' . $this->view->batch->getStartDate() ) );
        $lateEndDate = date ( 'Y-m-d', strtotime ( '-1 day' . $this->view->batch->getStartDate() ) );

        $facilities = $this->getLoggedUser()->getManagableFacilities(null, true);
        $this->view->facilities = $facilities;
        $this->view->availableSisterFacilities = $this->getLoggedUser()->getAccount()->getFacilities();

        foreach ($facilities as $facility) {
            //if we're looking at only 1 facility; skip all untill we get to the one we want...
            if (isset($this->view->facilityId) && $facility->getId() != $this->view->facilityId) {
                    continue;
            }

            $this->view->facility = Genesis_Service_Facility::loadById($facility->getId());

            $billableInstances = Genesis_Service_BillableInstance::loadResidualByFacilityStatementId($facility->getId(), $this->view->statement->getId());

            //get regular tenant and reservation lists
            if ($billableInstances) {
                foreach ($billableInstances as $billableInstance) {
                    $trans = Genesis_Service_Transaction::loadById($billableInstance->getConfirmationCode());
                    if (!$trans) {
                        throw new Exception("Could not load transaction [".$billableInstance->getConfirmationCode()."] for billable instance: ".$billableInstance->getBillableInstanceId());
                    }

                    // skip invalid bookings
                    if ($trans->getBookingState() == Genesis_Entity_Transaction::BOOKING_STATE_INVALID) continue;

                    if ($trans->getBookingState() == Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) {
                        $tenantList[$billableInstance->getBillableInstanceId()] = $trans;
                    } else {
                        //only put them on reservation list if reasons != delayed which means they'll be moved to the early move in list
                        if ($billableInstance->getReason() != Genesis_Entity_BillableInstance::REASON_MOVE_IN_DELAY) {
                            $reservationList[$billableInstance->getBillableInstanceId()] = $trans;
                        }
                    }

                    //look for a duplicate
                    $dupRecord = Genesis_Service_ReservationDuplicate::load(
                                Genesis_Db_Restriction::equal('confirmationCode', $trans->getConfirmationCode()))->uniqueResult();

                    //if found get the dup transaction
                    if ($dupRecord) {
                        $possibleDup = Genesis_Service_Transaction::loadById($dupRecord->getDupConfirmationCode());

                        //only tag dups in same facility
                        if ($possibleDup->getFacility()->getId() == $facility->getId() ) {
                                $possibleDups[] = $trans->getConfirmationCode();
                        }
                    }
                }
            }

            //get early bookings (all future pending)
            $earlyRestriction = Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                Genesis_Db_Restriction::greaterThan('moveIn', $earlyDate),
                Genesis_Db_Restriction::equal('bookingState', Genesis_Entity_Transaction::BOOKING_STATE_PENDING),
                Genesis_Db_Restriction::isNull('statementId'));
            $earlyRestriction->setOrder(Genesis_Db_Order::asc('moveIn'));
            $earlies = Genesis_Service_Transaction::load($earlyRestriction);
            foreach ($earlies as $e) {
                //skip if already on the billable instance somewhere and reason != delay (which means it was on the BI but MID changed)
                $bi = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($e->getConfirmationCode(), $this->view->statement->getId());
                if ($bi && $bi->getReason() != Genesis_Entity_BillableInstance::REASON_MOVE_IN_DELAY) {
                    continue;
                }

                $earlyList[] = $e;
            }

            //get late move-ins
            $lateRestriction = Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                Genesis_Db_Restriction::between('moveIn', $lateStartDate, $lateEndDate),
                Genesis_Db_Restriction::equal('bookingState', Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED));
            $lateRestriction->setOrder(Genesis_Db_Order::asc('moveIn'));
            $lates = Genesis_Service_Transaction::load($lateRestriction);
            foreach ($lates as $l) {
                $lateList[] = $l;
            }
        }

        //sort $tenantList by last name
        uasort($tenantList, array($this, 'cmpLastName'));

        //sort $reservationList by move in date
        uasort($reservationList, array($this, 'cmpMoveInDate'));

        $this->view->tenantTableList = $tenantList;
        $this->view->reservationTableList = $reservationList;

        $this->view->earlyTableList = $earlyList;
        $this->view->lateTableList = $lateList;

        $this->view->possibleDups = $possibleDups;

        $this->view->scripts = array('billing/residualstatement');
    }

    //when early & lates are changed to default price, they call this
    public function updateresidualmidAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $overrideMoveUntoOpenStatementRestriction = false;

        try {
            if (!$this->getParam('into_date')) {
                throw new Exception("You must select a move-in date.");
            }

            $trans = Genesis_Service_Transaction::loadById($this->getParam('confirmation_code'));

            if (!$trans) {
                throw new Exception("Could not find booking " . $this->getParam('confirmation_code'));
            }

            // If the logged in user is a myfoot god, we need to set $overrideMoveUntoOpenStatementRestriction to true so
            // the correct conditions are met in Genesis_Service_Transaction::updateMoveInDate to correctly mark the MID.
            // This is needed because within Genesis_Service_Transaction::updateMoveInDate, when $loggedUser->getUserAccess()->getManageableFacilityIds()
            // is called it uses the incorrect account id. It uses the account id of the logged in god user instead of the account id of the account the god user is viewing.
            // Because of this, if you didn't pass in $overrideMoveUntoOpenStatementRestriction in as true, the facility Id
            // wont be in the list of $loggedUser->getUserAccess()->getManageableFacilityIds() and booking will be left as pending.
            // See https://sparefoot.atlassian.net/browse/EPO-389.
            if ($this->getLoggedUser()->isMyFootGod()) {
                $overrideMoveUntoOpenStatementRestriction = true;
            }

            $trans->setMoveIn(date("Y-m-d", strtotime(str_replace("-", "/", $this->getParam('into_date')))));
            Genesis_Service_Transaction::updateMoveInDate($trans, $this->getLoggedUser(), true, false, $overrideMoveUntoOpenStatementRestriction); //this adds to bi's if needed
        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }

    public function updateresidualfacilityAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        try {

            $ret = array();
            $confirmationCode = $this->getParam('confirmation_code');
            $statementId = $this->getParam('statement_id');

            if (!AccountMgmt_Service_Statement::isStatementBatchOpen($statementId)) {
                throw new Exception('StatementClosed');
            }

            // load billable instance and booking
            $billableInstance = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId(
                $confirmationCode,
                $statementId
            );
            if (!$billableInstance) {
                throw new Exception("Could not load billable instance for confirmation code: " . $confirmationCode);
            }
            $booking = Genesis_Service_Transaction::loadById($this->getParam('confirmation_code'));
            if (!$booking) {
                throw new Exception("Could not load transaction for confirmation code: " . $confirmationCode);
            }

            //Check to see if facility name or unit number was changed
            $unitNumber = $this->getParam('unit_number');
            $newFacilityId = $this->getParam('facility_id');

            if ($unitNumber && $unitNumber != '') {

                $booking->setUnitNumber($unitNumber);
                Genesis_Service_Transaction::updateUnitNumber($booking, $this->getLoggedUser());
                $ret['unitNumber'] = $unitNumber;
            } else {
                //Saved without a unit number, delete current unit number
                $booking->setUnitNumber(null);
                Genesis_Service_Transaction::updateUnitNumber($booking, $this->getLoggedUser());

            }
            if ($newFacilityId) {
                $account = Genesis_Service_Account::loadById($billableInstance->getAccountId());
                if (!in_array($newFacilityId, $account->getFacilityIds())) {
                    throw new Exception("Cannot move to non-sister facility: " . $newFacilityId);
                }

                $billableInstance->setFacilityId($newFacilityId);
                $booking->setFacilityId($newFacilityId);
                $facility = Genesis_Service_Facility::loadById($newFacilityId);
                $ret['companyCode'] = $facility->getCompanyCode();
                $ret['title'] = $facility->getTitle();
                $ret['facilityId'] = $facility->getId();

                //update BI's last if passed all other saves
                if (!Genesis_Service_BillableInstance::save($billableInstance, $this->getLoggedUser())) {
                    throw new Exception("Couldn't save billable instance: " . $confirmationCode);
                }

                Genesis_Service_Transaction::updateFacilityId($booking);

            }

            echo json_encode($ret);
        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }

    public function updateresidualcontactnameAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $ret = array();
        try {
            $booking = Genesis_Service_Transaction::loadById($this->getParam('confirmation_code'));

            $firstName = $this->getParam('first_name');
            $lastName = $this->getParam('last_name');
            if ($firstName == "" || $lastName == "") {
                throw new Exception("You must supply both a first and last name");
            }
            $booking->setFirstName($firstName);
            $booking->setLastName($lastName);
            $ret['firstName'] = $firstName;
            $ret['lastName'] = $lastName;
            $booking = Genesis_Service_Transaction::updateName($booking, $this->getLoggedUser(), true); //this adds to bi's if needed

            echo json_encode($ret);
        } catch (Exception $e) {
                echo 'Error: ' . $e->getMessage();
        }
    }

    public function updateresidualmoveindateAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $overrideMoveUntoOpenStatementRestriction = false;

        $ret = array();
        try {
            $booking = Genesis_Service_Transaction::loadById($this->getParam('confirmation_code'));

            $statementId = $booking->getStatementId();
            if (!AccountMgmt_Service_Statement::isStatementBatchOpen($statementId)) {
                throw new Exception('StatementClosed');
            }

            if ( ! $this->getParam('into_date')) {
                throw new Exception("You must select a move-in date.");
            }

            if (! $booking) {
                throw new Exception("Could not find booking " . $this->getParam('confirmation_code'));
            }

            // If the logged in user is a myfoot god, we need to set $overrideMoveUntoOpenStatementRestriction to true so
            // the correct conditions are met in Genesis_Service_Transaction::updateMoveInDate to correctly mark the MID.
            // This is needed because within Genesis_Service_Transaction::updateMoveInDate, when $loggedUser->getUserAccess()->getManageableFacilityIds()
            // is called it uses the incorrect account id. It uses the account id of the logged in god user instead of the account id of the account the god user is viewing.
            // Because of this, if you didn't pass in $overrideMoveUntoOpenStatementRestriction in as true, the facility Id
            // wont be in the list of $loggedUser->getUserAccess()->getManageableFacilityIds() and booking will be left as pending.
            // See https://sparefoot.atlassian.net/browse/EPO-389.
            if ($this->getLoggedUser()->isMyFootGod()) {
                $overrideMoveUntoOpenStatementRestriction = true;
            }

            $booking->setMoveIn(date("Y-m-d", strtotime(str_replace("-", "/", $this->getParam('into_date')))));
            Genesis_Service_Transaction::updateMoveInDate($booking, $this->getLoggedUser(), true, false, $overrideMoveUntoOpenStatementRestriction); //this adds to bi's if needed

            echo json_encode($ret);
        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }

    }

    public function updateresidualrentAction()
    {
        try {
            $this->_helper->layout()->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);
            $overrideMoveUntoOpenStatementRestriction = false;

            $confirmationCode = $this->getParam('confirmation_code');
            $statementId = $this->getParam('id');
            $residualPercent = $this->getParam('residual_percent');
            $isTenant = $this->getParam('is_tenant');

            if (!AccountMgmt_Service_Statement::isStatementBatchOpen($statementId)) {
                throw new Exception('StatementClosed');
            }

            $booking = Genesis_Service_Transaction::loadById($this->getParam('confirmation_code'));
            if (! $booking) {
                throw new Exception("Could not load transaction for confirmation code: ".$confirmationCode);
            }

            // If the logged in user is a myfoot god, we need to set $overrideMoveUntoOpenStatementRestriction to true so
            // the correct conditions are met in Genesis_Service_Transaction::updateMoveInDate to correctly mark the MID.
            // This is needed because within Genesis_Service_Transaction::updateMoveInDate, when $loggedUser->getUserAccess()->getManageableFacilityIds()
            // is called it uses the incorrect account id. It uses the account id of the logged in god user instead of the account id of the account the god user is viewing.
            // Because of this, if you didn't pass in $overrideMoveUntoOpenStatementRestriction in as true, the facility Id
            // wont be in the list of $loggedUser->getUserAccess()->getManageableFacilityIds() and booking will be left as pending.
            // See https://sparefoot.atlassian.net/browse/EPO-389.
            if ($this->getLoggedUser()->isMyFootGod()) {
                $overrideMoveUntoOpenStatementRestriction = true;
            }

            //early late will come and will have early_late = 1 parameter, need to add to bi's
            if ($this->getParam('early_late')) {
                if ( ! $this->getParam('into_date')) {
                    throw new Exception("You must select a move-in date.");
                }
                $booking->setMoveIn(date("Y-m-d", strtotime(str_replace("-", "/", $this->getParam('into_date')))));
                Genesis_Service_Transaction::updateMoveInDate($booking, $this->getLoggedUser(), true, false, $overrideMoveUntoOpenStatementRestriction); //this adds to bi's if needed
            }


            // load billable instance and booking
            $billableInstance = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId(
                $confirmationCode,
                $statementId
            );
            if (! $billableInstance) {
                throw new Exception("Could not load billable instance for confirmation code: " . $confirmationCode . " statement Id: " . $statementId);
            }

            $isContinuingTenant = Genesis_Service_BillableInstance::isContinuingTenant($billableInstance->getConfirmationCode());
            //Those that are not continuing tenants need their booking state updated
            if (! $isContinuingTenant) {
                // reset the booking state to CONFIRMED/PENDING and let the rest of the logic flow as normal
                $booking->setBookingState(Genesis_Entity_Transaction::BOOKING_STATE_PENDING);
                $bookingUpdates[] = "updateState";
            }

            $thisMonthCollected = str_replace('$', '', $this->getParam('rent_other'));
            $amountIsValid = false;
            if (is_numeric($thisMonthCollected)) { $amountIsValid = true; }
            if ($thisMonthCollected === '0') { $amountIsValid = true; }
            if ($thisMonthCollected === '0.00') { $amountIsValid = true; }
            if ($thisMonthCollected < 0 ) {
                throw new Exception("Enter an amount for the rent collected that is greater than 0.");

            }
            if (! $amountIsValid) {
                throw new Exception("Enter a valid amount for the rent collected.");
            }
            //carry over to next month if the flag was set and amount collected is greater than unit price
            if ( ! empty($isTenant) && $thisMonthCollected > $billableInstance->getUnitPrice()) {
                $booking->setDataNextMonthAmountCollected($thisMonthCollected);
            }

            $billableInstance->setAmountCollected($thisMonthCollected);
            $billableInstance->setSparefootCharge($thisMonthCollected * $residualPercent);
            $billableInstance->setReason(NULL);
            Genesis_Service_Transaction::updateBookingData($booking);
            $booking->appendCallCenterNotes('Residual booking edited in MyFoot.');
            $bookingUpdates[] = "updateCallCenterNotes";

            //do the rest of the updates
            foreach ($bookingUpdates as $updateFunction) {
                Genesis_Service_Transaction::$updateFunction($booking);
            }

            //update BI's last if passed all other saves
            if (! Genesis_Service_BillableInstance::save($billableInstance, $this->getLoggedUser())) {
                throw new Exception("Couldn't save billable instance: ".$confirmationCode);
            }
        } catch(Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }

    public function denyresidualmoveinAction() {

        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        try {
            $confirmationCode = $this->getParam('confirmation_code');
            $statementId = $this->getParam('id');

            if (!AccountMgmt_Service_Statement::isStatementBatchOpen($statementId)) {
                throw new Exception('StatementClosed');
            }

            $booking = Genesis_Service_Transaction::loadById($this->getParam('confirmation_code'));
            if (! $booking) {
                throw new Exception("Could not load transaction for confirmation code: ".$confirmationCode);
            }
            // load billable instance and booking
            $billableInstance = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId(
                $confirmationCode,
                $statementId
            );

            $billableInstance->setAmountCollected(0);
            $billableInstance->setSparefootCharge(0);
            $booking->appendCallCenterNotes('Residual booking edited in MyFoot. $0');
            $bookingUpdates[] = "updateCallCenterNotes";
            if ($booking->getAutoState() === Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                && $booking->getBookingState() === Genesis_Entity_Transaction::BOOKING_STATE_PENDING) {
                if (Genesis_Service_Feature::isActive(Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS, array('account_id' => $billableInstance->getAccountId()))) {
                    $booking->setReviewStatus(Genesis_Entity_Transaction::STATUS_UNDER_REVIEW);
                    $bookingUpdates[] = "updateReviewStatus";
                }
            }

            $disputeReason = $this->getParam('rent-zero-reason');
            if(empty($disputeReason)) {
                //If there is no dispute Reason, assume that it is cancelled. Needs your review, late move ins, and auto matched do not take dispute reasons
                $booking->setDisputeReason(Genesis_Entity_BillableInstance::REASON_CANCELED);
                $bookingUpdates[] = "updateDisputeReason";
                $booking->setBookingState(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED);
                $bookingUpdates[] = "updateState";
            } else {
                //Do specific actions based on the dispute reason. This is used for the Tenants Section
                switch($disputeReason) {
                    case Genesis_Entity_BillableInstance::REASON_MOVED_OUT:
                        // assume first of last month as move-out date
                        $booking->setMoveOut(date("Y-m-d", strtotime('last day of -1 months')));
                        $bookingUpdates[] = "updateMoveOut";
                        break;
                    case Genesis_Entity_BillableInstance::REASON_DELIQUENCY:
                        // TODO: handle delinquent-specifics somewhere
                        // NOTE FROM ALAN: This was here prior, so adding it in. Not sure where delinquency logic lives atm
                        $delinquentSpecifics = $this->getParam('delinquent-specifics');
                        break;
                    default:
                        //other cases don't require any action
                        break;
                }
            }

            //do the rest of the updates
            foreach ($bookingUpdates as $updateFunction) {
                Genesis_Service_Transaction::$updateFunction($booking);
            }

            //Set a reason for dispute if there is one
            $billableInstance->setReason($this->getParam('rent-zero-reason'));

            //update BI's last if passed all other saves
            if (! Genesis_Service_BillableInstance::save($billableInstance, $this->getLoggedUser())) {
                throw new Exception("Couldn't save billable instance: ".$confirmationCode);
            }
        } catch(Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }


    //Is this used anywhere?  Looks janky -EH 10/11/2012
    public function changestateAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if (!$this->getParam('statement_ids')) {
            $this->redirect('/billing');
        }

        $codes = explode(',', $this->getParam('statement_ids'));
        $transItr = Genesis_Service_Transaction::load(Genesis_Db_Restriction::in('confirmationCode', $codes));
        /**
         * @var $trans Genesis_Entity_Transaction
         */
        foreach ($transItr as $trans) {
            $trans->setBookingState($this->getParam('state'));
            Genesis_Service_Transaction::updateState($trans, $this->getLoggedUser());
            if ($this->getParam('state') != Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED) {
                $trans->setDisputeReason('');
                Genesis_Service_Transaction::updateDisputeReason($trans, $this->getLoggedUser());
            }
        }
    }

    //CPA statement only
    public function changefacilityAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            if (!$this->getParam('id')) {
                $this->redirect('/billing');
            }

            if (!$this->getParam('facility_id')) {
                throw new Exception('Please select a facility to move this booking to.');
            }

            $code = $this->getParam('id');
            /**
             * @var $trans Genesis_Entity_Transaction
             */
            $trans = Genesis_Service_Transaction::load(Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();

            $trans->setFacilityId($this->getParam('facility_id'));

            //note: statement to account relationship is 1-1 and since users can only
            //move bookings to facilities within their account we don't have to check the statement id
            Genesis_Service_Transaction::updateFacilityId($trans, $this->getLoggedUser());

            //update billable instance reason & facility ID
            $bi = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());
            $bi->setReason(Genesis_Entity_BillableInstance::REASON_FACILITY_CHANGE);
            $bi->setFacilityId($this->getParam('facility_id'));
            Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());
        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }

    //CPA statement only
    public function changecustomernameAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            if (!$this->getParam('id')) {
                $this->redirect('/billing');
            }

            $code = $this->getParam('id');
            /**
             * @var $trans Genesis_Entity_Transaction
             */
            $trans = Genesis_Service_Transaction::load(Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();
            $statementId = $trans->getStatementId();
            if (!AccountMgmt_Service_Statement::isStatementBatchOpen($statementId)) {
                throw new Exception('StatementClosed');
            }

            if (!$this->getParam('first_name')) {
                throw new Exception('Please enter a first name.');
            }
            if (!$this->getParam('last_name')) {
                throw new Exception('Please enter a last name.');
            }

            $trans->setFirstName($this->getParam('first_name'));
            $trans->setLastName($this->getParam('last_name'));

            Genesis_Service_Transaction::updateName($trans, $this->getLoggedUser());

            //update billable instance reason
            $bi = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());
            $bi->setReason(Genesis_Entity_BillableInstance::REASON_NAME_CHANGE);
            Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());
        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }

    //CPA statement only
    public function submitdisputeAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if (!$this->getParam('id')) {
            $this->redirect('/billing');
        }

        $code = $this->getParam('id');
        /**
         * @var $trans Genesis_Entity_Transaction
         */
        $trans = Genesis_Service_Transaction::load(Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();

        $trans->setBookingState(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED);
        $trans->setDisputeReason($this->getParam('reason'));
        Genesis_Service_Transaction::updateDisputeReason($trans, $this->getLoggedUser());
        Genesis_Service_Transaction::updateState($trans, $this->getLoggedUser());
        Genesis_Dao_Transaction::unCheck($trans->getConfirmationCode());

        //update billable instance reason
        $bi = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());
        $bi->setReason(Genesis_Entity_BillableInstance::REASON_CANCELED);
        $bi->setSparefootCharge(0);
        Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());
    }

    //CPA statement only
    public function submitautoconfirmeddisputeAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if ( ! $this->getParam('id')) {
            $this->redirect('/billing');
        }

        $trans = Genesis_Service_Transaction::loadById($this->getParam('id'));

        try {
            if ($this->getParam('dispute')) {
                if (!$trans->getReviewStatus()) {
                    $trans->setReviewStatus(Genesis_Entity_Transaction::STATUS_UNDER_REVIEW);
                } elseif ($trans->getReviewStatus() == Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) {
                    throw new Exception('This booking is currently under review.');
                } elseif ($trans->getReviewStatus() == Genesis_Entity_Transaction::STATUS_REVIEWED) {
                    throw new Exception('This booking has already been reviewed.');
                }
            } else {
                $trans->setReviewStatus(null);
            }

            $bi = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());

            if ($this->getParam('dispute')) {
                $disputeReason = trim($this->getParam('reason'));
                if (!$disputeReason) {
                    throw new Exception('You must enter a dispute reason.');
                }

                $trans->setDisputeReason($disputeReason);
                $trans->setBookingState(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED);
                $bi->setReason(Genesis_Entity_BillableInstance::REASON_CANCELED);
                $bi->setSparefootCharge(0);
            } else {
                $trans->setBookingState(Genesis_Entity_Transaction::BOOKING_STATE_PENDING);
                $bi->setReason(null);
                $bi->setSparefootCharge($trans->getBidAmount());
            }

            Genesis_Service_Transaction::updateReviewStatus($trans, $this->getLoggedUser());
            Genesis_Service_Transaction::updateDisputeReason($trans, $this->getLoggedUser());
            Genesis_Service_Transaction::updateState($trans, $this->getLoggedUser());
            Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());
        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }

    //CPA statement only
    public function removedisputeAction()
    {
        try {
            $this->_helper->layout()->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->getParam('id')) {
                $this->redirect('/billing');
            }

            $code = $this->getParam('id');

            $trans = Genesis_Service_Transaction::load(Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();

            //update billable instance reason
            $bi = Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId(
                $trans->getConfirmationCode(),
                $trans->getStatementId()
            );

            if (is_bool($bi) && !$bi) {
                throw new \Exception('Billable instance could not be loaded');
            }

            $trans->setBookingState(Genesis_Entity_Transaction::BOOKING_STATE_PENDING);
            $trans->setDisputeReason('');
            Genesis_Service_Transaction::updateDisputeReason($trans, $this->getLoggedUser());
            Genesis_Service_Transaction::updateState($trans, $this->getLoggedUser());

            $bi->setReason(null);
            $bi->setSparefootCharge($trans->getBidAmount());
            Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());

            if($this->getParam('new_date')) {
                $this->submitmoveinchangeAction();
            }

        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
    }

    public function confirmconsumercontactAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {

            if (!$this->getParam('id')) {
                $this->redirect('/billing');
            }

            $consumerContactId = $this->getParam('id');
            $consumerContact = Genesis_Service_ConsumerContact::load(Genesis_Db_Restriction::equal('id', $consumerContactId))->uniqueResult();
            if (!$consumerContact) {
                throw new Exception('Could not load consumer contact by id '.$consumerContactId);
            }

            // if a booking has already been mapped to this conf code, then return it
            if ($consumerContact->getConfirmationCode()) {
                echo $consumerContact->getConfirmationCode();

                return;
            }

            $statementId = $this->getParam('statement_id');
            $statement = Genesis_Service_Statement::loadById($statementId);
            if ( ! $statement
                    //TODO: add check to make sure user has access to this statement
                    //|| $statement->getAccountId() != $user->getAccountId()
               ) {
                throw new Exception('Unable to load statement by id '.$statementId);
            }

            // create booking
            $booking = Genesis_Service_Transaction::createByConsumerContact($consumerContact);

            // create user (this also saves the user in the db)
            $identifier = 'cc-'.$consumerContact->getId().'@sparefoot.conscontact';
            $user = Genesis_Service_Transaction::createUserForBookingCreatedFromConsumerContact(
                        $booking, $identifier,
                        $consumerContact->getEmail(), $consumerContact->getPhone()
                    );
            $user = Genesis_Service_User::save($user);
            $booking->setUserId($user->getId());

            // set move-in date to the first day of the statement period
            $booking->setMoveIn($statement->getStatementBatch()->getStartDate());

            // set unit as cheapest unit at the facility
            if (!$booking->getUnitId()) {
                $unit = Genesis_Service_StorageSpace::loadCheapestByFacilityId($consumerContact->getListingAvailId());
                if (!$unit) {
                    throw new Exception('Unable to load a unit for consumer contact '.$consumerContact->getListingAvailId());
                }
                $booking->setUnitId($unit->getId());
            }

            $booking->appendCallCenterNotes("Manually created offline booking from consumer contact ({$consumerContact->getId()}) via statement UI");

            // save booking to db
            $booking = Genesis_Service_Transaction::create($booking);
            if (!$booking) {
                throw new Exception('Could not create booking record');
            }

            // map this booking to the consumer contact so it won't be considered in the future
            $consumerContact->setConfirmationCode($booking->getConfirmationCode());
            Genesis_Service_ConsumerContact::save($consumerContact);

            // update move-in date to handle moving unto correct batch
            Genesis_Service_Transaction::updateMoveInDate($booking,  $this->getLoggedUser(),
                                            $overrideQuoteExpirationRestriction = false,
                                            $overrideMaximumBookingDelayRestriction = false,
                                            $overrideMoveUntoOpenStatementRestriction = true);

            echo $booking->getConfirmationCode();

        } catch (Exception $e) {
            echo 'Error: '.$e->getMessage();
        }
    }

    /**
     * CPA statement only
     */
    public function submitmoveinchangeAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $overrideMoveUntoOpenStatementRestriction = false;

        if (! $this->getParam('id')) {
            $this->redirect('/billing');
        }

        $code = $this->getParam('id');
        $trans = Genesis_Service_Transaction::loadById($code);

        // If the logged in user is a myfoot god, we need to set $overrideMoveUntoOpenStatementRestriction to true so
        // the correct conditions are met in Genesis_Service_Transaction::updateMoveInDate to correctly mark the MID.
        // This is needed because within Genesis_Service_Transaction::updateMoveInDate, when $loggedUser->getUserAccess()->getManageableFacilityIds()
        // is called it uses the incorrect account id. It uses the account id of the logged in god user instead of the account id of the account the god user is viewing.
        // Because of this, if you didn't pass in $overrideMoveUntoOpenStatementRestriction in as true, the facility Id
        // wont be in the list of $loggedUser->getUserAccess()->getManageableFacilityIds() and booking will be left as pending.
        // See https://sparefoot.atlassian.net/browse/EPO-389.
        if ($this->getLoggedUser()->isMyFootGod()) {
            $overrideMoveUntoOpenStatementRestriction = true;
        }

        try {
            $trans->setMoveIn(date('Y-m-d',strtotime($this->getParam('new_date'))));
            Genesis_Service_Transaction::updateMoveInDate($trans, $this->getLoggedUser(), true, false, $overrideMoveUntoOpenStatementRestriction);
            //NOTE: billable instance reset is done in updateMoveInDate() service

        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
            return;
        }
    }

    protected function getSideBarContent()
    {
        $view = new Zend_View();
        $view->facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Order::ASC('title'));
        $view->loggedUser = $this->getLoggedUser();
        $view->selectedAction = Zend_Controller_Front::getInstance()->getRequest()->getActionName();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/facility/');
        if ($view->selectedAction != 'viewstatement') {
            return $view->render('left-sidebar-content.phtml');
        }
    }

    protected function getTab()
    {
        return self::TAB_BILLING;
    }

    public function mirAction()
    {
        //these are used to build the back link
        $this->view->statementId = $this->getParam('sid');
        $this->view->facilityId = $this->getParam('fid');
    }

    //returns an array of possible early move in.  1st 15 days of current month
    private function _getFacilityEarlyMoveIns($facility, $statement)
    {
        $earlyMoveIns = array();

        $dtFirstDay = date ( 'Y-m-d', strtotime ( '+1 day' . $statement->getStatementBatch()->getEndDate() ) );//date('Y-m-d', mktime(0, 0, 0, date("m") , 1, date("Y")));
        $dtLastDay = date ( 'Y-m-d', strtotime ( '+15 day' . $statement->getStatementBatch()->getEndDate() ) );//date('Y-m-d', mktime(0, 0, 0, date("m") , 15, date("Y")));

        $includeStates = array(Genesis_Entity_Transaction::BOOKING_STATE_PENDING, Genesis_Entity_Transaction::BOOKING_STATE_CANCELLED);
        $earlyMoveIns = Genesis_Service_Transaction::loadByFacilityMoveInDate($facility->getId(), $dtFirstDay, $dtLastDay, $includeStates, true);

        return $earlyMoveIns;
    }

    //returns an array of possible late move in.  last 10 days of previous month
    private function _getFacilityLateMoveIns($facility, $statement)
    {
        $lateMoveIns = array();

        $dtFirstDay = date ( 'Y-m-d', strtotime ( '-10 day' . $statement->getStatementBatch()->getStartDate() ) ); //date('Y-m-d', mktime(0, 0, 0, date("m") , -10, date("Y")));
        $dtLastDay = date ( 'Y-m-d', strtotime ( '-1 day' . $statement->getStatementBatch()->getStartDate() ) );//date('Y-m-d', mktime(0, 0, 0, date("m") , 0, date("Y")));

        $includeStates = array(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED, Genesis_Entity_Transaction::BOOKING_STATE_CANCELLED);
        $lateMoveIns = Genesis_Service_Transaction::loadByFacilityMoveInDate($facility->getId(), $dtFirstDay, $dtLastDay, $includeStates, true);

        return $lateMoveIns;
    }

    //return positive is $a is ahead of $b
    public function cmpLastName(Genesis_Entity_Transaction $a, Genesis_Entity_Transaction $b)
    {
        return strcmp(strtolower($a->getLastName()), strtolower($b->getLastName()));
    }

    //return positive is $a is ahead of $b
    public function cmpMoveInDate(Genesis_Entity_Transaction $a, Genesis_Entity_Transaction $b)
    {
        $d1 = strtotime($a->getMoveIn());
        $d2 = strtotime($b->getMoveIn());

        if ($d1 > $d2) {
            return 1;
        } else {
            return -1;
        }
    }
}
