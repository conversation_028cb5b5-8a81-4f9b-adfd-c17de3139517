<?php
use AccountMgmt_Models_ApiException as ApiException;
class ApiSpecialController extends AccountMgmt_Controller_ApiBaseController
{
    public function init() {
        parent::init();
    }

    public function indexAction()
    {
        $defaultSpecials = AccountMgmt_Service_Unit::getDefaultSpecials();
        $items = [];
        /**
         * @var $special Genesis_Entity_Special
         */
        foreach ($defaultSpecials as $special) {
            $item = [
                'id' => $special->getId(),
                'string' => $special->getString(),
                'special_type' => $special->getType(),
                'percent_off' => $special->getPercentOff(),
                'dollar_off' => $special->getDollarOff(),
                'dollar_override' => $special->getDollarOverride()
            ];
            if ($freeItem = $special->getFreeItem()) {
                $item['free_item_id'] = $freeItem->getId();
                $item['free_item_name'] = $freeItem->getName();
            }

            if ($t = $special->getRequiresPrepaidMonths()) {
                $item['requires_prepaid_months'] = $t;
            }
            
            if ($t = $special->getRequiresMinimumLeaseMonths()) {
                $item['requires_minimum_lease_months'] = $t;
            }

            if ($t = $special->getRequiresMinimumLeaseMonths()) {
                $item['months'] = $t;
            }

            $items[] = $item;
        }
        $result = [
            'count' => count($items),
            'data' => $items,
            'meta' => []
        ];
        $this->_helper->json($result);
    }
}
