<?php
use AccountMgmt_Models_ApiException as ApiException;
use Sparefoot\ServiceBundle\Stats\StatsInterface;
use Sparefoot\ServiceBundle\Stats\StatsD;
use DataDog\DogStatsd;

class ApiFacilityController extends AccountMgmt_Controller_ApiBaseController
{
    /** @var StatsInterface $statsClient */
    private $statsClient;

    public function init() {
        parent::init();
        $this->statsClient = new StatsD(new DogStatsd());
    }

    public function indexAction()
    {
        // path: /api/facilities/{ facility_id }

        $facilityId = $this->_getParam('facility_id');
        $user = $this->getLoggedUser();
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) { 
            throw new ApiException(ApiException::UNAUTHORIZED, 'Unauthorized access to facility.');
        }

        if ($this->_request->isGet() || $this->_request->isPut()) {
            $checkWriteAccess = $this->_request->isGet() ? false : true;
            $facility = $this->validateAndGetFacility($facilityId, $checkWriteAccess);
        }

        $json_body = $this->getRequest()->getRawBody();

        // GET - handle get requests
        if ($this->_request->isGet()) {
            $this->_helper->json(['data' => AccountMgmt_Service_Facility::toArray($facility)]);
        }
        if ($this->_request->isPut()) {
            $activePreValue = $facility->getActive();

            $user = $this->getLoggedUser();
            AccountMgmt_Service_Facility::updateFromJson($facility, $json_body, $user);
            $this->_helper->json(['data' => AccountMgmt_Service_Facility::toArray($facility)]);

            // Reload facility now that it has been updated in db and log this activity if facility active has changed
            $facility = $this->validateAndGetFacility($facilityId);
            $activePostValue = $facility->getActive();
            if ($activePreValue != $activePostValue) {
                $context['account_id'] = $facility->getAccountId();
                $context['hiding'] = $activePostValue;
                $this->statsClient->increment('myfoot.features.toggle_facility', $context);
            }
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function getallfacilitiesforaccountAction()
    {
        // path: /api/facilities/account_id/{account_id}
        $accountId = $this->_getParam('account_id');
        if (! $accountId) {
            throw new ApiException(ApiException::BAD_REQUEST, 'account_id is required');
        }
        $account = Genesis_Service_Account::loadById($accountId);
        if (! $account) {
            throw new ApiException(ApiException::BAD_REQUEST, 'no such account');
        }
        try {
            AccountMgmt_Service_User::validateAccountAccess($account);
        } catch (Exception $e) {
            // throw new ApiException(ApiException::FORBIDDEN, 'not authorized to load facilities from account ' . $accountId);
        }

        $limitedFields = $this->_getParam('fields');
        if ($limitedFields) {
            $whiteList = [
                'title',
                'company_code',
                'active'
            ];

            // Only accept alpha numeric, _, and comma.
            $limitedFields = preg_replace('/[^\w,]/', '', $limitedFields);
            $unfilteredFieldList = explode(",", $limitedFields);
            $limitedFieldsList = array_values(array_intersect($unfilteredFieldList, $whiteList));
        }

        $facilities = AccountMgmt_Service_Account::getAllFacilities($accountId);
        $facilitiesArray = [];
        foreach ($facilities as $facility) {
            if (AccountMgmt_Service_User::canAccessFacility($facility)) {
                if ($limitedFieldsList) {
                    $facilitiesArray[] = AccountMgmt_Service_Facility::toArrayWithFields($facility, $limitedFieldsList);
                } else {
                    $facilitiesArray[] = AccountMgmt_Service_Facility::toArray($facility);
                }
            }
        }
        $response = [
            'meta' => [
                'count' => count($facilitiesArray)
            ],
            'data' => $facilitiesArray,
            'account_id' => $accountId
        ];
        $this->_helper->json($response);
    }

    /*
     * URL : /api/facilities/:facility_id/units
     * GET, POST
     *
     */
    public function unitsAction()
    {
        $json_body = $this->getRequest()->getRawBody();
        $facilityId = $this->getParam('facility_id');
        $checkWriteAccess = $this->_request->isGet() ? false : true;
        $facility = $this->validateAndGetFacility($facilityId, $checkWriteAccess);

        // path: /api/facilities/{ facility_id }/units
        // GET - handle get requests
        if ($this->_request->isGet()) {
            $this->_helper->json(['data' => AccountMgmt_Service_Facility::getAllUnitsArray($facility)]);
        }

        if ($this->_request->isPost()) {
            $decoded_json = json_decode($json_body);
            if (!$decoded_json) {
                throw new ApiException(ApiException::BAD_REQUEST, 'invalid json body');
            }
            //we always set this from the url at this route
            $decoded_json->facility_id = $facility->getId();

            try {
                AccountMgmt_Service_Unit::validateInsert($decoded_json);
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            $unit = AccountMgmt_Service_Unit::getNewUnitFromType($decoded_json->unit_type);
            $unit->setFacilityId($facility->getId());
            $unitArray = AccountMgmt_Service_Unit::upsertFromJson($unit, json_encode($decoded_json));
            $this->_helper->json(['data' =>$unitArray]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function photosAction()
    {
        // path: /api/facilities/{ facility_id }/photos/{ :photo_id }
        $facilityId = $this->getParam('facility_id'); //must be set
        $photoId = $this->getParam('photo_id'); //maybe set
        $json_body = $this->getRequest()->getRawBody(); //might be set

        $checkWriteAccess = $this->_request->isGet() ? false : true;
        $facility = $this->validateAndGetFacility($facilityId, $checkWriteAccess);


        $user = $this->getLoggedUser();
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) { 
            throw new ApiException(ApiException::UNAUTHORIZED, 'Unauthorized access to facility.');
        }

        if ($this->_request->isGet()) {
            if ($photoId) {
                try { //valid photo id is required
                    $photo = AccountMgmt_Service_Facility::validatePhotoId($photoId, $facility);
                } catch (Exception $e) {
                    throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
                }
                $this->_helper->json(['data' => AccountMgmt_Service_Facility::serializeFacilityPhoto($photo)]);
            }
            $this->_helper->json(['data' => AccountMgmt_Service_Facility::serializeFacilityPhotos($facility)]);
        }

        if ($this->_request->isPost()) {
            $savedPhotos = AccountMgmt_Service_Facility::savePhotosFromUpload($facility);
            $return = [];

            foreach ($savedPhotos as $photo) {
                $return[] = AccountMgmt_Service_Facility::serializeFacilityPhoto($photo);
            }
            $this->_helper->json(['data' => $return]);
        }

        if ($this->_request->isPut()) {
            try { //valid photo id is required
                $photo = AccountMgmt_Service_Facility::validatePhotoId($photoId, $facility);
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            //This route only handles default:true
            $decoded_json = json_decode($json_body);
            if ($decoded_json->default) {
                try {
                    Genesis_Service_FacilityImage::setDefault($photo->getFacilityId(), $photo->getPictureNum());
                    $photo = Genesis_Service_FacilityImage::loadById($photoId);
                } catch (Exception $e) {
                    throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
                }
            }
            $this->_helper->json(['data' => AccountMgmt_Service_Facility::serializeFacilityPhoto($photo)]);
        }

        if ($this->_request->isDelete()) {
            try { //valid photo id is required
                $photo = AccountMgmt_Service_Facility::validatePhotoId($photoId, $facility);
            } catch (Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            try {
                Genesis_Service_FacilityImage::delete($facilityId, $photo->getPictureNum());
            } catch (Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }
            $this->_helper->json(['data' => AccountMgmt_Service_Facility::serializeFacilityPhotos($facility)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function bookingsAction() {
        // path: /api/facilities/:fid:/bookings
        $facilityId = $this->getParam('facility_id');
        $facility = $this->validateAndGetFacility($facilityId);

        if ($this->_request->isGet()) {
           $startDate = $this->getParam('start_date');
           $endDate = $this->getParam('end_date');
           self::validateStartEndDate($startDate,$endDate);

           $bookings = AccountMgmt_Service_Facility::serializeFacilityBookings($facility,$startDate,$endDate,$this->getAllParams());
           $meta = [
            'count' => sizeof($bookings)
           ];
           $this->_helper->json(['data' =>$bookings,'meta'=>$meta]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function inquiriesAction() {
        //path: /api/facilities/:fid/inquiries
        $facilityId = $this->getParam('facility_id');
        $facility = $this->validateAndGetFacility($facilityId);

        if ($this->_request->isGet()) {
            $startDate = $this->getParam('start_date');
            $endDate = $this->getParam('end_date');
            self::validateStartEndDate($startDate,$endDate);


            $inquiries = AccountMgmt_Service_Facility::serializeFacilityInquiries($facility,$startDate,$endDate);
            $meta = [
                'count' => sizeof($inquiries)
            ];
            $this->_helper->json(['data'=>$inquiries,'meta'=>$meta]);
        }
        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function inquiryAction() {
        //path: /api/facilities/:fid/inquiries/:inquiry_id
        $facilityId = $this->getParam('facility_id');
        $inquiryId = $this->getParam('inquiry_id');
        $facility = $this->validateAndGetFacility($facilityId);
        $inquiry = AccountMgmt_Service_Inquiry::validateInquiryId($inquiryId);

        if($inquiry->getListingAvailId() != $facilityId) {
            // throw new ApiException(ApiException::FORBIDDEN, 'Inquiry does not belong to facility');
        }

        if ($this->_request->isGet()) {
            $this->_helper->json(['data'=>AccountMgmt_Service_Inquiry::toArray($inquiry)]);
        }
        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /*
     * URL : /api/facilities/:facility_id/reviews
     * GET
     *
     */
    public function reviewsAction() {
        $facility_id = $this->getParam('facility_id');
        $user = $this->getLoggedUser();

        // Check if the user has access to the facility
        if (!$user->isMyFootGod() && !in_array($facility_id, $user->getManageableFacilityIds())) { 
            throw new ApiException(ApiException::FORBIDDEN, 'Unauthorised access to reviews');
        }
        $facility = $this->validateAndGetFacility($facility_id);
        $reviews = $facility->getFacilityReviews();

        if ($this->_request->isGet()) {
            $this->_helper->json(['data'=>AccountMgmt_Service_Facility::serializeFacilityReviews($facility)]);
        }
    }

    public function singleReviewAction() {
        $facility_id = $this->getParam('facility_id');
        $review_id = $this->getParam('review_id');
        $facility = $this->validateAndGetFacility($facility_id);

        $user = $this->getLoggedUser();

        // Check if the user has access to the facility
        if (!$user->isMyFootGod() && !in_array($facility_id, $user->getManageableFacilityIds())) { 
            throw new ApiException(ApiException::FORBIDDEN, 'Unauthorised access to reviews');
        }
        $review = AccountMgmt_Service_Review::validateAndGetReview($review_id,$facility_id);
        if ($this->_request->isGet()) {
            $this->_helper->json(['data'=>AccountMgmt_Service_Review::toArray($review)]);
        }
    }
}
