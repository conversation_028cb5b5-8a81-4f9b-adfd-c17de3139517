<?php
/*
 * User Controller
 *
 * @copyright SpareFoot Inc
 * <AUTHOR>
 */

class UserController extends AccountMgmt_Controller_Restricted
{
    public function aboutAction()
    {
        $account = $this->getLoggedUser()->getAccount();
        $this->view->account = $account;
    }

    public function usersAction()
    {
        $user = $this->getLoggedUser();
        $role = $user->getMyfootRole();
        if (!in_array($role, [
                Genesis_Entity_UserAccess::ROLE_GOD,
                Genesis_Entity_UserAccess::ROLE_ADMIN])) {
            $this->redirect($this->view->url([], 'settings'));
            return;
        }

        $account = $user->getAccount();

        $this->view->user = $user;
        $this->view->account = $account;

        $currentPage = 1;
        $resultsPerPage = 50;

        if ($this->getParam('p')) {
            $currentPage = $this->getParam('p');
        }

        $this->view->facilities = Genesis_Service_Facility::loadByAccountId(
            $account->getId(),
            Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::equal('approved', 1),
                Genesis_Db_Restriction::equal('published', 1)
            )
        )->toArray();

        $usersArray = [];

        //calculate number of facilities accessed for each user
        foreach (Genesis_Service_UserAccess::load(Genesis_Db_Restriction::equal('accountId', $account->getId())) as $user) {
            /**
             * @var $user Genesis_Entity_UserAccess
             */
            $manageableFacIds = $user->getManageableFacilityIds(null, false);
            $numFacilities = count($this->view->facilities) === count($manageableFacIds) ? 'All' : count($manageableFacIds);

            $usersArray[$user->getId()]['id']                 = $user->getId();
            $usersArray[$user->getId()]['email']              = $user->getEmail();
            $usersArray[$user->getId()]['first_name']         = $user->getFirstName();
            $usersArray[$user->getId()]['last_name']          = $user->getLastName();
            $usersArray[$user->getId()]['phone']              = $user->getPhone();
            $usersArray[$user->getId()]['myfootRole']         = $user->getMyfootRole();
            $usersArray[$user->getId()]['getsEmails']         = $user->getGetsEmails();
            $usersArray[$user->getId()]['getsStatements']     = $user->getGetsStatements();
            $usersArray[$user->getId()]['getsInquiries']      = $user->getGetsInquiries();
            $usersArray[$user->getId()]['accountId']          = $user->getAccountId();
            $usersArray[$user->getId()]['numFacilities']      = $numFacilities;
            $usersArray[$user->getId()]['allFacs']            = $user->getAllFacilities();
            $usersArray[$user->getId()]['managableFacs']      = $manageableFacIds;
        }

        //paginate users list if needed
        $usersIterator = new Genesis_Util_PaginatedIterator($usersArray, $currentPage, $resultsPerPage);

        $this->view->totalPages = $usersIterator->getNumPages();
        $this->view->currentPage = $currentPage;
        $this->view->users = $usersIterator;

        $this->view->scripts = array('user/users');
        $this->view->title = 'Users - Settings';
    }

    //updated myfoot role to admin or downgrade to limited
    public function isadminAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        // Make sure the user attempting privilege escalation is an admin
        // This feature is used with a js call to toggle admin permissions in user management
        $user = $this->getLoggedUser();
        if($user && $user->getId()) {
            $role = $user->getMyfootRole();

            if ($role && !in_array($role, [ Genesis_Entity_UserAccess::ROLE_GOD, Genesis_Entity_UserAccess::ROLE_ADMIN ])) {
                echo "Error: You must be an administrator to change user roles. \n";
                return;
            }

            $userId = $this->getParam('uid');
            $isAdmin = $this->getParam('isAdmin');

            try {
                $ua = Genesis_Service_UserAccess::loadById($userId);
                
                if(!$ua->isMyFootGod() && $ua->getAccountId() != $user->getAccountId()) {
                    echo "Error: You are not allowed to perform this action \n";
                    return;
                }

                $ua->setMyfootAdminStatus($isAdmin);

                Genesis_Service_User::save($ua);
                $ua = Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());

                if ($ua->getAllFacilities()) {
                    print "All";
                } else {
                    print count($ua->getManagableFacilities()->toArray());
                }

            } catch (Exception $e) {
                echo "Error: ", $e->getMessage(), "\n";
            }
        }
    }

    public function geopageonlyAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $userId = $this->getParam('uid');
        $geopageOnly = $this->getParam('geopageOnly');

        try {
            $ua = Genesis_Service_UserAccess::loadById($userId);

            if ($geopageOnly) {
                $ua->setMyfootRole(Genesis_Entity_UserAccess::ROLE_LIMITED);
                $ua->setGetsStatements(0);
            } else {
                $ua->setMyfootRole(null);
            }

            $ua = Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());

            if ($ua->getAllFacilities()) {
                print "All";
            } else {
                print count($ua->getManagableFacilities()->toArray());
            }

        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }

    /**
     * toggle if user gets inquiry and reservation emails
     */
    public function getsemailsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            $ua = Genesis_Service_UserAccess::loadById($this->getParam('uid'));
            switch ($this->getParam('type')) {
                case 'reservations':
                    $ua->setGetsEmails($this->getParam('value'));
                    break;
                case 'inquiries':
                    $ua->setGetsInquiries($this->getParam('value'));
                    break;
                case 'statements':
                    if ($ua->isMyfootAdmin() && ! $this->getParam('value')) {
                        throw new Exception('Admins must get statements.');
                    }
                    $ua->setGetsStatements($this->getParam('value'));

                    //must have access to myfoot for this
                    if (! $ua->getMyfootRole()) {
                        $ua->setMyfootRole(Genesis_Entity_UserAccess::ROLE_LIMITED);
                    }
                    break;
                default:
                    throw new Exception('incorrect type parameter');
            }
            Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());
        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }

    //toggle if user gets statements
    public function accessmyfootAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $userId = $this->getParam('uid');

        $checkPassword = array_key_exists('pass', $this->_getAllParams());
        $accessMyfoot = $this->getParam('accessMyfoot');
        $pass = $this->getParam('pass', false);
        $passconfirm = $this->getParam('passconfirm', false);
        $notify = $this->getParam('notify');
        $passwordWasSet = false;

        try {
            $ua = Genesis_Service_UserAccess::loadById($userId);
            $user = $this->getLoggedUser();

            if ($ua === null) {
                throw new Exception('a valid uid (myfoot userId) is required');
            }
            //toggle myfoot admin access
            if ($ua->getAccountId() == $user->getAccountId() || $ua->isMyFootGod()) {
                if ($accessMyfoot) {
                    //if they don't have myfoot access, then allow them
                    if (! $ua->getMyfootRole()) {
                        $ua->setMyfootRole(Genesis_Entity_UserAccess::ROLE_LIMITED);
                    }
                } else {
                    //cannot take away myfoot access from admins
                    if ($ua->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN) {
                        throw new Exception('You cannot take away MySpareFoot access from Admins.');
                    }
    
                    $ua->setMyfootRole(null);
                    //also cannot do statements without access
                    $ua->setGetsStatements(0);
                }
            } else {
                throw new Exception('You are not allowed to perform this action');
            }
            //only check and set password if it's passed in
            if ($checkPassword) {

                if ($pass == '') {
                    throw new Exception('Please enter an initial password.');
                }

                if ($pass !== $passconfirm) {
                    throw new Exception('Passwords do not match.');
                }

                Genesis_Service_User::updatePassword($ua, $pass);
                $passwordWasSet = true;
            }

            //send notification email if requested
            if ($notify && $passwordWasSet) {

                if ($this->getLoggedUser()->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD && $ua->getAccount()->getFirstAdmin()) {
                    $creator = $ua->getAccount()->getFirstAdmin();
                } else {
                    $creator = $this->getLoggedUser();
                }

                if ($ua->getEmail()) {
                    Genesis_Service_UserAccess::sendNewUserEmail($ua, $creator, $pass);
                }
            }

            if ($ua->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_LIMITED && ! $ua->getPassword() && ! $passwordWasSet) {
                throw new Exception('needpass.'); //this is interpreted on the front end to open the password dialog
            }

            Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());
        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }

    //toggle myfoot access
    public function getsstatementsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $userId = $this->getParam('uid');
        $getsStatements = $this->getParam('getsStatements');

        try {
            $ua = Genesis_Service_UserAccess::loadById($userId);

            //make sure if they're admins they cannot take this off
            if (!$getsStatements && $ua->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN) {
                throw new Exception('Admins must get statements.');
            }

            $ua->setGetsStatements($getsStatements);

            //must have access to myfoot for this
            if (!$ua->getMyfootRole()) {
                $ua->setMyfootRole(Genesis_Entity_UserAccess::ROLE_LIMITED);
            }

            Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());
        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }

    //updates the facilities a user has access too
    public function updatefacsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $user = $this->getloggedUser();
        $userId = $this->getParam('uid');
        $facIds = $this->getParam('facIds');
        $all = $this->getAllFacilityParam($facIds);

        try {
            $ua = Genesis_Service_UserAccess::loadById($userId);

            //remove any facility restrictions
            $facRestricts = Genesis_Service_UserFacilityRestrictions::load(Genesis_Db_Restriction::equal('userId', $userId));
            foreach ($facRestricts as $facRes) {
                Genesis_Service_UserFacilityRestrictions::delete($facRes);
            }

            //get out of here if user is god
            if ($ua->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD) {
                throw new Exception('You cannot change facility access for god users.  They will be able to access all facilities.');
            }

            //get out of here if user is an admin
            if ($ua->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_ADMIN) {
                throw new Exception('You cannot change facility access for admins.  They will be able to access all facilities.');
            }

            //if admin or god we would have already got outta here, so make sure they're full if they can access all or limited if not
            //but if no myfoot access previously or was geopage then keep it that way
            if ($ua->getMyfootRole()) {
                if ($all) {
                    $ua->setMyfootRole(Genesis_Entity_UserAccess::ROLE_FULL);
                } else {
                    $ua->setMyfootRole(Genesis_Entity_UserAccess::ROLE_LIMITED);
                }
            }

            if ($all) {
                $ua->setAllFacilities(1);
            } else {
                $ua->setAllFacilities(0);
            }

            //if there are facility id's then assign restrictions
            if ($facIds) {
                $facilityIds = explode(",", $facIds);
    
                // Validate ownership of facility IDs
                if (!$user->isMyFootGod()) {
                     // Fetch logged user's facilities
                    $allowedFacilitys = $user->getManagableFacilities()->toArray();
                    $allowedFacilityIds = array_map(function ($facility) {
                        return $facility->getId();
                    }, $allowedFacilitys);

                    foreach ($facilityIds as $facilityId) {
                        if (!in_array($facilityId, $allowedFacilityIds)) {
                            throw new Exception("Unauthorized: Facility ID $facilityId does not belong to your account.");
                        }
                    }
                }
    
                // Add new restrictions for valid facilities
                foreach ($facilityIds as $facilityId) {
                    $facRes = new Genesis_Entity_UserFacilityRestrictions();
                    $facRes->setFacilityId($facilityId);
                    $facRes->setUserId($userId);
                    Genesis_Service_UserFacilityRestrictions::save($facRes);
                }
            }

            $ua = Genesis_Service_UserAccess::save($ua, $user);
        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }

    public function getAllFacilityParam($selectedFacilities) {
        // if all facilities have been selected, return true
        if ($this->allFacilitiesSelected($selectedFacilities)) {
            $this->setParam('all', 1);
        }
        return $this->getParam('all');
    }

    public function allFacilitiesSelected($selectedFacilities) {
        $user = $this->getLoggedUser();
        $account = $user->getAccount();
        $accountFacilities = Genesis_Service_Facility::loadByAccountId(
            $account->getId(),
            Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::equal('approved', 1),
                Genesis_Db_Restriction::equal('published', 1)
            )
        )->toArray();

        $selectedFacilitiesArray = explode(",", $selectedFacilities);
        $accountFacilityIds = array_reduce($accountFacilities, function ($carry, Genesis_Entity_Facility $facility) {
            $carry[] = $facility->getId();
            return $carry;
        }, []);

        $intersectedArray = array_intersect($accountFacilityIds, $selectedFacilitiesArray);
        return count($accountFacilityIds) == count($intersectedArray);
    }

    //get which facilities user has access to
    //used to reset facility selector
    public function getfacaccessAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $userId = $this->getParam('uid');

        try {
            $userAccess = Genesis_Service_UserAccess::loadById($userId);

            if ($userAccess->canAccessAllFacilities()) {
                print "All";

                return;
            } else {
                $ids = [];
                $facilities = $userAccess->getManagableFacilities()->toArray();
                foreach ($facilities as $fac) {
                    $ids[] = $fac->getId();
                }
                echo json_encode($ids);
            }

        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }

    //create new user
    public function createAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $email = $this->getParam('email');
        $fname = $this->getParam('fname');
        $lname = $this->getParam('lname');
        $notify = $this->getParam('notify');
        $pass = $this->getParam('pass');
        $passconfirm = $this->getParam('passconfirm');
        $getsEmails = $this->getParam('getsEmails');
        $getsStatements = $this->getParam('getsStatements');
        $geopageOnly = $this->getParam('geopageOnly');
        $accessMyfoot = $this->getParam('accessMyfoot');
        $isAdmin = $this->getParam('isAdmin');
        $facilityIds = explode(",", $this->getParam('facilityIds'));
        $allFacs = $this->getParam('allFacs');

        try {
            if ($accessMyfoot) {
                if (!(strlen($pass) > 0)) {
                    throw new Exception('Please enter an initial password.');
                }

                if ($pass != $passconfirm) {
                    throw new Exception('Passwords do not match.');
                }
            }

            if (!(strlen($fname) > 0)) {
                throw new Exception('Please enter a first name.');
            }

            if (!(strlen($lname) > 0)) {
                throw new Exception('Please enter a last name.');
            }

            //validate email address
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Please enter a valid email address.');
            }

            //check if this user already exists
            $user = Genesis_Service_User::load(Genesis_Db_Restriction::equal('email', $email))->uniqueResult();
            if (!$user) {
                $user = new Genesis_Entity_User();
                $user->setEmail($email);
                $user->setFirstName($fname);
                $user->setLastName($lname);
                $user = Genesis_Service_User::save($user);

                if ($accessMyfoot) {
                    Genesis_Service_User::updatePassword($user, $pass);
                }

            }

            $newUserAccess = Genesis_Service_UserAccess::loadById($user->getId());

            //create a new user if they're not yet in mysparefoot
            if (!$newUserAccess) {
                $newUserAccess = new Genesis_Entity_UserAccess();
                $newUserAccess->setUserId($user->getId());
                $newUserAccess->setGetsStatements($getsStatements);
                $newUserAccess->setGetsEmails($getsEmails);
                $newUserAccess->setAccountId($this->getLoggedUser()->getAccountId());

                if ($isAdmin) {
                    $newUserAccess->setMyfootRole(Genesis_Entity_UserAccess::ROLE_ADMIN);
                    $newUserAccess->setAllFacilities(1);
                } elseif ($allFacs && !$geopageOnly) {
                    $newUserAccess->setMyfootRole(Genesis_Entity_UserAccess::ROLE_FULL);
                    $newUserAccess->setAllFacilities(1);
                } elseif ($accessMyfoot) {
                    $newUserAccess->setMyfootRole(Genesis_Entity_UserAccess::ROLE_LIMITED);
                }

                $newUserAccess = Genesis_Service_UserAccess::save($newUserAccess, $this->getLoggedUser());

                //if this email is already a user but not an accout mgmt user then still update password and add them
                if ($accessMyfoot) {
                    Genesis_Service_User::updatePassword($user, $passconfirm);
                }
            } else {
                if ($newUserAccess->getAccountId() == $this->getLoggedUser()->getAccountId()) {
                    throw new Exception('User ' . $email . ' already exists as a user for your account.');
                } else {
                    throw new Exception('User ' . $email . ' already has a MySpareFoot signin for another account.  <NAME_EMAIL> for help.');
                }
            }

            if ($this->getParam('facilityIds')) {
                if (!$this->getLoggedUser()->isMyFootGod()) {
                    $validFacilities = $this->getLoggedUser()->getManagableFacilities()->toArray();
                    foreach ($facilityIds as $facilityId) {
                        if (!in_array($facilityId, $validFacilities)) {
                            throw new Exception("Unauthorized access: Facility ID {$facilityId} does not belong to your account.");
                        }
                    }
                }

                //no facility ids if allFacs=1
                foreach ($facilityIds as $facilityId) {
                    $facRes = new Genesis_Entity_UserFacilityRestrictions();
                    $facRes->setFacilityId($facilityId);
                    $facRes->setUserId($user->getId());
                    Genesis_Service_UserFacilityRestrictions::save($facRes);
                }
            }

            //send notification email if requested
            if ($notify) {

                if ($this->getLoggedUser()->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_GOD && $newUserAccess->getAccount()->getFirstAdmin()) {
                    $creator = $newUserAccess->getAccount()->getFirstAdmin();
                } else {
                    $creator = $this->getLoggedUser();
                }

                if ($email) {
                    Genesis_Service_UserAccess::sendNewUserEmail($newUserAccess, $creator, $pass);
                }
            }

            $facNumAccess = "All";
            if (!$newUserAccess->getMyfootRole() ||
                    $newUserAccess->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_LIMITED) {
                $facNumAccess = count($newUserAccess->getManagableFacilities()->toArray());
            }

            $ret = array(
                'email' => $email,
                'admin' => $isAdmin,
                'getsEmails' => $getsEmails,
                'getsStatements' => $getsStatements,
                'accessMyfoot' => $accessMyfoot,
                'numFacilities' => $facNumAccess,
                'facIds' => $facilityIds,
                'userId' => $user->getId()
            );

            print json_encode($ret);

        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }

    }
}
