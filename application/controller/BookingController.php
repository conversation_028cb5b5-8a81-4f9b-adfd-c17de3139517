<?php
/*
 * Booking Controller
 *
 * @copyright 2015 SpareFoot Inc
 * <AUTHOR>
 */

class BookingController extends AccountMgmt_Controller_Restricted
{
    private $booking;
    private $user;

    protected function _init() {
        $this->_helper->layout->setLayout('layout-singlepage-minimal');

        $this->_helper->viewRenderer->setNoRender(true);
        $this->view->scripts = [
            '../dist/ember/features/assets/vendor',
            '../dist/ember/features/assets/features'
        ];

        // Inject the Auth Booking Token into the Page
        $secret = $this->getParam('s');
        $bookingConfirmationCode = $this->getParam('confirmation_code');
        $email = $this->getParam('email');
        $this->view->authBookingToken = AccountMgmt_Service_UserAuthByBooking::serializeToken($secret, $bookingConfirmationCode, $email);

        $this->user = Genesis_Service_User::loadByEmail($email);

        $confirmation_code = $this->getParam('confirmation_code');
        $this->booking = $booking = AccountMgmt_Service_Booking::validateAndGetBooking($confirmation_code);
        $this->view->facility = $booking->getFacility();
    }

    public function moveInAction()
    {
        //Nothing special needed, all handled in the init
    }

    public function remindMeLaterAction() {
        $this->booking->setBookingDataAttr(Genesis_Entity_BookingData::POST_MOVE_IN_EMAIL_REMINDER_DATE,date('Y-m-d',strtotime("+10 day")));
        Genesis_Service_Transaction::updateBookingData($this->booking);

        // Action Log
        $this->booking->facilityConfirmFutureMovein($this->user);
    }

    public function denyAction() {
        //Nothing special needed, all handled in the init
    }
}