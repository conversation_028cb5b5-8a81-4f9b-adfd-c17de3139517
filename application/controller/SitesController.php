<?php
/*
 * Sites Controller
 *
 * @copyright 2010 SpareFoot, Inc.
 * <AUTHOR>
 */

class SitesController extends AccountMgmt_Controller_Restricted
{
    protected function _init()
    {
        if ($this->getParam('fid')) {
            $facilityId = $this->getParam('fid');
        } else {
            $facilityId = $this->getSession()->facilityId;
        }

        $this->getSession()->facilityId = ($facilityId == -1 ? null : $facilityId);
    }

    public function indexAction()
    {
        $this->forward('overview');
    }

    public function overviewAction()
    {
        $account = $this->getLoggedUser()->getAccount();

        $this->getSession()->facilityId = 'all';

        $this->view->facilities = $this->_fetchFacilitiesData();

        $this->view->showUrls = $account->getNumBillableEntities() == 0 ? false : true;

        if ($this->getParam('export_to_excel')) {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);
            header('Content-type: text/csv');
            header('Content-disposition: attachment; filename="report.csv"');

            echo '"Facility","Visits","Reservations","Calls","URL"', "\n";

            foreach ($this->view->facilities as $facility) {
                $out = array(
                    $facility['entity']->getTitle(),
                    $facility['num_visits'],
                    $facility['num_reservations'],
                    $facility['num_calls'],
                    $this->view->showUrls ? Genesis_Util_Url::hostedsiteUrl($facility['entity']) : '',
                );

                $out = str_replace('"', '""', $out);

                echo '"', implode('","', $out), '"', "\n";
            }
        }

        $this->view->scripts = array('sites/overview');
    }

    public function callsAction()
    {
        $facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Restriction::equal('hostedWebsite', 1));
        $this->view->facilities = $facilities;
        $this->view->calls = $this->_fetchCallsData($facilities);

        $this->view->facility_id = $this->getSession()->facilityId;

        if ($this->getParam('export_to_excel')) {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);
            header('Content-type: text/csv');
            header('Content-disposition: attachment; filename="report.csv"');

            echo '"Time","Duration","Facility","Status","Caller ID","Recording URL"', "\n";

            foreach ($this->view->calls as $call) {
                $out = array(
                    date('m/d/Y g:ia',strtotime($call['start_time'])),
                    floor($call['duration']/60) . ':' . str_pad(($call['duration']%60),2,'0',STR_PAD_LEFT),
                    $call['facility_title'],
                    $call['dial_status'],
                    $call['caller_name'],
                    $call['recording_url'],
                );

                $out = str_replace('"', '""', $out);

                echo '"', implode('","', $out), '"', "\n";
            }
        }

        $this->view->scripts = array('sites/calls');
    }

    public function reservationsAction()
    {
        $facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Restriction::equal('hostedWebsite', 1));
        $this->view->facilities = $facilities;
        $this->view->reservations = $this->_fetchReservationsData($facilities);

        $this->view->facility_id = $this->getSession()->facilityId;

        if ($this->getParam('export_to_excel')) {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);
            header('Content-type: text/csv');
            header('Content-disposition: attachment; filename="report.csv"');

            echo '"Facility","Date Reserved","Last Name","First Name","Email","Phone","Unit","Monthly Rent","Move-In Date","Unit Size"', "\n";

            foreach ($this->view->reservations as $reservation) {
                $out = array(
                    $reservation['title'],
                    date("m/d/Y", strtotime($reservation['timestamp'])),
                    $reservation['last_name'],
                    $reservation['first_name'],
                    $reservation['email'],
                    $reservation['phone'],
                    $reservation['unit_number'],
                    '$' . number_format($reservation['monthly_rent'], 2),
                    date("m/d/Y", strtotime($reservation['move_in'])),
                    $reservation['size_w'] . ' x ' . $reservation['size_d'],
                );

                $out = str_replace('"', '""', $out);

                echo '"', implode('","', $out), '"', "\n";
            }
        }

        $this->view->scripts = array('sites/reservations');
    }

    public function setupAction()
    {
        $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Restriction::equal('hostedWebsite', 1));
        //Genesis_Service_Facility::loadByAccountId($this->getLoggedUser()->getAccountId(), Genesis_Db_Restriction::equal('hostedWebsite', 1)->setOrder(Genesis_Db_Order::asc('title')));
        $this->view->account = Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());

        $this->view->scripts = array('sites/setup');
    }

    protected function getTab()
    {
        return self::TAB_HOSTEDWEBSITE;
    }

    public function emailAction()
    {
        $account = $this->getLoggedUser()->getAccount();

        if (isset($_POST['send'])) {

        $body = '
'.$this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName().' of '.$account->getName().' has requested more information about hosted websites.

Name: '.$this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName().'
Email: '.$this->getLoggedUser()->getEmail().'
Phone: '.$this->getLoggedUser()->getPhone().'
Billing Type: '.$account->getPaymentType().'
Integrations: '.$account->getIntegrationsString().'
            ';

            mail('<EMAIL>', 'Hosted Websites Info Request', $body);
        }

    }

    private function _fetchFacilitiesData()
    {
        $facilities = $this->getLoggedUser()->getManagableFacilities(Genesis_Db_Restriction::equal('hostedWebsite', 1));
        /*$facilities = Genesis_Service_Facility::loadByAccountId(
                $this->getLoggedUser()->getAccountId(),
                Genesis_Db_Restriction::equal('hostedWebsite', 1));*/

        $startDate = $this->getTrueBeginDate();
        $endDate   = $this->getTrueEndDate();

        $facilityIds = array();
        $facilityDetails = array();

        foreach ($facilities as $facility) {
            $facilityId = $facility->getId();
            $facilityIds[] = $facilityId;
            $facilityDetails[$facilityId]['entity'] = $facility;
            $facilityDetails[$facilityId]['num_calls'] = 0;
            $facilityDetails[$facilityId]['num_reservations'] = 0;
            $facilityDetails[$facilityId]['num_visits'] = 0;
        }

        $callData = Genesis_Service_Reporting::getCallsByFacilities($facilityIds, $startDate, $endDate);
        foreach ($callData as $id => $dataArray) {
            $facilityDetails[$id]['num_calls'] = $dataArray['num_calls'];
        }

        $reservationData = Genesis_Service_Reporting::getValidHostedWebsiteReservationDataByFacilities($facilityIds, $startDate, $endDate);
        foreach ($reservationData as $id => $dataArray) {
            $facilityDetails[$id]['num_reservations'] = $dataArray['num_reservations'];
        }

        $visitsData = Genesis_Service_Reporting::getHostedWebsiteVisits($facilityIds, $startDate, $endDate);
        foreach ($visitsData as $id => $dataArray) {
            $facilityDetails[$id]['num_visits'] = $dataArray['num_visits'];
        }

        return $facilityDetails;
    }

    /**
     * Fetch and organize all of the data to populate the calls screen
     * @param $facilities = itr of facilities to get data for
     *
     * @return array
     */
    private function _fetchCallsData($facilities = null)
    {
        $startDate = $this->getTrueBeginDate();
        $endDate   = $this->getTrueEndDate() . ' 23:59:59';

        $facIds = array();
        foreach ($facilities as $fac) {
            $facIds[$fac->getId()] = true;
        }

        $reservationDetails = array();

        $facilityId = $this->getSession()->facilityId;

        $accountId = $this->getLoggedUser()->getAccountId();

        $callData = Genesis_Service_PhoneCall::loadByAccountId(
                $accountId, $startDate, $endDate);

        $callDetails = array();

        foreach ($callData as $id => $dataArray) {
            //if we are looking at facility subset and id not in array continue
            if ($facilities && !isset($facIds[$dataArray['facility_id']])) {
                continue;
            }

            //if a facility id was specified, only get those bookings
            if ($facilityId && $facilityId != 'all' && ($facilityId != $dataArray['facility_id'])) {
                continue;
            }

            $callDetails[$id]['facility_id']        = $dataArray['facility_id'];
            $callDetails[$id]['facility_title']     = $dataArray['facility_title'];
            $callDetails[$id]['to_phone_number']    = $dataArray['to_phone_number'];
            $callDetails[$id]['from_phone_number']  = $dataArray['from_phone_number'];
            $callDetails[$id]['start_time']         = $dataArray['start_time'];
            $callDetails[$id]['duration']           = $dataArray['duration'];
            $callDetails[$id]['dial_status']        = $dataArray['dial_status'];
            $callDetails[$id]['caller_name']        = $dataArray['caller_name'];
            $callDetails[$id]['recording_url']      = $dataArray['recording_url'];
        }

        return $callDetails;
    }

    /**
     * Fetch and organize all of the data to populate the reservations screen
     *
     * @return array
     */
    private function _fetchReservationsData($facilities = null)
    {
        $startDate = $this->getTrueBeginDate();
        $endDate   = $this->getTrueEndDate() . ' 23:59:59';

        $facIds = array();
        foreach ($facilities as $fac) {
            $facIds[$fac->getId()] = true;
        }

        $reservationDetails = array();

        $facilityId = $this->getSession()->facilityId;

        $accountId = $this->getLoggedUser()->getAccountId();

        $impData = Genesis_Service_Transaction::loadHostedWebsiteByAccountId(
                $accountId, $startDate, $endDate);

        foreach ($impData as $id => $dataArray) {
            //if we are looking at facility subset and id not in array continue
            if ($facilities && !isset($facIds[$dataArray['facility_id']])) {
                continue;
            }

            //skip invalid booking state
            if ($dataArray['booking_state'] == 'INVALID') {
                continue;
            }

            //if a facility id was specified, only get those bookings
            if ($facilityId && $facilityId != 'all' && ($facilityId != $dataArray['facility_id'])) {
                continue;
            }

            $reservationDetails[$id]['last_name']      = $dataArray['last_name'];
            $reservationDetails[$id]['first_name']     = $dataArray['first_name'];
            $reservationDetails[$id]['unit_number']    = $dataArray['unit_number'];
            $reservationDetails[$id]['monthly_rent']   = $dataArray['monthly_rent'];
            $reservationDetails[$id]['timestamp']      = $dataArray['timestamp'];
            $reservationDetails[$id]['move_in']        = $dataArray['move_in'];
            $reservationDetails[$id]['size_w']         = $dataArray['size_w'];
            $reservationDetails[$id]['size_d']         = $dataArray['size_d'];
            $reservationDetails[$id]['facility_id']    = $dataArray['facility_id'];
            $reservationDetails[$id]['title']          = $dataArray['title'];
            $reservationDetails[$id]['email']          = $dataArray['email'];
            $reservationDetails[$id]['phone']          = $dataArray['phone'];
            $reservationDetails[$id]['traffic_source'] = $dataArray['traffic_source'];
        }

        return $reservationDetails;
    }

    public function getfacilitysettingsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $facility = Genesis_Service_Facility::loadById($this->getParam('fid'));

        $settings = array();

        $settings['payment'] = $facility->getPaymentPortal();
        $settings['google'] = $facility->getGaAccount();
        $settings['video'] = $facility->getYouTubeVideo();
        $settings['logo'] = $facility->getLogo();

        print json_encode($settings);
    }

    public function savesettingsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            $facilityId = $this->getParam('facilityId');
            $youtubeLink = $this->getParam('video');
            $paymentPortalLink = $this->getParam('payment');
            $googleAccountNum = $this->getParam('google');

            //error checking
            if (strlen($youtubeLink) > 0 && !preg_match("/youtube/i", $youtubeLink)) {
                throw new Exception('Video link is not a valid YouTube link.');
            }

            $facility = Genesis_Service_Facility::loadById($facilityId);

            $facility->setYouTubeVideo($youtubeLink);
            $facility->setPaymentPortal($paymentPortalLink);
            $facility->setGaAccount($googleAccountNum);

            Genesis_Service_Facility::save($facility);

        } catch (Exception $e) {
            echo "Error: ",  $e->getMessage(), "\n";
        }
    }
}
