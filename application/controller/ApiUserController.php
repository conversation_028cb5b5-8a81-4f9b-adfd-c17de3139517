<?php
use AccountMgmt_Models_ApiException as ApiException;
class ApiUserController extends AccountMgmt_Controller_ApiBaseController
{
    public function init() {
        parent::init();
    }

    public function indexAction()
    {
        // path: /api/users/{ user_id }
        $userId = $this->_getParam('user_id');
        if ($this->_request->isGet() || $this->_request->isPut()) {
            $user = $this->validateAndGetUser($userId);
        }

        if($this->_request->isDelete()) {
            $user = $this->validateAndGetUser($userId,AccountMgmt_Service_Constants::ACTION_DELETE);
        }

        $json_body = $this->getRequest()->getRawBody();

        // GET
        if ($this->_request->isGet()) {
            $this->_helper->json(['data' => AccountMgmt_Service_User::toArray($user)]);
        }

        // PUT
        if ($this->_request->isPut()) {
            AccountMgmt_Service_User::updateFromJson($user, $json_body);
            $this->_helper->json(['data' => AccountMgmt_Service_User::toArray($user)]);
        }

        // DELETE
        if ($this->_request->isDelete()) {
            try {
                AccountMgmt_Service_User::deleteUser($user);
            } catch (Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }
            self::sendOKEmptyResponse();
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function meAction()
    {
        $user = AccountMgmt_Service_User::getLoggedUser();

        $this->_helper->json(['data' => AccountMgmt_Service_User::toArray($user)]);
    }
}
