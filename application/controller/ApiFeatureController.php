<?php

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 7/9/15
 * Time: 9:59 AM
 */
use AccountMgmt_Models_ApiException as ApiException;
class ApiFeatureController extends AccountMgmt_Controller_ApiBaseController
{
    public function init()
    {
        parent::init($requireAuth = false);
    }

    public function indexAction()
    {
        /**
         * @var $feature Genesis_Entity_Feature
         */

        $results = [];
        foreach (self::loadFeatures() as $feature) {
            //$results[$feature->getFeature()][$feature->getKey()] = [];
            $results[$feature->getFeature()][$feature->getKey()] = $feature->getValue();
            //$results[$feature->getFeature()][$feature->getKey()]['url'] = '/api/features/name/'.$feature->getFeature().'/'.$feature->getKey();
        }
        $json = Zend_Json::prettyPrint(Zend_Json::encode(['data'=>$results]));
        $this->_helper->json($json, true, array('encodeData' => false));
    }

    public function nameAction()
    {
        if (! $this->getParam('one')) {
            throw new ApiException(ApiException::BAD_REQUEST, 'name required');
        }
        $name = $this->getParam('one');
        if (! $this->getParam('two')) {
            throw new ApiException(ApiException::BAD_REQUEST, 'key required');
        }
        $key = $this->getParam('two');
        /**
         * @var $feature Genesis_Entity_Feature
         */
        $feature = Genesis_Service_Feature::load(
            Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::like('feature', $name),
                Genesis_Db_Restriction::like('key', $key)
            )
        )->current();
        if (! $feature) {
            $this->_helper->json(['empty'=> 1]);
        }
        $value = $this->getParam('three');
        if ($value !== null) {
            if (! $this->getLoggedUser()) {
                throw new AccountMgmt_Models_ApiException(ApiException::UNAUTHORIZED, 'must be signed in');
            }
            if (! $this->getLoggedUser()->isMyFootGod()) {
                throw new AccountMgmt_Models_ApiException(ApiException::UNAUTHORIZED, 'only gods can modify');
            }
            $feature->setValue($value);
            $feature = Genesis_Service_Feature::save($feature);
        }
        $this->_helper->json(['data'=> self::serialize($feature)]);
    }

    public function idAction()
    {
        $id = $this->getParam('one');
        if (! $id) {
            throw new ApiException(ApiException::BAD_REQUEST, 'id required');
        }

        /**
         * @var $feature Genesis_Entity_Feature
         */
        $feature = Genesis_Service_Feature::load(
            Genesis_Db_Restriction::equal('id', $id)
        )->current();
        $value = $this->getParam('two');
        if ($value !== null) {
            if (! $this->getLoggedUser()) {
                throw new AccountMgmt_Models_ApiException(ApiException::UNAUTHORIZED, 'must be signed in');
            }
            if (! $this->getLoggedUser()->isMyFootGod()) {
                throw new AccountMgmt_Models_ApiException(ApiException::UNAUTHORIZED, 'only gods can modify');
            }
            $feature->setValue($value);
            $feature = Genesis_Service_Feature::save($feature);
        }
        /**
         * @var $feature Genesis_Entity_Feature
         */
        $response = self::serialize($feature);
        $json = Zend_Json::prettyPrint(Zend_Json::encode(['data'=>$response]));
        $this->_helper->json($json,true, array('encodeData' => false));
    }

    public function diffAction()
    {
        if (Genesis_Config_Server::isProduction()) {
            throw new Exception('diff not possible to prod');
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $server = $this->getParam('one');
        switch ($server) {
            case 'dev':
            case 'development':
            case 'moreyard':
            case 3:
                $url = 'https://my.sparefoot.moreyard.com/api/features';
                break;
            case 'stage':
            case 2:
            case 'staging':
            case 'extrameter':
                $url = 'https://my.sparefoot.extrameter.com/api/features';
                break;
            case 'production':
            case 'prod':
            case 1:
            default:
                $url = 'https://my.sparefoot.com/api/features';
        }
        curl_setopt($ch, CURLOPT_URL, $url);
        $serverResponse = curl_exec($ch);
        $serverSettings = json_decode($serverResponse, true);
        if (! $serverSettings) {
            throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, 'unable to get settings from ' . $url);
        }
        $serverSettings = $serverSettings['data'];
        $diff = [];
        /**
         * @var $feature Genesis_Entity_Feature
         */
        foreach (self::loadFeatures() as $feature) {
            $name = $feature->getFeature();
            $key = $feature->getKey();
            $value = $feature->getValue();
            if (! array_key_exists($name, $serverSettings)) {
                $diff[$name][$key] = [
                    'remote' => '<unset>',
                    'local' => $feature->getValue()
                ];
                continue;
            }
            if (! array_key_exists($key, $serverSettings[$name])) {
                $diff[$name][$key] = [
                    'remote' => '<unset>',
                    'local' => $feature->getValue()
                ];
                continue;
            }
            if ($serverSettings[$name][$key] != $value) {
                $diff[$name][$key] = [
                    'remote' => $serverSettings[$name][$key],
                    'local' => $value
                ];
            }
            unset($serverSettings[$name][$key]);
        }
        //set any remainders
        foreach ($serverSettings as $name => $set) {
            foreach ($set as $key => $value) {
                $diff[$name][$key] = [
                    'remote' => $value,
                    'local' => '<unset>'
                ];
            }
        }
        $json = Zend_Json::prettyPrint(Zend_Json::encode(['data'=>$diff, 'meta'=>['server'=>$url]]));
        $this->_helper->json($json ,true, array('encodeData' => false));
    }

    private function serialize(Genesis_Entity_Feature $feature)
    {
        return [$feature->getFeature() => [
                $feature->getKey() => [
                    'value'=>$feature->getValue(),
                    'url' => '/api/features/name/'.$feature->getFeature().'/'.$feature->getKey()
                ]
            ]];
    }

    private function loadFeatures()
    {
        return $features = Genesis_Service_Feature::load(
            Genesis_Db_Restriction::or_(
                Genesis_Db_Restriction::like('feature', 'myfoot.%'),
                Genesis_Db_Restriction::like('feature', 'genesis.%')
            )->setOrder(Genesis_Db_Order::asc('feature')->asc('key'))
        );
    }
}