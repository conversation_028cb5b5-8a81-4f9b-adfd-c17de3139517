<?php
/*
 * Inventory Controller
 *
 * @copyright 2012 SpareFoot
 * <AUTHOR>
 */

class InventoryController extends AccountMgmt_Controller_Restricted
{
    protected function _init() {
        $facilityId = -1;
        $actionName = $this->getRequest()->getActionName();
        $needRedirect = (self::isBlacklisted($actionName) && false);

        if ($this->getparam('fid')) {
            $facilityId = $this->getparam('fid');
        } elseif (isset($this->getSession()->facilityId)) {
            $facilityId = $this->getSession()->facilityId;

            if ($needRedirect) {
                $this->redirect($this->view->url(['action' =>$actionName], 'features').'?fid='.$facilityId);
            }
        } else {
            //facility ID was not passed in via the query string, nor was it in the session...redirect to facility home
            if ($needRedirect) {
                $this->getSession()->facilityId = null;
                $this->forward('list');
            }
        }

        $this->getSession()->facilityId = ($facilityId == -1 ? null : $facilityId);

        if ($this->getparam('search_term')) {
            $this->getSession()->searchTerm = $this->getparam('search_term');
        }

        if ($this->getparam('clear_search_term')) {
            $this->getSession()->searchTerm = null;
        }
    }

    public function indexAction() {
        $this->getSession()->facilityId = null;
        $this->forward('units');
    }

    public function customPromotionAction() {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {

            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            if (!$facility) {
                throw new Exception('Cannot load facility for id: '.$this->getSession()->facilityId);
            }
            $this->view->facility = $facility;

            $special = $this->_buildCustomSpecialFromParams();

            //this returns an existing special if there's an identical one already saved
            $special = Genesis_Service_Special::save($special, $this->getLoggedUser());

            $this->setparam('special_id', $special->getId());
            $this->applySpecialAction();

        } catch (Exception $e) {
            $output = array(
                'success' => 0,
                'message' => $e->getMessage()
            );
            echo json_encode($output);
        }
    }

    private function _buildCustomSpecialFromParams() {
        $special = new Genesis_Entity_Special();

        if ($this->getparam('promotion_type')) {
            switch ($this->getparam('promotion_type')) {
                case 'percent_off':
                    if ($this->getparam('promotion_percent') <= 0 || $this->getparam('promotion_percent') > 100) {
                        throw new Exception("Percent must be between 1 and 100.");
                    }
                    $special->setType(Genesis_Entity_Special::TYPE_PROMO_PERCENT);
                    $special->setPercentOff(number_format(($this->getparam('promotion_percent')/100),2));
                    $special->setRequiresPrepaidMonths( $this->getparam('promotion_restrictions_prepaid_months') );
                    $special->setRequiresMinimumLeaseMonths( $this->getparam('promotion_restrictions_minimum_lease_length') );
                    break;
                case 'dollar_off':
                    if ($this->getparam('promotion_dollar') <= 0) {
                        throw new Exception("Price must be greater than zero.");
                    }
                    $special->setType(Genesis_Entity_Special::TYPE_PROMO_DOLLAR);
                    $special->setDollarOff(number_format($this->getparam('promotion_dollar'),2));
                    $special->setRequiresPrepaidMonths( $this->getparam('promotion_restrictions_prepaid_months') );
                    $special->setRequiresMinimumLeaseMonths( $this->getparam('promotion_restrictions_minimum_lease_length') );
                    break;
                case 'dollar_override':
                    if ($this->getparam('promotion_override') <= 0) {
                        throw new Exception("Price must be greater than zero.");
                    }
                    $special->setType(Genesis_Entity_Special::TYPE_PROMO_OVERRIDE);
                    $special->setDollarOverride(number_format($this->getparam('promotion_override'),2));
                    $special->setRequiresPrepaidMonths( $this->getparam('promotion_restrictions_prepaid_months') );
                    $special->setRequiresMinimumLeaseMonths( $this->getparam('promotion_restrictions_minimum_lease_length') );
                    break;
                default:
                    throw new Exception('Please select a promotion type');
            }

            $promoMonths = $this->getparam('promotion_months');
            if (!$promoMonths) {
                throw new Exception('To build a promotion, you must specify the months it applies to');
            }
            $special->setMonths(implode(',', $promoMonths));

        }

        if ($this->getparam('discount_type')) {
            switch ($this->getparam('discount_type')) {
                case 'percent_off':
                    if ($this->getparam('discount_percent', 0) <= 0 || $this->getparam('discount_percent') > 100) {
                        throw new Exception("Percent must be between 1 and 100.");
                    }
                    $special->setType(Genesis_Entity_Special::TYPE_DISCOUNT_PERCENT);
                    $special->setPercentOff(number_format(($this->getparam('discount_percent')/100),2));
                    break;
                case 'dollar_off':
                    if ($this->getparam('discount_dollar', 0) <= 0) {
                        throw new Exception("Price must be greater than zero.");
                    }
                    $special->setType(Genesis_Entity_Special::TYPE_DISCOUNT_DOLLAR);
                    $special->setDollarOff(number_format($this->getparam('discount_dollar'),2));
                    break;
                /*
                case 'dollar_override':
                    if ($this->getparam('discount_override') <= 0) {
                        throw new Exception("Price must be greater than zero.");
                    }
                    $special->setType(Genesis_Entity_Special::TYPE_DISCOUNT_OVERRIDE);
                    $special->setDollarOverride(number_format($this->getparam('discount_override'),2));
                    break;
                */
                default:
                    throw new Exception('Unknown discount type');
            }
        }

        return $special;
    }

    public function customDiscountAction() {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {

            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            if (!$facility) {
                throw new Exception('Cannot load facility for id: '.$this->getSession()->facilityId);
            }
            $this->view->facility = $facility;

            $special = $this->_buildCustomSpecialFromParams();

            //this returns an existing special if there's an identical one already saved
            $special = Genesis_Service_Special::save($special, $this->getLoggedUser());

            $this->setparam('special_id', $special->getId());
            $this->applySpecialAction();

        } catch (Exception $e) {
            $output = array(
                'success' => 0,
                'message' => $e->getMessage()
            );
            echo json_encode($output);
        }
    }

    public function unitsAction() {
        $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);

        //if sitelink or centershift 4 account then load grouped unit view
        /*if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SITELINK ||
                $facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_CENTERSHIFT4 ||
                $facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) {
            $this->forward('groupedinventory');
        } else {*/
            //$facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $this->view->facility = $facility;
            $this->view->sourceType = $facility->getCorporation()->getSourceId();

            $this->view->inventory = $this->_fetchInventoryData();
            if (! $this->view->inventory) {
                /* If no units are returned, check to see if there are truly no
                 * units associated with that facility, or if the facility has units,
                 * but they're publish = 0.
                 */
                $restriction = Genesis_Db_Restriction::and_(
                                   Genesis_Db_Restriction::equal('facilityId', $this->view->facility->getId()),
                                   Genesis_Db_Restriction::equal('publish', 0)
                               );
                $units = Genesis_Service_StorageSpace::load($restriction);
                if ($units->current()) {
                    $this->view->unpublishedUnits = 1;
                } else {
                    $this->view->unpublishedUnits = 0;
                }
            /*}*/
            }

        //get the default specials
        $this->view->defaultSpecials = Genesis_Service_Special::loadDefaultSpecials();


        $this->_getCustomSpecials();
        //needed for scripts
        $this->getResponse()->appendBody(
            '<script> var controller = "/features/";</script>'
        );
        $scripts = ['facility/hide-facility-reason-modal', 'inventory/units-shared'];
        $scripts[] = 'inventory/units';
        $this->view->scripts = $scripts;
    }

    public function unitAction()
    {
        $this->_helper->layout()->disableLayout();
        if (count($this->getParam('unit_ids')) > 0) {
            $this->view->units = $this->_fetchInventoryData($this->getParam('unit_ids'));
            $this->view->facility = Genesis_Service_Facility::loadById($this->view->units[0]['facility']);
        } else {
            $this->view->units = array();
            $this->view->facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        }

        $this->view->unitGroupCount = $this->getParam('unit_group_count');

        if ($this->getRequest()->isPost() )  {

            $this->_helper->viewRenderer->setNoRender(true);

            try {
                //put unit ids into array in case there are a lot of them
                if ($this->getparam('unit_ids')) {
                    $unitIds = $this->getparam('unit_ids');
                } else {
                    //throw new Exception('Please select units to update.');

                    //create new space based on type if no ids passed in (MANUALS do this)
                    switch ($this->getparam('unit_type')) {
                        case Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT:
                            $space = new Genesis_Entity_StorageUnit();
                            break;
                        case Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                            $space = new Genesis_Entity_ParkingSpace();
                            break;
                        case Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                            $space = new Genesis_Entity_Workspace();
                            break;
                        case Genesis_Entity_StorageSpace::TYPE_WINE:
                            $space = new Genesis_Entity_WineStorage();
                            break;
                        case Genesis_Entity_StorageSpace::TYPE_LOCKER:
                            $space = new Genesis_Entity_StorageLocker();
                            break;
                        case Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                            $space = new Genesis_Entity_StorageOutdoor();
                            break;
                        default:
                            throw new Exception('Error selecting the unit type');
                            break;
                    }

                    //set active and published for newly added units: immediately visible on frontends
                    $space->setActive(1);
                    $space->setPublish(1);
                    $space->setQuantity(1);

                    $space->setFacilityId($this->getSession()->facilityId);

                    if ($this->getparam('unit_type')) {
                        $space->setType($this->getparam('unit_type'));
                    }

                    // set price for new unit
                    $space->setRegularPrice($this->getparam('standard_rate'));

                    $savedSpace = Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());

                    $al = new Genesis_Util_ActionLogger();
                    $al->logAction('add_unit', 0, 0, $this->getLoggedUser()->getId(), $this->getSession()->facilityId, $savedSpace->getId());

                    //put id in unitids array
                    $unitIds = array($savedSpace->getId());
                }

                //now we have $unitIds array to update rest of params
                foreach ($unitIds as $unitId) {
                    //determine if this is a new creation or an update
                    $space = Genesis_Service_StorageSpace::loadById($unitId);

                    if ($space) {
                        // make sure existing supp data gets lazy loaded
                        $suppData = $space->getSupplementalData();

                        if ($this->getparam('unit_type') != $space->getType()) {
                            $space->setType($this->getparam('unit_type'));
                            // We have to save the space here to "transform" it into the
                            // proper class (Genesis_Entity_ParkingSpace, etc.)
                            $space = Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                        }

                        //populate fields that all types of units have
                        $space->setWidth($this->getparam('width'));
                        $space->setLength($this->getparam('length'));
                        $space->setRegularPrice($this->getparam('standard_rate'));
                        $space->setUnitLights($this->getparam('unit_lights') ? 1 : 0);
                        $space->setShelvesInUnit($this->getparam('shelves_in_unit') ? 1 : 0);
                        $space->setAirCooledOnly($this->getparam('air_cooled') ? 1 : 0);
                        $space->setHeatedOnly($this->getparam('heated') ? 1 : 0);
                        $space->setTwentyFourHourAccess($this->getparam('twenty_four_hour_access') ? 1 : 0);
                        $space->setSkybox($this->getparam('stacked') ? 1 : 0);
                        $space->setBasement($this->getparam('basement') ? 1 : 0);
                        $space->setParkingWarehouse($this->getparam('parking_warehouse') == 'on' ? 1 : 0);
                        $space->setPullThrough($this->getparam('pull_thru') ? 1 : 0);
                        $space->setFloor($this->getparam('floor')); //form passes in 1 when outdoor
                        $space->setPremiumUnit($this->getparam('premium') ? 1 : 0);
                        $space->setAdaAccessible($this->getparam('ada') ? 1 : 0);

                        //populate fields based on type
                        switch ($this->getparam('unit_type')) {
                            case Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                                if ($this->getparam('covered') == 1) {
                                    $space->setCovered(1);
                                } else {$space->setCovered(0);}

                                $space->setHeight($this->getparam('height'));

                                if ($this->getparam('power')) {
                                    $space->setPower(1);
                                } else {$space->setPower(0);}

                                if ($this->getparam('lot_type')) {
                                    $space->setLotType($this->getparam('lot_type'));
                                } else {
                                    $space->SetLotType(null);
                                }

                                if ($this->getparam('door_type')) {
                                  $space->setDoorType($this->getparam('door_type'));//ROLL_UP,SWING,NONE
                                }


                                if ($this->getparam('climate_controlled')) {
                                   $space->setClimateControlled(1);
                                } else { $space->setClimateControlled(0);}


                                if ($this->getparam('humidity_controlled')) {
                                    $space->setHumidityControlled(1);
                                } else {$space->setHumidityControlled(0);}
                                //NOTE: vehicle/driveup set to 1 in class constructor

                                break;
                            case Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                                if ($this->getparam('covered') == 1) {
                                    $space->setCovered(1);
                                } else {$space->setCovered(0);}

                                if ($this->getparam('drive_up') == 1) {
                                    $space->setDriveUp(1);
                                } else {$space->setDriveUp(0);}

                                if ($this->getparam('vehicle') == 'only') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(1);
                                } elseif ($this->getparam('vehicle') == 'yes') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(0);
                                } else {
                                    $space->setVehicle(0);
                                    $space->setVehicleStorageOnly(0);
                                }

                                $space->setHeight($this->getparam('height'));

                                //NOTE: outdoor is set to true by default in constructor

                                break;
                            case Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT:
                            case Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                                if ($this->getparam('height')) {
                                    $space->setHeight($this->getparam('height'));
                                } else {$space->setHeight(null);}

                                if ($this->getparam('power')) {
                                    $space->setPower(1);
                                } else {$space->setPower(0);}

                                if ($this->getparam('alarm')) {
                                    $space->setAlarm(1);
                                } else {$space->setAlarm(0);}

                                if ($this->getparam('climate_controlled')) {
                                    $space->setClimateControlled(1);
                                } else {$space->setClimateControlled(0);}

                                if ($this->getparam('humidity_controlled')) {
                                    $space->setHumidityControlled(1);
                                } else {$space->setHumidityControlled(0);}

                                if ($this->getparam('drive_up') == 1) {
                                    $space->setDriveUp(1);
                                } else {$space->setDriveUp(0);}

                                if ($this->getparam('outdoor_access') == 1) {
                                    $space->setOutdoorAccess(1);
                                } else {$space->setOutdoorAccess(0);}

                                if ($this->getparam('vehicle') == 'only') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(1);
                                } elseif ($this->getparam('vehicle') == 'yes') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(0);
                                } else {
                                    $space->setVehicle(0);
                                    $space->setVehicleStorageOnly(0);
                                }

                                $space->setDoorType($this->getparam('door_type'));//ROLL_UP,SWING,NONE

                                if ($this->getparam('covered')) {
                                    $space->setCovered(1);
                                } else {$space->setCovered(0);}

                                break;
                            case Genesis_Entity_StorageSpace::TYPE_WINE:
                            case Genesis_Entity_StorageSpace::TYPE_LOCKER:
                                if ($this->getparam('height')) {
                                    $space->setHeight($this->getparam('height'));
                                } else {$space->setHeight(null);}

                                if ($this->getparam('power')) {
                                    $space->setPower(1);
                                } else {$space->setPower(0);}

                                if ($this->getparam('alarm')) {
                                    $space->setAlarm(1);
                                } else {$space->setAlarm(0);}

                                if ($this->getparam('climate_controlled')) {
                                    $space->setClimateControlled(1);
                                } else {$space->setClimateControlled(0);}

                                if ($this->getparam('humidity_controlled')) {
                                    $space->setHumidityControlled(1);
                                } else {$space->setHumidityControlled(0);}

                                if ($this->getparam('drive_up') == 1) {
                                    $space->setDriveUp(1);
                                } else {$space->setDriveUp(0);}

                                if ($this->getparam('outdoor_access') == 1) {
                                    $space->setOutdoorAccess(1);
                                } else {$space->setOutdoorAccess(0);}

                                $space->setDoorType($this->getparam('door_type'));//ROLL_UP,SWING,NONE

                                $space->setCovered(1); //covered is assumed

                                break;
                            default:
                                throw new Exception('Could not match type of storage space');
                                break;
                        }

                        Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                    }
                }

                $output = array(
                    'success' => 1,
                );
            } catch (Exception $e) {
                $output = array(
                    'success' => 0,
                    'message' => $e->getMessage()
                );
            }

            echo json_encode($output);
        }
    }

    public function toggleUnitsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $response = ['success' => null, 'modified' => 0, 'skipped'=>0, 'units' => []];
        try {

            $unitIds = $this->getParam('unit_ids');

            if (! is_array($unitIds) ) {
                throw new Exception('no unit ids found');
            }

            // active in frontends; won't actually appear unless publish=1
            if ($this->getparam('active') == 'true') {
                $active = 1;
            } elseif ($this->getparam('active') == 'false') {
                $active = 0;
            } else {
                throw new Exception ('active state not provided');
            }
            $response['active'] = $active;
            foreach ($unitIds as $unitId) {
                $unit = Genesis_Service_StorageSpace::loadById($unitId);
                if (! $unit) {
                    continue;
                }
                if (! in_array($unit->getFacilityId(), $this->getLoggedUser()->getManageableFacilityIds())) {
                    throw new Exception('Unable to access facility: '.$unit->getFacilityId());
                }
                if ($unit->getActive() == $active) {
                    $response['skipped']++;
                } else {
                    $unit->setActive($active);
                    Genesis_Service_StorageSpace::save($unit, $this->getLoggedUser()->getId());
                    $response['modified']++;
                }
                $response['count']++;
                $response['units'][] = $unit->getId();
            }
            $response['success'] = true;

        } catch (Exception $e) {
            $response['success'] = false;
            $response['message'] = $e->getMessage();
        }

        echo json_encode($response);
    }

    private function _getCustomSpecials() {
        $this->view->customPromos  = array();
        $this->view->customDiscounts  = array();
        $acctSpecials  = Genesis_Service_Special::loadByAccountId($this->getLoggedUser()->getAccountId());
        foreach ($acctSpecials as $special) {
            if ($special->getDefault()) continue;
            if ($special->isPromo()) {
                $this->view->customPromos[] = array(
                        'id'=>$special->getId(),
                        'name'=>$special->getString(),
                    );
            }
            if ($special->isDiscount()) {
                $this->view->customDiscounts[] = array(
                        'id'=>$special->getId(),
                        'name'=>$special->getString(),
                    );
            }
        }
    }

    public function applySpecialAction() {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {

            $specialId = $this->getparam('special_id');
            if (!$specialId) {
                throw new Exception('No special id specified.');
            }

            $unitRows = json_decode(stripslashes($this->getparam('unit_rows')));

            $updatedUnitRows = array();

            foreach ($unitRows as $unitRow) {
                /**
                 * @var $unitId int
                 */
                foreach ($unitRow->unitIds as $unitId) {
                    Genesis_Service_UnitSpecial::applyUnitSpecial($unitId, $this->getparam('special_id'));
                }

                $updatedUnit = Genesis_Service_StorageSpace::loadById($unitRow->unitIds[0]);
                $updatedUnitRows[] = array(
                                        'unitIndex' => $unitRow->unitIndex,
                                        'regularPrice' => number_format($updatedUnit->getRegularPrice(), 2),
                                        'effectivePrice' => number_format($updatedUnit->getEffectivePrice(), 2),
                                        'specialString' => $updatedUnit->buildSpecialString(),
                                        'discountString' => $updatedUnit->buildDiscountString(),
                                     );
            }

            $this->_getCustomSpecials();

            $output = array(
                'success' => 1,
                'unitRows' => $updatedUnitRows,
                'customPromos' => $this->view->customPromos,
                'customDiscounts' => $this->view->customDiscounts,
            );

        } catch (Exception $e) {
            $output = array(
                'success' => 0,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
        }

        echo json_encode($output);
    }

    /**
     * Fetch and organize all of the data to populate the inventory screen
     * @var $unitIds
     * @return array
     */
    private function _fetchInventoryData($unitIds = null) {
        //if unit ids were passed in then only load those
        if ($unitIds) {
            $restriction =  Genesis_Db_Restriction::in('id', $unitIds);
            $units = Genesis_Service_StorageSpace::load($restriction);
            $facility = $units->current()->getFacility();
        } else {
            $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $units = $facility->getGroupedUnits(true);
        }

        //set flag for grouped version
        $showGrouped = 0;
        if ($facility->canGroupUnits()) {
            $showGrouped = 1;
        }

        $this->view->showGrouped = $showGrouped;

        if ($units) {
            $i = 0;

            foreach ($units as $unit) {

                if (! $unitIds && $showGrouped) {
                    $groupUnitIds = $unit->getGroupedUnitIds();
                } else {
                    $groupUnitIds = array($unit->getId());
                }

                $unitDetails[$i]['unitIds']            = $groupUnitIds;
                $unitDetails[$i]['facility']           = $unit->getFacility()->getId();
                $unitDetails[$i]['type']               = $unit->stringType();
                $unitDetails[$i]['typeNum']            = $unit->getType();

                $unitDetails[$i]['width']              = $unit->getWidth();
                $unitDetails[$i]['length']             = $unit->getLength();
                $unitDetails[$i]['height']             = $unit->getHeight();
                $unitDetails[$i]['squareFootage']       = $unit->getSquareFootage();

                //some instantiation for ones that don't always get set
                $unitDetails[$i]['driveUp'] = null;
                $unitDetails[$i]['vehicle'] = null;
                $unitDetails[$i]['doorWidth'] = null;
                $unitDetails[$i]['doorHeight'] = null;
                $unitDetails[$i]['climateControlled'] = null;
                $unitDetails[$i]['humidityControlled'] = null;
                $unitDetails[$i]['alarm'] = null;
                $unitDetails[$i]['power'] = null;
                $unitDetails[$i]['outdoorAccess'] = null;
                $unitDetails[$i]['doorType'] = null;
                $unitDetails[$i]['covered'] = null;

                $unitDetails[$i]['dimensions']         = $unit->stringDimensions(false);
                $unitDetails[$i]['exportDimensions']   = $unit->stringDimensions(false);
                $unitDetails[$i]['amenities']          = $unit->stringAmenities();
                $unitDetails[$i]['floor']              = $unit->getFloor();
                $unitDetails[$i]['hasDiscountSpecial'] = $unit->hasDiscountSpecial();
                $unitDetails[$i]['sparefootPrice']     = "";
                if ($unit->getSparefootPrice() > 0) {
                    $unitDetails[$i]['sparefootPrice']    = $unit->getSparefootPrice();
                }
                $unitDetails[$i]['standardRate']       = $unit->getRegularPrice();
                $unitDetails[$i]['quantity']           = $unit->getQuantity();
                $unitDetails[$i]['approved']           = $unit->getApproved() ? true : false;
                $unitDetails[$i]['published']          = $unit->getPublish() ? true : false; // Rentable in the FMS; only set by FMS

                if ($unit->getActive() == 1) {
                    $unitDetails[$i]['active']         = true; // active in frontends; won't actually appear unless publish=1
                } else {
                    $unitDetails[$i]['active']         = false;
                }

                $unitDetails[$i]['deposit'] = $unit->getDeposit();
                $unitDetails[$i]['quantity'] = $unit->getQuantity();
                $unitDetails[$i]['description'] = $unit->getDescription();

                $unitDetails[$i]['specialString'] = $unit->buildSpecialString();
                $unitDetails[$i]['discountString'] = $unit->buildDiscountString();

                // Supp unit data
                $unitDetails[$i]['stacked'] = $unit->getSkybox();
                $unitDetails[$i]['premium'] = $unit->getPremiumUnit();
                $unitDetails[$i]['heated'] = $unit->getHeatedOnly();
                $unitDetails[$i]['airCooled'] = $unit->getAirCooledOnly();
                $unitDetails[$i]['ada'] = $unit->getAdaAccessible();
                $unitDetails[$i]['unitLights'] = $unit->getUnitLights();
                $unitDetails[$i]['twentyFourHourAccess'] = $unit->getTwentyFourHourAccess();

                $unitDetails[$i]['shelvesInUnit'] = $unit->getShelvesInUnit();
                $unitDetails[$i]['basement'] = $unit->getBasement();
                $unitDetails[$i]['parkingWarehouse'] = $unit->getParkingWarehouse();
                $unitDetails[$i]['pullThru'] = $unit->getPullThrough();

                $unitDetails[$i]['lotType'] = $unit->getSuppAttr(Genesis_Entity_UnitData::LOT_TYPE);

                //this section does not apply to outdoor only
                if($unit->getType() != Genesis_Entity_StorageSpace::TYPE_OUTDOOR) {

                    if ($unit->getClimateControlled() == 1) { $unitDetails[$i]['climateControlled'] = true; } else { $unitDetails[$i]['climateControlled'] = false; }
                    if ($unit->getHumidityControlled() == 1) { $unitDetails[$i]['humidityControlled'] = true; } else { $unitDetails[$i]['humidityControlled'] = false; }
                    if ($unit->getDoorType() == Genesis_Entity_StorageSpace::DOOR_ROLL_UP) {
                        $unitDetails[$i]['doorType'] = 'roll_up';
                    } elseif ($unit->getDoorType() == Genesis_Entity_StorageSpace::DOOR_SWING) {
                        $unitDetails[$i]['doorType'] = 'swing';
                    } else {
                        $unitDetails[$i]['doorType'] = 'none';
                    }
                    if ($unit->getAlarm() == 1) { $unitDetails[$i]['alarm'] = true; } else { $unitDetails[$i]['alarm'] = false; }
                    if ($unit->getPower() == 1) { $unitDetails[$i]['power'] = true; } else { $unitDetails[$i]['power'] = false; }
                }

                //this section does not apply to parking or outdoor
                if ($unit->getType() != Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE &&
                        $unit->getType() != Genesis_Entity_StorageSpace::TYPE_OUTDOOR) {

                    if ($unit->getOutdoorAccess() == 1) { $unitDetails[$i]['outdoorAccess'] = true; } else { $unitDetails[$i]['outdoorAccess'] = false; }

                }


                //this section does not apply to parking, wine or locker
                if ($unit->getType() != Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE &&
                        $unit->getType() != Genesis_Entity_StorageSpace::TYPE_LOCKER &&
                        $unit->getType() != Genesis_Entity_StorageSpace::TYPE_WINE) {

                    if ($unit->getDriveUp() == 1) {
                         $unitDetails[$i]['driveUp'] = true;
                    } else {
                        $unitDetails[$i]['driveUp'] = false;
                    }

                    if ($unit->getVehicleStorageOnly()) {
                        $unitDetails[$i]['vehicle'] = 'only';
                    } elseif ($unit->getVehicle() == 1) {
                        $unitDetails[$i]['vehicle'] = 'yes';
                    } else {
                        $unitDetails[$i]['vehicle'] = false;
                    }
                }

                //covered does not apply to wine or locker
                if ($unit->getType() != Genesis_Entity_StorageSpace::TYPE_LOCKER &&
                        $unit->getType() != Genesis_Entity_StorageSpace::TYPE_WINE) {
                    $unitDetails[$i]['covered'] = $unit->getCovered();
                }

                //do grouped version only stuff
                if ($showGrouped) {
                    $unitDetails[$i]['numRentable'] = $unit->getGroupedNumAvailable();
                }

                //if sitelink, then add the sitelink unit type from sitelink_full_units
                if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SITELINK) {
                        $slFullUnit = Genesis_Service_SitelinkFullUnit::loadById($unit->getId());
                        $unitDetails[$i]['classType'] = $slFullUnit->getSitelinkType();
                        $unitDetails[$i]['unitName'] = $slFullUnit->getSitelinkUnitName();
                } elseif ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_CENTERSHIFT4
                            || $facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_CENTERSHIFT4_LEADS360) {
                        $csUnit = Genesis_Service_Centershift4Unit::loadById($unit->getId());
                        $unitDetails[$i]['classType'] = $csUnit->getClassDesc();
                        $unitDetails[$i]['unitName'] = $csUnit->getUnitNumber();
                } elseif ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) {
                        $ssmUnit = Genesis_Service_SelfStorageManagerUnit::loadById($unit->getId());
                        $unitDetails[$i]['classType'] = $ssmUnit->getUnitTypeId();
                        $unitDetails[$i]['unitName'] = $ssmUnit->getUnitNumber();
                }

                $unitDetails[$i]['specials'] = $unit->getSpecials();

                $i++;
            }

            usort($unitDetails, array($this, 'sortSquareFootage'));

            if(isset($unitDetails))

                return $unitDetails;
        }
    }

    public static function isBlacklisted($actionName) {
        switch ($actionName) {
            case 'add':
            case 'type':
            case 'index':
            case 'addsummary':
            case 'deleteimage':
            case 'defaultimage':
            case 'update_facility':
            case 'selectionhandler':
                return true;
        }

        return false;
    }

    //"deletes" a special - remove it from account_specials
    //params: id - special id
    public function deleteSpecialAction() {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {

            $unitRows = json_decode($this->getparam('unit_rows'));
            $rootType = $this->getparam('root_type');

            // for each group of units (or single unit if not applicable)
            foreach ($unitRows as $unitRow) {
                // for each unit
                foreach ($unitRow->unitIds as $unitId) {
                    //not every unit in the batch will have every type, from a mixed batch of units, but handle it nicely
                    if (! Genesis_Service_UnitSpecial::hasRootType($unitId, $rootType)) {
                        //but still put it in the output, since the ajax all expects it and will freak out
                        $updatedUnitIds[$unitId] = $unitRow->unitIndex;
                        continue;
                    }
                    if (! Genesis_Service_UnitSpecial::deleteByRootType($unitId, $rootType)) {
                        throw new Exception('Could not delete unit specials');
                    }
                    $updatedUnitIds[$unitId] = $unitRow->unitIndex;
                }
            }

            $updatedUnitRows = array();
            foreach ($updatedUnitIds as $unitId => $unitIndex) {
                $unit = Genesis_Service_StorageSpace::loadById($unitId);
                $updatedUnitRows[] = array(
                                        'unitIndex' => $unitIndex,
                                        'regularPrice' => number_format($unit->getRegularPrice(), 2),
                                        'effectivePrice' => number_format($unit->getEffectivePrice(), 2),
                                        'specialString' => $unit->buildSpecialString(),
                                        'discountString' => $unit->buildDiscountString(),
                                     );
            }

            $output = array(
                'success' => 1,
                'unitRows' => $updatedUnitRows,
            );
        } catch (Exception $e) {
            $output = array(
                'success' => 0,
                'message' => $e->getMessage()
            );
        }

        echo json_encode($output);
    }

    //gets a mock special string based on inputs (on da fly)
    public function getSpecialStringPreviewAction() {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {

            $special = $this->_buildCustomSpecialFromParams();

            $output = array(
                'success' => 1,
                'message' => $special->getString()
            );
        } catch (Exception $e) {
            $output = array(
                'success' => 0,
                'message' => $e->getMessage()
            );
        }

        echo json_encode($output);
    }

    private function sortSquareFootage($a, $b) {
        if ($a['squareFootage'] == $b['squareFootage']) {
            return 0;
        }

        return ($a['squareFootage'] < $b['squareFootage']) ? -1 : 1;
    }
}
