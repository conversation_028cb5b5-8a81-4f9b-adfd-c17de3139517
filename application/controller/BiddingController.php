<?php
/*
 * Bidding Controller
 *
 * @copyright 2010 SpareFoot Inc
 * <AUTHOR>
 */

class BiddingController extends AccountMgmt_Controller_Restricted
{

    /**
     * Initialize controller
     */
    protected function _init()
    {
        $this->view->facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        if (! Genesis_Service_Feature::isActive(
            'genesis.unit_level_bidding',
            ['facility_id'=>$this->view->facility->getId(), 'account_id'=>$this->view->facility->getAccountId()])) {
            $this->redirect('features');
        }
    }

    public function indexAction()
    {
        $this->view->units = $this->view->facility->getGroupedUnits();
        
        if ($this->getParam('submit')) {
            
            $bidAmounts = [];
            foreach ($this->view->units as $unit) {
                
                //echo "Saving bid for ".$unit->getId()." as ".."<br/>\n";
                
                $bidAmount = $this->get<PERSON>ara<PERSON>('bid_amount_'.$unit->getId());
                if ($bidAmount) {
                    
                    if (! is_numeric($bidAmount)) {
                        throw new Exception('Invalid bid amount: '.$bidAmount);
                    }
                    
                    if ($bidAmount < Genesis_Entity_StorageSpace::MINIMUM_UNIT_BID_AMOUNT) {
                        throw new Exception('Bid cannot be less than '.Genesis_Entity_StorageSpace::MINIMUM_UNIT_BID_AMOUNT);
                    }
                    
                    $unit->setUnitBidAmount($bidAmount);
                    Genesis_Service_StorageSpace::save($unit);
                }
                
                $bidAmounts[] = $unit->getUnitBidAmount();
            }
            
            if (count($bidAmounts) > 0) {
                $averageBidAmount = array_sum($bidAmounts) / count($bidAmounts);
            } else {
                $averageBidAmount = 0;
            }
            
            echo "Setting new facility average bid to: ".$averageBidAmount."<br/>\n";
            $this->view->facility->setBidFlat($averageBidAmount);
            Genesis_Service_Facility::save($this->view->facility);
            
        }
        
    }

}
