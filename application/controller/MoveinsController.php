<?php

use AccountMgmt_Models_ApiException as ApiException;

/**
 * Class MoveinsController
 *
 * This is for contactless & online move-ins, not to be confused with the "MoveIn" controller
 * used for reconciliation.
 */
class MoveinsController extends AccountMgmt_Controller_Restricted
{
    /** @var Genesis_Util_ActionLogger */
    private $logger;

    /**
     * Initialize controller
     */
    protected function _init()
    {
        if (! Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::CONTACTLESS_MOVEIN_BADGING, [])) {
            $this->redirect('dashboard');
        }
        $this->logger = new Genesis_Util_ActionLogger();

        $this->view->banner = [
            'showNotificationBanner' => AccountMgmt_Models_BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount())
        ];
    }

    /**
     * Index Action
     */
    public function indexAction()
    {
        $this->redirect($this->view->url(['action' => 'contactless'], 'contactless-move-ins'));
    }

    public function contactlessAction() {
        $user = $this->getLoggedUser();
        $facilities = $user->getManagableFacilities()->toArray();
        $publishedFacilities = array_values(array_filter($facilities, function(Genesis_Entity_Facility $f) {
            return $f->getPublished();
        }));
        $contactlessFacilitiesView = array_map(function(Genesis_Entity_Facility $f) {
            return [
                'name' => $f->getName(),
                'id' => $f->getId(),
                'contactless' => $f->getHasContactlessMoveins()
            ];
        }, $publishedFacilities);

        // set variables to be used in the View
        $this->view->title = 'Contactless Badge';
        $this->view->hasOnlineMoveInFmsSoftware = AccountMgmt_Service_User::hasAccessToOmiCapableFms();
        $this->view->facilities = $contactlessFacilitiesView;
        $this->view->scripts = [
            '../dist/ember/features/assets/vendor',
            '../dist/ember/features/assets/features'
        ];
    }

    public function updateContactlessAction() {
        if ($this->_request->isPost()) {
            $user = $this->getLoggedUser();
            $jsonBody = $this->getRequest()->getRawBody();
            $decodedJson = json_decode($jsonBody);

            if (!$decodedJson) {
                throw new ApiException(ApiException::BAD_REQUEST);
            }

            $facilities = $decodedJson->facilities;
            $accessibleFacilities = $user->getManagableFacilities()->toArray();
            $accessibleFacilityIds = array_map(function($f) { return $f->getId(); }, $accessibleFacilities);

            foreach ($facilities as $facility) {
                if(!in_array($facility->id, $accessibleFacilityIds)) {
                    throw new ApiException(
                        ApiException::FORBIDDEN,
                        "You do not have permission to update this facility: {$facility->id}"
                    );
                }
            }

            foreach ($facilities as $facility) {
                try {
                    $facilityObj = Genesis_Service_Facility::loadById($facility->id);
                    $facilityObj->setHasContactlessMoveins(boolval($facility->checked));
                    $facilityObj = Genesis_Service_Facility::save($facilityObj);

                    $this->logger->logAction(
                        'toggle_contactless_facility',
                        intval($facility->originalValue),
                        intval($facility->checked),
                        $user->getId(),
                        $facility->id
                    );
                } catch (Exception $e) {
                    throw new AccountMgmt_Models_ApiException(AccountMgmt_Models_ApiException::NOT_ACCEPTABLE, $e->getMessage());
                }
            }

            $this->_helper->json(['success' => true]);
        }
    }

    public function onlineAction() {
        $user = $this->getLoggedUser();
        $facilities = $user->getManagableFacilities()->toArray();
        $publishedOmiCapableFacilities = array_values(array_filter($facilities, function(Genesis_Entity_Facility $f) {
            return ($f->getPublished() && $f->hasOmiCapableFms());
        }));
        $omiFacilitiesView = array_map(function(Genesis_Entity_Facility $f) {
            return [
                'name' => $f->getName(),
                'id' => $f->getId(),
                'online' => $f->getHasOnlineMoveins()
            ];
        }, $publishedOmiCapableFacilities);

        // set variables to be used in the View
        $this->view->title = 'Online Move-Ins';
        $this->view->hasOnlineMoveInFmsSoftware = AccountMgmt_Service_User::hasAccessToOmiCapableFms();
        $this->view->facilities = $omiFacilitiesView;
        $this->view->scripts = [
            '../dist/ember/features/assets/vendor',
            '../dist/ember/features/assets/features'
        ];
    }

    public function updateOnlineAction() {
        if ($this->_request->isPost()) {
            $user = $this->getLoggedUser();
            $jsonBody = $this->getRequest()->getRawBody();
            $decodedJson = json_decode($jsonBody);

            if (!$decodedJson) {
                throw new ApiException(ApiException::BAD_REQUEST);
            }

            $facilities = $decodedJson->facilities;
            $accessibleFacilities = $user->getManagableFacilities()->toArray();
            $accessibleFacilityIds = array_map(function($f) { return $f->getId(); }, $accessibleFacilities);

            foreach ($facilities as $facility) {
                if(!in_array($facility->id, $accessibleFacilityIds)) {
                    throw new ApiException(
                        ApiException::FORBIDDEN,
                        "You do not have permission to update this facility: {$facility->id}"
                    );
                }
            }

            foreach ($facilities as $facility) {
                try {
                    $facilityObj = Genesis_Service_Facility::loadById($facility->id);
                    $facilityObj->setHasOnlineMoveins(boolval($facility->checked));
                    $facilityObj = Genesis_Service_Facility::save($facilityObj);

                    $this->logger->logAction(
                        'toggle_facility_online_moveins',
                        intval($facility->originalValue),
                        intval($facility->checked),
                        $user->getId(),
                        $facility->id
                    );
                } catch (Exception $e) {
                    throw new ApiException(ApiException::NOT_ACCEPTABLE, $e->getMessage());
                }
            }

            $this->_helper->json(['success' => true]);
        }
    }

    protected function getTab()
    {
        return self::TAB_MOVE_INS;
    }
}
