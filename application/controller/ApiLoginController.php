<?php
use AccountMgmt_Models_ApiException as ApiException;
use <PERSON>refoot\Authorization\SDK\Token;
class ApiLoginController extends AccountMgmt_Controller_ApiBaseController
{
    public function init() {
        parent::init();
    }

    public function indexAction()
    {
        if (! $this->_request->isPost() && ! $this->_request->isGet()) {
            throw new ApiException(ApiException::NOT_IMPLEMENTED);
        }
        if (! $this->_getParam('email')) {
            throw new ApiException(ApiException::BAD_REQUEST, 'Email is required');
        }
        if (! $this->_getParam('password')) {
            throw new ApiException(ApiException::BAD_REQUEST, 'Password is required');
        }
        try {
            AccountMgmt_Service_UserOauth::authenticate(
                $this->_getParam('email'),
                $this->_getParam('password')
            );
            $this->_helper->json(['success'=>true]);
        } catch (Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
        }
    }

    public function logoutAction()
    {
        AccountMgmt_Service_UserOauth::logout();
        $this->_helper->json(['success'=>true]);
    }

    public function renewAction()
    {
        try {
            AccountMgmt_Service_UserOauth::renew();
        } catch (Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }
        $this->_helper->json(['success'=>true]);
    }

    public function tokenAction()
    {
        $t = AccountMgmt_Service_UserOauth::getToken();
        if (! $t) {
            throw new ApiException(ApiException::PRECONDITION_FAILED, 'no token exists, maybe you need to sign in?');
        }
        $remainingSeconds = $t->getExpires() - time();
        $days = floor($remainingSeconds / (60 * 60 * 24));
        $remainingSeconds = $remainingSeconds % (60 * 60 * 24);
        $hours = floor($remainingSeconds / (60 * 60));
        $remainingSeconds = $remainingSeconds % (60 * 60);
        $minutes = floor($remainingSeconds / 60);
        $seconds = $remainingSeconds % 60;
        $response = [
            'data' => [
                'token' => [
                    'identifier' => $t->getIdentifier(),
                    'domain' => $t->getDomain(),
                    'host' => $t->getHost(),
                    'expires' => $t->getExpires()
                ],
                'meta' => [
                    'human_expires' => date('Y-m-d H:i:s', $t->getExpires()),
                    'needs_renew' => AccountMgmt_Service_UserOauth::needsRenew(),
                    'ttl' => [
                        'days' => $days,
                        'minutes' => $minutes,
                        'hours' => $hours,
                        'seconds' => $seconds,
                    ]
                ]
            ]
        ];
        $this->_helper->json($response, JSON_PRETTY_PRINT);
    }

    public function userAction()
    {
        $user = AccountMgmt_Service_UserOauth::getUser();
        if (! $user) {
            throw new ApiException(ApiException::PRECONDITION_FAILED, 'must be signed in');
        }
        $response = [
            'data' => [
                'email' =>  $user->getEmail(),
                'first_name' => $user->getFirstName(),
                'last_name' =>  $user->getLastName(),
                'account_id' => $user->getAccountId(),
                'myfoot_role' => $user->getMyfootRole(),
                'user_id' => $user->getUserId(),
                'restricted_to_facility_ids' => $user->getRestrictedToFacilityIds()
            ]
        ];
        $this->_helper->json($response, JSON_PRETTY_PRINT);
    }

    public function cookieAction()
    {
        $cookieClass = new AccountMgmt_Service_UserCookie();
        $reflection = new ReflectionClass($cookieClass);

        foreach ($reflection->getConstants() as $constantKey => $constantValue) {
            $data[$constantValue] = AccountMgmt_Service_UserCookie::get($constantValue, 'not set');
        }
        $this->_helper->json(['data'=>$data]);
    }

    /**
     * this is for testing renew. it sets the TTL down in cookie
     * so that a subsequent request gets renewed
     * so manually go to /api/login/token after and see the ttl back at 14 days
     *
     * @throws AccountMgmt_Models_ApiException
     */
    public function prenewAction()
    {
        $user = AccountMgmt_Service_UserOauth::getUser();
        if (! $user) {
            throw new ApiException(ApiException::PRECONDITION_FAILED, 'must be signed in');
        }
        //manually mess with the cookie
        $rawtoken = AccountMgmt_Service_UserCookie::get(AccountMgmt_Service_UserOauth::AUTH_BEARER_TOKEN);
        $token = Token::createFromRaw($rawtoken);
        if (! $token) {
            throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, 'did not assemble token from cookie');
        }
        $token->setExpires(strtotime("tomorrow"));
        AccountMgmt_Service_UserCookie::set(AccountMgmt_Service_UserOauth::AUTH_BEARER_TOKEN, $token->__toString());

        $this->_helper->json(['success'=>1]);
        //now check
        //$this->redirect($this->view->url(['action'=>'token']))
    }
}
