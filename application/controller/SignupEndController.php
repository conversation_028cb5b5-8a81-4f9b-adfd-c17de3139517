<?php

/**
 * SignupEndController
 *
 * @copyright 2013 SpareFoot
 * <AUTHOR>
 */
class SignupEndController extends AccountMgmt_Controller_Restricted
{
    public const SIGNUP_END_CSRF_TOKEN = 'signup_end_csrf_token';

    protected function _init()
    {
        $this->_helper->layout->setLayout('signup-layout');
        //give the layout what action we're on (for knowing what step)
        $this->view->action = $this->getParam('action');
        $this->view->loggedUser = $this->getLoggedUser();
    }

    /**
     * collect company details
     */
    public function termsAction()
    {
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_END_CSRF_TOKEN);
        $user = $this->getLoggedUser();
        $account = $user->getAccount();
        $termsVersion = Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION;

        if (
            ($account->getBidType() === Genesis_Entity_Account::BID_TYPE_PERCENT
                || $this->getParam('agree1', false) === 'on'
            ) &&
            $this->getParam('agree2', false) === 'on' &&
            $this->getParam('agree3', false) === 'on' &&
            $this->getParam('agree4', false) === 'on'
        ) {

             if (!CsrfUtil::validateToken(self::SIGNUP_END_CSRF_TOKEN, $this->getParam('csrf_token'))) {
                $this->view->error = "There was an error during  the submission. Please refresh and try again.";
             } else {
                // Save terms info
                $account->setTermsVersion($termsVersion);
                $account->setTermsAgreedByUserId($user->getId());
                $account->setTermsAgreedDate(date('Y-m-d H:i:s'));

                Genesis_Service_Account::save($account);

                $this->redirect('/signup-end/billing');
             }
        }

        // Make sure the user can go back to the other controller
        $this->getSession()->userId = $user->getId();

        $this->view->scripts = array('signup-end/terms');

        $this->view->backlink = '/signup-start/company/';

        $this->view->termsVersion = $termsVersion;

        $this->view->account = $account;
        $this->view->bidType = $account->getBidType();
        $this->view->agree1 = $account->getTermsVersion() ? 1 : $this->getParam('agree1', false);
        $this->view->agree2 = $account->getTermsVersion() ? 1 : $this->getParam('agree2', false);
        $this->view->agree3 = $account->getTermsVersion() ? 1 : $this->getParam('agree3', false);
        $this->view->agree4 = $account->getTermsVersion() ? 1 : $this->getParam('agree4', false);
    }

    public function billingAction()
    {
        $this->view->backlink = '/signup-end/terms/';
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_END_CSRF_TOKEN);

        $user = $this->getLoggedUser();

        $this->view->user = $user;
        $account = Genesis_Service_Account::loadById($user->getAccountId());
        $this->view->accountId = $account->getAccountId();

        $this->view->completed = "false";

        $this->view->beId = 0;
        $this->view->emails = $user->getEmail();
        $this->view->paymentTypeNickname = "";
        $this->view->address = "";
        $this->view->city = "";
        $this->view->state = "";
        $this->view->zip = "";

        $this->view->creditCardNumber = "";
        $this->view->ccType = ""; //VISA, etc
        $this->view->ccNsId = "";
        $this->view->ccName = "";
        $this->view->ccExpM = "";
        $this->view->ccExpY = "";

        //pass field for prepop if needed
        $be = Genesis_Service_BillableEntity::loadByAccount($account)->uniqueResult();

        //if there is already a billable entity for this account, then signal view to notify user they're done
        if ($be) {
            $this->view->completed = 'true';
        }

        $this->view->scripts = array('signup-end/billing');
    }

    public function softwareAction()
    {
        $user = $this->getLoggedUser();
        $this->view->myFootLink = AccountMgmt_Models_Util::getMyFootLandingPage();

        // TODO: Why are we loading this?
        $account = Genesis_Service_Account::loadById($user->getAccountId());

        /* TODO: actually save which software integrations were reported here */
        /* plus hide/show the add facility form based on whether it will be a manual integration or not */

        $this->view->scripts = array('signup-end/software');
        $this->view->backlink = '/signup-end/billing';
    }

    public function recordSoftwareAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            $user = $this->getLoggedUser();
            $account = Genesis_Service_Account::loadById($user->getAccountId());

            // record their software types
            if (is_array($this->getParam('integration_type'))) {
                foreach ($this->getParam('integration_type') as $key => $val) {
                    $software = new Genesis_Entity_AccountSoftware();
                    $software->setAccountId($account->getId());
                    $software->setSourceId($val);
                    $software->setUserId($user->getId());
                    Genesis_Service_AccountSoftware::save($software);
                }
            }

            if ($this->getParam('facility_name')) {

                //make the 1st facility
                $corp = Genesis_Service_Corporation::loadByAcctIdAndSourceId(
                        $account->getAccountId(), Genesis_Entity_Source::ID_MANUAL);

                if (!$corp) {
                    $corp = new Genesis_Entity_ManualCorporation();
                    $corp->setAccountId($account->getAccountId());
                    $corp->setCreated(date("Y-m-d H:i:s", time()));
                    $corp->setCorpname($account->getName());
                    $corp->setSourceId(Genesis_Entity_Source::ID_MANUAL);
                    $corp = Genesis_Service_Corporation::save($corp);
                }

                $facility = new Genesis_Entity_Facility();
                $facility->setTitle($this->getParam('facility_name'));
                $facility->setCorporationId($corp->getId());
                $facility->setSourceId($corp->getSourceId());
                $facility->setActive(0);
                $facility->setPublished(1);
                $facility->setApproved(1);
                $facility->setPhone($this->getParam('phone'));

                $location = Genesis_Service_Location::loadByAddress($this->getParam('address1'),
                            $this->getParam('city'),
                            $this->getParam('state'),
                            $this->getParam('zip'));

                //does this location already exist?
                if (!$location) {
                    //call geocoder
                    $location = Genesis_Service_Location::geoCodePhysicalAddress($this->getParam('address') . " " . $this->getParam('city') . " " . $this->getParam('state') . " " . $this->getParam('zip'));
                    $location = Genesis_Service_Location::save($location);
                }

                $facility->setLocationId($location->getId());

                Genesis_Service_Facility::save($facility, $user);
            }

            echo json_encode(array('success' => true));

        } catch (Exception $e) {
             Genesis_Util_ErrorLogger::exceptionToHipChat($e);
             echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        }
    }

    //gets the address to put in the address field when user checks
    //"billing address is same as company address"
    public function sameCoAddressAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            $user = $this->getLoggedUser();
            $account = Genesis_Service_Account::loadById($user->getAccountId());

            if ($account && $account->getLocation()) {
                $address = array();
                $address['name'] = $account->getName();
                $address['addr'] = $account->getLocation()->getAddress1();
                $address['city'] = $account->getLocation()->getCity();
                $address['state'] = $account->getLocation()->getState();
                $address['zip'] = $account->getLocation()->getZip();
                $address['phone'] = $user->getPhone();

                echo json_encode($address);
            } else {
                echo json_encode(array('success' => false, 'message' => 'No corporate address on file.'));
            }
        } catch (Exception $e) {
            Genesis_Util_ErrorLogger::exceptionToHipChat($e);
            echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        }
    }

}
