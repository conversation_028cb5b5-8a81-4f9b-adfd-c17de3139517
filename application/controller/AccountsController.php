<?php
/*
 * Accounts Controller
 *
 * @copyright 2013 SpareFoot
 * <AUTHOR>
 */

use AccountMgmt_Models_FormValidationException as FormValidationException;

class AccountsController extends AccountMgmt_Controller_Restricted
{
    public function indexAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->view->accounts = Genesis_Dao_Account::selectBriefAccountsForMenu();
    }

    public function downloadTermsAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        $tosData = $this->getLoggedUser()->getAccount()->retrieveTOSData();

        header('Content-Disposition: attachment; filename="Sparefoot_terms.pdf"');

        $this->getResponse()
            ->setHeader('Content-Length', $tosData->BodyLength)
            ->setHeader('Content-Type', $tosData->ContentType)
            ->setBody($tosData->Body);
    }

    public function updatetermsAction() {
        // Disable views (we'll be returning JSON)
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        try {
            $termsVersion = $this->getParam('terms_version', Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION);
            $agreed = $this->getParam('agreed', false);

            if ($agreed !== 'agreed') {
                throw new FormValidationException('Must agree to the new Terms of Service.');
            }

            if ($this->getLoggedUser()->getMyfootRole() === Genesis_Entity_UserAccess::ROLE_GOD
                && ! $this->getLoggedUser()->getAccount()->getTestAccount()
            ) {
                throw new FormValidationException('You cannot agree to the terms on this account.');
            }

            // Save terms version info
            $account = $this->getLoggedUser()->getAccount();
            $account->setTermsVersion($termsVersion);
            $account->setTermsAgreedByUserId($this->getLoggedUser()->getId());
            $account->setTermsAgreedDate(date('Y-m-d H:i:s'));
            Genesis_Service_Account::save($account);

            $response = ['success' => true];
        } catch (FormValidationException $e) {
            http_response_code(400);
            $response = ['formError' => $e->getMessage()];
        } catch (Exception $e) {
            http_response_code(400);
            $response = ['error' => $e->getMessage()];
        } finally {
            header('Content-Type: application/json');
            echo json_encode($response);
        }
    }

}
