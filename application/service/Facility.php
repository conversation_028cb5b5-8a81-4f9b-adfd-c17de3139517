<?php

/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 4/22/15
 * Time: 2:33 PM
 */
use Sparefoot\SalesForce\Client;
use Sparefoot\SalesForce\SalesForceClientException;

class AccountMgmt_Service_Facility {
    public static function hasReconciled(Genesis_Entity_Facility $facility) {
        $bi = Genesis_Service_BillableInstance::load(Genesis_Db_Restriction::equal('facilityId',$facility->getId()));
        return ($bi->valid());
    }

    /*
       Note: $facility->getGroupedUnits() does not have a way to get published and both inactive and active units
       Though this could be done query side, it would mean having to create another function that traversed all the way down to the sql layer, duplicating a lot of logic
       Touching the current sql class (sql/StorageSpace::getGroupedByFacilityPublished()), seemed too risky because we don't know where else that logic is used
     */
    public static function getPublishedGroupedUnits(Genesis_Entity_Facility $facility) {
        $units = $facility->getGroupedUnits(true);
        $publishedUnits = [];
        /**
         * @var $unit Genesis_Entity_StorageSpace
         */
        foreach($units as $unit) {
            if($unit->getPublish()) {
                $publishedUnits[] = $unit;
            }
        }
        return $publishedUnits;
    }

    public static function getAmenities(Genesis_Entity_Facility $facility) {
        $amenities = $facility->getAmenitiesComprehensive();

        $return = [];

        foreach($amenities as $key=>$amenity) {
            if ($amenity && get_class($amenity) == 'Genesis_Entity_FacilityData') {
                $hasAttr = $amenity->getHasAttribute();
                $label = $amenity->getAttribute();
                $value = $amenity->getValue();
            } elseif ($amenity && is_object($amenity)) {
                $hasAttr = $amenity->hasAttribute;
                $label = $amenity->attribute;
                $value = $amenity->value;
            } elseif ($amenity === NULL) {
                $hasAttr = NULL;
                $label = NULL;
                $value = NULL;
            }

            if ($value == 1 || $value == '1') {
                $value = true;
            }
            if ($hasAttr && $value === NULL) {
                $value = true;
            }
            if (! $hasAttr || $value == '0') {
                $value = false;
            }
            if ($hasAttr === NULL) {
                $value = NULL;
            }
            if(AccountMgmt_Service_Util::isJson($value) || AccountMgmt_Service_Util::isJsonArray($value)) {
                $value = json_decode($value);
            }
            $key = AccountMgmt_Service_Util::toSnakeCase($key);
            if (! in_array($key,self::getAmenitiesExclusions())) {
                $return[$key] = $value;
            }
        }
        return $return;
    }

    public static function getHideReasonParams() {
        $hide_reason_params = [
            'hide_facility_reason',
            'hide_facility_unhappy_because' ,
            'hide_facility_new_owner',
            'hide_facility_how_full'
        ];
        return $hide_reason_params;
    }

    /* Removes these keys from the amenities list that returns. These are here because we moved them to the root level json*/
    public static function getAmenitiesExclusions() {
        return [
            'alternate_address',
            'appointment_only_office_hours',
            'tenant_connect_sms_number',
            'automatic_reactivation_date'
        ];
    }

    public static function serializeOfficeHours(Genesis_Entity_Facility $facility) {
        //Makes the key lowercase
        $return = [];
        $appointmentOnlyDays = $facility->getAppointmentOnlyOfficeHours();
        $officeHours = $facility->getOfficeHours();
        if ($officeHours) { //may not be hours set
            $hours = $officeHours->getHours();

            foreach ($hours as $day => $hours) {
                $day = strtolower($day);

                $return[$day] = $hours;
                $return[$day]['appointment_only'] = (bool)$appointmentOnlyDays->$day;
            }
        }
        if(empty($return)) {
            return null;
        }
        return $return;
    }

    public static function serializeAccessHours(Genesis_Entity_Facility $facility) {
        //TODO COMBINE THIS AND serializeOfficeHours into one function
        $return = [];
        //Makes the key lowercase
        $officeHours = $facility->getAccessHours();
        if ($officeHours) { //many not have hours
            $hours = $officeHours->getHours();

            foreach ($hours as $day => $hours) {
                $return[strtolower($day)] = $hours;
            }
        }
        if(empty($return)) {
            return null;
        }
        return $return;
    }

    public static function toArrayWithFields(Genesis_Entity_Facility $facility, $fields = []) {
        $response = [
            'id' => $facility->getId()
        ];

        foreach ($fields as $field) {
            $getter = 'get'. ucfirst(AccountMgmt_Service_Util::snakeToCamel($field));
            $fieldValue = $facility->$getter();

            if ($field === 'active') {
                $fieldValue = (bool) $fieldValue;
            }

            $response[$field] = $fieldValue;
        }
        return $response;
    }

    public static function toArray(Genesis_Entity_Facility $facility) {
        $response = [
            'id' => $facility->getId(),
            'type' => $facility->getType(),
            'source_id' => $facility->getSourceId(),
            'corporation_id' => $facility->getCorporationId(),
            'title' => $facility->getTitle(),
            'company_code' => $facility->getCompanyCode(),
            'description' => $facility->getDescription(),
            'phone' => $facility->getPhone(),
            'cell_phone' => $facility->getTenantConnectSMSNumber(),
            'active' => (bool) $facility->getActive(),
            'published' => (bool) $facility->getPublished(),
            'admin_fee' => $facility->getAdminFee(),
            'num_reviews' => $facility->getNumReviews(),
            'average_review' => $facility->getAverageReview(),
            'service_area' => $facility->getServiceArea(),
            'service_zipcodes' => $facility->getServiceZipcodes(),
            'url' => $facility->getUrl(),
            'logo' => $facility->getLogo()
        ];

        $closures = array();
        foreach ($facility->getUpcomingClosures() as $facClsObj) {

            $closures[] = array('closureId' => $facClsObj->getId(),
                'closedDateStart' => $facClsObj->getClosedDateStart(),
                'closedDateEnd' => $facClsObj->getClosedDateEnd(),
                'dateString' => $facClsObj->getDisplayDate()
            );
        }

        $response['closures'] = $closures;

        $reactivation_date = $facility->getAutomaticReactivationDate();
        if( ! $reactivation_date) {
            $response['automatic_reactivation_date'] = null;
        } else {
            $response['automatic_reactivation_date'] = date('c',strtotime($reactivation_date));
        }

        if( ! $response['cell_phone']) {
            $response['cell_phone'] = null;
        }

        //Location
        $location = $facility->getLocation();

        $response['location'] = [
            'address1' => $location->getAddress1(),
            'address2' => $location->getAddress2(),
            'city' => $location->getCity(),
            'state' => $location->getState(),
            'zip' => $location->getZip(),
            'full_zip' => $location->getFullZip(),
            'latitude' => $location->getLatitude(),
            'longitude' => $location->getLongitude(),
            'real_address' => $location->getRealAddress(),
            'country' => $location->getCountry()
        ];

        //Alternate Address
        $alt_address = $facility->getAltAddress();
        if(! $alt_address) {
            $alt_address = NULL;
        }
        $response['alternate_address'] = $alt_address;

        //Rating
        $response['rating'] = [
            'overall' => $facility->getOverallRating(),
            'value' => $facility->getValueRating(),
            'security' => $facility->getSecurityRating(),
            'cleanliness' => $facility->getCleanlinessRating(),
            'paperwork' => $facility->getPaperworkRating()
        ];

        //Discounts
        $response['discounts'] = [
            'military' => $facility->getMilitaryDiscount(),
            'senior' => $facility->getSeniorDiscount(),
            'student' => $facility->getStudentDiscount()
        ];

        //Amenities
        $amenities = AccountMgmt_Service_Facility::getAmenities($facility);
        $response['amenities'] = $amenities;

        //photos
        $response['photos'] = self::serializeFacilityPhotos($facility);

        /**
         * @var $image Genesis_Entity_FacilityImage
         */



        //Hours
        $response['office_hours'] = self::serializeOfficeHours($facility);
        $response['access_hours'] = self::serializeAccessHours($facility);

        //Reservation Window
        $response['reservation_window_rules'] = [];
        /**
         * @var $rule Genesis_Entity_ReservationWindowRule
         **/
        foreach ($facility->getReservationWindowRules() as $rule) {
            $response['reservation_window_rules'][] = [
                'occupancy_type' => $rule->getOccupancyType(),
                'occupancy_value' => $rule->getOccupancyValue(),
                'reservation_window_days' => $rule->getReservationWindowDays()
            ];
        };

        //Hide Reasons
        $response['hide_reason'] = self::serializeFacilityHideReason($facility);

        return $response;
    }

    public static function toJson(\Genesis_Entity_Facility $facility) {
        return json_encode(self::toArray($facility));
    }

    public static function serializeFacilityPhotos(\Genesis_Entity_Facility $facility) {
        $return = [];
        foreach ($facility->getImages() as $image) {
            $return[] = self::serializeFacilityPhoto($image);
        }
        return $return;
    }

    public static function serializeFacilityPhoto(Genesis_Entity_FacilityImage $photo) {
        return [
            'id' => $photo->getPhotoId(),
            'filename' => $photo->getPictureFilename(),
            'small' => $photo->getCdnSmall(),
            'medium' => $photo->getCdnMedium(),
            'large' => $photo->getCdnLarge(),
            'timestamp' => $photo->getPictureTimestamp(),
            'md5check' => $photo->getMd5Chksum(),
            'picture_number' => $photo->getPictureNum()
        ];
    }

    public static function serializeFacilityHideReason(Genesis_Entity_Facility $facility) {
        $result = [];
        foreach(self::getHideReasonParams() as $reason) {
            $restriction = Genesis_Db_Restriction::and_(Genesis_Db_Restriction::equal('facilityId',$facility->getId()),Genesis_Db_Restriction::equal('param',$reason));
            $restriction = $restriction->setOrder(Genesis_Db_Order::desc('timestamp'));
            $action = Genesis_Service_ActionLog::load($restriction);
            if($action->current()) {
                $result[$reason] = $action->current()->getPostValue();
            } else {
                $result[$reason] = NULL;
            }

        }
        return $result;
    }

    /**
     * @param Genesis_Entity_Facility $facility
     * @param string $json
     * @param Genesis_Entity_UserAccess|null $user
     * @return false|string
     * @throws AccountMgmt_Models_ApiException
     * @throws SalesForceClientException
     * @throws Exception
     */
    public static function updateFromJson(
        Genesis_Entity_Facility $facility,
        $json,
        Genesis_Entity_UserAccess $user = NULL) {

        $data = json_decode($json);

        //Set all basic info from json
        foreach ($data as $field=>$value) {
            if (! in_array($field,self::getUpdateFromJsonExclusions()) && ! is_array($value) && ! is_object($value) &&
                ! array_key_exists($field,self::getUpdateFromJsonSetterExceptionMap())) {
                $setter = 'set'. ucfirst(AccountMgmt_Service_Util::snakeToCamel($field));
                $facility->$setter($value);
            }
        }

        //Set all fields that don't have direct mappings
        foreach (self::getUpdateFromJsonSetterExceptionMap() as $key => $setter) {
            if(property_exists($data, $key)) {
                $facility->$setter($data->$key);
            }
        }

        //Set Active
        if(property_exists($data,'active')) {
            self::updateActive($facility,$data->active);
        }

        //Set Automatic Reactivation
        if (property_exists($data,'automatic_reactivation_date')) {
            self::updateReactivationDate($facility,$data->automatic_reactivation_date);
        }

        //Set Location
        if (property_exists($data,'location')) {
            $location_json_object = $data->location;
            //create new location
            $location = Genesis_Service_Location::loadByAddress($location_json_object->address1,
                $location_json_object->city,
                $location_json_object->state,
                $location_json_object->zip);
            //does this location already exist
            if ($location) {
                //yes, do nothing
            } else {
                if(property_exists($location_json_object,'override')) {
                    $location->setOverride($location_json_object->override);
                }
                //call geocoder
                $location = Genesis_Service_Location::geoCodePhysicalAddress($location_json_object->address1 . " " . $location_json_object->city . " " . $location_json_object->state . " " . $location_json_object->zip);
                $location = Genesis_Service_Location::save($location);
            }
            $facility->setLocationId($location->getId());
            $facility->setLocation($location);
        }

        //Set alternate address
        if (property_exists($data,'alternate_address')) {
            $facility->setAltAddress($data->alternate_address);
        }
        //Set Discounts
        if (property_exists($data,'discounts')) {
            foreach ($data->discounts as $discount => $value) {
                $setter = "set" . ucfirst($discount) . "Discount";
                $facility->$setter($value);
            }
        }

        //Amenities...........
        if(property_exists($data,'amenities')) {
            $amenities_obj = $data->amenities;
            $map = self::getAmenetiesJsonKeyToSetterMap();
            foreach($map as $json_key => $setter) {
                if(property_exists($amenities_obj,$json_key)) {
                    $facility->$setter($amenities_obj->$json_key);
                }
            }
            $facility = self::setPaymentTypesFromJson($facility,$json);
            $facility = self::setVehicleAmenitiesFromJson($facility,$json);
        }

        //Office Hours
        if(property_exists($data,'office_hours')) {
            $facility = self::setHoursFromJson($facility,$json,Genesis_Entity_FacilityHours::HOURS_TYPE_OFFICE);
        }
        if(property_exists($data,'access_hours')) {
            $facility = self::setHoursFromJson($facility,$json,Genesis_Entity_FacilityHours::HOURS_TYPE_ACCESS);
        }

        //Reservation Rules
        if(property_exists($data,'reservation_window_rules')) {
            $rules = [];
            foreach($data->reservation_window_rules as $rule) {
                $rules[$rule->occupancy_value] = $rule->reservation_window_days;
            }
            ksort($rules);
            //TODO This always saves as occupanct_type_percent for now, change when needed
            Genesis_Service_ReservationWindowRule::saveRules(
                $facility->getAccountId(),
                $facility->getId(),
                null,
                Genesis_Entity_ReservationWindowRule::OCCUPANCY_TYPE_PERCENT,
                $rules,
                AccountMgmt_Service_User::getLoggedUser()->getId()
            );
            Genesis_Service_ReservationWindowRule::applyRulesByFacilityId($facility->getId());
        }

        //Hide Reason
        if (property_exists($data, 'hide_reason')) {
            $hideReason = $data->hide_reason;

            // Create ticket if customer is unhappy
            $reasonUnhappy = $hideReason->hide_facility_unhappy_because;
            if ($reasonUnhappy) {
                self::createUnhappyFacilityTicket($facility, $reasonUnhappy, $user);
            }

            // or sold the facility
            $reasonNewOwner = $hideReason->hide_facility_new_owner;
            if ($reasonNewOwner) {
                self::createChangeOfOwnershipTicket($facility, $reasonNewOwner, $user);
            }

            self::updateHideReasons($facility, $hideReason);
        }
        try {
            $facility = Genesis_Service_Facility::save($facility);
        } catch (Exception $e) {
            throw new AccountMgmt_Models_ApiException(AccountMgmt_Models_ApiException::NOT_ACCEPTABLE, $e->getMessage());
        }

        // Service Zipcodes
        if(property_exists($data, 'service_zipcodes')) {
            $facility->setServiceZipcodes($data->service_zipcodes);
        }

        return AccountMgmt_Service_Facility::toJson($facility);
    }

    /**
     * @param array $ticketOptions
     * @param int $facilityId
     * @throws SalesForceClientException
     */
    private static function createSalesForceTicket(array $ticketOptions, int $facilityId) {

        // Not frequently used (hopefully), so create a client per update
        $salesForceClient = new Client();
        $salesForceClient->createTicket($ticketOptions, $facilityId);
    }

    /**
     * @param Genesis_Entity_Facility $facility
     * @param string $reason
     * @param Genesis_Entity_UserAccess $user
     * @throws SalesForceClientException
     */
    public static function createUnhappyFacilityTicket(
        Genesis_Entity_Facility $facility,
        string $reason,
        Genesis_Entity_UserAccess $user)
    {
        if ($reason && $facility) {

            // For list of keys: https://developer.salesforce.com/docs/api-explorer/sobject/Case
            $ticketOptions = array(
                // Owner ID set in SalesForce Client
                'Type' => 'Question',
                'Origin' => 'Internal',
                'Status' => 'New',
                'Subject' => $facility->getName() . ' - Hidden Facility Notice',
                'Priority' => 'Low',

                'SuppliedName' => $user->getFirstName() . " " . $user->getLastName(),
                'SuppliedEmail' => $user->getEmail(),
                'SuppliedPhone' => $user->getPhone(),
                'SuppliedCompany' => $facility->getName(),

                'Description' => 'Facility Name: ' . $facility->getName() . PHP_EOL
                    . 'Facility Address: ' . $facility->getLocation()->stringAddress() . PHP_EOL
                    . 'Facility ID: ' . $facility->getUniqueId() . PHP_EOL
                    . PHP_EOL
                    . 'Reason: ' . $reason . PHP_EOL
                    . PHP_EOL
                    . 'Email:' . $user->getEmail() . PHP_EOL
                    . PHP_EOL
                    . 'mysparefoot_hidden-facility_R7F8yQ'
            );

            self::createSalesForceTicket($ticketOptions, $facility->getId());
        }
    }

    /**
     * @param Genesis_Entity_Facility $facility
     * @param string $reason
     * @param Genesis_Entity_UserAccess $user
     * @throws SalesForceClientException
     */
    public static function createChangeOfOwnershipTicket(
        Genesis_Entity_Facility $facility,
        string $reason,
        Genesis_Entity_UserAccess $user)
    {
        if ($reason && $facility) {

            // For list of keys: https://developer.salesforce.com/docs/api-explorer/sobject/Case
            $ticketOptions = array(
                // Owner ID set in SalesForce Client
                'Type' => 'Question',
                'Origin' => 'Internal',
                'Status' => 'New',
                'Subject' => $facility->getName() . ' - Change of Ownership Notice',
                'Priority' => 'Low',

                'SuppliedName' => $user->getFirstName() . " " . $user->getLastName(),
                'SuppliedEmail' => $user->getEmail(),
                'SuppliedPhone' => $user->getPhone(),
                'SuppliedCompany' => $facility->getName(),

                'Description' => 'Facility Name: ' . $facility->getName() . PHP_EOL
                    . 'Facility Address: ' . $facility->getLocation()->stringAddress() . PHP_EOL
                    . 'Facility ID: ' . $facility->getUniqueId() . PHP_EOL
                    . PHP_EOL
                    . 'Reason: This facility is no longer owned/managed by my company.' . PHP_EOL
                    . 'New Owner Information: ' . $reason . PHP_EOL
                    . PHP_EOL
                    . 'Email:' . $user->getEmail() . PHP_EOL
                    . PHP_EOL
                    . 'mysparefoot_facility-sale_VHcv5B'
            );

            self::createSalesForceTicket($ticketOptions, $facility->getId());
        }
    }

    public static function getUpdateFromJsonSetterExceptionMap() {
        return [
            'cell_phone' => 'setTenantConnectSMSNumber'
        ];
    }

    /* Fields that either should not be updatable via API or have custom method for updating */
    public static function getUpdateFromJsonExclusions() {
        return [
            'id',
            'location',
            'alternate_address',
            'hidden_reasons',
            'active'
        ];
    }

    public static function updateReactivationDate(Genesis_Entity_Facility $facility, $isoDateString) {
        if ($isoDateString === null || $isoDateString === '' || $isoDateString === false) { //can be null too
            $facility->setAutomaticReactivationDate(null);
            return $facility;
        }

        if ( ! AccountMgmt_Service_Util::isValidIsoDate($isoDateString)) {
            throw new \Exception('Invalid reactivation date format. Must be ISO 8601, got \''.print_r($isoDateString,1).'\'');
        }
        $date = AccountMgmt_Service_Util::convertISODate($isoDateString);
        $reactivation_date = $date->format('Y-m-d');
        $facility->setAutomaticReactivationDate($reactivation_date);
        return $facility;
    }

    public static function updateActive(Genesis_Entity_Facility $facility, $active) {
        if ($active) {
            $facility->setAutomaticReactivationDate(0);
        }
        $facility->setActive($active);
        return $facility;
    }

    public static function updateHideReasons(Genesis_Entity_Facility $facility,stdClass $reasons) {
        $hide_reasons = self::getHideReasonParams();
        foreach ($hide_reasons as $param) {


                $action_log = new Genesis_Entity_ActionLog();
                $action_log->setFacilityId($facility->getId());
                $action_log->setParam($param);
                $action_log->setPreValue(null);
                $action_log->setPostValue($reasons->$param);
                $action_log->setUserId(AccountMgmt_Service_User::getLoggedUser()->getId());
                $action_log->setTimestamp(date("Y-m-d H:i:s"));
                Genesis_Service_ActionLog::save($action_log);

        }
    }

    public static function setHoursFromJson(Genesis_Entity_Facility $facility,$json,$hours_type = Genesis_Entity_FacilityHours::HOURS_TYPE_OFFICE) {
        $data = json_decode($json);
        if($hours_type == Genesis_Entity_FacilityHours::HOURS_TYPE_OFFICE){
            $property = 'office_hours';
        } elseif ($hours_type == Genesis_Entity_FacilityHours::HOURS_TYPE_ACCESS) {
            $property = 'access_hours';
        } else {
            throw new Exception('Invalid Facility Hour Type');
        }
        $appointment_only = [];
        if(property_exists($data,$property)) {
            $hours = new Genesis_Entity_FacilityHours();
            $hoursRequest = $data->$property;
            //VALIDATION
            foreach ($hoursRequest as $day => $values) {
                foreach($values as $key=>$value) {
                    if ($key != 'start' && $key != 'end' && $key != 'appointment_only') {
                        throw new Exception('Office hours improperly formatted');
                    }

                    if (($key == 'start' || $key == 'end' ) && ! (AccountMgmt_Service_Util::isValidTime($value) || $value === NULL) ) {
                        throw new Exception($day . " " . $key . " office hour time is improperly formatted");
                    }

                    if($property == 'office_hours') {
                        if($key == 'appointment_only') {
                            $appointment_only[$day] = $value;
                        }
                    }
                }

            }

            //Set all basic info from json
            //setup new office hours objects and save then,  save will update automaticaly if they already exist

            $hours->setFacilityId($facility->getId());
            $hours->setType($hours_type);
            $hours->setManualEdit(true);
            $hours->setSunStart($hoursRequest->sun->start);
            $hours->setSunEnd($hoursRequest->sun->end);
            $hours->setMonStart($hoursRequest->mon->start);
            $hours->setMonEnd($hoursRequest->mon->end);
            $hours->setTueStart($hoursRequest->tue->start);
            $hours->setTueEnd($hoursRequest->tue->end);
            $hours->setWedStart($hoursRequest->wed->start);
            $hours->setWedEnd($hoursRequest->wed->end);
            $hours->setThuStart($hoursRequest->thu->start);
            $hours->setThuEnd($hoursRequest->thu->end);
            $hours->setFriStart($hoursRequest->fri->start);
            $hours->setFriEnd($hoursRequest->fri->end);
            $hours->setSatStart($hoursRequest->sat->start);
            $hours->setSatEnd($hoursRequest->sat->end);

            if($hours_type == Genesis_Entity_FacilityHours::HOURS_TYPE_OFFICE){
                if ( ! empty($appointment_only)) {
                    $facility->setAppointmentOnlyOfficeHours($appointment_only);
                }
                $facility->setOfficeHours($hours);
            } elseif ($hours_type == Genesis_Entity_FacilityHours::HOURS_TYPE_ACCESS) {
                $facility->setAccessHours($hours);
            }
            return $facility;
        } else {
            return;
        }
    }

    private static function setVehicleAmenitiesFromJson(Genesis_Entity_Facility $facility,$json) {
        $data = json_decode($json);
        $amenities_obj = $data->amenities;
        $vehicleData = $facility->getVehicleStorage();

        if(property_exists($amenities_obj,'vehicle_requires_title')) {
            $vehicleRequireTitle = $amenities_obj->vehicle_requires_title;
        } else {
            $vehicleRequireTitle = $vehicleData[0];
        }

        if(property_exists($amenities_obj,'title_must_match_renter')) {
            $vehicleRenterTitled = $amenities_obj->title_must_match_renter;
        } else {
            $vehicleRenterTitled = $vehicleData[1];
        }

        if(property_exists($amenities_obj,'vehicle_must_be_running')) {
            $vehicleRequireRunning = $amenities_obj->vehicle_must_be_running;
        } else {
            $vehicleRequireRunning = $vehicleData[2];
        }

        if(property_exists($amenities_obj,'vehicle_requires_insurance')) {
            $vehicleRequireInsurance = $amenities_obj->vehicle_requires_insurance;
        } else {
            $vehicleRequireInsurance = $vehicleData[3];
        }

        if(property_exists($amenities_obj,'vehicle_requires_registration')) {
            $vehicleRequireRegistration = $amenities_obj->vehicle_requires_registration;
        } else {
            $vehicleRequireRegistration = $vehicleData[4];
        }

        $facility->setVehicleStorage(
            $vehicleRequireTitle,
            $vehicleRenterTitled,
            $vehicleRequireRunning,
            $vehicleRequireInsurance,
            $vehicleRequireRegistration
        );

        return $facility;
    }

    private static function setPaymentTypesFromJson(Genesis_Entity_Facility $facility,$json) {
        $data = json_decode($json);
        $amenities_obj = $data->amenities;
        $options = $facility->getPaymentOptions();

        if(property_exists($amenities_obj,'cash_accepted')) {
            $paymentAcceptCash = $amenities_obj->cash_accepted;
        } else {
            $paymentAcceptCash = $options[0];
        }

        if(property_exists($amenities_obj,'checks_accepted')) {
            $paymentAcceptCheck = $amenities_obj->checks_accepted;
        } else {
            $paymentAcceptCheck = $options[1];
        }

        if(property_exists($amenities_obj,'credit_cards_accepted')) {
            $paymentAcceptCredit = $amenities_obj->credit_cards_accepted;
        } else {
            $paymentAcceptCredit = $options[2];
        }

        if(property_exists($amenities_obj,'accepted_credit_cards')) {
            $paymentAcceptedCards = $amenities_obj->accepted_credit_cards;
        } else {
            $paymentAcceptedCards = $options[3];
        }

        $facility->setPaymentOptions(
            $paymentAcceptCash,
            $paymentAcceptCheck,
            $paymentAcceptCredit,
            $paymentAcceptedCards
        );
        return $facility;
    }

    public static function getAmenetiesJsonKeyToSetterMap() {
        /* TODO WTF are these fields? Can't find them in the db associated to any facilities, nor in the front end edits
        for amenities

        'for_specific_units'
        'for_business_users_only'
        'for_additional_$%s_per_month'
        'with_background_check'
        'with_manager_approval'
        'rent due on 1st of each month'
        'rent_due_on_same_day_each_month_per_lease_start'
        'rent_due_as_per_contract'
        'local_disaster_free_first_month'
         */


        return [
            '24_hour_access' => 'setTwentyFourHourAccess',
            'free_use_of_truck' => 'setFreeTruckRental',
            'truck_rental_available' => 'setTruckRental',
            'loading_dock'=> 'setTruckAccess',
            'truck_access_size'=> 'setTruckAccessSize',
            'video_cameras_on_site' => 'setSurveillance',
            'electronic_gate_access' => 'setEGateAccess',
            'fenced_and_lighted' => 'setFencedLighted',
            'elevator_available' => 'setElevator',
            'handcarts_available' => 'setHandcarts',
            '24_hour_kiosk_service' => 'setKiosk',
            '24_hour_access_restrictions_apply' => 'setTwentyFourHourAccessRestricted',
            '24_hour_access_type' => 'setTwentyFourHourAccessSupplemental',
            '24_hour_access_fee' => 'setTwentyFourHourAccessFee',
            'insurance_available' => 'setInsuranceAvailable',
            'insurance_required' => 'setInsuranceRequired',
            'protection_plan_available' => 'setProtectionPlanAvailable',
            'protection_plan_required' => 'setProtectionPlanRequired',
            'homeowners/renters_insurance_accepted' => 'setHomeownersInsuranceAccepted',
            'military_discount_percent' => 'setMilitaryDiscount',
            'military_discount_applies_to_reserves' => 'setMilitaryDiscountAppliesToReserves',
            'military_discount_applies_to_veterans' => 'setMilitaryDiscountAppliesToVeterans',
            'student_discount_percent' => 'setStudentDiscount',
            'senior_discount_percent' => 'setSeniorDiscount',
            'email_invoicing_available' => 'setEmailInvoicingAvailable',
            'auto_pay_available' => 'setAutoPayAvailable',
            'accept_tenant_mail' => 'setAcceptTenantMail',
            'truck_rental_mileage_limit' => 'setTruckDistanceLimit',
            'truck_rental_insurance' => 'setTruckInsurance',
            'truck_refueled_by_tenant' => 'setTruckRefuelPolicy',
            'sells_boxes_or_other_moving_supplies' => 'setSellsMovingSupplies',
            'bilingual_manager_language' => 'setBilingualManager',
            'resident_manager' => 'setResidentManager',
            'charge_date' => 'setChargeDate',
            'security_deposit' => 'setSecurityDeposit',
            'security_deposit_refundable' => 'setSecurityDepositRefundable',
            'alternate_address' => 'setAltAddress',
            'security_deposit_type' => 'setSecurityDepositType',
            'security_deposit_dollar' => 'setSecurityDepositDollar',
            'security_deposit_percent' => 'setSecurityDepositPercent',
            'tenant_connect_override_phone_number' => 'setTenantConnectOverridePhone',
            'tenant_connect_sms_number' => 'setTenantConnectSMSNumber',
            'wash_station_available' => 'setWashStationAvailable',
            'dump_station_available' => 'setDumpStationAvailable',
            'new_facility_id' => 'setNewFacilityId',
            'band_practice_allowed' => 'setBandPracticeAllowed',
            'paperwork_can_be_done_remotely' => 'setRemotePaperwork',
            'last_tenant_sync_date' => 'setLastTenantSyncDate',
            'last_inventory_update_date' => 'setLastInventoryUpdateDate',
            'allow_18_wheeler_dropoff' => 'setAllow18WheelerDropoff',
            'has_18_wheeler_alleys' => 'setHas18WheelerAlleys',
            'onsite_office_at_facility' => 'setOnsiteOfficeAtFacility',
            'observed_holidays' => 'setObservedHolidays'
        ];
    }

    public static function getAllUnitsArray(Genesis_Entity_Facility $facility) {
        if (! $facility->getPublished()) {
           return [];
        }
        if ($facility->canGroupUnits()) {
           $units = $facility->getGroupedUnits();
        } else {
           $units = $facility->getUnits();
        }
        $response = [];
        foreach ($units as $unit) {
           $response[] = AccountMgmt_Service_Unit::toArray($unit);
        }
        return $response;
    }

    public static function savePhotosFromUpload(Genesis_Entity_Facility $facility) {
        if (! $facility->getPublished()) {
           throw new Exception('images cannot be added to unpublished facilities');
        }
        $results = [];
        $maxsize=5242880;
        $fileCount = count($_FILES['photos']['name']);
        $savedImages = [];
        for ($i = 0; $i < $fileCount; $i++) {

           if ($_FILES['photo']['size'][$i] < $maxsize) {
               $extension = explode('.', $_FILES['photos']['name'][$i]);
               $extension = $extension[count($extension) - 1];

               $facilityId = $facility->getId();

               $savedImage = null;

               try {
                   /** @var Genesis_Entity_FacilityImage $savedImage */
                   $savedImage = Genesis_Service_FacilityImage::append($facilityId, $extension, $_FILES['photos']['tmp_name'][$i]);
                   $savedImages[] = $savedImage;
               } catch (Exception $e) {
                   throw new Exception('This image cannot be uploaded. Please upload another image.');
               }

               $loggedInUser = AccountMgmt_Service_User::getLoggedUser();

               if ($loggedInUser->isMyFootGod()) {
                   // if uploaded by a god user, automatically set it as reviewed
                   $savedImage->setReviewed(1);
               } else {
                   $savedImage->setReviewed(0);
               }

               $savedImage->setUploadedByUserId($loggedInUser->getUserId());

               $savedImage = Genesis_Service_FacilityImage::save($savedImage);

           } else {
               throw new Exception('Image too big. Uploads must be smaller than 5 MB.');
           }
        }
        return $savedImages;
    }

    /**
     * only verfifies the unit is valid. does not do ACL!!
     * @param $facilityId
     * @return Genesis_Entity_Facility
     * @throws Exception
    */
    public static function validateFacilityId($facilityId) {
        if (! $facilityId) {
            throw new Exception('facility_id is required');
        }
        $facility = Genesis_Service_Facility::loadById($facilityId);
        if (! $facility) {
            throw new Exception('no such facility');
        }
        if (! $facility->getPublished()) {
            throw new Exception('facility is not published on SF network');
        }

        return $facility;
    }

    public static function validatePhotoId($photoId, $facility = null) {
        if (! $photoId) {
            throw new Exception('photo_id is required');
        }
        $photo = Genesis_Service_FacilityImage::loadById($photoId);
        if (! $photo) {
            throw new Exception('invalid photo');
        }
        //throws E
        self::validateFacilityId($photo->getFacilityId());

        //does it match what we passed?
        if ($facility && $photo->getFacilityId() !== $facility->getId()) {
            throw new Exception('Photo Id does not exist for this facility');
        }
        return $photo;
    }

    public static function serializeFacilityBookings(Genesis_Entity_Facility $facility,$startDate = null,$endDate = null,$params = []) {
        if($startDate == NULL) {
            $startDate = date('Y-m-01', strtotime('-2 months'));
        }

        if ($endDate === NULL) {
            $endDate = date('Y-m-d', time()+86400); //End date by default set as tomorrow
        }

        if (array_key_exists('use_statement_date',$params)) {
            if($params['use_statement_date'] == 'true') {
                //Look for most recent statemnt
                $latestStatementBatch = Genesis_Service_StatementBatch::loadByHighestId();
                if($latestStatementBatch->getStatus() == Genesis_Entity_StatementBatch::STATUS_OPEN) {
                    $startDate = date( 'Y-m-d', strtotime ($latestStatementBatch->getStartDate()));
                } else {
                    $startDate = date('Y-m-01');
                }
            }
        }

        $restriction = Genesis_Db_Restriction::and_(
            Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
            Genesis_Db_Restriction::between('timestamp', $startDate, $endDate));

        if (array_key_exists('booking_state',$params)) {
            $booking_states = explode(",",$params['booking_state']);
            $restriction = Genesis_Db_Restriction::and_(
                $restriction,
                Genesis_Db_Restriction::in('bookingState',$booking_states)
            );
        }

        if (array_key_exists('customer_state',$params)) {
            $params['customer_state'] = strtoupper($params['customer_state']);
            $customer_states = explode(",",$params['customer_state']);

            $customer_state_restrictions = [];
            if(in_array(AccountMgmt_Service_Booking::VERIFIED_STATE_NEW,$customer_states)) {

                $customer_state_restrictions[] = Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::isNull('autoState'),
                    Genesis_Db_Restriction::equal('bookingState',Genesis_Entity_Transaction::BOOKING_STATE_PENDING)
                );
            }

            if(in_array(AccountMgmt_Service_Booking::VERIFIED_STATE_MOVED_IN,$customer_states)) {
                $customer_state_restrictions[] = Genesis_Db_Restriction::or_(
                    Genesis_Db_Restriction::equal('autoState',Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED),
                    Genesis_Db_Restriction::equal('bookingState',Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                );
            }

            if(in_array(AccountMgmt_Service_Booking::VERIFIED_STATE_MOVED_OUT,$customer_states)) {
                $customer_state_restrictions[] = Genesis_Db_Restriction::lessThanOrEqual('moveOut',date('Y-m-d'));
            } else {
                //This is a special case, because we need to make sure to disclude moveouts when filtering for other types
                $restriction = Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::or_(
                        Genesis_Db_Restriction::greaterThan('moveOut',date('Y-m-d')),
                        Genesis_Db_Restriction::isNull('moveOut')
                    ),
                    $restriction
                );
            }

            if(in_array(AccountMgmt_Service_Booking::VERIFIED_STATE_DID_NOT_MOVE_IN,$customer_states)) {
                $customer_state_restrictions[] = Genesis_Db_Restriction::or_(
                    Genesis_Db_Restriction::equal('autoState',Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED),
                    Genesis_Db_Restriction::equal('bookingState',Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED)
                );
            }

            $full_customer_state_restriction = null;
            $i = 0;
            foreach($customer_state_restrictions as $customer_state_restriction) {

                if($i == 0) {
                    $full_customer_state_restriction = $customer_state_restriction;
                }
                $full_customer_state_restriction = Genesis_Db_Restriction::or_(
                    $customer_state_restriction,
                    $full_customer_state_restriction
                );
                $i++;
            }

            if($full_customer_state_restriction !== NULL) {
                $restriction = Genesis_Db_Restriction::and_(
                    $restriction,
                    $full_customer_state_restriction
                );
            }
        }

        $restriction = $restriction->setOrder(Genesis_Db_Order::desc('timestamp'));
        $reservations = Genesis_Service_Transaction::load($restriction)->toArray();

        if(array_key_exists('include_current_tenants',$params)) {
            $restriction =
                Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::equal('facilityId',$facility->getId()),
                    Genesis_Db_Restriction::or_(
                    //CUSTOMER STATUS MOVED IN
                        Genesis_Db_Restriction::equal('autoState',Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED),
                        Genesis_Db_Restriction::equal('bookingState',Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                    ),
                    Genesis_Db_Restriction::or_(
                    //CUSTOMER STATUS IS NOT MOVED OUT
                        Genesis_Db_Restriction::greaterThan('moveOut',date('Y-m-d')),
                        Genesis_Db_Restriction::isNull('moveOut')
                    )
                );

            $restriction = $restriction->setOrder(Genesis_Db_Order::desc('timestamp'));
            $current_tenants = Genesis_Service_Transaction::load($restriction)->toArray();
            $reservations = array_merge($reservations,$current_tenants);
        }

        if(array_key_exists('include_late_move_ins',$params)) {
            $late_days_max = Genesis_Entity_Statement_Cpa::LATE_DAYS_MAX;
            $late_days_min = Genesis_Entity_Statement_Cpa::LATE_DAYS_MIN;

            /**
             * @var $latestStatementBatch Genesis_Entity_StatementBatch
             */
            $latestStatementBatch = Genesis_Service_StatementBatch::loadByHighestId();
            if($latestStatementBatch->getStatus() == Genesis_Entity_StatementBatch::STATUS_OPEN) {

                $dtFirstDay = date( 'Y-m-d', strtotime ( '-' . $late_days_max . ' day'. $latestStatementBatch->getStartDate() ) );
                $dtLastDay = date( 'Y-m-d', strtotime ( '-' . $late_days_min . ' day' . $latestStatementBatch->getStartDate() ) );
            } else {
                $first_of_the_month = date('Y-m-01');

                $dtFirstDay = date( 'Y-m-d', strtotime ( '-' . $late_days_max . ' day' . $first_of_the_month ) );
                $dtLastDay = date( 'Y-m-d', strtotime ( '-' . $late_days_min . ' day' . $first_of_the_month) );
            }

            $includeStates = array(Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED, Genesis_Entity_Transaction::BOOKING_STATE_CANCELLED);
            $restriction = Genesis_Db_Restriction::and_(
                Genesis_Db_Restriction::between("moveIn", $dtFirstDay, $dtLastDay),
                Genesis_Db_Restriction::in("bookingState", $includeStates),
                Genesis_Db_Restriction::equal('facilityId',$facility->getId()),
                Genesis_Db_Restriction::not(Genesis_Db_Restriction::equal('free', 1))
            );
            $late_move_ins = Genesis_Service_Transaction::load($restriction)->toArray();
            $reservations = array_merge($reservations,$late_move_ins);
        }

        $reservations_array = [];
        foreach($reservations as $reservation) {
             //Use confirmation code as key so we can filter out duplicates
             $reservations_array[$reservation->getConfirmationCode()] = AccountMgmt_Service_Booking::toArray($reservation);
        }

        //Unset the keys so we get a flat json structure
        $reservations_array = array_values($reservations_array);
        return $reservations_array;
    }

    public static function serializeFacilityInquiries(Genesis_Entity_Facility $facility, $startDate = null, $endDate = null) {
        if ($endDate === NULL) {
            $endDate = date('Y-m-d', time()+86400); //End date by default set as tomorrow
        }
        $inquiries = [];
        foreach (Genesis_Service_ConsumerContact::loadUniqueByFacilityIdAndDateRange($facility->getId(), $startDate, $endDate) as $inquiry) {
            $inquiries[] = AccountMgmt_Service_Inquiry::toArray($inquiry);
        }
        return $inquiries;
    }

    public static function serializeFacilityReviews(Genesis_Entity_Facility $facility) {
        $facilityReviews = $facility->getFacilityReviews();
        $reviews = [];
        foreach($facilityReviews as $review) {
            $reviews[] = AccountMgmt_Service_Review::toArray($review);
        }
        return $reviews;
    }
}
