<?php
/**
 * @Author: k<PERSON>gham
 * @Date:   2015-10-29 10:47:31
 */

class AccountMgmt_Service_UserAuthByBooking
{
    const HEADER = 'X-AUTH-BOOKING';
    const SALT = 'y6uh8`HFYp}+gwb%feXT=&T8o=r.-[bO5iPaH~N@';
    const VALID_FOR_DAYS = 30;

    private static $_userId = null;

    public static function getUserId()
    {
        if (self::$_userId === null) {
            // Check if token is in the header?
            $payload = self::_getAuthByBookingHeader();
            if ($payload) {
                $secret = $payload->secret;
                $confirmationCode = $payload->booking;
                $email = $payload->email;
            }

            // Check if token is in the params?
            $paramsSet = self::_areParamsSet();
            if ($paramsSet) {
                $request = Zend_Controller_Front::getInstance()->getRequest();
                $secret = $request->getParam('s');
                $confirmationCode = $request->getParam('confirmation_code');
                $email = $request->getParam('email');
            }

            if ($secret && $confirmationCode && $email) {
                if (self::isSecretValid($secret, $confirmationCode, $email)) {
                    $user = Genesis_Service_User::loadByEmail($email);
                    if ($user) {
                        self::$_userId = $user->getId();
                    }
                }
            }
        }

        return self::$_userId;
    }

    public static function serializeToken($secret, $bookingConfirmationCode, $email)
    {
        $pieces = [
            'secret' => $secret,
            'booking' => $bookingConfirmationCode,
            'email' => $email
        ];
        return json_encode($pieces);
    }

    public static function logout()
    {
        self::$_userId = null;
    }

    private static function isSecretValid($secret, $confirmationCode, $email)
    {
        $result = false;

        // Today + 1 day for time drift
        $date = (new DateTime())->modify('+1 day');

        for ($i=0; $i <= self::VALID_FOR_DAYS + 1; $i++) {
            $time = $date->format('Y-m-d');
            $hash = md5($confirmationCode . self::SALT . $time . $email);

            if ($hash == $secret) {
                $result = true;
                break;
            }

            $date->modify('-1 day');
        }

        return $result;
    }

    private static function _areParamsSet()
    {
        $result = false;

        $request = Zend_Controller_Front::getInstance()->getRequest();
        $secret = $request->getParam('s');
        $confirmationCode = $request->getParam('confirmation_code');
        $email = $request->getParam('email');

        if ($secret && $confirmationCode && $email) {
            $result = true;
        }

        return $result;
    }

    private static function _getAuthByBookingHeader()
    {
        $payload = Zend_Controller_Front::getInstance()->getRequest()->getHeader(self::HEADER);
        if ($payload) {
            return json_decode($payload);
        } else {
            return false;
        }
    }
}