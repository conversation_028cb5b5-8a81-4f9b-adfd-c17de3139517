<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 4/22/15
 * Time: 2:33 PM
 */


class AccountMgmt_Service_Statement {

    public static function getBanners($statement_id,$facility_id = null) {
        return [];
    }

    public static function getUserFacilitiesByStatementId($statement_id) {
        $facilitiesRestriction = Genesis_Db_Order::ASC('title');
        $facilities = AccountMgmt_Service_User::getLoggedUser()->getManagableFacilities($facilitiesRestriction,true);
        $facilities_with_bookings = array();
        foreach ($facilities as $facility) {
            $bookings = Genesis_Service_BillableInstance::loadBookingsByFacilityStatementId($facility->getId(), $statement_id);
            if (count($bookings) > 0) {
                $facilities_with_bookings[] = $facility;
            }
        }
        return $facilities_with_bookings;
    }

    /**
     * Utility to determine if the batch associated with a statement currently has an OPEN status.
     *
     * @param $statement_id The statement id.
     * @return bool True is the statement batch associated with the provided statement id is currently OPEN.
     */
    public static function isStatementBatchOpen($statement_id) {
        $statement = Genesis_Service_Statement::loadById($statement_id);
        $statementBatchId = $statement->getStatementBatchId();
        $batch = Genesis_Service_StatementBatch::loadById($statementBatchId);
        return $batch->getStatus() == Genesis_Entity_StatementBatch::STATUS_OPEN;
    }

    private static function _getManualStatementAccountBanners($statement_id,$facility_id) {
        $facility_to_display = null;
        $banners = [];
        //Display only if we are in April, May, or June
        $current_month = date('n');
        if(in_array($current_month,[4,5,6])) {
            if($facility_id) {
                //if Facility ID in context, use that one
                $facility = Genesis_Service_Facility::loadById($facility_id);
                if($facility->getSourceId() == Genesis_Entity_Source::ID_MANUAL) {
                    $facility_to_display = $facility;
                }
            } else {

                $facilities = self::getUserFacilitiesByStatementId($statement_id);
                /**
                 * @var $facility Genesis_Entity_Facility
                 */
                foreach($facilities as $facility) {

                    if($facility->getSourceId() == Genesis_Entity_Source::ID_MANUAL) {
                        $facility_to_display = $facility;
                    }
                }
            }
            //return the banner
            if($facility_to_display) {
                //TODO FIX THE ACTION LINK AFTER NEW UI IS RELEASED
                $banners[] = [
                    "message"=>"It's almost summer! Is your inventory up to date?",
                    "action_title"=>"Check now",
                    "action_link"=>"/facility/inventory/" . $facility_to_display->getId(),
                    "id" => "inventory_update"
                ];
            }
        }
        return $banners;
    }
}
