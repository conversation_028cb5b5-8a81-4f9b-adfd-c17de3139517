<?php

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 7/7/15
 * Time: 9:47 AM
 */
class AccountMgmt_Service_UserCookie
{
    const REMEMBER_ME_EMAIL     = 'remember_me_email';
    const USER_REDIRECT         = 'redirect_url';
    const ACTIVE_ACCOUNT_ID     = 'active_account_id';
    const ACTIVE_FACILITY_ID    = 'active_facility_id';
    const AUTH_BEARER_TOKEN     = 'auth_bearer_token';

    protected static $hostOnly = [
        self::AUTH_BEARER_TOKEN => 1
    ];

    private static $_latches = [];

    public static function get($name, $default = null)
    {
        if (array_key_exists($name, self::$_latches)) {
            return self::$_latches[$name];
        }
        return Zend_Controller_Front::getInstance()->getRequest()->getCookie($name, $default);
    }

    public static function set($name = null, $value = null, $expire = null, $path = null)
    {
        if (!$expire) {
            $expire = time() + 86400 * 30;
        }
        if (!$path) {
            $path = "/";
        }
        if (self::hostOnly($name)) {
            setcookie($name, $value, $expire, $path);
        } else {
            setcookie($name, $value, $expire, $path, str_replace("my", "", $_SERVER['SERVER_NAME']));
        }

        self::$_latches[$name] = $value;
    }

    public static function clear($name)
    {
        if (self::hostOnly($name)) {
            setcookie($name, '', 1, "/");
        } else {
            setcookie($name, '', 1, "/", str_replace("my", "", $_SERVER['SERVER_NAME']));
        }
        self::$_latches[$name] = false;
    }

    private static function hostOnly($cookieKey)
    {
        return isset(self::$hostOnly[$cookieKey]);
    }
}
