<?php

class AccountMgmt_Service_IntegratedFields
{
    /**
     * Contains the map of fields from Quicktagger (keys) to the MyFoot Form fields (values)
     *
     * @static
     * @var array
     * IMPORTANT: Not all of the fields have been mapped
     *
     * If you need a reference, look at https://sparefoot.atlassian.net/browse/SF-5698
    */
    public static $mappedFields = [
        // START: For unit (single and grouped) edit
        'active' => 'active',
        'adaAccessible' => 'ada',
        'airCooledOnly' => 'aircooled',
        'alarm' => 'alarm',
        'availTypeOfSpaceId' => 'unit_type',
        'basement' => 'basement',
        'climateControlled' => 'climate',
        'covered' => 'covered',
        'doorType' => 'door_type',
        'driveUp' => 'driveup',
        'floor' => 'floor',
        'heatedOnly' => 'heated',
        'height' => 'unit_height',
        'humidityControlled' => 'humidity',
        'length' => 'unit_length',
        'lightInUnit' => 'unitlights',
        'outdoorAccess' => 'outdoor_access',
        'parkingWarehouse' => 'parkingwarehouse',
        'power' => 'power',
        'premiumUnit' => 'premium',
        'pullThrough' => 'pullthru',
        'quantity' => 'qty',
        'regularPrice' => 'price_regular',
        'shelvesInUnit' => 'shelvesinunit',
        'skybox' => 'stacked',
        'sparefootPrice' => 'price_sf',
        'specialString' => 'promotion',
        'twentyFourHourAccess' => 'twentyfourhouraccess',
        'vehicle' => 'vehicle',
        'width' => 'unit_width',
        // END: For unit (single and grouped) edit

        // START: Deprecated
        'approved' => 'approved',
        'doorHeight' => 'door_height',
        'doorLength' => 'doorLength',
        'doorWidth' => 'door_width',
        // END: Deprecated

        // START: Facility details
        'deposit' => 'security_deposit_required',
        // END: Facility details

        // START: Amenities
        'description' => 'facility_description',
        // END: Amenities
        // url won't behave like an integrated input
        'url' => 'url',
        // END: Facility details

        // Unknown
        'type' => 'type',
        'unitName' => 'unitName',

        // Not being used anymore / are not related to MyFoot
        'publish' => 'publish',
        'upstairsAccessViaElevator' => 'upstairsAccessViaElevator',
        'upstairsAccessViaLift' => 'upstairsAccessViaLift',
        'upstairsAccessViaStairs' => 'upstairsAccessViaStairs',
        'vehicleStorageOnly' => 'vehicleStorageOnly',

        // Not part of MyFoot forms
        'externalId' => 'externalId',
        'listingAvailId' => 'listingAvailId',
        'listingAvailSpaceId' => 'listingAvailSpaceId',
        'accountId' => 'accountId'
    ];

    /**
     * Returns a list of fields that are filled by the external software integration
     * Integrated fields are NOT editable in MyFoot because the FMS is "Source of Truth"
     * These fields are set via Omnom/Phido and Fields from QuickTagger
     *
     * @static
     * @return array List of integrated fields | null
     * @var array
     */
    // If you add a new integration or update the fields, add/update the unit test:
    // tests/UnitTests/models/DisabledIntegratedFieldsTest.php
    public static function getBaseIntegratedFields (int $integrationSourceId) {
        $integrations = [
            Genesis_Entity_Source::ID_SITELINK => array_unique(
                array_map(
                    function ($item) {
                        return AccountMgmt_Service_IntegratedFields::$mappedFields[$item];
                    },
                    [
                        'accountId',
                        'adaAccessible',
                        'alarm',
                        'approved',
                        'availTypeOfSpaceId',
                        'climateControlled',
                        'floor',
                        'length',
                        'listingAvailId',
                        'listingAvailSpaceId',
                        'outdoorAccess',
                        'power',
                        'publish',
                        'quantity',
                        'regularPrice',
                        'specialString',
                        'width'
                    ]
                )
            ),
            Genesis_Entity_Source::ID_STOREDGE => array_unique(
                array_map(
                    function ($item) {
                        return AccountMgmt_Service_IntegratedFields::$mappedFields[$item];
                    },
                    [
                        'specialString',
                        'sparefootPrice',
                        'regularPrice',
                    ]
                )
            ),
            Genesis_Entity_Source::ID_ESS => array_unique(
                array_map(
                    function ($item) {
                        return AccountMgmt_Service_IntegratedFields::$mappedFields[$item];
                    },
                    [
                        'regularPrice',
                        'unitName',
                        'width',
                        'length',
                        'height',
                    ]
                )
            )
            // No Excluded Fields:
            // Genesis_Entity_Source::ID_DOORSWAP => [ ],
            // Genesis_Entity_Source::ID_CENTERSHIFT4 => [ ],
            // Genesis_Entity_Source::ID_DOMICO => [ ],
            // Genesis_Entity_Source::ID_QUIKSTOR => [ ],
            // Genesis_Entity_Source::ID_METRO => [ ],
            // Genesis_Entity_Source::ID_SAFEGUARD => [ ],
            // Genesis_Entity_Source::ID_SELFSTORAGEMANAGER => [ ],
            // Genesis_Entity_Source::ID_SENTRY => [ ],
            // Genesis_Entity_Source::ID_STORAGEMART => [ ],
        ];

        return $integrations[$integrationSourceId] ?? null;
    }

     /**
     * Gets the column name based on the integrated field name
     *
     * @param string Name of the integrated field
     *
     * @return string|null Name of the colum name based on the QuickTagger fields. If not found returns null.
     */
    public static function getDbColumnName($inputName)
    {
        $inputNames = array_flip(self::$mappedFields);

        if (in_array($inputName, array_keys($inputNames))) {
            return $inputNames[$inputName];
        }

        return null;
    }
}
