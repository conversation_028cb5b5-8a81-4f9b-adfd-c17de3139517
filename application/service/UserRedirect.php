<?php

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 7/7/15
 * Time: 9:20 AM
 */
class AccountMgmt_Service_UserRedirect
{
    const REDIRECT = AccountMgmt_Service_UserCookie::USER_REDIRECT;
    static private $_redirect = false;

    public static function needsRedirect()
    {
        if (! self::getRedirect()) {
            return false;
        }
        if (self::getRedirect() === "/") {
            self::clearRedirect();
            return false;
        }
        /**
         * @var $req Zend_Controller_Request_Http
         */
        $server = Zend_Controller_Front::getInstance()->getRequest()->getServer();
//        if ($server['REDIRECT_URL']) { //do not double redirect, and blow our payload
//            self::clearRedirect();
//            return false;
//        }
        if ($server["REQUEST_URI"] === self::getRedirect()) {
            self::clearRedirect();
            return false;
        }

        return true;
    }

    public static function setRedirect($url)
    {
        if (self::$_redirect) {
            throw new Exception("cannot set redirect url twice per http request");
        }

        self::$_redirect = $url;
        AccountMgmt_Service_UserCookie::set(self::REDIRECT, $url, '0', '/');
    }

    public static function getRedirect()
    {
        if (self::$_redirect) {
            return self::$_redirect;
        }

        if (AccountMgmt_Service_UserCookie::get(self::REDIRECT)) {
            return AccountMgmt_Service_UserCookie::get(self::REDIRECT);
        }
        return false;
    }

    public static function clearRedirect()
    {
        AccountMgmt_Service_UserCookie::clear(self::REDIRECT);
    }
}