<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 4/6/15
 * Time: 2:17 PM
 */


class AccountMgmt_Service_UserAccountFacilityContext
{
    const ACTIVE_ACCOUNT_ID = AccountMgmt_Service_UserCookie::ACTIVE_ACCOUNT_ID;
    const ACTIVE_FACILITY_ID = AccountMgmt_Service_UserCookie::ACTIVE_FACILITY_ID;
    const ACCOUNT_POST_VAR      = 'account_id';
    const FACILITY_POST_VAR     = 'fid';
    const CABINET_GOD_ACCOUNT_ID = 'myFootGodAccountId';

    private static $initMode;
    private static $userAccess;


    /**
     * getter
     * @return bool|mixed
     */
    private static function _activeAccountIdPost()
    {
        $request = Zend_Controller_Front::getInstance()->getRequest();
        $accountId = $request->getParam(self::ACCOUNT_POST_VAR, false);
        if (! $accountId > 0) {
            return false;
        }

        return $accountId;
    }

    /**
     * getter
     * @return bool|mixed
     */
    private static function _activeFacilityIdPost()
    {
        $request = Zend_Controller_Front::getInstance()->getRequest();
        $facilityId = $request->getParam(self::FACILITY_POST_VAR, false);
        if (! $facilityId > 0) {
            return false;
        }

        return $facilityId;
    }

    /**
     * getter/setter
     * @param null $accountId
     * @return mixed
     * @throws Exception
     */
    private static function _activeAccountIdCookie($accountId = null)
    {
        if (! $accountId > 0) {
            return AccountMgmt_Service_UserCookie::get(self::ACTIVE_ACCOUNT_ID, false);
        }

        //do not check cookie, just do
        AccountMgmt_Service_UserCookie::set(self::ACTIVE_ACCOUNT_ID, $accountId);
    }

    /**
     * getter/setter
     * @param null $facilityId
     * @return mixed
     * @throws Exception
     */
    private static function _activeFacilityIdCookie($facilityId = null)
    {
        if (! $facilityId > 0) {
            return AccountMgmt_Service_UserCookie::get(self::ACTIVE_FACILITY_ID, false);
        }

        //do not check cookie, just do
        AccountMgmt_Service_UserCookie::set(self::ACTIVE_FACILITY_ID, $facilityId);
    }

    /**
     * get the first facility ID
     * @param Genesis_Entity_UserAccess $userAccess
     * @return mixed
     */
    private function _getDefaultFacilityId(Genesis_Entity_UserAccess $userAccess, $accountId = null)
    {
        static $defaultFacilityId;
        if ($defaultFacilityId) {
            return $defaultFacilityId;
        }
        $restriction = Genesis_Db_Restriction::equal('published', 1);

        if ($accountId && $userAccess->isMyFootGod()) {
            $facilities = Genesis_Service_Facility::loadByAccountId($accountId, $restriction);
        } else {
            $facilities = $userAccess->getManagableFacilities($restriction);
        }

        if (! count($facilities)) {
            return false;
        }

        /**
         * @var $firstFacility Genesis_Entity_Facility
         * @var $nextFacility Genesis_Entity_Facility
         */
        $firstFacility = false;
        foreach ($facilities as $nextFacility) {
            if ($firstFacility === false || $firstFacility->getPublished() == 0) {
                $firstFacility = $nextFacility;
                continue;
            } elseif (strcasecmp($nextFacility->getTitle(), $firstFacility->getTitle()) > 0) {
                continue;
            }
            $firstFacility = $nextFacility;
        }
        return $defaultFacilityId = $firstFacility->getId();
    }

    private static function _getAccountIdForFacilityId($facilityId)
    {
        try {
            $facility = self::_facilityExists($facilityId);
            if (! $facility) {
                return false;
            }
            return $facility->getAccountId();
        } catch (\Exception $e) {
            return false;
        }
    }

    private static function _keysMatch($accountId, $facilityId)
    {
        if (! self::_accountExists($accountId)) {
            return false;
        }
        if (! self::_facilityExists($facilityId)) {
            return false;
        }
        return self::_getAccountIdForFacilityId($facilityId) === $accountId;
    }

    private static function _activeAccountIdSession($accountId = null)
    {
        if (! $accountId > 0) {
            return AccountMgmt_Service_User::getSession()->accountId;
        }

        AccountMgmt_Service_User::getSession()->accountId = $accountId;
    }

    private static function _activeFacilityIdSession($facilityId = null)
    {
        if (! $facilityId > 0) {
            return AccountMgmt_Service_User::getSession()->facilityId;
        }

        AccountMgmt_Service_User::getSession()->facilityId = $facilityId;
    }

    private function _setAllServices(Genesis_Entity_UserAccess $userAccess, $facilityId = null, $accountId = null)
    {
        if (! self::_accountExists($accountId)) {
            //fallback to default accountId (before accountId was modified)
            $accountId = $userAccess->getAccountId();
        }

        if ($userAccess->isMyFootGod()) {
            //cabinet
            self::_godAccountId($userAccess, $accountId);
            //useraccess
            $userAccess->setAccountId($accountId);

            //check for mismatch between acct and facility
            if (! self::_keysMatch($accountId, $facilityId)) {
                $facilityId = self::_getDefaultFacilityId($userAccess);
            }
        }
        //now set facility (everyone)
        //guardians
        if (! self::_facilityExists($facilityId)) {
            $facilityId = self::_getDefaultFacilityId($userAccess);
        }

        if (! in_array($facilityId, $userAccess->getManageableFacilityIds())) {
            $facilityId = self::_getDefaultFacilityId($userAccess);
        }

        //session
        self::_activeAccountIdSession($accountId);
        self::_activeFacilityIdSession($facilityId);
        self::_activeAccountIdCookie($userAccess->getAccountId());
        //cookie
        self::_activeAccountIdCookie($accountId);
        self::_activeFacilityIdCookie($facilityId);
        self::_debugExit($userAccess);
        return self::$userAccess = $userAccess;
    }

    /**
     * order is
     * - post
     * - cookie
     * - Cabinet (account only)
     * - NOT session
     *
     * @param $userId
     * @return Genesis_Entity_UserAccess
     * @throws Exception
     */
    public static function init($userId)
    {
        if (self::$initMode) {
            return self::$userAccess;
        }
        $userAccess = Genesis_Service_UserAccess::loadById($userId);
        if (! $userAccess) { //bail when failed
            throw new Exception('no user ID');
        }
        //get all possible
        $accountIdPost = self::_activeAccountIdPost();
        $facilityIdPost = self::_activeFacilityIdPost();
        $accountIdSession = self::_activeAccountIdSession();
        $facilityIdSession = self::_activeFacilityIdSession();
        $accountIdCookie = self::_activeAccountIdCookie();
        $facilityIdCookie = self::_activeFacilityIdCookie();
        $accountIdCabinet = self::_godAccountId($userAccess);

        //switch contexts to new account for account post
        if ($accountIdPost !== false && $accountIdPost != $accountIdSession) {
            self::$initMode = 'new accountId post';
            return self::_setAllServices($userAccess, null, $accountIdPost);
        }

        //switch context for new facility for facility post
        if ($facilityIdPost !== false && $facilityIdPost != $facilityIdSession) {
            self::$initMode = 'new facilityId post';
            return self::_setAllServices($userAccess, $facilityIdPost, self::_getAccountIdForFacilityId($facilityIdPost));
        }

        //double session set
        if (self::_keysMatch($accountIdSession, $facilityIdSession)) {
            self::$initMode = "double session";
            return self::_setAllServices($userAccess, $facilityIdSession, $accountIdSession);
        }

        //cookies, try both first
        if (self::_keysMatch($accountIdCookie, $facilityIdCookie)) {
            self::$initMode = 'double cookies';
            return self::_setAllServices($userAccess, $facilityIdCookie, $accountIdCookie);
        }

        //cookie fallback to account
        if (false !== $accountIdCookie) {
            self::$initMode = 'account cookie fallback';
            return self::_setAllServices($userAccess, null, $accountIdCookie);
        }

        //cookie fallback to facility
        if (false !== $facilityIdCookie) {
            self::$initMode = 'facility cookie fallback';
            return self::_setAllServices($userAccess, $facilityIdCookie, self::_getAccountIdForFacilityId($facilityIdCookie));
        }

        //cabinet
        if (false !== $accountIdCabinet) {
            self::$initMode = 'cabinet account fallback';
            return self::_setAllServices($userAccess, null, $accountIdCabinet);
        }

        //ignore session

        //brand new user to get this far
        self::$initMode = 'useraccess account init';
        return self::_setAllServices($userAccess);
    }

    private static function _accountExists($accountId)
    {
        static $loadedAccount = [];
        if (! isset($loadedAccount[$accountId])) {
            $loadedAccount[$accountId] = Genesis_Service_Account::loadById($accountId);
        }
        return $loadedAccount[$accountId];
    }

    private static function _facilityExists($facilityId)
    {
        static $loadedFacility = [];
        if (! isset($loadedFacility[$facilityId])) {
            $loadedFacility[$facilityId] = Genesis_Service_Facility::load(
                Genesis_Db_Restriction::and_(
                    Genesis_Db_Restriction::equal('id', $facilityId),
                    Genesis_Db_Restriction::equal('published', 1)
                ))->current();
        }
        return $loadedFacility[$facilityId];
    }

    /**
     * getter/setter depending on if account ID is passed
     * @param Genesis_Entity_UserAccess $userAccess
     * @param null $accountId
     * @return bool|Genesis_Entity_Cabinet|null
     */
    private static function _godAccountId(Genesis_Entity_UserAccess $userAccess, $accountId = null)
    {
        if (! $userAccess || ! $userAccess->isMyFootGod()) {
            return false;
        }
        $cabinet = Genesis_Service_Cabinet::get();
        if ($accountId) {
            // Check for valid acct
            if (! self::_accountExists($accountId)) {
                return false;
            }

            if ($cabinet->getMeta(self::CABINET_GOD_ACCOUNT_ID) == $accountId) {
                return $accountId; //skip changing a nochange
            }

            $cabinet->setMeta(self::CABINET_GOD_ACCOUNT_ID, $accountId);
            Genesis_Service_Cabinet::save($cabinet);
        }

        if (! $cabinet->getMeta(self::CABINET_GOD_ACCOUNT_ID)) {
            return false;
        }
        return $cabinet->getMeta(self::CABINET_GOD_ACCOUNT_ID);
    }

    private static function _debugExit(Genesis_Entity_UserAccess $userAccess)
    {
        return;
        $session = AccountMgmt_Service_User::getSession();
        error_log('['.self::$initMode.':'.$_SERVER["REQUEST_URI"].']');
        error_log("Fs ".$session->facilityId .' Fc '.self::_activeFacilityIdCookie().' Fp '.self::_activeFacilityIdPost());
        error_log("As ".$session->accountId .' Ac '.self::_activeAccountIdCookie().' Ap '.self::_activeAccountIdPost());

        //error_log($message);
//            self::$initMode .' '.$facilityId. ' f=' . self::_getActiveFacilityIdPost() .' a='. self::_getActiveAccountIdPost().' '.$_SERVER["REQUEST_URI"]);
       // die();
    }

    public static function clearContext()
    {
        AccountMgmt_Service_UserCookie::clear(self::ACTIVE_ACCOUNT_ID);
        AccountMgmt_Service_UserCookie::clear(self::ACTIVE_FACILITY_ID);
        AccountMgmt_Service_User::getSession()->facilityId = null;
        AccountMgmt_Service_User::getSession()->accountId = null;
    }
}
