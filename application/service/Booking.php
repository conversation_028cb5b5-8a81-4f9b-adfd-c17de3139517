<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 7/29/15
 * Time: 3:42 PM
 */

class AccountMgmt_Service_Booking
{
    const VERIFIED_STATE_NEW = "NEW";
    const VERIFIED_STATE_MOVED_IN = "MOVED_IN";
    const VERIFIED_STATE_DID_NOT_MOVE_IN = 'DID_NOT_MOVE_IN';
    const VERIFIED_STATE_MOVED_OUT = "MOVED_OUT";

    public static function toArray(Genesis_Entity_Transaction $transaction) {
        $account = $transaction->getFacility()->getAccount();
        $booking = [];

        $booking['id'] = $transaction->getConfirmationCode();
        $booking['facility_id'] = $transaction->getFacilityId();
        $booking['unit_id'] = $transaction->getUnitId();
        $booking['unit_number'] = $transaction->getUnitNumber();
        $booking['first_name']         = $transaction->getFirstName();
        $booking['last_name']          = $transaction->getLastName();
        $booking['email']             = $transaction->getUser()->getEmail();
        $booking['phone']             = $transaction->getPhone();
        $booking['monthly_rent']    = $transaction->getPrice();
        $booking['move_in_date']            = $transaction->getMoveIn();
        $booking['original_move_in_date'] = $transaction->getMoveInOriginal();
        $booking['move_out_date'] = $transaction->getMoveOut();
        $booking['deposit_paid']    = $transaction->getDepositPaid();
        $booking['bid_amount']         = $transaction->getBidAmount();
        $booking['is_free']           = $transaction->getFree();
        $booking['base_bid']           = (! empty($baseBid) ? $baseBid : $transaction->getBidAmount());
        $booking['booking_state']    = $transaction->getBookingState();
        $booking['customer_state'] = self::getCustomerState($transaction);

        $booking['dispute_reason']    = $transaction->getDisputeReason();
        $booking['booked_size_length']             = $transaction->getSizeD();
        $booking['booked_size_width']             = $transaction->getSizeW();
        $booking['notes'] = $transaction->getCustomerNotes();

        $tcCall = Genesis_Service_TenantConnectCall::loadByConfirmationCode($transaction->getConfirmationCode());
        if($tcCall) {
            $booking['tenant_connect_call_url'] = $tcCall->getRecordingUrl();
            $booking['tenant_connect_call_status'] = $tcCall->getStatus();
        } else {
            $booking['tenant_connect_call_url'] = null;
            $booking['tenant_connect_call_status'] = null;
         }

        if($transaction->getActualD() !== NULL) {
            $booking['size_length'] = $transaction->getActualD();
        } else {
            $booking['size_length'] = $transaction->getSizeD();
        }

        if($transaction->getActualW() !== NULL ){
            $booking['size_width'] = $transaction->getActualW();
        } else {
            $booking['size_width'] = $transaction->getSizeW();
        }

        $booking['aaa_member_number'] = $transaction->getAAAMemberNumber();
        $booking['auto_state'] = ($transaction->getAutoState()) ? strtolower($transaction->getAutoState()) : null;
        $booking['timestamp']         = $transaction->getTimestamp();
        $booking['verified_by'] = $transaction->getMoveInVerifiedByDetails();
        foreach($booking['verified_by'] as &$verified_by) {
            $verified_by['timestamp'] = AccountMgmt_Service_Util::standardDateStringToIso($verified_by['timestamp']);
        }

        if($account->getBidType() == Genesis_Entity_Account::BID_TYPE_TIERED) {
            $booking['transaction_fee'] = $transaction->getBaseBid();
        } elseif($account->getBidType() == Genesis_Entity_Account::BID_TYPE_RESIDUAL) {
            $booking['transaction_fee'] = number_format($transaction->getPrice() * $account->getLtvPercent(),2);
        }

        return $booking;
    }


    public static function updateFromJson(Genesis_Entity_Transaction $booking,$json_body) {

        $loggedUser = AccountMgmt_Service_User::getLoggedUser();
        $data = json_decode($json_body);
        foreach ($data as $field=>$value) {
            if (! in_array($field,self::getUpdateFromJsonExclusions()) && ! is_array($value) && ! is_object($value) &&
                ! array_key_exists($field,self::getUpdateFromJsonSetterExceptionMap())) {
                $setter = 'set'. ucfirst(AccountMgmt_Service_Util::snakeToCamel($field));
                $booking->$setter($value);

            }
        }

        //Set all fields that don't have direct mappings
        foreach (self::getUpdateFromJsonSetterExceptionMap() as $key => $setter) {
            if(property_exists($data,$key)) {
                $booking->$setter($data->$key);
            }
        }

        if (property_exists($data,'move_in_date')) {
            if ( ! AccountMgmt_Service_Util::isStandardDateString($data->move_in_date)) {
                throw new Exception('Move in date must be in YYYY-MM-DD format');
            }
            Genesis_Service_Transaction::updateMoveInDate($booking,$loggedUser,true,false,true);
        }

        if(property_exists($data,'move_out_date')) {
            if ( ! AccountMgmt_Service_Util::isStandardDateString($data->move_out_date)) {
                throw new Exception('Move out date must be in YYYY-MM-DD format');
            }
            Genesis_Service_Transaction::updateMoveOut($booking);
        }

        if(property_exists($data,'first_name') || property_exists($data,'last_name')) {
            Genesis_Service_Transaction::updateName($booking,$loggedUser);
        }

        if (property_exists($data,'phone')) {
            if ( ! AccountMgmt_Service_Util::isValidPhoneNumber($data->phone)) {
                throw new Exception('Invalid phone number');
            }
            Genesis_Service_Transaction::updatePhone($booking);
        }


        if (property_exists($data,'facility_id')) {
            $facility = Genesis_Service_Facility::loadById($data->facility_id);
            //Make sure facility exists
            AccountMgmt_Service_Facility::validateFacilityId($data->facility_id);
            //Make sure facility belongs to user
            AccountMgmt_Service_User::validateFacilityAccess($facility);
            Genesis_Service_Transaction::updateFacilityId($booking,$loggedUser);
        }


        if(property_exists($data,'booking_state')) {
           Genesis_Service_Transaction::updateState($booking,$loggedUser);
        }
        if (property_exists($data,'unit_id')) {
            //Make sure unit exists and belongs to facility
            $unit = Genesis_Service_StorageSpace::loadByUnitIdAndFacilityId($data->unit_id,$booking->getFacilityId());
            if ( ! $unit) {
                throw new Exception('Unit does not exist or is not associated with the facility of the booking');
            }
            Genesis_Service_Transaction::updateUnitId($booking,$loggedUser);
        }

        if(property_exists($data,'unit_number')) {
            Genesis_Service_Transaction::updateUnitNumber($booking,$loggedUser);
        }

        if(property_exists($data, 'size_length') || property_exists($data, 'size_width')) {
            Genesis_Service_Transaction::updateActualSize($booking,$loggedUser);
        }

        if(property_exists($data,'dispute_reason')) {
            Genesis_Service_Transaction::updateDisputeReason($booking,$loggedUser);
        }

        if(property_exists($data,'auto_state')) {
            Genesis_Service_Transaction::updateAutoState($booking,$loggedUser);
            //TODO Handle AutoState change logic
        }

        if(property_exists($data,'monthly_rent')) {
            Genesis_Service_Transaction::updatePrice($booking,$loggedUser);
        }

        if(property_exists($data,'customer_state')) {
            self::updateCustomerState($booking,$data->customer_state);
        }


        $booking = Genesis_Service_Transaction::save($booking);
        $booking = Genesis_Service_Transaction::loadById($booking->getConfirmationCode());
        return self::toArray($booking);
    }

    public static function getUpdateFromJsonExclusions() {
        return [
            'id',
            'timestamp',
            'aaa_member_number',
            'is_free',
            'base_bid',
            'deposit_paid',
            'bid_amount',
            'customer_state'
        ];
    }


    public function getUpdateFromJsonSetterExceptionMap() {
        return [
            'email'=>'changeEmail',
            'monthly_rent'=>'setPrice',
            'size_length' => 'setActualD',
            'size_width' => 'setActualW',
            'move_in_date' => 'setMoveIn',
            'notes' => 'setCustomerNotes',
            'move_out_date' => 'setMoveOut'
        ];
    }

    public static function validateConfirmationCode($confirmation_code)
    {
        if (! $confirmation_code) {
            throw new Exception('confirmation_code is required');
        }
        $booking = Genesis_Service_Transaction::loadById($confirmation_code);
        if (! $booking) {
            throw new Exception('No such booking');
        }

        return $booking;
    }

    public static function getCustomerState(Genesis_Entity_Transaction $transaction) {
        if($transaction->getMoveOut() !== NULL) {
            if(strtotime($transaction->getMoveOut()) < time()) {
                return self::VERIFIED_STATE_MOVED_OUT;
            }
        }
        $autoState = $transaction->getAutoState();
        $bookingState = $transaction->getBookingState();
        if($autoState === NULL && $bookingState == $transaction::BOOKING_STATE_PENDING) {
            return self::VERIFIED_STATE_NEW;
        }

        if($autoState == $transaction::BOOKING_STATE_CONFIRMED || $bookingState == $transaction::BOOKING_STATE_CONFIRMED) {
            return self::VERIFIED_STATE_MOVED_IN;
        } elseif($autoState == $transaction::BOOKING_STATE_DISPUTED || $bookingState == $transaction::BOOKING_STATE_DISPUTED) {
            return self::VERIFIED_STATE_DID_NOT_MOVE_IN;
        }
    }

    public static function updateCustomerState(Genesis_Entity_Transaction $transaction, $state) {
        $state = strtoupper($state);
        $confirmingUser = AccountMgmt_Service_User::getLoggedUser();
        switch($state) {
            case self::VERIFIED_STATE_NEW:
                $transaction->setAutoState(null);
                $transaction->setBookingState(Genesis_Entity_Transaction::BOOKING_STATE_PENDING);
                Genesis_Service_Transaction::updateAutoState($transaction, $confirmingUser);
                Genesis_Service_Transaction::updateState($transaction);
                break;
            case self::VERIFIED_STATE_MOVED_IN:
                $transaction->facilityConfirmMovein($confirmingUser);
                $transaction->setMoveOut(null);
                $transaction->setBookingState(Genesis_Entity_Transaction::BOOKING_STATE_PENDING);
                Genesis_Service_Transaction::updateMoveOut($transaction);
                Genesis_Service_Transaction::updateState($transaction);
                //Check to see if the transaction is a late transaction
                if(strtotime($transaction->getMoveIn()) < strtotime(date('Y-m-01'))) {
                    $transaction->setMoveIn(date('Y-m-d'));
                    Genesis_Service_Transaction::updateMoveInDate($transaction,$confirmingUser,true,false,true);
                }
                break;
            case self::VERIFIED_STATE_DID_NOT_MOVE_IN:
                $transaction->facilityConfirmNoMovein($confirmingUser);
                $transaction->setAutoState($transaction::BOOKING_STATE_DISPUTED);
                $transaction->setBookingState($transaction::BOOKING_STATE_DISPUTED);
                $transaction->setMatchReasons('Facility Admin pre-disputes move-in');
                Genesis_Service_Transaction::updateAutoState($transaction, $confirmingUser);
    		    Genesis_Service_Transaction::updateMatchReasons($transaction, $confirmingUser);
                break;
            case self::VERIFIED_STATE_MOVED_OUT:
                $transaction->setMoveOut(date('Y-m-d'));
                Genesis_Service_Transaction::updateMoveOut($transaction);
                break;
        }


        $logger = new Genesis_Util_ActionLogger();
        $logger->logAction(Genesis_Util_ActionLogger::SUPPORT_NOTES_BOOKING_FACILITY_ACKNOWLEDGE
            , null , $confirmingUser->getEmail(), $confirmingUser->getId(), $transaction->getFacilityId(), $transaction->getConfirmationCode());

        return;
    }

    public static function validateAndGetBooking($confirmation_code) {

        $booking = AccountMgmt_Service_Booking::validateConfirmationCode($confirmation_code);

        $facility = $booking->getFacility();
        AccountMgmt_Service_User::validateFacilityAccess($facility);

        return $booking;
    }

    /**
     * Book Unit
     *
     * @param Genesis_Entity_ReservationRequest $request
     * @return Genesis_Entity_ReservationResponse
     * @throws Exception
     */
    public static function bookUnit(Genesis_Entity_ReservationRequest $request)
    {
        try {
            $client = new GuzzleHttp\Client();

            $bookingServiceUrl = getenv('URL_BOOKING_SERVICE');

            /** @var GuzzleHttp\Psr7\Response $bookingResponse */
            $bookingResponse = $client->post($bookingServiceUrl . "/booking?token=iamsparefoot", ['json' => $request->toArray()]);
            $bookingResponseData = json_decode($bookingResponse->getBody(), true);
            return Genesis_Entity_ReservationResponse::buildFromBookingServiceResponse($request, $bookingResponseData);
        } catch (GuzzleHttp\Exception\BadResponseException $e) {
            // BadResponseException is 400 and 500 level responses
            // The booking service error response includes the exception class name as "error_class" and the message
            // as details. This tries to instantiate one of those exceptions to be thrown to the caller. Hopefully
            // this means that this function will behave the same as it did before it was changed to use the booking
            // service.
            $bookingResponseData = json_decode($e->getResponse()->getBody(), true);
            if(isset($bookingResponseData["errors"]) && count($bookingResponseData["errors"])
                && isset($bookingResponseData["errors"][0]["error_class"])) {
                // Create a new instance of the exception class indicated by the booking service.
                $errorClass = $bookingResponseData["errors"][0]["error_class"];
                // Certain error types need different parameters
                switch ($errorClass) {
                    case 'Genesis_Booking_Exception_FatalApiError':
                        $booking = Genesis_Service_Transaction::loadById($bookingResponseData['confirmationCode']);
                        throw new $errorClass($bookingResponseData["errors"][0]["details"], $booking);
                        break;
                    default:
                        throw new $errorClass($bookingResponseData["errors"][0]["details"]);
                        break;
                }
            } else {
                throw new Exception("Booking Service error: " . $e->getResponse()->getBody());
            }
        } catch(Exception $e) {
            throw new Exception("Unable to book unit with booking service: " . $e->getMessage());
        }
    }

}