<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 4/22/15
 * Time: 2:33 PM
 */

class AccountMgmt_Service_Unit {
    const TYPE_STORAGE_UNIT  = 'unit';
    const TYPE_PARKING_SPACE = 'parking';
    const TYPE_WINE          = 'wine';
    const TYPE_LOCKER        = 'locker';
    const TYPE_WORKSPACE     = 'workspace';
    const TYPE_OUTDOOR       = 'outdoor';

    public static function getNewUnitFromType($type) {
        $type = strtolower($type);
        switch ($type) {
            case self::TYPE_STORAGE_UNIT:
                $unit = new Genesis_Entity_StorageUnit();
                break;
            case self::TYPE_PARKING_SPACE:
                $unit = new Genesis_Entity_ParkingSpace();
                break;
            case self::TYPE_WORKSPACE:
                $unit = new Genesis_Entity_Workspace();
                break;
            case self::TYPE_WINE:
                $unit = new Genesis_Entity_WineStorage();
                break;
            case self::TYPE_LOCKER:
                $unit = new Genesis_Entity_StorageLocker();
                break;
            case self::TYPE_OUTDOOR:
                $unit = new Genesis_Entity_StorageOutdoor();
                break;
            default:
                throw new Exception('Error selecting the unit type');
                break;
        }
        return $unit;
    }

    public static function toArray(Genesis_Entity_StorageSpace $unit) {
        $unitTypes = [
            'Unit' => 'unit',
            'Parking' => 'parking',
            'Wine Storage' => 'wine',
            'Locker' => 'locker',
            'Workspace' => 'workspace',
            'Land/Open Lot' => 'outdoor'
        ];

        $response = [
            'id' => $unit->getId(),
            'facility_id' => $unit->getFacilityId(),
            'size_length' => $unit->getLength(),
            'size_width' => $unit->getWidth(),
            'unit_type' => $unitTypes[$unit->stringType()],
            'title' => $unit->stringDimensions(false),
            'amenities_string'=>$unit->stringAmenities(),
            'special_string'=>$unit->getSpecialString(),
            'floor' => $unit->getFloor(),
            'reservation_days' => $unit->getReservationDays(),
            'url' => $unit->getUrl(),
            'regular_price' => $unit->getRegularPrice(),
            'sparefoot_price' => $unit->getSparefootPrice(),
            'deposit' => $unit->getDeposit(),
            'quantity' => $unit->getQuantity(),
            'climate_controlled' => (bool) $unit->getClimateControlled(),
            'humidity_controlled' => (bool) $unit->getHumidityControlled(),
            'air_cooled' => (bool) $unit->getAirCooledOnly(),
            'ada_accessible' => (bool) $unit->getAdaAccessible(),
            'heated' => (bool) $unit->getHeatedOnly(),
            'covered'=> (bool) $unit->getCovered(),
            'vehicle_allowed'=> (bool) $unit->getVehicle(),
            'vehicle_only' => (bool) $unit->getVehicleStorageOnly(),
            'drive_up' => (bool) $unit->getDriveUp(),
            'stacked' => (bool) $unit->getSkybox(),
            'underground' => (bool) $unit->getBasement(),
            'parking_warehouse' => (bool) $unit->getParkingWarehouse(),
            'pull_through' => (bool) $unit->getPullThrough(),
            'premium' => (bool) $unit->getPremiumUnit(),
            'is_grouped' => (bool) $unit->isGrouped(),
            'grouped_num_available' => $unit->getGroupedNumAvailable(),
            'grouped_quantity' => $unit->getGroupedQuantity(),
            'power'=>(bool) $unit->getPower(),
            'alarm'=>(bool) $unit->getAlarm(),
            'shelves' => (bool) $unit->getShelvesInUnit(),
            'lighted' => (bool) $unit->getUnitLights(),
            'active' => (bool) $unit->getActive(),
            'publish' => (bool) $unit->getPublish(),
            'approved' => (bool) $unit->getApproved(),
            'twenty_four_hour_access' => (bool) $unit->getFacility()->getTwentyFourHourAccess()
                && is_array($unit->getFacility()->getTwentyFourHourAccessSupplemental())
                && in_array(Genesis_Entity_FacilityData::SPECIFIC_UNITS_24_HOUR_ACCESS, $unit->getFacility()->getTwentyFourHourAccessSupplemental())
                && $unit->getTwentyFourHourAccess(),
            'back_in_parking' => (bool) $unit->getBackInParking(),
            'vehicle_types_allowed' => json_decode($unit->getVehicleTypesAllowed()),
            'self_serve_parking' => (bool) $unit->getSelfServeParking(),
            'stacked_parking' => (bool) $unit->getStackedParking(),
            'parking_assistance_required' => (bool) $unit->getParkingAssistanceRequired()
        ];

        $specials = $unit->getSpecials();
        $response['specials'] = [];
        foreach ($specials as $special) {
            $response['specials'][] = self::serializeUnitSpecial($special);
        }
        if (method_exists($unit, 'getHeight')) {
            $response['size_height'] = $unit->getHeight();
        }
        if (method_exists($unit, 'getLotType')) {
            $response['lot_type'] = $unit->getLotType();
        }
        if (method_exists($unit, 'getDoorType')) {
            $response['door_type'] = $unit->getDoorType();
        }
        if (method_exists($unit, 'getDoorWidth')) {
            $response['door_width'] = $unit->getDoorWidth();
        }
        if (method_exists($unit, 'getDoorHeight')) {
            $response['door_height'] = $unit->getDoorHeight();
        }
        if (method_exists($unit, 'getOutdoorAccess')) {
            $response['outdoor_access'] = (bool) $unit->getOutdoorAccess();

            // Shows "Interior" if outdoor_access is not set
            if ( ! $unit->getOutdoorAccess()) {
                $unit->setOutdoorAccess(false);
                $response['amenities_string'] = $unit->stringAmenities();
            }
        }
        if (method_exists($unit, 'getBackInParking')) {
            $response['back_in_parking'] = $unit->getBackInParking();
        }

        if (method_exists($unit, 'getVehicleTypesAllowed')) {
            $response['vehicle_types_allowed'] = json_decode($unit->getVehicleTypesAllowed());
        }

        foreach ($unit->getReservationWindowRules() as $rule) {
            $response['reservation_window_rules'][] = [
                'occupancy_type' => $rule->getOccupancyType(),
                'occupancy_value' => $rule->getOccupancyValue(),
                'reservation_window_days' => $rule->getReservationWindowDays()
            ];
        };

        return $response;
    }

    public static function toJson(Genesis_Entity_StorageSpace $unit) {
        return json_encode(self::toArray($unit));
    }

    public static function createSpecialFromJson(Genesis_Entity_StorageSpace $unit, $json) {
        $data = json_decode($json);

        if (property_exists($data,'id')) {
            $special = Genesis_Service_Special::loadById($data->id);
            if(! $special) {
               throw new Exception('Special does not exist with that id');
            }

        } else {
            $special = new Genesis_Entity_Special();
            if (property_exists($data, 'special_type')) {
                $promotion_type = strtolower($data->special_type);
                $special->setType($promotion_type);
            }

            if (! isset($promotion_type)) {
                throw new Exception('Canot create promotion type from blank');
            }
            if ($promotion_type == Genesis_Entity_Special::TYPE_PROMO_PERCENT
                || $promotion_type == Genesis_Entity_Special::TYPE_PROMO_DOLLAR
                || $promotion_type == Genesis_Entity_Special::TYPE_PROMO_OVERRIDE) {
                $special->setRequiresPrepaidMonths($data->requires_prepaid_months);
                $special->setRequiresMinimumLeaseMonths($data->requires_minimum_lease_months);
                $promoMonths = $data->months;
                $special->setMonths($promoMonths);
            }

            switch ($promotion_type) {
                case Genesis_Entity_Special::TYPE_DISCOUNT_PERCENT:
                case Genesis_Entity_Special::TYPE_PROMO_PERCENT:
                    if ($data->percent_off <= 0 || $data->percent_off > 1) {
                        throw new Exception("Percent must be between 0 and 1.");
                    }

                    $special->setPercentOff($data->percent_off);
                     break;
                case Genesis_Entity_Special::TYPE_DISCOUNT_DOLLAR:
                case Genesis_Entity_SPECIAL::TYPE_PROMO_DOLLAR:
                    if ($data->dollar_off <= 0) {
                        throw new Exception("Price must be greater than zero.");
                    }
                    $special->setDollarOff(number_format($data->dollar_off,2));
                    break;
                case Genesis_Entity_Special::TYPE_DISCOUNT_OVERRIDE:
                case Genesis_Entity_Special::TYPE_PROMO_OVERRIDE:
                    if ($data->dollar_override <= 0) {
                        throw new Exception("Price must be greater than zero.");
                    }
                    $special->setDollarOverride(number_format($data->dollar_override,2));
                    break;
                case Genesis_Entity_Special::TYPE_FREE_ITEM:
                    $freeItem = Genesis_Service_FreeItem::loadById($data->free_item_id);
                    if(! $freeItem) {
                        throw new Exception("Invalid Free Item Id");
                    }
                    break;
                default:
                    throw new Exception($promotion_type . ' is an Invalid Promotion Type');
            }

            $special = Genesis_Service_Special::save($special);
        }

        Genesis_Service_UnitSpecial::applyUnitSpecial($unit->getId(),$special->getId());

        return $special;
    }

    public static function toSpecialsArray(Genesis_Entity_StorageSpace $unit) {
        $arr = [];
        foreach ($unit->getUnitSpecials() as $special) {
            $arr[] = self::serializeUnitSpecial($special->getSpecial());
        }
        return $arr;
    }

    /**
     * @return Genesis_Util_EntityIterator
     */
    public static function getDefaultSpecials() {
        return Genesis_Service_Special::loadDefaultSpecials();
    }

    public static function getUpdateFromJsonExclude() {
        return [
            'id',
            'title',
            'unit_type',
            'reservation_window_days'
        ];
    }

    public static function getUpdateFromJsonSetterExceptionMap() {
        return [
            'air_cooled' => 'setAirCooledOnly',
            'heated' => 'setHeatedOnly',
            'lighted' => 'setUnitLights',
            'outdoor_access' => 'setOutdoorAccess',
            'premium' => 'setPremiumUnit',
            'stacked' => 'setSkybox',
            'shelves' => 'setShelvesInUnit',
            'size_width' => 'setWidth',
            'size_length' => 'setLength',
            'size_height' => 'setHeight',
            'underground' => 'setBasement',
            'vehicle_allowed' => 'setVehicle',
            'vehicle_only' => 'setVehicleStorageOnly'
        ];
    }

    public static function getRequiredCreationFields() {
        return [
            'unit_type',
            'size_width',
            'size_length',
            'regular_price',
            'facility_id'
        ];
    }

    public static function validateInsert($data) {
        foreach (self::getRequiredCreationFields() as $required_field) {
            if (! array_key_exists($required_field, $data)) {
                throw new Exception('Missing required field: ' . $required_field);
            }
        }
    }

    public static function upsertFromJson(Genesis_Entity_StorageSpace $unit, $json) {
        $data = json_decode($json);

        if ($unit->getId() === null) {
            //If new unit, automatically set these fields
            $unit->setActive(1);
            $unit->setPublish(1);
            $unit->setQuantity(1);
        }

        // Set all fields that have a matching setter in Genesis Model
        foreach ($data as $field => $value) {
            if ($field != 'id' && ! is_array($value) && ! is_object($value) &&
                ! in_array($field, self::getUpdateFromJsonExclude()) && ! array_key_exists($field, self::getUpdateFromJsonSetterExceptionMap())) {
                $setter = 'set'. ucfirst(AccountMgmt_Service_Util::snakeToCamel($field));
                if(method_exists($unit, $setter)) {
                    $unit->$setter($value);
                }
            }
        }

        // Set all fields that don't have direct mappings
        foreach (self::getUpdateFromJsonSetterExceptionMap() as $key=>$setter) {
            if (property_exists($data, $key)) {
                if(method_exists($unit, $setter)) {
                    $unit->$setter($data->$key);
                }
            }
        }

        // Updating type is weird, they are integer constants in Genesis and we don't want to have to memorize them for json updating
        if (property_exists($data, 'unit_type')) {
            self::updateType($unit, $data->unit_type);
        }

        // If outdoor_access is not set, make sure it is false so "Interior" shows up on amenitiesString
        if (method_exists($unit, 'getOutdoorAccess') && ! $unit->getOutdoorAccess()) {
            $unit->setOutdoorAccess(false);
        }

        $vehicleTypesAllowedKey = 'vehicle_types_allowed';
        if (property_exists($data, $vehicleTypesAllowedKey)) {
            $values = $data->{$vehicleTypesAllowedKey};

            if (!empty($values)) {
                $unit->setVehicleTypesAllowed(json_encode($values));
            } else {
                $unit->setVehicleTypesAllowed(false);
            }
        }

        $unit = Genesis_Service_StorageSpace::save($unit,AccountMgmt_Service_User::getLoggedUser()->getId());

        // Reservation Rules
        if(property_exists($data, 'reservation_window_rules')) {
            $rules = [];
            foreach($data->reservation_window_rules as $rule) {
                $rules[$rule->occupancy_value] = $rule->reservation_window_days;
            }
            ksort($rules);
            //TODO This always saves as occupanct_type_percent for now, change when needed
            Genesis_Service_ReservationWindowRule::saveRules(
                $unit->getFacility()->getAccountId(),
                $unit->getFacilityId(),
                $unit->getId(),
                Genesis_Entity_ReservationWindowRule::OCCUPANCY_TYPE_PERCENT,
                $rules,
                AccountMgmt_Service_User::getLoggedUser()->getId()
            );
            Genesis_Service_ReservationWindowRule::applyRulesByFacilityId($unit->getFacilityId());

            //Reload unit with new rules or else it won't return updates in json
            $unit = Genesis_Service_StorageSpace::loadById($unit->getId());
        }

        return self::toArray($unit);
    }

    public static function delete(Genesis_Entity_StorageSpace $unit, $reason = null) {
        // Hide & Unpublish Unit
        $unit->setActive(0);
        $unit->setPublish(0);

        // Save reason for unit deletion
        $unit->setDeleteReason($reason);

        Genesis_Service_StorageSpace::save($unit, AccountMgmt_Service_User::getLoggedUser()->getId());
    }

    public static function updateType(Genesis_Entity_StorageSpace $unit, $type_name) {
        $type_name = strtolower($type_name);
        switch ($type_name) {
            case self::TYPE_STORAGE_UNIT:
                $unit->setType(Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT);
                break;
            case self::TYPE_PARKING_SPACE:
                $unit->setType(Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE);
                break;
            case self::TYPE_WORKSPACE:
                $unit->setType(Genesis_Entity_StorageSpace::TYPE_WORKSPACE);
                break;
            case self::TYPE_WINE:
                $unit->setType(Genesis_Entity_StorageSpace::TYPE_WINE);
                break;
            case self::TYPE_LOCKER:
                $unit->setType(Genesis_Entity_StorageSpace::TYPE_LOCKER);
                break;
            case self::TYPE_OUTDOOR:
                $unit->setType(Genesis_Entity_StorageSpace::TYPE_OUTDOOR);
                break;
            default:
                throw new Exception('Error selecting the unit type for updating');
                break;
        }
        return $unit;
    }

    public static function serializeUnitSpecial(Genesis_Entity_Special $special) {
        //TODO isFree() is broken in genesis...
        $return = [
            'id' => $special->getId(),
            'special_type' => $special->getType(),
            'is_promo' => (bool) $special->isPromo(),
            //'is_free' => (bool) $special->isFree(),
            'percent_off' => $special->getPercentOff(),
            'dollar_off' => $special->getDollarOff(),
            'dollar_override' => $special->getDollarOverride(),
            'free_item_id' => $special->getFreeItemId(),
            'requires_prepaid_months' => (bool) $special->getRequiresPrepaidMonths(),
            'requires_minimum_lease_months' => (bool) $special->getRequiresMinimumLeaseMonths(),
            'months' => $special->getMonths(),
            'string' => $special->getString()

        ];
        if($special->getFreeItem()) {
            $return['free_item_name'] = $special->getFreeItem()->getName();
        } else {
            $return['free_item_name'] = null;
        }

        return $return;
    }

    public static function validateUnitId($unitId) {
        if (! $unitId) {
            throw new Exception('invalid unit_id');
        }
        $unit = Genesis_Service_StorageSpace::loadById($unitId);
        if (! $unit) {
            throw new Exception('no such unit');
        }
        //check publish first, the other part is slow
        if (! $unit->getPublish() && $unit->getFacility()->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_MANUAL) {
            throw new Exception('unit is unpublished');
        }
        //check the facility, throws E
        AccountMgmt_Service_Facility::validateFacilityId($unit->getFacilityId());

        return $unit;
    }

    /**
     * @param $unitSpecialId
     * @return Genesis_Entity_Special
     * @throws Exception
     */
    public static function validateSpecialId($unitSpecialId) {
        if (! $unitSpecialId) {
            throw new Exception('unit_special_id is required');
        }
        $special = Genesis_Service_Special::loadById($unitSpecialId);

        if (! $special) {
            throw new Exception('no such unit special');
        }
        return $special;
    }
}
