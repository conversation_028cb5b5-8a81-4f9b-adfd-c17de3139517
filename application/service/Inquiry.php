<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 7/29/15
 * Time: 3:42 PM
 */

class AccountMgmt_Service_Inquiry
{
    public static function toArray(Genesis_Entity_ConsumerContact $inquiry) {

        $inquiry_array = [
            'id' => $inquiry->getId(),
            'unit_id' => $inquiry->getUnitId(),
            'facility_id' => $inquiry->getListingAvailId(),
            'move_in_date' => $inquiry->getMoveInDate(),
            'timestamp' => $inquiry->getTimestamp(),
            'phone' => $inquiry->getPhone(),
            'email' => $inquiry->getEmail(),
            'first_name' => $inquiry->getFirstName(),
            'last_name' => $inquiry->getLastName(),
            'bid_amount' => $inquiry->getBidAmount(),
            'referrer_id' => $inquiry->getReferrerId(),
            'affiliate_id' => $inquiry->getAffiliateId(),
            'site_id' => $inquiry->getSiteId(),
            'visit_id' => $inquiry->getVisitId(),
            'confirmation_code' => $inquiry->getConfirmationCode(),
            'email_sent' => (bool) $inquiry->getEmailSent(),
            'booking_agent_id' => $inquiry->getBookingAgentId(),
            'saved_litings_id' => $inquiry->getSavedListingsId(),
            'content_type' => $inquiry->getContentType(),
            'info_shareable' => (bool) $inquiry->getInfoShareable()
        ];
        return $inquiry_array;
    }

    public static function validateInquiryId($inquiry_id) {
        if (! $inquiry_id) {
            throw new Exception('inquiry_id is required');
        }
        $inquiry = Genesis_Service_ConsumerContact::loadById($inquiry_id);
        if (! $inquiry) {
            throw new Exception('No such inquiry');
        }

        return $inquiry;
    }
}