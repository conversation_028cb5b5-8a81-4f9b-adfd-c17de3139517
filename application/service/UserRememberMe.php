<?php

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 7/6/15
 * Time: 9:48 AM
 */
class AccountMgmt_Service_UserRememberMe
{

    const REMEMBER_ME = AccountMgmt_Service_UserCookie::REMEMBER_ME_EMAIL;
    /**
     * Read value by passing null
     * set with 'on' or true
     * unset with anything else
     * @return bool|null
     */
    public static function getSetRememberMe($postVarValue = null, $email = '')
    {
        if ($postVarValue === 'on' || $postVarValue === true) {
            AccountMgmt_Service_UserCookie::set(
                self::REMEMBER_ME,
                $email,
                null,
                '/login'
            );

            return $email;
        }

        return AccountMgmt_Service_UserCookie::get(self::REMEMBER_ME);
    }
}