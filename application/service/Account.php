<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 4/22/15
 * Time: 2:33 PM
 */
class AccountMgmt_Service_Account {

    public static function getAllFacilities($accountId)
    {
        return Genesis_Service_Facility::loadByAccountId(
            $accountId,
            Genesis_Db_Restriction::equal('published', 1)
        );
    }

    public static function validateAccountId($account_id)
    {
        if (! $account_id) {
            throw new Exception('account_id is required');
        }
        $account = Genesis_Service_Account::loadById($account_id);
        if (! $account) {
            throw new Exception('no such account');
        }
        if ($account->getStatus() != Genesis_Entity_Account::STATUS_LIVE) {
            throw new Exception('account is not live');
        }

        return $account;
    }

    // PublicStorage is enabled but they don't use MyFoot
    const FMS_WITH_ONLINE_MOVEINS = [
        Genesis_Entity_Source::ID_SITELINK,
        Genesis_Entity_Source::ID_STOREDGE,
        Genesis_Entity_Source::ID_PUBLIC_STORAGE
    ];

    public static function accountHasFmsSupportingOnlineMoveins(Genesis_Entity_Account $account) {
        $sourceIds = Genesis_Service_Account::getAccountSources($account->getId());

        foreach ($sourceIds as $sourceId) {
            if (in_array(intval($sourceId), self::FMS_WITH_ONLINE_MOVEINS)) {
                return true;
            }
        }

        return false;
    }

    public static function toArray(Genesis_Entity_Account $account)
    {
        $response = [
            'id'=> $account->getId(),
            'sf_account_id' => $account->getSfAccountId(),
            'name' => $account->getName(),
            'status' => $account->getStatus(),
            'num_facilities' => $account->getNumFacilities(),
            'bid_type' => $account->getBidType(),
        ];
        return $response;
    }
}
