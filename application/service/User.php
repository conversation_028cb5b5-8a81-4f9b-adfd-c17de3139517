<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 4/22/15
 * Time: 2:33 PM
 */
class AccountMgmt_Service_User
{
    private static $_userAccess = null;
    private static $session = false;
    /**
     * @return false|Genesis_Entity_UserAccess
     */
    public static function getLoggedUser()
    {
        // Validate if auth cookie has been disabled before checking user access.
        // Auth cookie is disabled on logout.
        $cabinet = Genesis_Service_Cabinet::get();

        if (!$cabinet->getMeta(AccountMgmt_Service_UserOauth::CABINET_AUTH_REFRESH_TOKEN)) {
            return false;
        }

        if (self::$_userAccess) {
            return self::$_userAccess;
        }

        try {
            $userId = AccountMgmt_Service_UserAuthByBooking::getUserId();

            if (!$userId) {
                $user = AccountMgmt_Service_UserOauth::getUser();

                if ($user) {
                    $userId = $user->getUserId();
                }
            }

            if (!$userId) {
                return false;
            }

            // Init usercookie and post vars for facility/account in session
            self::$_userAccess = AccountMgmt_Service_UserAccountFacilityContext::init($userId);
            return self::$_userAccess;
        } catch (Exception $e) {
            return false;
        }
    }

    public static function getSession()
    {
        if (! self::$session) {
            self::$session = new Zend_Session_Namespace('default');
        }
        return self::$session;
    }

    public static function deleteUser(Genesis_Entity_UserAccess $user) {
        $logger = new Genesis_Util_ActionLogger();
        $logger->logAction('delete_user_access', $user->getEmail(), null, self::getLoggedUser()->getId(), null, null);
        Genesis_Service_UserAccess::delete($user);
    }

    public static function logout()
    {
        $auth = Zend_Auth::getInstance();
        $auth->clearIdentity();
        AccountMgmt_Service_UserAuthByBooking::logout();
        AccountMgmt_Service_UserOauth::logout();
        AccountMgmt_Service_UserRedirect::clearRedirect();
        AccountMgmt_Service_UserAccountFacilityContext::clearContext();

        // Unset all of the session variables.
        // If it's desired to kill the session, also delete the session cookie.
        // Note: This will destroy the session, and not just the session data!
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        // Clear the user ID from the cabinet
        $cabinet = Genesis_Service_Cabinet::get();
        $cabinet->setUserId(null);
        Genesis_Service_Cabinet::save($cabinet);

        // Finally, destroy the session.
        unset($_COOKIE);
        session_destroy();
        $_SESSION = array();
    }

    /**
     * @param Genesis_Entity_Facility $facility
     * @return boolean
     */
    public static function canAccessFacility(Genesis_Entity_Facility $facility)
    {
        $result = false;
        $user = self::getLoggedUser();
        if ($user->isMyFootGod()) {
            $result = true;
        } else if ($user->canAccessFacility($facility)) {
            $result = true;
        }
        return $result;
    }

    /**
     * @param Genesis_Entity_Facility $facility
     * @return boolean
     */
    public static function canModifyFacility(Genesis_Entity_Facility $facility)
    {
        $result = false;
        $canAccess = self::canAccessFacility($facility);

        $user = self::getLoggedUser();
        $canModify = $user->isMyfootAdmin() || $user->getMyfootRole() == Genesis_Entity_UserAccess::ROLE_LIMITED;

        if ($canAccess && $canModify) {
            $result = true;
        }
        return $result;
    }

    /**
     * @param Genesis_Entity_Facility $facility
     * @param boolean $checkWriteAccess
     * @throws Exception
     */
    public static function validateFacilityAccess(Genesis_Entity_Facility $facility, $checkWriteAccess = false)
    {
        $result = false;
        if ($checkWriteAccess) {
            $result = self::canModifyFacility($facility);
        } else {
            $result = self::canAccessFacility($facility);
        }

        if (!$result) {
            throw new Exception('User cannot access this facility');
        }
    }

    /**
     * @return bool
     */
    public static function hasAccessToOmiCapableFms(): bool
    {
        $user = self::getLoggedUser();
        $facilities = $user->getManagableFacilities()->toArray();
        foreach ($facilities as $facility){
            if ($facility->hasOmiCapableFms()) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param Genesis_Entity_StorageSpace $unit
     * @param boolean $checkWriteAccess
     * @throws Exception
     */
    public static function validateUnitAccess(Genesis_Entity_StorageSpace $unit, $checkWriteAccess = false)
    {
        // Keep this here to short circuit "faster" for GOD users
        $user = self::getLoggedUser();
        if ($user->isMyFootGod()) {
            return;
        }

        $facility = $unit->getFacility();
        self::validateFacilityAccess($facility, $checkWriteAccess);
    }

    public static function validateAccountAccess(Genesis_Entity_Account $account)
    {
        $user = self::getLoggedUser();
        if ($user->isMyFootGod()) {
            return;
        }
        if ($account && $user->getAccountId() == $account->getId()) {
            return;
        }
        throw new Exception('User cannot access this account');
    }

    public static function validateUserAccess(Genesis_Entity_UserAccess $user,$action = null) {
        $loggedUser = self::getLoggedUser();
        if($loggedUser->isMyFootGod()) {
            return;
        }

        if($loggedUser->getAccountId() != $user->getAccountId()) {
            throw new Exception ('User cannot access this user');
        }

        if($action == AccountMgmt_Service_Constants::ACTION_DELETE) {
            if( !$loggedUser->isMyfootAdmin()) {
                throw new Exception ('User does not have permission to delete this user');
            }
            if($user->getId() == $loggedUser->getId()) {
                throw new Exception ('User does not have permission to delete itself');
            }
        }
    }

    public static function validateUserId($userId) {
        if (! $userId) {
            throw new Exception('user_id is required');
        }
        $user = Genesis_Service_UserAccess::loadById($userId);
        if (! $user) {
            throw new Exception('no such user with given id');
        }
        return $user;
    }

    public static function isFeatureActive($featureName, $extraParams = array())
    {
        $user = self::getLoggedUser();
        $featureParams = array_merge(
            array(
                'user_id'=>$user->getId(),
                'account_id'=>$user->getAccountId(),
                'facility_id'=>self::getSession()->facilityId,
            ),
            $extraParams
        );
        return Genesis_Service_Feature::isActive($featureName, $featureParams);
    }

    public static function toArray($user)
    {
        $response = [
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'first_name' => $user->getFirstName(),
            'last_name' => $user->getLastName(),
            'phone' => $user->getPhone()
        ];
        return $response;
    }

    /* Fields that either should not be updatable via API or have custom method for updating */
    public static function getUpdateFromJsonExclusions() {
        return [
            'id'
        ];
    }

    // White List - Restrict to update ONLY these fields
    public static function getUpdateFromJsonWhiteList() {
        return [
            'email',
            'first_name',
            'last_name',
            'phone'
        ];
    }

    public static function updateFromJson($user, $json)
    {
        $data = json_decode($json);

        //Set all basic info from json
        foreach ($data as $field=>$value) {
            if (in_array($field, self::getUpdateFromJsonWhiteList()) &&
                !in_array($field, self::getUpdateFromJsonExclusions()) &&
                !is_array($value) && !is_object($value)) {
                $setter = 'set'. ucfirst(AccountMgmt_Service_Util::snakeToCamel($field));
                $formattedValue = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                $user->$setter($formattedValue);
            }
        }

        $user = Genesis_Service_User::save($user);
        return $user;
    }
}
