<?php ;
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 3/16/15
 * Time: 2:33 PM
 */

use ReflectionClass;
/**
 * Class Serializer
 * @package Inventory\Services
 */
class AccountMgmt_Service_Serializer
{
    public static function reflect($object)
    {
        /**
         * @param \Closure
         * @return array
         */

        $reflectionClass = new ReflectionClass($object);
        $reflection = self::reflectClass($object, $reflectionClass);
        //go reflect for parent properties
        while ($parentReflectionClass = $reflectionClass->getParentClass()) {
            $reflection = array_merge($reflection, self::reflectClass($object, $parentReflectionClass));
            $reflectionClass = $parentReflectionClass;
        }

        return $reflection;

    }

    private static function reflectClass($object, \ReflectionClass $reflection)
    {
        $reflectedArray = [];
        $array = [
            'static'    => \ReflectionProperty::IS_STATIC,
            'public'    => \ReflectionProperty::IS_PUBLIC,
            'protected' => \ReflectionProperty::IS_PROTECTED,
            'private'   => \ReflectionProperty::IS_PRIVATE
        ];
        foreach ($array as $name => $visibility) {
            $properties = $reflection->getProperties($visibility);
            if (! count($properties)) {
                continue;
            }

            foreach ($properties as $property) {
                $property->setAccessible(1); //in case its private
                $propertyName = $property->getName();
                $reflectedArray[$propertyName] = $property->getValue($object);
            }
        }

        return $reflectedArray;
    }
}