<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 4/22/15
 * Time: 2:33 PM
 */


class AccountMgmt_Service_Util {

   public static function toSnakeCase($string) {
       $string = strtolower($string);
       $string = str_replace(' ', '_', $string);
       $string = str_replace('-', '_', $string);
       return $string;
   }

   public static function snakeToCamel($string) {
       $arr = explode('_',$string);
       for($i = 1; $i < sizeof($arr); $i++) {
           $arr[$i] = ucfirst($arr[$i]);
       }
       return implode('',$arr);
   }

   public static function camelToSnake($string) {
       preg_match_all('!([A-Z][A-Z0-9]*(?=$|[A-Z][a-z0-9])|[A-Za-z][a-z0-9]+)!', $string, $matches);
       $ret = $matches[0];
       foreach ($ret as &$match) {
           $match = $match == strtoupper($match) ? strtolower($match) : lcfirst($match);
       }
       return implode('_', $ret);
   }

    public static function isJson($string){
        return is_string($string) && is_object(json_decode($string)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
    }

    public static function isJsonArray($string) {
        return is_string($string) && is_array(json_decode($string)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
    }

    public static function isValidTime($string) {
        if (preg_match("/\d{2}:\d{2}:\d{2}/", $string)) {
            $pieces = explode(':',$string);
            if($pieces[0] > 23) {
                return false;
            }
            if($pieces[1] > 59) {
                return false;
            }
            if($pieces[2] > 99) {
                return false;
            }
            return TRUE;
        } else {
            return FALSE;
        }

    }

    public static function isStandardDateString($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Trim the Milliseconds from the ISO Date
     *
     * PHP doesn't support milliseconds in ISO-8601 yet
     */
    public static function trimMillisecondsFromISODate($date)
    {
        // 2015-08-08T19:20:18+00:00
        // 2015-08-08T19:20:18.000+00:00
        // 2015-08-08T19:20:18.00Z
        // 2015-08-08T19:20:18.0Z
        // 2015-08-08T19:20:18Z

        // Remove the .000
        return preg_replace('/\.[0-9]{1,3}/', '', $date);
    }

    public static function convertISODate($date)
    {
        $date = self::trimMillisecondsFromISODate($date);
        return DateTime::createFromFormat(DateTime::ISO8601, $date);
    }

    public static function standardDateStringToIso($dateString) {
        //Y-m-d H:i:s to ISO
        $date = new DateTime($dateString);
        return $date->format(DateTime::ISO8601);
    }

    public static function isValidIsoDate($date)
    {
        $converted = self::convertISODate($date);
        if ($converted === false) {
            return false;
        } else {
            return true;
        }
    }

    /*
     * ************: valid
        5555425555: valid
        ************: valid
        1**************: valid
        ****************: valid
        1-************: valid
        ****************: valid
        1-(555)-555-25555: invalid
     */
    public static function isValidPhoneNumber($phone_number) {
        $regex = '/^(\d[\s-]?)?[\(\[\s-]{0,2}?\d{3}[\)\]\s-]{0,2}?\d{3}[\s-]?\d{4}$/i';
        if( preg_match($regex,$phone_number)) {
            return true;
        } else {
            return false;
        }
    }
}