<?php
/**
 * Created by PhpStorm.
 * User: anguyen
 * Date: 7/29/15
 * Time: 3:42 PM
 */

use AccountMgmt_Models_ApiException as ApiException;

class AccountMgmt_Service_Review
{
    public static function toArray(Genesis_Entity_Review $review)
    {
        $reviewResponse = Genesis_Service_Review::loadChild($review->getId());
        $responseId = $reviewResponse ? $reviewResponse->getId() : null;
        $responseMessage = $reviewResponse ? $reviewResponse->getMessage() : null;
        $responseStatus = $reviewResponse ? $reviewResponse->getStatus() : null;
        $responseTimestamp = $reviewResponse ? AccountMgmt_Service_Util::standardDateStringToIso($reviewResponse->getTimestamp()) : null;

        $response = [
            'id'=>$review->getId(),
            'facility_id' => $review->getFacilityId(),
            'rating' => $review->getRating(),
            'title' => $review->getTitle(),
            'message' => $review->getMessage(),
            'site_id' => $review->getSiteId(),
            'nickname' => $review->getNickname(),
            'rating_recommended' => $review->getRatingRecommended(),
            'rating_price' => $review->getRatingPrice(),
            'rating_location' => $review->getRatingLocation(),
            'rating_security' => $review->getRatingSecurity(),
            'rating_service' => $review->getRatingService(),
            'rating_cleanliness' => $review->getRatingCleanliness(),
            'rating_paperwork' => $review->getRatingPaperwork(),
            'visit_id' => $review->getVisitId(),
            'status' => $review->getStatus(),
            'denial_reason' => $review->getDenialReason(),
            'deleted' => (bool)$review->getDeleted(),
            'timestamp' => AccountMgmt_Service_Util::standardDateStringToIso($review->getTimestamp()),
            'source' => $review->getSource(),
            'ip_address' => $review->getIpAddress(),
            'user_id' => $review->getUserId(),

            'response_id' => $responseId,
            'response_message' => $responseMessage,
            'response_status' => $responseStatus,
            'response_timestamp' => $responseTimestamp
        ];

        return $response;
    }

    /*
     * Validates to see if the review exists and belongs to a certain facility
     */
    public static function validateAndGetReview($review_id,$facility_id = null) {

        $review = Genesis_Service_Review::loadById($review_id);
        if (! $review) {
            throw new ApiException(ApiException::BAD_REQUEST, 'Review does not exist');
        }

        try {
            $facility = $review->getFacility();
            AccountMgmt_Service_User::validateFacilityAccess($facility);
        } catch (Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED);
        }

        if ($facility_id && $review->getFacilityId() != $facility_id) {
            throw new ApiException(ApiException::BAD_REQUEST, 'Review does not belong to given facility');
        }

        return $review;
    }
}