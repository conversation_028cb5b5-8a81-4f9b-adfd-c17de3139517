[production]
phpSettings.display_startup_errors = 0
phpSettings.display_errors = 0
includePaths.library = APPLICATION_PATH "/../vendor"
bootstrap.path = APPLICATION_PATH "/Bootstrap.php"
bootstrap.class = "AccountMgmt_Bootstrap"
resources.frontController.controllerDirectory = APPLICATION_PATH "/controller"
rollbar.accessToken = "60bd7101ccf34a569f3872206c2a5399"

[staging : production]

[testing : production]
phpSettings.display_startup_errors = 1
phpSettings.display_errors = 1

[development : production]
phpSettings.display_startup_errors = 1
phpSettings.display_errors = 1

[local : production]
phpSettings.display_startup_errors = 1
phpSettings.display_errors = 1

