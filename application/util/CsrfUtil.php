<?php

class CsrfUtil
{
	/**
	 * @param string $key Session token name
	 * @return string Returns the session token to be used
	 */
	public static function getToken(string $key) {
		if (!isset(\AccountMgmt_Service_User::getSession()->$key)) {
			\AccountMgmt_Service_User::getSession()->$key = base64_encode(openssl_random_pseudo_bytes(4));
		}
		return \AccountMgmt_Service_User::getSession()->$key;
	}

	/**
	 * @param string $key Session token name
	 * @param string|null $value Token name to validate
	 * @return bool
	 */
	public static function validateToken(string $key, $value) {
		return (
			isset(\AccountMgmt_Service_User::getSession()->$key) &&
			\AccountMgmt_Service_User::getSession()->$key === $value);
	}
}
