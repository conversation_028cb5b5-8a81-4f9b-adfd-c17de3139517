<?php
/*
 * Abstract Flot Chart
 *
 * @copyright SpareFoot inc
 * <AUTHOR>
 */

abstract class AccountMgmt_Flot_Abstract
{
    private $_id;
    private $_height = 300;
    private $_width  = 600;

    public function __construct($id)
    {
        $this->_id = $id;
    }

    abstract public function render();

    /**
     *
     * @return Zend_View
     */
    protected function getView()
    {
        $view = new Zend_View();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/flot');
        $view->id = $this->_id;
        $view->height = $this->_height;
        $view->width = $this->_width;

        return $view;
    }

    public function setWidth($w)
    {
        $this->_width = $w;
    }

    public function setHeight($h)
    {
        $this->_height = $h;
    }

    public function getHeight()
    {
        return $this->_height;
    }

    public function getWidth()
    {
        return $this->_width;
    }
}
