<?php
/*
 * Flot Bookings Bar
 *
 * @copyright SpareFoot Inc
 * <AUTHOR>
 */
class AccountMgmt_Flot_MtdBookingsBar extends AccountMgmt_Flot_Abstract
{
    private $accountId;

    public function __construct($id, $accountId)
    {
        parent::__construct($id);
        $this->accountId = $accountId;
    }

    public function render()
    {
        $view = $this->getView();

        return $view->render('mtd_bookings_bar.phtml');
    }

    public function getjson()
    {
        return $this->_getData();
    }

    private function _getData()
    {
        $sql = <<<SQL
SELECT
    DATE(timestamp) AS date,
    COUNT(1) AS bookings,
    (SELECT COUNT(1) / 7 FROM listing_rent_submission AS b2 JOIN listing_avail USING (listing_avail_id) INNER JOIN corporations ON (listing_avail.corporation_id = corporations.corporation_id) WHERE b2.timestamp BETWEEN DATE_SUB(b1.timestamp, INTERVAL 6 DAY) AND DATE_ADD(b1.timestamp, INTERVAL 1 DAY) AND booking_state IN ('PENDING', 'CONFIRMED') AND booking_widget = 1 AND corporations.account_id =:account_id) AS avg_7_bookings,
    (SELECT COUNT(1) / 30 FROM listing_rent_submission AS b2 JOIN listing_avail USING (listing_avail_id) INNER JOIN corporations ON (listing_avail.corporation_id = corporations.corporation_id) WHERE b2.timestamp BETWEEN DATE_SUB(b1.timestamp, INTERVAL 29 DAY) AND DATE_ADD(b1.timestamp, INTERVAL 1 DAY) AND booking_state IN ('PENDING', 'CONFIRMED') AND booking_widget = 1 AND corporations.account_id =:account_id) AS avg_30_bookings
FROM
        listing_rent_submission AS b1
JOIN
        listing_avail
USING
        (listing_avail_id)
INNER JOIN
        corporations
ON
        (listing_avail.corporation_id = corporations.corporation_id)
WHERE
        timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 1 MONTH) AND NOW()
AND
        booking_state IN ('PENDING', 'CONFIRMED')
AND
        booking_widget = 1
AND
        corporations.account_id =:account_id
GROUP BY 1

SQL;
        $params = array("account_id" => $this->accountId);
        $stmt = Genesis_Db_Connection::getInstance()->prepare($sql);
        $stmt->execute($params);

    $ret = array();

        while ($r = $stmt->fetch(PDO::FETCH_ASSOC)) {
          $ret[$r['date']]['bookings'] = $r['bookings'];
          $ret[$r['date']]['avg_7_bookings'] = $r['avg_7_bookings'];
          $ret[$r['date']]['avg_30_bookings'] = $r['avg_30_bookings'];

    }

        $arrayRet = array(
            'bookings' => array(),
            'avg_7_bookings' => array(),
            'avg_30_bookings' => array()
        );

        foreach ($ret as $date => $value) {
            foreach ($value as $type => $num) {
                $arrayRet[$type][] = array(date("M j", strtotime($date)), $num);
            }
        }

        return $arrayRet;
    }
}
