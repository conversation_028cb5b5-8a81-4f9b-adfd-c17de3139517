<?php
/*
 * Flot Search Position Line Chart
 *
 * @copyright SpareFoot Inc
 * <AUTHOR>
 */
class AccountMgmt_Flot_SearchPositionLine extends AccountMgmt_Flot_Abstract
{

    /**
     * @var Genesis_Entity_Facility
     */
    private $_facility;
    private $_startDate;
    private $_endDate;

    public function __construct($id, Genesis_Entity_Facility $facility, $startDate, $endDate)
    {
        parent::__construct($id);
        $this->_facility  = $facility;
        $this->_startDate = $startDate;
        $this->_endDate   = $endDate;
    }

    public function render()
    {
        $view = $this->getView();
        $view->data = $this->_getDailyAveragePosition();

        return $view->render('search_position_line.phtml');
    }

    private function _getDailyAveragePosition()
    {
        $data = Genesis_Service_Reporting::getDailyImpressionsByFacility($this->_facility->getId(), $this->_startDate, $this->_endDate);

        $retData = array();

        if(is_array($data))
        foreach ($data as $date => $row) {
            if ($row['avg_position']>0) {
                $retData[$date] = $row['avg_position'];
            }
        }

        return $retData;
    }
}
