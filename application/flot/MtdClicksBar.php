<?php
/*
 * Flot Widget Clicks Bar
 *
 * @copyright SpareFoot Inc
 * <AUTHOR>
 */
class AccountMgmt_Flot_MtdClicksBar extends AccountMgmt_Flot_Abstract
{
    private $accountId;

    public function __construct($id, $accountId)
    {
        parent::__construct($id);
        $this->accountId = $accountId;
    }

    public function render()
    {
        $view = $this->getView();

        return $view->render('mtd_clicks_bar.phtml');
    }

    public function getjson()
    {
        return $this->_getData();
    }

    private function _getData()
    {
        $sql = <<<SQL
SELECT
    DATE(action_time) AS date,
    SUM(IF(action_type = 'IMPRESSION', 1, 0)) AS impressions,
    SUM(IF(action_type = 'CLICK', 1, 0)) AS clicks
FROM
        booking_widget_actions
JOIN
        listing_avail
USING
        (listing_avail_id)
INNER JOIN
        corporations
ON
        (listing_avail.corporation_id = corporations.corporation_id)
WHERE
        action_time BETWEEN DATE_SUB(NOW(), INTERVAL 31 DAY) AND NOW()
AND
        corporations.account_id =:account_id
GROUP BY 1

SQL;

        $params = array("account_id" => $this->accountId);
        $stmt = Genesis_Db_Connection::getInstance()->prepare($sql);
        $stmt->execute($params);

    $ret = array();

        while ($r = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $ret[$r['date']]['impressions'] = $r['impressions'];
            $ret[$r['date']]['clicks'] = $r['clicks'];
    }

        $arrayRet = array();

        foreach ($ret as $date => $value) {
            foreach ($value as $type => $num) {
                $arrayRet[$type][] = array(date("M j", strtotime($date)), $num);
            }
        }

        return $arrayRet;
    }
}
