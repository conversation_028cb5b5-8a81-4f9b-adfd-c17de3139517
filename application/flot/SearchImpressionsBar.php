<?php
/*
 * Flot Search Impressions Bar Chart
 *
 * @copyright SpareFoot Inc
 * <AUTHOR>
 */

class AccountMgmt_Flot_SearchImpressionsBar extends AccountMgmt_Flot_Abstract
{

    /**
     * @var Genesis_Entity_Facility
     */
    private $_entity;
    private $_startDate;
    private $_endDate;

    public function __construct($id, $entity, $startDate, $endDate)
    {
        parent::__construct($id);
        $this->_entity  = $entity;
        $this->_startDate = $startDate;
        $this->_endDate   = $endDate;
    }

    public function render()
    {
        $view = $this->getView();

        if ($this->_entity instanceof Genesis_Entity_UserAccess) {
            $facs = $this->_entity->getManagableFacilities(null,true);
            $facIds = array();

            foreach ($facs as $fac) {
                $facIds[] = $fac->getId();
            }

            $view->data = $this->_getDailyFacilityImpressions(implode(",", $facIds));

        } elseif ($this->_entity instanceof Genesis_Entity_Facility) {
            $view->data = $this->_getDailyFacilityImpressions($this->_entity->getId());
        } else {
            throw new Exception('Entity must be of type Genesis_Entity_Account or Genesis_Entity_Facility');
        }

        return $view->render('search_impressions_bar.phtml');

    }

    /*private function _getDailyAccountImpressions() {
        $data = Genesis_Service_Reporting::getDailyImpressionsByAccount(
                $this->_entity, $this->_startDate, $this->_endDate);

        $retData = array();

        if(is_array($data))
            foreach ($data as $date => $row) {
                $retData[$date] = $row['num_impressions'];
            }

        return $retData;
    }*/

    private function _getDailyFacilityImpressions($ids)
    {
        $data = Genesis_Service_Reporting::getDailyImpressionsByFacility(
                $ids, $this->_startDate, $this->_endDate);

        $retData = array();

        if(is_array($data))
            foreach ($data as $date => $row) {
                $retData[$date] = $row['num_impressions'];
            }

        return $retData;
    }
}
