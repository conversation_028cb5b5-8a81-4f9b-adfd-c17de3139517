<?php
/*
 * Flot Facilities Line
 *
 * @copyright SpareFoot Inc
 * <AUTHOR>
 */
class AccountMgmt_Flot_MtdVisitsLine extends AccountMgmt_Flot_Abstract
{
    private $accountId;

    public function __construct($id, $accountId)
    {
        parent::__construct($id);
        $this->accountId = $accountId;
    }

    public function render()
    {
        $view = $this->getView();

        return $view->render('mtd_visits_line.phtml');
    }

    public function getjson()
    {
        return $this->_getData();
    }

    private function _getData()
    {
        $sql = <<<SQL
SELECT
    DATE(visit_time) AS date,
    SUM(IF(`is_unique`, 1, 0)) AS unique_visits,
    COUNT(1) AS visits

FROM
        external_visits
WHERE
        visit_time BETWEEN DATE_SUB(NOW(), INTERVAL 31 DAY) AND NOW()
AND
        ip_address NOT IN (SELECT ip_address FROM filtered_ips)
AND
        account_id =:account_id
GROUP BY 1

SQL;

        $params = array("account_id" => $this->accountId);
        $stmt = Genesis_Db_Connection::getInstance()->prepare($sql);
        $stmt->execute($params);

    $ret = array();

        while ($r = $stmt->fetch(PDO::FETCH_ASSOC)) {
          $ret[$r['date']]['visits'] = $r['visits'];
          $ret[$r['date']]['unique_visits'] = $r['unique_visits'];
    }

        $arrayRet = array();

        foreach ($ret as $date => $value) {
            foreach ($value as $type => $num) {
                $arrayRet[$type][] = array(strtotime($date . " GMT")*1000, $num);
            }
        }

        return $arrayRet;
    }
}
