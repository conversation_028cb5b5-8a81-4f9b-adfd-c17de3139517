<?php

/**
 * Messaging Service
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR> <<EMAIL>>
 */
class AccountMgmt_Models_Messaging
{
    public static function getMessages($num)
    {
        $db_connection = Genesis_Db_Connection::getInstance();
        $stmt = $db_connection->prepare("SELECT timestamp, type, subject, body FROM messages ORDER BY timestamp DESC LIMIT 0, " . $num);
        $stmt->execute();

        $messageDetails = array();

        while ($dataArray = $stmt->fetch()) {
            $id = $dataArray['timestamp'];
            $messageDetails[$id]['timestamp'] = $dataArray['timestamp'];
            $messageDetails[$id]['subject'] = $dataArray['subject'];
            $messageDetails[$id]['body'] = $dataArray['body'];
            $messageDetails[$id]['type'] = $dataArray['type'];
        }

        return $messageDetails;
    }
}
