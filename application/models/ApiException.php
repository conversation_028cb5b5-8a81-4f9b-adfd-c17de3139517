<?php

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 6/26/15
 * Time: 1:29 PM
 */
class AccountMgmt_Models_ApiException extends Exception
{
    protected $extended;

    protected $code;

    public function __construct($code = self::BAD_REQUEST, $extended = '')
    {
        if (! is_numeric($code)) {
            $this->code = self::TEAPOT;
            $this->extended = 'string passed to api exception handler, please pass a HTTP code instead of: '.$code;
        } elseif ((int) $code < 400 || (int) $code > 599) {
            $this->code = self::TEAPOT; //done fucked up. cast this to something reasonable
            $this->extended = 'unsupported HTTP code passed to api exception handler. Please use a defined code instead of: '.$code;
        } else {
            $this->code = $code;
            $this->message = self::getErrorClassMessage();
            $this->extended = $extended;
        }
    }

    const BAD_REQUEST           = 400;
    const UNAUTHORIZED          = 401;
    const FORBIDDEN             = 403;
    const NOT_FOUND             = 404;
    const METHOD_NOT_ALLOWED    = 405;
    const NOT_ACCEPTABLE        = 406;
    const TIMEOUT               = 408;
    const CONFLICT              = 409;
    const GONE                  = 410;
    const PRECONDITION_FAILED   = 412;
    const TEAPOT                = 418;

    const INTERNAL_SERVER_ERROR     = 500;
    const NOT_IMPLEMENTED           = 501;
    const BAD_GATEWAY               = 502;
    const SERVICE_UNAVAILABLE       = 503;
    const GATEWAY_TIMEOUT           = 504;

    public function getErrorClassMessage()
    {
        switch((int) $this->getCode()) {
            case 400: return 'Bad Request';
            case 401: return 'Unauthorized';
            case 403: return 'Forbidden';
            case 404: return 'Not Found';
            case 405: return 'Method Not Allowed';
            case 406: return 'Not Acceptable';
            case 408: return 'Timeout';
            case 409: return 'Conflict';
            case 410: return 'Gone';
            case 412: return 'Precondition Failed';
            case 418: return 'Illegal HTTP Code';
            case 500: return 'Internal Server Error';
            case 501: return 'Not Implemented';
            case 502: return 'Bad Gateway';
            case 503: return 'Service Unavailable';
            case 504: return 'Gateway Timeout';
            default: return 'Unknown';
        }
    }

    public final function getExtended()
    {
        return $this->extended;
    }

    public static function apiExceptionHandler($e)
    {
        $body = [];
        $code  = 400;


        if ($e instanceof AccountMgmt_Models_ApiException) {
            $body['errors'][] = [
                'status' => $e->getCode(),
                'title' => $e->getMessage(),
                'detail' => $e->getExtended()
            ];
            $code = $e->getCode();
        } else { //someone used Exception instead of ApiException, so no real http code
            $body['errors'][] = [
                'status' => 400,
                'title' => 'Internal Server Error',
                'detail' => $e->getMessage()
            ];
            $body['errors'][] = [
                'status' => 400,
                'title' => 'Internal Server Error',
                'detail' => 'ApiException invoked using an Exception instead of ApiException at line ' . $e->getLine() . ' of ' . $e->getFile()
            ];
        }

        $logger = new Zend_Log();
        $writer = new Zend_Log_Writer_Stream('php://stderr');
        $formatter = new Zend_Log_Formatter_Simple();
        $writer->setFormatter($formatter);
        $logger->addWriter($writer);
        $logger->registerErrorHandler();

        $logger->log(json_encode([
            $e->getMessage(),
            $e->getTraceAsString()
        ]), Zend_Log::ERR);

        header('Content-Type: application/json');
        http_response_code($code);
        exit(json_encode($body));
    }
}