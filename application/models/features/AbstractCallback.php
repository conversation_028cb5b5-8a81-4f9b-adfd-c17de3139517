<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 7/1/15
 * Time: 12:22 PM
 */
class AccountMgmt_Models_Features_AbstractCallback implements Genesis_Service_Feature_Callback
{
    public static function getName()
    {
        return get_called_class();
    }

    public static function getDescription() {
        return self::getName();
    }

    public static function check($feature, array $params = [])
    {
        return false;
    }

    public static function getRequiredParams() {
        return [];
    }

    public static function getActiveOnString($feature) {
        return "";
    }
}