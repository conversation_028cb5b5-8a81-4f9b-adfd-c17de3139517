<?php
/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 4/6/15
 * Time: 2:09 PM
 */

class AccountMgmt_Models_Util
{
    public static function getMyFootLandingPage()
    {
        $result = '/';

        // Specific facility?
        $session = AccountMgmt_Service_User::getSession();

        // Determine page
        $userAccess = AccountMgmt_Service_User::getLoggedUser();
        if ($userAccess->isMyfootAdmin() && count($userAccess->getManageableFacilityIds()) > 1) {
            $result .= 'overview';
            if ($session->accountId) {
                $result .= '?account_id=' . $session->accountId;
            }
        } else {
            $result .= 'dashboard';
            if ($session->facilityId) {
                $result .= '?fid=' . $session->facilityId;
            }
        }

        return $result;
    }
}