<?php

class AccountMgmt_Models_Features
{
    const TENANT_CONNECT_TOGGLE = 'myfoot.tenant_connect_toggle';
    const CPA_STATEMENT_CONSUMER_CONTACTS = 'myfoot.show_consumer_contacts_on_statements';
    const AUTH_BY_OAUTH = 'myfoot.auth_by_oauth_service'; //sign in by oauth
    const LOG_AUTH_ERROR = 'myfoot.log_auth_error'; //log auth errors if SHTF, turn it on then
    const QUALAROO_HOOK = 'myfoot.qualaroo_hook'; //footer tracker
    const SESSION_CAM_RECORDER = 'myfoot.session_cam'; //footer tracker
    const SALESFORCE_PARDOT = 'myfoot.pardot'; //footer tracker
    const OVERVIEW_SINGLE_PAGE = 'myfoot.overview_single_page'; // Account Overview SPA
    const CUSTOMERS_SINGLE_PAGE = 'myfoot.customers_single_page'; // Customers SPA
    const REVIEWS_SINGLE_PAGE = 'myfoot.reviews_single_page'; // Reviews SPA
    const DELETE_USER = 'myfoot.delete_user'; // Delete user trash can on settings
    const COVID_MODAL = 'myfoot.covid_closure_modal'; // Display COVID-19 message
    const COVID_BANNER = 'myfoot.covid_closure_banner'; // Display COVID-19 message
    const CUSTOM_CLOSURES = 'myfoot.custom_closures_form'; // Display COVID-19 message
    const CONTACTLESS_MOVEIN_BADGING = 'myfoot.contactless_move_in_badging'; // COVID-19 Contactless Badging
    const ONLINE_MOVEINS = 'myfoot.online_move_ins'; // COVID-19 Online Move Ins
    const MOVE_IN_BANNER = 'myfoot.move_in_banner';
    const MOVE_IN_ONLINE_BANNER = 'myfoot.move_in_online_banner';
    const MIN_BID_NOTIFICATION_BANNER = 'myfoot.show_notification_banner';
    const BID_OPTIMIZER = 'myfoot.bid_optimizer_beta';
    const NEW_STATEMENTS_PAGE = 'myfoot.new_reconciliation_page';
    const SITELINK_STRIKETROUGH_PRICE = 'phido.sitelink_strikethrough_pricing';

    // JS Feature Flags
    const UNIT_DELETE = 'myfoot.js.unit_delete';
    const VEHICLE_AMENITIES = 'myfoot.js.vehicleAmenitiesFeature';
    const VEHICLE_MINIMUM_STAY = 'myfoot.minimum_stay';
}
