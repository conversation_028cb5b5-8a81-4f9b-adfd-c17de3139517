<?php
/**
 * Account Manager Bootstrap
 *
 * Stuff here gets run on every HTTP request so use discretion when putting
 * things here.
 *
 * @copyright 2009 Sparefoot Inc
 * @auth Patrick J. Mi<PERSON>
 */

use Rollbar;

class AccountMgmt_Bootstrap extends Zend_Application_Bootstrap_Bootstrap
{

    /**
     * Assign any autoloaders here.
     */
    protected function _initAutoload()
    {
        require_once APPLICATION_PATH . '/ClassLoader.php';
        $autoLoader = Zend_Loader_Autoloader::getInstance();
        $autoLoader->pushAutoloader(array('AccountMgmt_ClassLoader', 'load'), 'AccountMgmt');
        $autoLoader->pushAutoloader(array('AccountMgmt_ClassLoader', 'load'), 'BidOptimizer');

        /* Register error logger */
        Genesis_Util_ErrorLogger::register();
    }

    public function _initCustomRoute()
    {
        $router = Zend_Controller_Front::getInstance()->getRouter();

        /* Ping */
        $route = new Zend_Controller_Router_Route('ping', array(
            'controller' => 'ping',
            'action' => 'index'
        ));
        $router->addRoute('ping', $route);

        $route = new Zend_Controller_Router_Route('/dashboard', array(
            'action' => 'index',
            'controller' => 'dashboard'
        ));
        $router->addRoute('dashboard', $route);

//        $route = new Zend_Controller_Router_Route('/facility/:action/:fid', array(
//                'action' => 'inventory',
//                'controller' => 'facility',
//                'module' => 'default',
//                'fid'   => null
//            )
//        );
//        $router->addRoute('facility', $route);

        $route = new Zend_Controller_Router_Route('/reports/:action/', array(
            'action' => 'index',
            'controller' => 'reports'
        ));
        $router->addRoute('reports', $route);

        $route = new Zend_Controller_Router_Route('/bookings/:confirmation_code/:action/', array(
            'action' => 'index',
            'controller' => 'booking'
        ));
        $router->addRoute('bookings', $route);

        $route = new Zend_Controller_Router_Route('/statement/:action/:id/:user_id', array(
            'action' => 'list',
            'controller' => 'statement',
            'id' => 0,
            'user_id' => null
        ));
        $router->addRoute('statement', $route);

        $route = new Zend_Controller_Router_Route('/settings/:action/', array(
            'action' => 'myaccount',
            'controller' => 'settings'
        ));
        $router->addRoute('settings', $route);

        $route = new Zend_Controller_Router_Route('/payment/:action', array(
            'action' => 'index',
            'controller' => 'payment'
        ));
        $router->addRoute('payment', $route);

        $route = new Zend_Controller_Router_Route('/user/:action', array(
            'action' => 'users',
            'controller' => 'user'
        ));
        $router->addRoute('user', $route);

        $route = new Zend_Controller_Router_Route('/inventory/:action', array(
            'action' => 'redirectoldroutes',
            'controller' => 'inventory'
        ));
        $router->addRoute('inventory', $route);

        $route = new Zend_Controller_Router_Route('/login/:action', array(
            'action' => 'index',
            'controller' => 'login'
        ));
        $router->addRoute('login', $route);

        $route = new Zend_Controller_Router_Route('/logout', array(
            'action' => 'logout',
            'controller' => 'login'
        ));
        $router->addRoute('logout', $route);

        // TODO: REMOVE widget endpoint
        $route = new Zend_Controller_Router_Route('/widget/:action', array(
            'action' => 'index',
            'controller' => 'widget'
        ));
        $router->addRoute('widget', $route);

        $route = new Zend_Controller_Router_Route('/sites/:action', array(
            'action' => 'index',
            'controller' => 'sites'
        ));
        $router->addRoute('sites', $route);

        $route = new Zend_Controller_Router_Route('/insights/:action', array(
            'action' => 'index',
            'controller' => 'insights'
        ));
        $router->addRoute('insights', $route);

        $route = new Zend_Controller_Router_Route('/signup-end/:action', array(
            'action' => 'terms',
            'controller' => 'signup-end'
        ));
        $router->addRoute('signup-end', $route);

        $route = new Zend_Controller_Router_Route('/signup-start/:action', array(
            'action' => 'index',
            'controller' => 'signup-start'
        ));
        $router->addRoute('signup-start', $route);

        // Account Overview
        $route = new Zend_Controller_Router_Route('/overview', array(
            'controller' => 'facility',
            'action' => 'list',
        ));
        $router->addRoute('account-overview', $route);

        // Account Update ToS
        $route = new Zend_Controller_Router_Route('/accounts/update-terms', array(
            'controller' => 'accounts',
            'action' => 'updateTerms',
        ));
        $router->addRoute('account-update-terms', $route);

        /* New Routes for new UI */

        $route = new Zend_Controller_Router_Route('customers/:action', array(
            'action' => 'index',
            'controller' => 'customer'
        ));
        $router->addRoute('customers', $route);

        $route = new Zend_Controller_Router_Route('features/:action', array(
            'controller' => 'facility',
            'action' => 'index',
        ));
        $router->addRoute('features', $route);

        // Bidding
        $route = new Zend_Controller_Router_Route('features/bid', array(
            'controller' => 'facility',
            'action' => 'bid',
        ));
        $router->addRoute('features-bid', $route);

        // BidOptimizer
        $route = new Zend_Controller_Router_Route('features/demandoptimizer', array(
            'controller' => 'facility',
            'action' => 'bid-optimizer',
        ));
        $router->addRoute('features-demand-optimizer', $route);

        $route = new Zend_Controller_Router_Route('features/bid-custom', array(
            'controller' => 'facility',
            'action' => 'bid-custom',
        ));
        $router->addRoute('features-bid-custom', $route);
        
        $route = new Zend_Controller_Router_Route('features/bidOptimizer-health', array(
            'controller' => 'facility',
            'action' => 'bid-optimizer-health',
        ));
        $router->addRoute('bid-health',$route);

        $route = new Zend_Controller_Router_Route('features/bidoptimizer-bidupdate', array(
            'controller' => 'facility',
            'action' => 'bid-optimizer-bid-update',
        ));
        $router->addRoute('features-bid-optimizer-bid-update',$route);

        $route = new Zend_Controller_Router_Route('features/bidoptimizer-bidsexport', array(
            'controller' => 'facility',
            'action' => 'bid-optimizer-bids-export',
        ));
        $router->addRoute('features-bid-optimizer-bids-export',$route);
        // End - Bidding

        //need to figure a better way
        $route = new Zend_Controller_Router_Route('features/:action/uid/:unitIds', array(
            'controller' => 'facility',
            'action' => 'groupedinventory',
            'unitIds' => null
        ));
        $router->addRoute('features-uid', $route);

        $route = new Zend_Controller_Router_Route('features/units', array(
            'controller' => 'facility',
            'action' => 'inventory',
        ));
        $router->addRoute('features-units', $route);

        $route = new Zend_Controller_Router_Route('/listings', array(
            'controller' => 'facility',
            'action' => 'listings',
        ));
        $router->addRoute('features-listings', $route);

        $route = new Zend_Controller_Router_Route('reviews/:action/:rid', array(
            'controller' => 'reviews',
            'action' => 'index',
            'rid' => null
        ));
        $router->addRoute('reviews', $route);


        $route = new Zend_Controller_Router_Route('reviews/:rid', array(
            'controller' => 'reviews',
            'action' => 'index'
        ));
        $router->addRoute('reviews-one', $route);


        $route = new Zend_Controller_Router_Route('reviews/response', array(
            'controller' => 'reviews',
            'action' => 'response',
        ));
        $router->addRoute('reviews-response', $route);

        $route = new Zend_Controller_Router_Route('reviews/request', array(
            'controller' => 'reviews',
            'action' => 'request',
        ));
        $router->addRoute('reviews-request', $route);

        $route = new Zend_Controller_Router_Route('bidding/:action', array(
            'controller' => 'bidding',
            'action' => 'index'
        ));

        $router->addRoute('bidding', $route);

        // This is for contactless & online move-ins, not to be confused with the "MoveIn" controller used
        // for reconciliation
        $route = new Zend_Controller_Router_Route('move-ins', array(
            'controller' => 'moveins',
            'action' => 'index'
        ));
        $router->addRoute('move-ins', $route);

        $route = new Zend_Controller_Router_Route('move-ins/contactless', array(
            'controller' => 'moveins',
            'action' => 'contactless'
        ));
        $router->addRoute('contactless-move-ins', $route);

        $route = new Zend_Controller_Router_Route('move-ins/update-contactless', array(
            'controller' => 'moveins',
            'action' => 'update-contactless'
        ));
        $router->addRoute('update-contactless-move-ins', $route);

        $route = new Zend_Controller_Router_Route('move-ins/online', array(
            'controller' => 'moveins',
            'action' => 'online'
        ));
        $router->addRoute('online-move-ins', $route);

        $route = new Zend_Controller_Router_Route('move-ins/update-online', array(
            'controller' => 'moveins',
            'action' => 'update-online'
        ));
        $router->addRoute('update-online-move-ins', $route);

        $route = new Zend_Controller_Router_Route('partners', array(
            'controller' => 'partners',
            'action' => 'index'
        ));
        $router->addRoute('partners', $route);

        /**
         * API Endpoints
         */

        /* Facilities Routes */
        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id', array(
            'controller' => 'api-facility',
            'action' => 'index',
            'facility_id'=> null
        ));
        $router->addRoute('api_existing_facilities', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/account_id/:account_id', array(
            'controller' => 'api-facility',
            'action' => 'getallfacilitiesforaccount',
        ));
        $router->addRoute('api_all_facilities_old', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/units', array(
            'controller' => 'api-facility',
            'action' => 'units',
        ));
        $router->addRoute('api_facility_all_units', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/photos/:photo_id', array(
            'controller' => 'api-facility',
            'action' => 'photos',
            'photo_id' => null
        ));
        $router->addRoute('api_facility_photos', $route);

        $route = new Zend_Controller_Router_Route('api/location/polygon', [
            'controller' => 'api-location',
            'action' => 'polygon'
        ]);
        $router->addRoute('api_location_polygon', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/bookings', array(
            'controller' => 'api-facility',
            'action' => 'bookings',
        ));

        $router->addRoute('api_facility_bookings', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/reviews', array(
            'controller' => 'api-facility',
            'action' => 'reviews',
        ));

        $router->addRoute('api_facility_reviews', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/reviews/:review_id', array(
            'controller' => 'api-review',
            'action' => 'index',
        ));

        $router->addRoute('api_facility_single_review', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/bookings/:confirmation_code', array(
            'controller' => 'api-booking',
            'action' => 'index',
        ));
        $router->addRoute('api_facility_single_booking', $route);

        /* Accounts Routes */
        $route = new Zend_Controller_Router_Route('api/accounts/:account_id', array(
            'controller' => 'api-account',
            'action' => 'index',
            'account_id' => null
        ));
        $router->addRoute('api_all_accounts', $route);

        $route = new Zend_Controller_Router_Route('api/accounts/:account_id/specials', array(
            'controller' => 'api-account',
            'action' => 'specials'
        ));
        $router->addRoute('api_account_specials', $route);

        $route = new Zend_Controller_Router_Route('api/accounts/:account_id/facilities', array(
            'controller' => 'api-facility',
            'action' => 'getallfacilitiesforaccount'
        ));
        $router->addRoute('api_all_facilities_for_account', $route);

        /* Units Routes */
        $route = new Zend_Controller_Router_Route('api/units/:unit_id/specials/:special_id', array(
            'controller' => 'api-unit',
            'action' => 'specials'
        ));
        $router->addRoute('api_units_specials_type', $route);

        $route = new Zend_Controller_Router_Route('api/units/:unit_id/specials', array(
            'controller' => 'api-unit',
            'action' => 'specials'
        ));
        $router->addRoute('api_units_specials', $route);

        $route = new Zend_Controller_Router_Route('api/units/:unit_id', array(
            'controller' => 'api-unit',
            'action' => 'index'
        ));
        $router->addRoute('api_single_units', $route);

        $route = new Zend_Controller_Router_Route('api/specials', array(
            'controller' => 'api-special',
            'action' => 'index'
        ));
        $router->addRoute('api_units_default_specials', $route);

        $route = new Zend_Controller_Router_Route('api/units', array(
            'controller' => 'api-unit',
            'action' => 'index'
        ));
        $router->addRoute('api_units', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/units/:unit_id', array(
            'controller' => 'api-unit',
            'action' => 'index',
        ));
        $router->addRoute('api_facilities_units', $route);


        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/inquiries', array(
            'controller' => 'api-facility',
            'action' => 'inquiries',
        ));
        $router->addRoute('api_facilities_inquiries', $route);

        $route = new Zend_Controller_Router_Route('api/facilities/:facility_id/inquiries/:inquiry_id', array(
            'controller' => 'api-facility',
            'action' => 'inquiry',
        ));
        $router->addRoute('api_facilities_inquiry', $route);

        /* Bookings Route */
        $route = new Zend_Controller_Router_Route('api/bookings/:confirmation_code', array(
            'controller' => 'api-booking',
            'action' => 'index',
        ));
        $router->addRoute('api_bookings', $route);

        /*Login Route*/
        $route = new Zend_Controller_Router_Route('api/login/:action', array(
            'controller' => 'api-login',
            'action' => 'index',
        ));
        $router->addRoute('api_login', $route);

        $route = new Zend_Controller_Router_Route('api/features/:action/:one/:two/:three', array(
            'controller' => 'api-feature',
            'action' => 'index',
            'one' => null,
            'two' => null,
            'three' => null
        ));
        $router->addRoute('api_feature', $route);

        $route = new Zend_Controller_Router_Route('/facilities/:facility_id/viewOn/:site_id', array(
            'controller' => 'view-on',
            'action' => 'facility',
        ));
        $router->addRoute('facility_view_on', $route);

        /*User Routes */
        $route = new Zend_Controller_Router_Route('api/users/:user_id', array(
            'controller' => 'api-user',
            'action' => 'index',
        ));
        $router->addRoute('api_users', $route);

        $route = new Zend_Controller_Router_Route('api/me', array(
            'controller' => 'api-user',
            'action' => 'me',
        ));
        $router->addRoute('api_users_me', $route);

        /* Reviews Routes */
        $route = new Zend_Controller_Router_Route('api/reviews/:review_id', array(
            'controller' => 'api-review',
            'action' => 'index'
        ));
        $router->addRoute('api_review_single', $route);

        $route = new Zend_Controller_Router_Route_Static('document/terms-addendum', [
            'controller' => 'document',
            'action' => 'termsAddendum'
        ]);
    }

    /**
     * Init Zend MVC
     */
    protected function _initMvc()
    {
        Zend_Layout::startMvc();
        $view = new Zend_View();
    }

    protected function _initGzip()
    {
        /* Turn on gzip */
        ob_start('ob_gzhandler');
    }
}
