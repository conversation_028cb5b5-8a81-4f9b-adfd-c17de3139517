<?php

use \GuzzleHttp\Client;
use \GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Promise\PromiseInterface;
use Psr\Http\Message\ResponseInterface;
use function GuzzleHttp\Promise\settle;
use function GuzzleHttp\Psr7\str;

class AccountMgmt_Clients_SearchClient {

    private $responseFormatAssoc = true;
    private $applicationName = NULL;
    /** @var Client $client */
    private $client;

    /**
     * AccountMgmt_Clients_SearchClient constructor.
     * @param string $applicationName sent on requests as x-sparefoot-app-tag
     * @param bool $responseFormatAssoc tell results to be in associative array or object.
     */
    public function __construct($applicationName, $responseFormatAssoc = true) {
        $this->applicationName = $applicationName;
        $this->responseFormatAssoc = $responseFormatAssoc;
    }

    /**
     * @return Client
     */
    private function getClient() {
        if ( !$this->client) {
            $this->client = new Client();
        }
        return $this->client;
    }

    public function getSearchResults($path, $params)
    {
        $baseParams = [
            'token' => 'iamsparefoot',
            'shouldFetchUnitSummary' => false
        ];
        $guzzleClient = $this->getClient();
        $urlString = getenv('URL_SEARCH_SERVICE') . $path;

        try {
            $response = $guzzleClient->get($urlString, [
                'query' => array_merge($baseParams, $params),
                'headers' => [
                    'x-sparefoot-app' => 'myfoot',
                    'x-sparefoot-app-tag' => $this->applicationName
                ]
            ]);
        } catch (RequestException $e) {
            // DO THIS FIRST!!
            $this->recordExceptionInPhlow($path);

            if ($e->hasResponse()) {
                // We expect a valid JSON response...
                $response = json_decode(str($e->getResponse()), true);

                // If JSON decoding failed, then just use the raw response body
                if (!$response) {
                    $response = $e->getResponse();
                }

                return $response;
            }

            return ['error' => $e->getMessage()];
        }  catch (Exception $e) {
            $this->recordExceptionInPhlow($path);

            return ['error' => $e->getMessage()];
        }

        return json_decode($response->getBody(), $this->responseFormatAssoc);
    }

    private function getSearchResultsAsync($path, $params) {
        $baseParams = [
            'token' => 'iamsparefoot',
            'shouldFetchUnitSummary' => false
        ];
        $guzzleClient = $this->getClient();
        $path = getenv('URL_SEARCH_SERVICE') . $path;

        try {
            $promise = $guzzleClient->getAsync($path, [
                'headers' => [
                    'x-sparefoot-app' => 'myfoot',
                    'x-sparefoot-app-tag' => $this->applicationName
                ],
                'query' => $params
            ]);
        } catch (Exception $e) {
            $this->logException($path);
            throw $e;
        }

        return $promise;
    }

    /**
     * @param $facility
     * @param $bid
     * @param AccountMgmt_Models_SearchRankQuery[] $searchRankQueries One of the Genesis_Service_BidMgmt::BID_MODE_* constants
     * @return mixed
     * @throws
     */
    public function getSearchRanks($facility, $bid, $searchRankQueries = []) {
        $promises = [];
        foreach($searchRankQueries as $rankQuery) {
            $params = [
                'token' => 'iamsparefoot',
                'listingsPerPage' => 500,
                'location' => $rankQuery->location,
                'bidAmount' => $bid,
                'oppType' => $rankQuery->bidOppType,
                'facilityId' => $facility->getId()
            ];
            $promises[$rankQuery->label] = $this->getSearchResultsAsync('/bid/search', $params);
        }

        $results = settle($promises)->wait();
        $ranks = [];

        foreach($results as $label => $result) {
            if ($result['state'] === PromiseInterface::REJECTED) {
                $ranks[$label] = null;
            } else {
                // Find their proposed new rank here now.
                $body = json_decode($result['value']->getBody(), true);
                $positionBidArray = $body[Genesis_Service_BidMgmt::ARRAY_POSITION];
                if (!$positionBidArray) {
                    $ranks[$label] = 0;
                }
                $ranks[$label] = self::_determineRankFromBid($bid, $positionBidArray);
            }
        }

        return $ranks;
    }

    private static function _determineRankFromBid( $bid, $bidForRankPositions)
    {
        $currentRank = null; // if no position found, don't know the rank
        foreach ($bidForRankPositions as $rank => $bidForRank) {
            // $bidForRank will be a sequence of decreasing values, or
            // null when there is no distinct bid for a rank.
            if ($bidForRank && ($bid >= $bidForRank)) {
                $currentRank = $rank;
                break;
            }
        }

        return $currentRank;
    }

    /**
     * @param $path
     */
    private function recordExceptionInPhlow($path): void
    {
        AccountMgmt_Service_Phlow::getClient()->increment(
            'service_request_exception',
            AccountMgmt_Service_Phlow::getClient()->arrayToCsv([
                'service' => 'search',
                'path' => $path
            ])
        );
    }
}
