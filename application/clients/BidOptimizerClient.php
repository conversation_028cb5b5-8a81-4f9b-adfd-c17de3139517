<?php

use GuzzleHttp\Client;

class BidOptimizer_Clients_BidOptimizerClient {

    private $httpClient;
    private $base_url;

    public function __construct(){
        $this->httpClient = new Client();
        $this->base_url = getenv('URL_BID_OPTIMIZER_SERVICE'); 
    }

    public function getHealthStatus(){
        return $this->get('/health');
    }

    public function postBidUpdate($data){
        return $this->post('/bid/update', $data);
    }

    public function getBidsExport($userId, $params) {
        return $this->get("/bids/export/{$userId}", $params);
    }

    /**
     * Function that perfomrs a Get Request using the given params and headers
     * @param string $url fullpath for the request
     * @param array $params Query params for the request
     * @param array $headers
     * @return array/json 
     * @throws Exception 
     */
    public function get(string $url, array $params=[], array $headers = [])
    {
        $response = $this->httpClient->get($this->base_url . $url, [
            'query' => $params,
            'headers' => $headers
        ]);

        $tmpBody = $response->getBody()->getContents();

        if (empty($tmpBody) && $response->getStatusCode() !== 204) {
            throw new \Exception('Empty HTTP Response');
        }

        $response = json_decode($tmpBody, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception(json_last_error_msg());
        }

        return $response;
    }

    /**
     * Function that perfomrs a Post Request using the given body and headers
     * @param string $url fullpath for the request
     * @param array $body body for the request
     * @param array $headers
     * @return array/json 
     * @throws Exception 
     */
    public function post(string $url, array $body = [], array $headers = [])
    {
        $response = $this->httpClient->post($this->base_url . $url, [
            'json' => $body,
            'headers' => $headers 
        ]);

        $tmpBody = $response->getBody()->getContents();

        if (empty($tmpBody) || $response->getStatusCode() !== 200) {
            throw new \Exception('Empty HTTP Response');
        }

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception(json_last_error_msg());
        }

        return $tmpBody;
    }

}