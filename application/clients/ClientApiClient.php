<?php

use \GuzzleHttp\Client;
use \GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Promise\PromiseInterface;
use Psr\Http\Message\ResponseInterface;
use function GuzzleHttp\Promise\settle;

class AccountMgmt_Clients_ClientApiClient {

    const MAX_BID_OPPORTUNITIES_SHOWN = 20;
    /** @var Client $client */
    private $client;

    public function pingAuth(string $authToken, $params)
    {
        $path = '/pingAuth';
        $headers = [ 'Authorization' => $authToken ];

        try {
            $response = $this->makeRequest($path, $params, $headers);
            return json_decode($response->getBody(), true);
        } catch (Exception $e) {
            return ['exception' => $e->getMessage()];
        }
    }

    public function getCityAndZipBidOpps($accountId, $facilityId, string $authToken)
    {
        $path = "/report/bids/account/$accountId/facility/$facilityId";
        $headers = [ 'Authorization' => $authToken ];
        $params = ['round' => 1];

        try {
            $cityParams = array_merge(['oppType' => 'city'], $params);
            $cityPromise = $this->makeAsyncRequest($path, $cityParams, $headers);
            $zipParams = array_merge(['oppType' => 'zip'], $params);
            $zipPromise = $this->makeAsyncRequest($path, $zipParams, $headers);
            $results = settle([ 'cityBidOpps' => $cityPromise, 'zipBidOpps' => $zipPromise])->wait();

            if ($results['cityBidOpps']['state'] === PromiseInterface::REJECTED) {
                $cityBidOpps = ['exception' => $results['cityBidOpps']['reason']];
            } else {
                $cityBidOpps = json_decode($results['cityBidOpps']['value']->getBody(), true);
            }

            if ($results['zipBidOpps']['state'] === PromiseInterface::REJECTED) {
                $zipBidOpps = ['exception' => $results['zipBidOpps']['reason']];
            } else {
                $zipBidOpps = json_decode($results['zipBidOpps']['value']->getBody(), true);
            }

            return [
                'cityBidOpps' => $cityBidOpps,
                'zipBidOpps' => $zipBidOpps
            ];
        } catch (Exception $e) {
            return ['exception' => $e->getMessage()];
        }
    }

    public function getBidOppsByFacility($accountId, $facilityId, string $authToken, $params)
    {
        $path = "/report/bids/account/$accountId/facility/$facilityId";
        $headers = [ 'Authorization' => $authToken ];

        try {
            $response = $this->makeRequest($path, $params, $headers);
            return json_decode($response->getBody(), true);
        } catch (Exception $e) {
            return ['exception' => $e->getMessage()];
        }
    }

    public function getBidsReportByAccount($accountId, string $authToken, $oppType)
    {
        $path = "/report/bids/account/$accountId";
        $headers = [ 'Authorization' => $authToken ];
        $params = [ 'oppType' => $oppType ];

        try {
            $response = $this->makeRequest($path, $params, $headers);
            return json_decode($response->getBody(), true);
        } catch (Exception $e) {
            return ['exception' => $e->getMessage()];
        }
    }

    public function getJobById($jobId, string $authToken)
    {
        $path = "/job/$jobId";
        $headers = [ 'Authorization' => $authToken ];

        try {
            $response = $this->makeRequest($path, [], $headers);
            return json_decode($response->getBody(), true);
        } catch (Exception $e) {
            return ['exception' => $e->getMessage()];
        }
    }

    public function getJobResultById($jobId, string $authToken)
    {
        $path = "/job/$jobId/result";
        $headers = [ 'Accept' => 'text/csv', 'Authorization' => $authToken ];

        try {
            $response = $this->makeRequest($path, [], $headers);
            return $response->getBody()->getContents();
        } catch (Exception $e) {
            return ['exception' => $e->getMessage()];
        }
    }

    /**
     * @param $path
     * @param array $params
     * @param array $headers
     * @return ResponseInterface|null
     * @throws Exception already logged to Phlow
     */
    private function makeRequest($path, $params = [], $headers = [])
    {
        $guzzleClient = $this->getClient();
        $headers = array_merge([ 'x-sparefoot-app' => 'myfoot' ], $headers);
        $urlString = getenv('URL_CLIENT_API_SERVICE') . $path;

        try {
            $response = $guzzleClient->get($urlString, [
                'headers' => $headers,
                'query' => $params
            ]);
        } catch (RequestException $e) {
            $this->logException($path);
            if (($e->getResponse()->getStatusCode() >= 400) && ($e->getResponse()->getStatusCode() < 500)) {
                $response = $e->getResponse();
            } else {
                throw $e;
            }
        } catch (Exception $e) {
            $this->logException($path);
            throw $e;
        }

        return $response;
    }

    private function makeAsyncRequest($path, $params = [], $headers = [])
    {
        $guzzleClient = $this->getClient();
        $headers = array_merge([ 'x-sparefoot-app' => 'myfoot' ], $headers);
        $path = getenv('URL_CLIENT_API_SERVICE') . $path;

        try {
            $promise = $guzzleClient->getAsync($path, [
                'headers' => $headers,
                'query' => $params
            ]);
        } catch (Exception $e) {
            $this->logException($path);
            throw $e;
        }

        return $promise;
    }

    private function logException($path)
    {
        AccountMgmt_Service_Phlow::getClient()->increment(
            'service_request_exception',
            AccountMgmt_Service_Phlow::getClient()->arrayToCsv([
                'service' => 'client_api',
                'path' => $path
            ])
        );
    }

    /**
     * @return Client
     */
    private function getClient() {
        if ( !$this->client) {
            $this->client = new Client();
        }
        return $this->client;
    }
}
